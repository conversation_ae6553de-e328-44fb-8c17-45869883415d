import{b as e,s,j as t,a,B as i,z as o,u as n,U as r,F as l,J as c,ax as m,ay as d,N as p}from"../entries/index-CEzJO5Xy.js";import{d as u,r as j}from"./router-BtYqujaw.js";import{u as N,s as f,a as h,z as S}from"./zod-4O8Zwsja.js";import{L as b}from"./Layout-BQBjg4Lf.js";import{u as T,g as x}from"./BankDetailsService-Od7WKGPo.js";import{S as g}from"./SimpleBackdrop-Bf3qjF13.js";import{A as E}from"./Avatar-BtfxKR-8.js";import{s as I}from"./bank-details-form-C8d7v4La.js";import{P as A}from"./Paper-CcwAvfvc.js";import{F as _,I as C}from"./InputLabel-BbcIE26O.js";import{I as D}from"./Input-BQdee9z7.js";import{F as k,S as y}from"./Switch-BWPUOSX1.js";import{B}from"./Button-DGZYUY3P.js";import{F as w}from"./FormHelperText-DFSsjBsL.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./DressService-J0XavNJj.js";import"./LocationService-BtQFgoWL.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./SwitchBase-BIeqtL5F.js";const O=new e({fr:{SETTINGS_UPDATED:"Paramètres modifiés avec succès.",NETWORK_SETTINGS:"Paramètres Réseau",SETTINGS_EMAIL_NOTIFICATIONS:"Activer les notifications par email"},en:{SETTINGS_UPDATED:"Settings updated successfully.",NETWORK_SETTINGS:"Network settings",SETTINGS_EMAIL_NOTIFICATIONS:"Enable email notifications"},es:{SETTINGS_UPDATED:"Configuración actualizada correctamente.",NETWORK_SETTINGS:"Configuración de red",SETTINGS_EMAIL_NOTIFICATIONS:"Habilitar notificaciones por correo electrónico"},ar:{SETTINGS_UPDATED:"تم تحديث الإعدادات بنجاح.",NETWORK_SETTINGS:"إعدادات الشبكة",SETTINGS_EMAIL_NOTIFICATIONS:"تفعيل إشعارات البريد الإلكتروني"}});s(O);const P=S.object({accountHolder:S.string(),bankName:S.string(),iban:S.string(),swiftBic:S.string(),showBankDetailsPage:S.boolean()}),L=({bankDetails:e,onSubmit:s})=>{const n=u(),{register:r,control:l,handleSubmit:c,formState:{isSubmitting:m},setValue:d}=N({resolver:f(P),mode:"onSubmit"}),{showBankDetailsPage:p}=h({control:l});return j.useEffect((()=>{e&&(d("accountHolder",e.accountHolder),d("bankName",e.bankName),d("iban",e.iban),d("swiftBic",e.swiftBic),d("showBankDetailsPage",e.showBankDetailsPage))}),[e,d]),t.jsx(A,{className:"settings-form settings-form-wrapper",elevation:10,children:t.jsxs("form",{onSubmit:c((async t=>{try{const a={_id:e?._id,accountHolder:t.accountHolder,bankName:t.bankName,iban:t.iban,swiftBic:t.swiftBic,showBankDetailsPage:t.showBankDetailsPage},{status:n,data:r}=await T(a);200===n?(s&&s(r),i(O.SETTINGS_UPDATED)):o()}catch(a){o(a)}})),children:[t.jsx("h1",{className:"settings-form-title",children:I.BANK_DETAILS}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:I.ACCOUNT_HOLDER}),t.jsx(D,{...r("accountHolder"),type:"text",required:!0,autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:I.BANK_NAME}),t.jsx(D,{...r("bankName"),type:"text",required:!0,autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:I.IBAN}),t.jsx(D,{...r("iban"),type:"text",required:!0,autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:I.SWIFT_BIC}),t.jsx(D,{...r("swiftBic"),type:"text",required:!0,autoComplete:"off"})]}),t.jsx(_,{component:"fieldset",children:t.jsx(k,{control:t.jsx(y,{checked:p||!1,onChange:e=>d("showBankDetailsPage",e.target.checked)}),label:I.SHOW_BANK_DETAILS_PAGE})}),t.jsxs("div",{className:"buttons",children:[t.jsx(B,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:m,children:a.SAVE}),t.jsx(B,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>n("/"),children:a.CANCEL})]})]})})},v=S.object({fullName:S.string().min(1),email:S.string().email({message:a.EMAIL_NOT_VALID}),phone:S.string().refine((e=>!e||p.isMobilePhone(e)),{message:a.PHONE_NOT_VALID}).optional(),location:S.string().optional(),bio:S.string().optional()}),W=()=>{const e=u(),{user:s,setUser:p}=n(),[h,S]=j.useState(!1),[T,I]=j.useState(!1),[P,W]=j.useState(!0),[G,F]=j.useState(!1),[H,U]=j.useState(null),{register:q,handleSubmit:M,formState:{errors:R,isSubmitting:z},clearErrors:K,setValue:V}=N({resolver:f(v),mode:"onSubmit"});return t.jsxs(b,{onLoad:async e=>{if(e){p(e),S(l(e)),V("email",e.email),V("fullName",e.fullName),V("phone",e.phone||""),V("location",e.location||""),V("bio",e.bio||""),F(e.enableEmailNotifications||!1);const s=await x();s&&U(s),I(!0),W(!1)}},strict:!0,children:[T&&s&&t.jsxs("div",{className:"settings",children:[t.jsx(A,{className:"settings-form settings-form-wrapper",elevation:10,children:t.jsxs("form",{onSubmit:M((async e=>{try{if(!s)return;const t={_id:s._id,fullName:e.fullName,phone:e.phone||"",location:e.location||"",bio:e.bio||""};200===await m(t)?i(O.SETTINGS_UPDATED):o()}catch(t){o(t)}})),children:[t.jsx(E,{type:s.type,mode:"update",record:s,size:"large",readonly:!1,onBeforeUpload:()=>{W(!0)},onChange:e=>{const t=c(s);t.avatar=e,p(t),W(!1)},hideDelete:!h,color:"disabled",className:"avatar-ctn"}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:a.FULL_NAME}),t.jsx(D,{...q("fullName"),type:"text",required:!0,autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:a.EMAIL}),t.jsx(D,{...q("email"),type:"text",disabled:!0})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",error:!!R.phone,children:[t.jsx(C,{children:a.PHONE}),t.jsx(D,{...q("phone"),type:"text",autoComplete:"off",onChange:()=>K()}),t.jsx(w,{children:R.phone?.message||""})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{children:a.LOCATION}),t.jsx(D,{...q("location"),type:"text",autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{children:a.BIO}),t.jsx(D,{...q("bio"),type:"text",autoComplete:"off"})]}),t.jsxs("div",{className:"buttons",children:[t.jsx(B,{variant:"contained",className:"btn-primary btn-margin btn-margin-bottom",size:"small",onClick:()=>e("/change-password"),children:a.RESET_PASSWORD}),t.jsx(B,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:z,children:a.SAVE}),t.jsx(B,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>e("/"),children:a.CANCEL})]})]})}),t.jsxs(A,{className:"settings-net settings-net-wrapper",elevation:10,children:[t.jsx("h1",{className:"settings-form-title",children:O.NETWORK_SETTINGS}),t.jsx(_,{component:"fieldset",children:t.jsx(k,{control:t.jsx(y,{checked:G,onChange:async e=>{try{if(s){F(e.target.checked);const t=c(s);t.enableEmailNotifications=e.target.checked;const a={_id:s._id,enableEmailNotifications:t.enableEmailNotifications};200===await d(a)?(p(t),i(O.SETTINGS_UPDATED)):o()}else o()}catch(t){o(t)}}}),label:O.SETTINGS_EMAIL_NOTIFICATIONS})})]}),s.type===r.Admin&&t.jsx(L,{bankDetails:H,onSubmit:e=>U(e)})]}),P&&t.jsx(g,{text:a.PLEASE_WAIT})]})};export{W as default};
