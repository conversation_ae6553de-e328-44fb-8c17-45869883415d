import{j as a,p as e,a as s,h as i,R as t,z as r,G as c,e as o,aC as n,g as l,L as d,aD as h,aE as m}from"../entries/index-xsXxT3-W.js";import{r as v}from"./router-BtYqujaw.js";import{D as u,b as x,f as j,a as f,h as p,i as g}from"./DressService-DkS6e_O5.js";import{L as N,a as w,f as E,e as D,h as y,i as S}from"./LocationService-6NvQT9iL.js";import{B as L}from"./Badge-zckTAo43.js";import{T as C}from"./Tooltip-CKMkVqOx.js";import{B as R}from"./Box-Dm2ZtwWL.js";import{V as _}from"./Check-BO6X9Q-4.js";import{A as b}from"./Avatar-Dvwllg8p.js";import{c as z,D as A,a as I,b as O,d as T}from"./Grow-Cp8xsNYl.js";import{A as U}from"./AccountCircle-DdIeIbov.js";import{B as V}from"./Button-BeKLLPpp.js";const M=z([a.jsx("path",{d:"m13.99 15.41-4-4-4 4-.99-.99V19h14v-6.57l-1.01-1.01zM5 11.59l.99 1 4-4 4 4 4-4.01L19 9.59V5H5z",opacity:".3"},"0"),a.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16H5v-4.58l.99.99 4-4 4 4 4-3.99L19 12.43zm0-9.41-1.01-1.01-4 4.01-4-4-4 4-.99-1V5h14z"},"1")]),H=z(a.jsx("path",{d:"M12 7V3H2v18h20V7zm-2 12H4v-2h6zm0-4H4v-2h6zm0-4H4V9h6zm0-4H4V5h6zm10 12h-8V9h8zm-2-8h-4v2h4zm0 4h-4v2h4z"})),$=z([a.jsx("circle",{cx:"12",cy:"12",r:"3.2"},"0"),a.jsx("path",{d:"M9 2 7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5"},"1")]),k=({avatar:z,width:k,height:B,mode:F,type:P,record:G,size:W,readonly:K,color:J,className:Q,verified:X,hideDelete:Y,onValidate:q,onBeforeUpload:Z,onChange:aa})=>{const[ea,sa]=v.useState(!1),[ia,ta]=v.useState(!1),[ra,ca]=v.useState(!1),[oa,na]=v.useState(),[la,da]=v.useState(null),[ha,ma]=v.useState(!0);v.useEffect((()=>{da(z)}),[z]);const va=async(a,e)=>{if(k&&B){const s=window.URL||window.webkitURL,i=new Image,t=s.createObjectURL(a);i.onload=async()=>{k!==i.width||B!==i.height?q&&q(!1):(q&&q(!0),e&&await e()),s.revokeObjectURL(t)},i.src=t}else e&&e()},ua=()=>{if(!P)return void ca(!0);const a=document.getElementById("upload");a.value="",setTimeout((()=>{a.click()}),0)},xa=()=>{ta(!1)},ja=()=>P===t.Dress?"create"===F?o.CDN_TEMP_DRESSES:o.CDN_DRESSES:P===t.Location?"create"===F?o.CDN_TEMP_LOCATIONS:o.CDN_LOCATIONS:"create"===F?o.CDN_TEMP_USERS:o.CDN_USERS;v.useEffect((()=>{const a=e();s.setLanguage(a),i()?G?(na(G),P===t.Dress||P===t.Location?da(G.image):da(G.avatar),ma(!1)):"create"===F&&ma(!1):(sa(!0),r())}),[G,P,F]);const fa={width:o.SUPPLIER_IMAGE_WIDTH},pa={width:o.DRESS_IMAGE_WIDTH},ga={maxWidth:"100%",maxHeight:"100%"},Na=la?la.startsWith("http")?la:c(ja(),la):"",wa=la?a.jsx(b,{src:Na,className:W?`avatar-${W}`:"avatar"}):a.jsx(a.Fragment,{}),Ea=a.jsx(U,{className:W?`avatar-${W}`:"avatar",color:J||"inherit"});return ea||ha?null:a.jsxs("div",{className:Q,children:[la?K?P===t.Dress?a.jsx("img",{style:pa,src:c(ja(),la),alt:oa&&oa.name}):P===t.Location?a.jsx("img",{style:ga,src:c(ja(),la),alt:oa&&oa.name}):P===t.Supplier?a.jsx("div",{className:"supplier-avatar-readonly",children:a.jsx("img",{src:c(ja(),la),alt:oa&&oa.fullName})}):X&&oa&&oa.verified?a.jsx(L,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:a.jsx(C,{title:s.VERIFIED,children:a.jsx(R,{sx:{borderRadius:"50%"},className:W?`user-avatar-verified-${W}`:"user-avatar-verified-medium",children:a.jsx(_,{className:W?`user-avatar-verified-icon-${W}`:"user-avatar-verified-icon-medium"})})}),children:wa}):wa:a.jsx(L,{overlap:"circular",anchorOrigin:{vertical:"top",horizontal:"right"},badgeContent:Y?a.jsx(a.Fragment,{}):a.jsx(C,{title:s.DELETE_IMAGE,children:a.jsx(R,{sx:{borderRadius:"50%"},className:"avatar-action-box",onClick:a=>{a.preventDefault(),ta(!0)},children:a.jsx(M,{className:"avatar-action-icon"})})}),children:a.jsx(L,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},className:P===t.Supplier?"supplier-avatar":"",badgeContent:a.jsx(C,{title:s.UPLOAD_IMAGE,children:a.jsx(R,{sx:{borderRadius:"50%"},className:"avatar-action-box",onClick:ua,children:a.jsx($,{className:"avatar-action-icon"})})}),children:P===t.Dress?a.jsx("div",{className:"dress-avatar",children:a.jsx("img",{src:c(ja(),la),alt:oa&&oa.name})}):P===t.Location?a.jsx("div",{className:"location-avatar",children:a.jsx("img",{src:c(ja(),la),alt:oa&&oa.name})}):P===t.Supplier?a.jsx("img",{style:fa,src:c(ja(),la),alt:oa&&oa.fullName}):a.jsx(b,{src:Na,className:W?`avatar-${W}`:"avatar"})})}):K?P===t.Dress?a.jsx(u,{style:pa,color:J||"inherit"}):P===t.Location?a.jsx(N,{style:ga,color:J||"inherit"}):P===t.Supplier?a.jsx(H,{style:fa,color:J||"inherit"}):X&&oa&&oa.verified?a.jsx(L,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:a.jsx(C,{title:s.VERIFIED,children:a.jsx(R,{sx:{borderRadius:"50%"},className:W?`user-avatar-verified-${W}`:"user-avatar-verified-medium",children:a.jsx(_,{className:W?`user-avatar-verified-icon-${W}`:"user-avatar-verified-icon-medium"})})}),children:Ea}):Ea:a.jsx(L,{overlap:"circular",anchorOrigin:{vertical:"top",horizontal:"right"},children:a.jsx(L,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:a.jsx(C,{title:s.UPLOAD_IMAGE,children:a.jsx(R,{sx:{borderRadius:"50%"},className:"avatar-action-box",onClick:ua,children:a.jsx($,{className:"avatar-action-icon"})})}),children:P===t.Dress?a.jsx(u,{className:W?`avatar-${W}`:"avatar",color:J||"inherit"}):P===t.Location?a.jsx(N,{className:W?`avatar-${W}`:"avatar",color:J||"inherit"}):P===t.Supplier?a.jsx(H,{className:W?`avatar-${W}`:"avatar",color:J||"inherit"}):a.jsx(U,{className:W?`avatar-${W}`:"avatar",color:J||"inherit"})})}),a.jsxs(A,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ra,children:[a.jsx(I,{className:"dialog-header",children:s.INFO}),a.jsx(O,{children:s.USER_TYPE_REQUIRED}),a.jsx(T,{className:"dialog-actions",children:a.jsx(V,{onClick:()=>{ca(!1)},variant:"contained",className:"btn-secondary",children:s.CLOSE})})]}),a.jsxs(A,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ia,children:[a.jsx(I,{className:"dialog-header",children:s.CONFIRM_TITLE}),a.jsx(O,{children:s.DELETE_AVATAR_CONFIRM}),a.jsxs(T,{className:"dialog-actions",children:[a.jsx(V,{onClick:()=>{xa()},className:"btn-secondary",children:s.CANCEL}),a.jsx(V,{onClick:async()=>{try{if(P===t.Admin||P===t.Supplier||P===t.User)if(oa&&"update"===F){const{_id:a}=oa;if(!a)return void r();if(200===await n(a)){const e=await l(a);e?(na(e),da(null),aa&&aa(""),xa()):r()}else r()}else oa||"create"!==F||(200===await d(la)?(da(null),aa&&aa(""),xa()):r());else if(P===t.Dress)if(oa||"create"!==F){if(oa&&"update"===F){const{_id:a}=oa;if(!a)return void r();if(200===await j(a)){const e=await f(a);e?(na(e),da(null),aa&&aa(""),xa()):r()}else r()}}else 200===await x(la)?(da(null),aa&&aa(""),xa()):r();else if(P===t.Location)if(oa||"create"!==F){if(oa&&"update"===F){const{_id:a}=oa;if(!a)return void r();if(200===await E(a)){const e=await D(a);e?(na(e),da(null),aa&&aa(""),xa()):r()}else r()}}else 200===await w(la)?(da(null),aa&&aa(""),xa()):r()}catch(a){r(a)}},color:"error",variant:"contained",children:s.DELETE})]})]}),!K&&a.jsx("input",{id:"upload",type:"file",hidden:!0,onChange:a=>{if(!a.target.files)return void r();Z&&Z();const e=new FileReader,s=a.target.files[0];e.onloadend=async()=>{if(P===t.Admin||P===t.Supplier||P===t.User){if("create"===F){const a=async()=>{try{la&&await d(la);const a=await h(s);da(a),aa&&aa(a)}catch(a){r(a)}};await va(s,a)}else if(oa&&"update"===F){const a=async()=>{try{const{_id:a}=oa;if(!a)return void r();if(200===await m(a,s)){const e=await l(a);e?(na(e),da(e.avatar||""),aa&&aa(e.avatar||"")):r()}else r()}catch(a){r(a)}};await va(s,a)}}else if(P===t.Dress){if("create"===F){const a=async()=>{try{la&&await x(la);const a=await p(s);da(a),aa&&aa(a)}catch(a){r(a)}};await va(s,a)}else if("update"===F){const a=async()=>{try{if(!oa)return void r();const{_id:a}=oa;if(!a)return void r();if(200===await g(a,s)){const e=await f(a);e?(na(e),da(e.image||""),aa&&aa(e.image||"")):r()}else r()}catch(a){r(a)}};await va(s,a)}}else if(P===t.Location)if("create"===F){const a=async()=>{try{la&&await w(la);const a=await y(s);da(a),aa&&aa(a)}catch(a){r(a)}};await va(s,a)}else if("update"===F){const a=async()=>{try{if(!oa)return void r();const{_id:a}=oa;if(!a)return void r();if(200===await S(a,s)){const e=await D(a);e?(na(e),da(e.image||""),aa&&aa(e.image||"")):r()}else r()}catch(a){r(a)}};await va(s,a)}},e.readAsDataURL(s)}})]})};export{k as A,H as S};
