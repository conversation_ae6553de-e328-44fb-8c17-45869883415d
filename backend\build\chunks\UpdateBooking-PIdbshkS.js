import{a9 as e,j as s,a as t,F as r,z as i}from"../entries/index-CEzJO5Xy.js";import{d as a,r as o}from"./router-BtYqujaw.js";import{L as n}from"./Layout-BQBjg4Lf.js";import{g as l,u as m}from"./BookingService-BJ4R0IJT.js";import{U as d,D as c,s as p}from"./create-booking-CE8s_Uic.js";import{L as u}from"./LocationSelectList-DYJOB_U9.js";import j from"./NoMatch-jvHCs4x8.js";import{E as h}from"./Error-koMug0_G.js";import{P as f}from"./Paper-CcwAvfvc.js";import{F as x,I as g}from"./InputLabel-BbcIE26O.js";import{L as S,A as b}from"./useMobilePicker-Cpitw7qm.js";import{D as v}from"./DateTimePicker-Di47kkTW.js";import{S as C}from"./TextField-BAse--ht.js";import{M as L}from"./MenuItem-suKfXYI2.js";import{I as P}from"./Input-BQdee9z7.js";import{F as N,S as I}from"./Switch-BWPUOSX1.js";import{B as E}from"./Button-DGZYUY3P.js";import{F as R}from"./FormHelperText-DFSsjBsL.js";import"./vendor-dblfw9z9.js";import"./MultipleSelect-C7xTvWe9.js";import"./Autocomplete-CviOU_ku.js";import"./OutlinedInput-g8mR4MM3.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-CtA82Ni6.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./Backdrop-Bzn12VyM.js";import"./AccountCircle-khVEeiad.js";import"./Chip-CAtDqtgp.js";import"./IconButton-CnBvmeAK.js";import"./Avatar-Dix3YM8x.js";import"./LocationService-BtQFgoWL.js";import"./Flag-BR6CpE1z.js";import"./DressService-J0XavNJj.js";import"./ListItemAvatar-Bv6onK36.js";import"./Menu-ZU0DMgjT.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-Cay5TZBz.js";import"./ListItemText-DBn_RuMq.js";import"./listItemTextClasses-DFwCkkgK.js";import"./getThemeProps-gt86ccpv.js";import"./format-4arn0GRM.js";import"./ListItem-D1VHRhQp.js";import"./KeyboardArrowRight-BV-h2cWM.js";import"./SwitchBase-BIeqtL5F.js";const _=()=>{const _=a(),[k,y]=o.useState(),[D,O]=o.useState(!1),[A,T]=o.useState(!1),[F,w]=o.useState(!1),[W,q]=o.useState(!1),[M,U]=o.useState(),[B,K]=o.useState(""),[z,G]=o.useState(""),[H,V]=o.useState(""),[J,Q]=o.useState(""),[Z,$]=o.useState(""),[X,Y]=o.useState(null),[ee,se]=o.useState(null),[te,re]=o.useState(e.Pending),[ie,ae]=o.useState(!1),[oe,ne]=o.useState(!1),[le,me]=o.useState(""),[de,ce]=o.useState(!1),[pe,ue]=o.useState(!1);return W?s.jsx(h,{}):F?s.jsx(j,{}):s.jsx(n,{onLoad:async e=>{if(e&&e.verified){y(e);const t=new URLSearchParams(window.location.search);if(t.has("b")){const e=t.get("b");if(e&&""!==e)try{const s=await l(e);s?(U(s),K("string"==typeof s.supplier?s.supplier:s.supplier._id),G("string"==typeof s.dress?s.dress:s.dress._id),V("string"==typeof s.customer?s.customer:s.customer._id),Q("string"==typeof s.pickupLocation?s.pickupLocation:s.pickupLocation._id),$("string"==typeof s.dropOffLocation?s.dropOffLocation:s.dropOffLocation._id),Y(new Date(s.from)),se(new Date(s.to)),re(s.status),ae(s.cancellation||!1),ne(s.amendments||!1),me(s.price?.toString()||""),T(!0)):w(!0)}catch(s){console.error(s),q(!0)}else w(!0)}else w(!0)}},strict:!0,children:s.jsx("div",{className:"create-booking",children:s.jsxs(f,{className:"booking-form booking-form-wrapper",elevation:10,style:A?{}:{display:"none"},children:[s.jsx("h1",{className:"booking-form-title",children:t.UPDATE}),s.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(!(B&&z&&H&&J&&Z&&X&&ee&&le))return ce(!0),void ue(!1);O(!0),ce(!1),ue(!1);const e={booking:{_id:M._id,supplier:B,dress:z,customer:H,pickupLocation:J,dropOffLocation:Z,from:X,to:ee,status:te,cancellation:ie,amendments:oe,price:Number(le)}};200===await m(e)?_("/"):i()}catch(s){i(s)}finally{O(!1)}},children:[r(k)&&s.jsx(x,{fullWidth:!0,margin:"dense",children:s.jsx(d,{label:t.SUPPLIER,required:!0,value:B?[{_id:B,name:""}]:[],onChange:e=>{K(e.length>0?e[0]._id:"")}})}),s.jsx(x,{fullWidth:!0,margin:"dense",children:s.jsx(c,{label:p.DRESS,required:!0,supplier:B,value:z,onChange:e=>{G(e.length>0?e[0]._id:"")}})}),s.jsx(x,{fullWidth:!0,margin:"dense",children:s.jsx(d,{label:p.DRIVER,required:!0,value:H?[{_id:H,name:""}]:[],onChange:e=>{V(e.length>0?e[0]._id:"")}})}),s.jsx(x,{fullWidth:!0,margin:"dense",children:s.jsx(u,{label:p.PICKUP_LOCATION,required:!0,value:J?[{_id:J,name:""}]:[],onChange:e=>{Q(e.length>0?e[0]._id:"")}})}),s.jsx(x,{fullWidth:!0,margin:"dense",children:s.jsx(u,{label:p.DROP_OFF_LOCATION,required:!0,value:Z?[{_id:Z,name:""}]:[],onChange:e=>{$(e.length>0?e[0]._id:"")}})}),s.jsxs(S,{dateAdapter:b,children:[s.jsx(x,{fullWidth:!0,margin:"dense",children:s.jsx(v,{label:p.FROM,value:X,onChange:Y,slotProps:{textField:{required:!0,variant:"standard"}}})}),s.jsx(x,{fullWidth:!0,margin:"dense",children:s.jsx(v,{label:p.TO,value:ee,onChange:se,slotProps:{textField:{required:!0,variant:"standard"}}})})]}),s.jsxs(x,{fullWidth:!0,margin:"dense",children:[s.jsx(g,{className:"required",children:p.STATUS}),s.jsxs(C,{value:te,onChange:e=>{re(e.target.value)},variant:"standard",required:!0,children:[s.jsx(L,{value:e.Pending,children:p.PENDING}),s.jsx(L,{value:e.Deposit,children:p.DEPOSIT}),s.jsx(L,{value:e.Paid,children:p.PAID}),s.jsx(L,{value:e.Reserved,children:p.RESERVED}),s.jsx(L,{value:e.Cancelled,children:p.CANCELLED})]})]}),s.jsxs(x,{fullWidth:!0,margin:"dense",children:[s.jsx(g,{className:"required",children:p.PRICE}),s.jsx(P,{type:"number",required:!0,value:le,onChange:e=>me(e.target.value),inputProps:{min:0,step:.01}})]}),s.jsx(x,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(N,{control:s.jsx(I,{checked:ie,onChange:e=>ae(e.target.checked),color:"primary"}),label:p.CANCELLATION})}),s.jsx(x,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(N,{control:s.jsx(I,{checked:oe,onChange:e=>ne(e.target.checked),color:"primary"}),label:p.AMENDMENTS})}),s.jsxs("div",{className:"buttons",children:[s.jsx(E,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:D,children:t.UPDATE}),s.jsx(E,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>_("/"),children:t.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[de&&s.jsx(R,{error:!0,children:t.FORM_ERROR}),pe&&s.jsx(R,{error:!0,children:t.GENERIC_ERROR})]})]})]})})})};export{_ as default};
