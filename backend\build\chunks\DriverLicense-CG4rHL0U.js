import{g as s,d as e,n as t,a as n,b as a}from"./normalizeInterval-CrKB48xo.js";import{a as o,b as i,e as r,c}from"./DatePicker-BDzBD9XN.js";import{t as l,c as u,n as d,m}from"./fr-DJt_zj3p.js";import{b as f,s as h,c as E,j,a as p,z as D,ax as y,av as N,ay as _,O as I,e as g,az as v,aA as x}from"../entries/index-xsXxT3-W.js";import{r as C}from"./router-BtYqujaw.js";import{I as R}from"./Input-D1AdR9CM.js";import{O as A}from"./OutlinedInput-BX8yFQbF.js";import{I as S}from"./IconButton-CxOCoGF3.js";import{U as T}from"./create-supplier-BpB8o_Zh.js";import{V as w}from"./Visibility-D3efFHY1.js";import{D as F}from"./Delete-BfnPAJno.js";function M(s,e,t){const{years:n=0,months:a=0,weeks:r=0,days:c=0,hours:d=0,minutes:m=0,seconds:f=0}=e,h=l(s,t?.in),E=a||n?o(h,a+12*n):h,j=c||r?i(E,c+7*r):E;return u(s,+j+1e3*(f+60*(m+60*d)))}function U(s,e){const t=+l(s)-+l(e);return t<0?-1:t>0?1:t}function k(o,i){const{start:u,end:f}=t(i?.in,o),h={},E=function(s,e){const[t,n]=d(undefined,s,e),a=U(t,n),o=Math.abs(function(s,e){const[t,n]=d(void 0,s,e);return t.getFullYear()-n.getFullYear()}(t,n));t.setFullYear(1584),n.setFullYear(1584);const i=a*(o-+(U(t,n)===-a));return 0===i?0:i}(f,u);E&&(h.years=E);const j=M(u,{years:h.years}),p=function(s,e){const[t,n,a]=d(undefined,s,s,e),o=U(n,a),i=Math.abs(function(s,e){const[t,n]=d(void 0,s,e);return 12*(t.getFullYear()-n.getFullYear())+(t.getMonth()-n.getMonth())}(n,a));if(i<1)return 0;1===n.getMonth()&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-o*i);let u=U(n,a)===-o;(function(s,e){const t=l(s,e?.in);return+r(t,e)===+c(t,e)})(t)&&1===i&&1===U(t,a)&&(u=!1);const m=o*(i-+u);return 0===m?0:m}(f,j);p&&(h.months=p);const D=M(j,{months:h.months}),y=n(f,D);y&&(h.days=y);const N=M(D,{days:h.days}),_=function(e,t,n){const[a,o]=d(n?.in,e,t),i=(+a-+o)/m;return s(n?.roundingMethod)(i)}(f,N);_&&(h.hours=_);const I=M(N,{hours:h.hours}),g=a(f,I);g&&(h.minutes=g);const v=function(t,n){const a=e(t,n)/1e3;return s(void 0)(a)}(f,M(I,{minutes:h.minutes}));return v&&(h.seconds=v),h}const L=new f({fr:{CREATE_USER_HEADING:"Nouvelle utilisateur",BIRTH_DATE:"Date de naissance"},en:{CREATE_USER_HEADING:"New user",BIRTH_DATE:"Birth date"},es:{CREATE_USER_HEADING:"Nuevo usuario",BIRTH_DATE:"Fecha de nacimiento"}});h(L);const H=s=>{const e=E.c(21),{user:t,variant:n,className:a,onUpload:o,onDelete:i}=s,r=void 0===n?"standard":n,[c,l]=C.useState(t?.license||null),u=O;let d;e[0]!==c||e[1]!==o||e[2]!==t?(d=s=>{if(!s.target.files)return void D();const e=new FileReader,n=s.target.files[0];e.onloadend=async()=>{try{let s=null;if(t){const e=await y(t._id,n);200===e.status?s=e.data:D()}else c&&await N(c),s=await _(n);s&&o&&o(s),l(s)}catch(s){D(s)}},e.readAsDataURL(n)},e[0]=c,e[1]=o,e[2]=t,e[3]=d):d=e[3];const m=d,f=`driver-license ${a||""}`;let h,M,U,k,L,H;return e[4]!==c||e[5]!==r?(h="standard"===r?j.jsx(R,{value:c||p.UPLOAD_DRIVER_LICENSE,readOnly:!0,onClick:u,className:"filename"}):j.jsx(A,{value:c||p.UPLOAD_DRIVER_LICENSE,readOnly:!0,onClick:u,className:"filename"}),e[4]=c,e[5]=r,e[6]=h):h=e[6],e[7]===Symbol.for("react.memo_cache_sentinel")?(M=j.jsx(S,{size:"small",onClick:u,children:j.jsx(T,{className:"icon"})}),e[7]=M):M=e[7],e[8]!==c||e[9]!==i||e[10]!==t?(U=c&&j.jsxs(j.Fragment,{children:[j.jsx(S,{size:"small",onClick:()=>{const s=`${I(t?g.CDN_LICENSES:g.CDN_TEMP_LICENSES,"/")}/${c}`;v(s)},children:j.jsx(w,{className:"icon"})}),j.jsx(S,{size:"small",onClick:async()=>{try{let s;s=t?await x(t._id):await N(c),200===s?(l(null),i&&i()):D()}catch(s){D(s)}},children:j.jsx(F,{className:"icon"})})]}),e[8]=c,e[9]=i,e[10]=t,e[11]=U):U=e[11],e[12]!==U?(k=j.jsxs("div",{className:"actions",children:[M,U]}),e[12]=U,e[13]=k):k=e[13],e[14]!==m?(L=j.jsx("input",{id:"upload-license",type:"file",hidden:!0,onChange:m}),e[14]=m,e[15]=L):L=e[15],e[16]!==f||e[17]!==h||e[18]!==k||e[19]!==L?(H=j.jsxs("div",{className:f,children:[h,k,L]}),e[16]=f,e[17]=h,e[18]=k,e[19]=L,e[20]=H):H=e[20],H};async function O(){const s=document.getElementById("upload-license");s.value="",setTimeout((()=>{s.click()}),0)}export{H as D,k as i,L as s};
