/**
 * Connect to database.
 *
 * @export
 * @async
 * @param {string} uri
 * @param {boolean} ssl
 * @param {boolean} debug
 * @returns {Promise<boolean>}
 */
export declare const connect: (uri: string, ssl: boolean, debug: boolean) => Promise<boolean>;
/**
 * Close database connection.
 *
 * @export
 * @async
 * @param {boolean} force
 * @returns {Promise<void>}
 */
export declare const close: (force?: boolean) => Promise<void>;
/**
 * Initialize locations.
 * If a new language is added, english values will be added by default with the new language.
 * The new language values must be updated from the backend.
 *
 * @async
 * @returns {*}
 */
export declare const initializeLocations: () => Promise<boolean>;
/**
 * Initialize countries.
 * If a new language is added, english values will be added by default with the new language.
 * The new language values must be updated from the backend.
 *
 * @async
 * @returns {*}
 */
export declare const initializeCountries: () => Promise<boolean>;
/**
 * Initialize admin user.
 *
 * @async
 * @returns {*}
 */
export declare const initializeAdmin: () => Promise<boolean>;
/**
 * Initialize parkingSpots.
 * If a new language is added, english values will be added by default with the new language.
 * The new language values must be updated from the backend.
 *
 * @async
 * @returns {*}
 */
export declare const initializeParkingSpots: () => Promise<boolean>;
/**
 * Initialize database.
 *
 * @async
 * @returns {Promise<boolean>}
 */
export declare const initialize: () => Promise<boolean>;
