import{c as e,e as o,j as r}from"../entries/index-CEzJO5Xy.js";import{C as n}from"./Button-DGZYUY3P.js";import{T as s,B as t}from"./Backdrop-Bzn12VyM.js";const i=o.isMobile?56:64,c=o=>{const c=e.c(8),{progress:m,text:l}=o;let a,d,p,x;return c[0]===Symbol.for("react.memo_cache_sentinel")?(a={color:"#fff",zIndex:1402,height:window.innerHeight,marginTop:(document.documentElement.scrollTop>0?document.documentElement.scrollTop-i:0)+"px"},c[0]=a):a=c[0],c[1]!==m?(d=m&&r.jsx(n,{color:"inherit",sx:{marginRight:5}}),c[1]=m,c[2]=d):d=c[2],c[3]!==l?(p=r.jsx(s,{color:"inherit",children:l}),c[3]=l,c[4]=p):p=c[4],c[5]!==d||c[6]!==p?(x=r.jsx("div",{children:r.jsxs(t,{open:!0,sx:a,children:[d,p]})}),c[5]=d,c[6]=p,c[7]=x):x=c[7],x};export{c as S};
