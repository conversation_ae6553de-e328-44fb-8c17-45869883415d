import{j as s,a as r,aK as e,aL as a,aM as o,B as t,z as n,e as i}from"../entries/index-xsXxT3-W.js";import{d,r as m}from"./router-BtYqujaw.js";import{u as c,s as l,z as p}from"./zod-4O8Zwsja.js";import{L as w}from"./Layout-DaeN7D4t.js";import{s as S}from"./change-password-CrWxmtFu.js";import{S as j}from"./SimpleBackdrop-CqsJhYJ4.js";import{P as u}from"./Paper-C-atefOs.js";import{F as P,I as R}from"./InputLabel-C8rcdOGQ.js";import{I as f}from"./Input-D1AdR9CM.js";import{F as h}from"./FormHelperText-DDZ4BMA4.js";import{B as x}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Czag2Ija.js";import"./useSlot-DiTut-u0.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const _=p.object({currentPassword:p.string().min(i.PASSWORD_MIN_LENGTH,{message:r.PASSWORD_ERROR}).optional(),newPassword:p.string().min(i.PASSWORD_MIN_LENGTH,{message:r.PASSWORD_ERROR}),confirmPassword:p.string()}).refine((s=>s.newPassword===s.confirmPassword),{path:["confirmPassword"],message:r.PASSWORDS_DONT_MATCH}),g=()=>{const i=d(),[p,g]=m.useState(),[N,A]=m.useState(),[E,O]=m.useState(!0),[W,D]=m.useState(!1),[b,C]=m.useState(!1),{register:y,handleSubmit:T,formState:{errors:I,isSubmitting:L},clearErrors:v,setError:H,reset:q}=c({resolver:l(_),mode:"onSubmit"});return s.jsxs(w,{onLoad:async s=>{if(s){const r=new URLSearchParams(window.location.search);let a=s?._id;r.has("u")&&(a=r.get("u")||void 0,A(a));const o=await e(a);C(200===o),g(s),O(!1),D(!0)}},strict:!0,children:[s.jsx("div",{className:"password-reset",style:W?{}:{display:"none"},children:s.jsxs(u,{className:"password-reset-form password-reset-form-wrapper",elevation:10,children:[s.jsx("h1",{className:"password-reset-form-title",children:S.CHANGE_PASSWORD_HEADING}),s.jsxs("form",{className:"form",onSubmit:T((async({currentPassword:s,newPassword:r})=>{try{if(!N&&!p)return;if(200===(b?await a(N||p?._id,s):200)){const e={_id:N||p?._id,password:s||"",newPassword:r,strict:b};200===await o(e)?(C(!0),q(),t(S.PASSWORD_UPDATE)):n(null,S.PASSWORD_UPDATE_ERROR)}else H("currentPassword",{message:S.CURRENT_PASSWORD_ERROR})}catch(e){n(e)}})),children:[b&&s.jsxs(P,{fullWidth:!0,margin:"dense",error:!!I.currentPassword,children:[s.jsx(R,{className:"required",children:S.CURRENT_PASSWORD}),s.jsx(f,{...y("currentPassword"),type:"password",required:!0,onChange:()=>v()}),s.jsx(h,{children:I.currentPassword?.message||""})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",error:!!I.newPassword,children:[s.jsx(R,{className:"required",children:S.NEW_PASSWORD}),s.jsx(f,{...y("newPassword"),type:"password",required:!0,onChange:()=>v()}),s.jsx(h,{children:I.newPassword?.message||""})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",error:!!I.confirmPassword,children:[s.jsx(R,{className:"required",children:r.CONFIRM_PASSWORD}),s.jsx(f,{...y("confirmPassword"),type:"password",required:!0,onChange:()=>v()}),s.jsx(h,{children:I.confirmPassword?.message||""})]}),s.jsxs("div",{className:"buttons",children:[s.jsx(x,{type:"submit",className:"btn-primary btn-margin btn-margin-bottom",size:"small",variant:"contained",disabled:L,children:r.RESET_PASSWORD}),s.jsx(x,{className:"btn-secondary btn-margin-bottom",size:"small",variant:"contained",onClick:()=>i("/"),children:r.CANCEL})]})]})]})}),E&&s.jsx(j,{text:r.PLEASE_WAIT})]})};export{g as default};
