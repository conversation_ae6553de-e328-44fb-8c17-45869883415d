import{c as s,j as e}from"../entries/index-CEzJO5Xy.js";import{I as o}from"./Info-C_WcR51V.js";const a=a=>{const n=s.c(6),{className:c,value:i}=a,r=`info-box${c?" ":""}${c||""}`;let t,m,l;return n[0]===Symbol.for("react.memo_cache_sentinel")?(t=e.jsx(o,{className:"info-box-icon"}),n[0]=t):t=n[0],n[1]!==i?(m=e.jsx("span",{className:"info-box-text",children:i}),n[1]=i,n[2]=m):m=n[2],n[3]!==r||n[4]!==m?(l=e.jsxs("div",{className:r,children:[t,m]}),n[3]=r,n[4]=m,n[5]=l):l=n[5],l};export{a as I};
