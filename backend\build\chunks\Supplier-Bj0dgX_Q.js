import{c as e,e as s,R as a,j as r,G as o,I as i,a as t,J as l,z as c}from"../entries/index-CEzJO5Xy.js";import{d as n,r as m}from"./router-BtYqujaw.js";import{s as p}from"./supplier-list-Br6cMD8V.js";import{d,a as j}from"./SupplierService-DSnTbAgG.js";import{L as h}from"./Layout-BQBjg4Lf.js";import{S as f}from"./SimpleBackdrop-Bf3qjF13.js";import{A as u}from"./Avatar-BtfxKR-8.js";import{D as x}from"./DressList-P4mtcbk2.js";import{I as S}from"./InfoBox-Csm94Gbd.js";import{E as _}from"./Error-koMug0_G.js";import N from"./NoMatch-jvHCs4x8.js";import{T as E}from"./Backdrop-Bzn12VyM.js";import{L as v}from"./Link-B-UCzRRJ.js";import{T as y}from"./Tooltip-BkJF6Mu0.js";import{I as b}from"./IconButton-CnBvmeAK.js";import{E as D}from"./Edit-DIF9Bumd.js";import{D as L}from"./Delete-CnqjtpsJ.js";import{D as g}from"./DialogTitle-BZXwroUN.js";import{a as w,b as C,D as I}from"./Grow-CjOKj0i1.js";import{B as k}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./DressService-J0XavNJj.js";import"./LocationService-BtQFgoWL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./useSlot-CtA82Ni6.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./Pager-C0zDCFUN.js";import"./ArrowForwardIos-BMce9t8T.js";import"./Paper-CcwAvfvc.js";import"./SupplierBadge-C4yEjxHC.js";import"./Straighten-6ibCitj5.js";import"./Clear-BpXDeTL8.js";import"./Visibility-BpW589Gm.js";import"./Info-C_WcR51V.js";import"./ownerWindow-ChLfdzZL.js";const A=()=>{const A=e.c(43),T=n(),[B,P]=m.useState(),[R,U]=m.useState();let G;A[0]===Symbol.for("react.memo_cache_sentinel")?(G=[],A[0]=G):G=A[0];const[W,$]=m.useState(G),[z,F]=m.useState(!1),[H,M]=m.useState(!1),[O,V]=m.useState(!0),[q,J]=m.useState(!1),[K,Q]=m.useState(!1),[X,Y]=m.useState(-1),[Z,ee]=m.useState(s.DEFAULT_LANGUAGE);let se;A[1]===Symbol.for("react.memo_cache_sentinel")?(se=()=>{V(!0)},A[1]=se):se=A[1];const ae=se;let re;A[2]!==R||A[3]!==B?(re=e=>{if(B&&R&&B._id===R._id){const s=l(B);s.avatar=e,P(s)}V(!1)},A[2]=R,A[3]=B,A[4]=re):re=A[4];const oe=re;let ie;A[5]===Symbol.for("react.memo_cache_sentinel")?(ie=()=>{Q(!0)},A[5]=ie):ie=A[5];const te=ie;let le;A[6]!==T||A[7]!==R?(le=async()=>{if(R)try{Q(!1),200===await d(R._id)?T("/suppliers"):c()}catch(e){c(e)}else c()},A[6]=T,A[7]=R,A[8]=le):le=A[8];const ce=le;let ne;A[9]===Symbol.for("react.memo_cache_sentinel")?(ne=()=>{Q(!1)},A[9]=ne):ne=A[9];const me=ne;let pe;A[10]===Symbol.for("react.memo_cache_sentinel")?(pe=e=>{e&&Y(e.rowCount)},A[10]=pe):pe=A[10];const de=pe;let je;A[11]===Symbol.for("react.memo_cache_sentinel")?(je=e=>{Y(e)},A[11]=je):je=A[11];const he=je;let fe;A[12]===Symbol.for("react.memo_cache_sentinel")?(fe=async e=>{if(P(e),ee(e?.language),e&&e.verified){const e=new URLSearchParams(window.location.search);if(e.has("c")){const s=e.get("c");if(s&&""!==s)try{const e=await j(s);e?(U(e),$([e._id]),M(!0),V(!1)):(V(!1),J(!0))}catch{V(!1),F(!0),M(!1)}else V(!1),J(!0)}else V(!1),J(!0)}},A[12]=fe):fe=A[12];const ue=fe,xe=B&&R&&(B.type===a.Admin||B._id===R._id);let Se,_e,Ne,Ee,ve,ye,be,De,Le,ge;return A[13]!==xe||A[14]!==Z||A[15]!==T||A[16]!==oe||A[17]!==X||A[18]!==R||A[19]!==W||A[20]!==B||A[21]!==H?(Se=H&&R&&W&&r.jsxs("div",{className:"supplier",children:[r.jsxs("div",{className:"col-1",children:[r.jsx("section",{className:"supplier-avatar-sec",children:xe?r.jsx(u,{record:R,type:a.Supplier,mode:"update",size:"large",hideDelete:!0,onBeforeUpload:ae,onChange:oe,readonly:!xe,color:"disabled",className:"supplier-avatar"}):r.jsxs("div",{className:"car-supplier",children:[r.jsx("span",{className:"car-supplier-logo",children:r.jsx("img",{src:o(s.CDN_USERS,R.avatar),alt:R.fullName,style:{width:s.SUPPLIER_IMAGE_WIDTH}})}),r.jsx("span",{className:"car-supplier-info",children:R.fullName})]})}),xe&&r.jsx(E,{variant:"h4",className:"supplier-name",children:R.fullName}),R.bio&&(i(R.bio)?r.jsx(v,{href:R.bio,target:"_blank",rel:"noreferrer",className:"supplier-bio-link",children:R.bio}):r.jsx(E,{variant:"h6",className:"supplier-info",children:R.bio})),R.location&&""!==R.location&&r.jsx(E,{variant:"h6",className:"supplier-info",children:R.location}),R.phone&&""!==R.phone&&r.jsx(E,{variant:"h6",className:"supplier-info",children:R.phone}),r.jsxs("div",{className:"supplier-actions",children:[xe&&r.jsx(y,{title:t.UPDATE,children:r.jsx(b,{onClick:()=>T(`/update-supplier?c=${R._id}`),children:r.jsx(D,{})})}),xe&&r.jsx(y,{title:t.DELETE,children:r.jsx(b,{"data-id":R._id,onClick:te,children:r.jsx(L,{})})})]}),X>0&&r.jsx(S,{value:`${X} ${X>1?t.DRESSES:t.DRESS}`,className:"dress-count"})]}),r.jsx("div",{className:"col-2",children:r.jsx(x,{user:B,suppliers:W,keyword:"",reload:!1,language:Z,onLoad:de,onDelete:he,hideSupplier:!0})})]}),A[13]=xe,A[14]=Z,A[15]=T,A[16]=oe,A[17]=X,A[18]=R,A[19]=W,A[20]=B,A[21]=H,A[22]=Se):Se=A[22],A[23]===Symbol.for("react.memo_cache_sentinel")?(_e=r.jsx(g,{className:"dialog-header",children:t.CONFIRM_TITLE}),Ne=r.jsx(w,{children:p.DELETE_SUPPLIER}),A[23]=_e,A[24]=Ne):(_e=A[23],Ne=A[24]),A[25]===Symbol.for("react.memo_cache_sentinel")?(Ee=r.jsx(k,{onClick:me,variant:"contained",className:"btn-secondary",children:t.CANCEL}),A[25]=Ee):Ee=A[25],A[26]!==ce?(ve=r.jsxs(C,{className:"dialog-actions",children:[Ee,r.jsx(k,{onClick:ce,variant:"contained",color:"error",children:t.DELETE})]}),A[26]=ce,A[27]=ve):ve=A[27],A[28]!==K||A[29]!==ve?(ye=r.jsxs(I,{disableEscapeKeyDown:!0,maxWidth:"xs",open:K,children:[_e,Ne,ve]}),A[28]=K,A[29]=ve,A[30]=ye):ye=A[30],A[31]!==O?(be=O&&r.jsx(f,{text:t.LOADING}),A[31]=O,A[32]=be):be=A[32],A[33]!==z?(De=z&&r.jsx(_,{}),A[33]=z,A[34]=De):De=A[34],A[35]!==q?(Le=q&&r.jsx(N,{hideHeader:!0}),A[35]=q,A[36]=Le):Le=A[36],A[37]!==ye||A[38]!==be||A[39]!==De||A[40]!==Le||A[41]!==Se?(ge=r.jsxs(h,{onLoad:ue,strict:!0,children:[Se,ye,be,De,Le]}),A[37]=ye,A[38]=be,A[39]=De,A[40]=Le,A[41]=Se,A[42]=ge):ge=A[42],ge};export{A as default};
