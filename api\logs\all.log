2025-06-04 02:20:27 info: Database is connected
2025-06-04 02:20:28 info: Initializing locations...
2025-06-04 02:20:28 info: Locations initialized
2025-06-04 02:20:28 info: Initializing countries...
2025-06-04 02:20:28 info: Countries initialized
2025-06-04 02:20:28 info: Initializing parkingSpots...
2025-06-04 02:20:28 info: ParkingSpots initialized
2025-06-04 02:20:28 info: Initializing admin user...
2025-06-04 02:20:28 info: Default admin user created successfully
2025-06-04 02:20:28 info: Email: <EMAIL>
2025-06-04 02:20:28 info: Password: admin123
2025-06-04 02:20:28 info: Please change the password after first login
2025-06-04 02:20:28 info: Initializing dress rental data...
2025-06-04 02:20:28 error: Error while initializing dress rental data: language override unsupported: ar MongoBulkWriteError: language override unsupported: ar
    at OrderedBulkOperation.handleWriteError (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongodb\lib\bulk\common.js:817:19)
    at executeCommands (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongodb\lib\bulk\common.js:346:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BulkWriteShimOperation.execute (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongodb\lib\bulk\common.js:521:16)
    at async tryOperation (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongodb\lib\operations\execute_operation.js:207:20)
    at async executeOperation (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongodb\lib\operations\execute_operation.js:75:16)
    at async OrderedBulkOperation.execute (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongodb\lib\bulk\common.js:806:16)
    at async BulkWriteOperation.execute (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongodb\lib\operations\bulk_write.js:34:16)
    at async InsertManyOperation.execute (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongodb\lib\operations\insert.js:82:25)
    at async tryOperation (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongodb\lib\operations\execute_operation.js:207:20)
2025-06-04 02:45:42 info: Database is connected
2025-06-04 02:45:42 info: Initializing locations...
2025-06-04 02:45:42 info: Locations initialized
2025-06-04 02:45:42 info: Initializing countries...
2025-06-04 02:45:42 info: Countries initialized
2025-06-04 02:45:42 info: Initializing parkingSpots...
2025-06-04 02:45:42 info: ParkingSpots initialized
2025-06-04 02:45:42 info: Initializing admin user...
2025-06-04 02:45:42 info: Admin user already exists
2025-06-04 02:45:42 info: Initializing dress rental data...
2025-06-04 02:45:42 info: Palestine country created
2025-06-04 02:45:43 info: Sofia Bridal & Soiree Boutique supplier created
2025-06-04 02:45:43 info: Al-Jalbouni Circle location created
2025-06-04 02:45:43 error: Error while initializing dress rental data: Dress validation failed: material: `tulle` is not a valid enum value for path `material`. ValidationError: Dress validation failed: material: `tulle` is not a valid enum value for path `material`.
    at Document.invalidate (C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongoose\lib\document.js:3343:32)
    at C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongoose\lib\document.js:3104:17
    at C:\Users\<USER>\Desktop\Workspace\bookDress\api\node_modules\mongoose\lib\schemaType.js:1407:9
    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)
