import{b as e,s,c as r,j as i}from"../entries/index-CEzJO5Xy.js";import{L as a}from"./Layout-BQBjg4Lf.js";import{P as l}from"./Paper-CcwAvfvc.js";import"./vendor-dblfw9z9.js";import"./router-BtYqujaw.js";import"./Button-DGZYUY3P.js";const n=new e({fr:{TITLE:"Plans tarifaires",FREE_PLAN:"Plan gratuit",FREE_PLAN_PRICE:"Gratuit",FEATURE_1:"Créer un nombre illimité de voitures",FEATURE_2:"1 voiture dans les résultats de recherche",BASIC_PLAN:"Plan de base",BASIC_PLAN_PRICE:"$10/mois",FEATURE_3:"5 voitures dans les résultats de recherche",FEATURE_4:"Support prioritaire",PREMIUM_PLAN:"Plan premium",CONTACT_US:"Contactez-nous",FEATURE_5:"Voitures illimitées dans les résultats de recherche"},en:{TITLE:"Pricing Plans",FREE_PLAN:"Free Plan",FREE_PLAN_PRICE:"Free",FEATURE_1:"Create unlimited cars",FEATURE_2:"1 car in search results",BASIC_PLAN:"Basic Plan",BASIC_PLAN_PRICE:"$10/month",FEATURE_3:"5 cars in search results",FEATURE_4:"Priority support",PREMIUM_PLAN:"Premium Plan",CONTACT_US:"Contact us",FEATURE_5:"Unlimited cars in search results"},es:{TITLE:"Planes de precios",FREE_PLAN:"Plan gratuito",FREE_PLAN_PRICE:"Gratis",FEATURE_1:"Crear coches ilimitados",FEATURE_2:"1 coche en los resultados de búsqueda",BASIC_PLAN:"Plan básico",BASIC_PLAN_PRICE:"$10/mes",FEATURE_3:"5 coches en los resultados de búsqueda",FEATURE_4:"Soporte prioritario",PREMIUM_PLAN:"Plan premium",CONTACT_US:"Contáctenos",FEATURE_5:"Coches ilimitados en los resultados de búsqueda"},ar:{TITLE:"خطط الأسعار",FREE_PLAN:"الخطة المجانية",FREE_PLAN_PRICE:"مجاني",FEATURE_1:"إنشاء فساتين غير محدودة",FEATURE_2:"فستان واحد في نتائج البحث",BASIC_PLAN:"الخطة الأساسية",BASIC_PLAN_PRICE:"$10/شهر",FEATURE_3:"5 فساتين في نتائج البحث",FEATURE_4:"دعم أولوية",PREMIUM_PLAN:"الخطة المميزة",CONTACT_US:"اتصل بنا",FEATURE_5:"فساتين غير محدودة في نتائج البحث"}});s(n);const c=()=>{const e=r.c(10),s=E;let c,t,_,o,A,p,m,P,R,d;return e[0]===Symbol.for("react.memo_cache_sentinel")?(c=i.jsx("h1",{className:"pricing-title",children:n.TITLE}),e[0]=c):c=e[0],e[1]===Symbol.for("react.memo_cache_sentinel")?(t=i.jsx("h2",{className:"pricing-plan-title",children:n.FREE_PLAN}),_=i.jsx("p",{className:"pricing-plan-price",children:n.FREE_PLAN_PRICE}),e[1]=t,e[2]=_):(t=e[1],_=e[2]),e[3]===Symbol.for("react.memo_cache_sentinel")?(o=i.jsxs(l,{className:"pricing-plan pricing-plan-wrapper",elevation:10,children:[t,_,i.jsxs("ul",{className:"pricing-plan-features",children:[i.jsx("li",{children:n.FEATURE_1}),i.jsx("li",{children:n.FEATURE_2})]})]}),e[3]=o):o=e[3],e[4]===Symbol.for("react.memo_cache_sentinel")?(A=i.jsx("h2",{className:"pricing-plan-title",children:n.BASIC_PLAN}),p=i.jsx("p",{className:"pricing-plan-price",children:n.BASIC_PLAN_PRICE}),e[4]=A,e[5]=p):(A=e[4],p=e[5]),e[6]===Symbol.for("react.memo_cache_sentinel")?(m=i.jsxs(l,{className:"pricing-plan pricing-plan-wrapper",elevation:10,children:[A,p,i.jsxs("ul",{className:"pricing-plan-features",children:[i.jsx("li",{children:n.FEATURE_1}),i.jsx("li",{children:n.FEATURE_3}),i.jsx("li",{children:n.FEATURE_4})]})]}),e[6]=m):m=e[6],e[7]===Symbol.for("react.memo_cache_sentinel")?(P=i.jsx("h2",{className:"pricing-plan-title",children:n.PREMIUM_PLAN}),R=i.jsx("p",{className:"pricing-plan-price",children:n.CONTACT_US}),e[7]=P,e[8]=R):(P=e[7],R=e[8]),e[9]===Symbol.for("react.memo_cache_sentinel")?(d=i.jsx(a,{onLoad:s,strict:!0,children:i.jsxs("div",{className:"pricing",children:[c,i.jsxs("div",{className:"pricing-plans",children:[o,m,i.jsxs(l,{className:"pricing-plan pricing-plan-wrapper",elevation:10,children:[P,R,i.jsxs("ul",{className:"pricing-plan-features",children:[i.jsx("li",{children:n.FEATURE_1}),i.jsx("li",{children:n.FEATURE_5}),i.jsx("li",{children:n.FEATURE_4})]})]})]})]})}),e[9]=d):d=e[9],d};function E(){}export{c as default};
