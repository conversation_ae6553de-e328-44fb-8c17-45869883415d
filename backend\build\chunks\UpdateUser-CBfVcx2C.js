import{b as e,s as t,R as r,j as s,a,a9 as i,U as n,e as l,g as o,F as d,z as u,p as m,aw as p,J as c,B as j,au as h,A as f,N as x}from"../entries/index-xsXxT3-W.js";import{d as S,r as g}from"./router-BtYqujaw.js";import{L as v}from"./Layout-DaeN7D4t.js";import{s as E}from"./create-supplier-BpB8o_Zh.js";import{s as N,D as A,i as I}from"./DriverLicense-CG4rHL0U.js";import{v as _}from"./SupplierService-9DC5V5ZJ.js";import y from"./NoMatch-DMPclUW6.js";import{E as C}from"./Error-DRzAdbbx.js";import{S as D}from"./SimpleBackdrop-CqsJhYJ4.js";import{A as T}from"./Avatar-CvDHTACZ.js";import{D as b}from"./DatePicker-mLpxfIrI.js";import{P as L}from"./Paper-C-atefOs.js";import{I as R}from"./Info-CNP9gYBt.js";import{F as P,I as U}from"./InputLabel-C8rcdOGQ.js";import{S as w}from"./TextField-D_yQOTzE.js";import{M}from"./MenuItem-P0BnGnrT.js";import{I as O}from"./Input-D1AdR9CM.js";import{F as W}from"./FormHelperText-DDZ4BMA4.js";import{F as k,S as B}from"./Switch-C5asfh_w.js";import{B as G}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-DiTut-u0.js";import"./Backdrop-Czag2Ija.js";import"./normalizeInterval-CrKB48xo.js";import"./fr-DJt_zj3p.js";import"./DatePicker-BDzBD9XN.js";import"./getThemeProps-DSP27jpP.js";import"./IconButton-CxOCoGF3.js";import"./useFormControl-B7jXtRD7.js";import"./ListItem-Bmdw8GrH.js";import"./isHostComponent-DR4iSCFs.js";import"./Menu-C_-X8cS7.js";import"./mergeSlotProps-DEridHif.js";import"./Chip-MGF1mKZa.js";import"./OutlinedInput-BX8yFQbF.js";import"./Visibility-D3efFHY1.js";import"./Delete-BfnPAJno.js";import"./DressService-DkS6e_O5.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./listItemTextClasses-BcbgzvlE.js";import"./SwitchBase-DrUkTXjH.js";const F=new e({fr:{UPDATE_USER_HEADING:"Modification de l'utilisateur"},en:{UPDATE_USER_HEADING:"User update"},es:{UPDATE_USER_HEADING:"Actualización del usuario"}});t(F);const H=()=>{const e=S(),[t,H]=g.useState(),[q,z]=g.useState(),[V,Y]=g.useState(!1),[$,K]=g.useState(!1),[Q,J]=g.useState(!1),[X,Z]=g.useState(""),[ee,te]=g.useState(""),[re,se]=g.useState(""),[ae,ie]=g.useState(""),[ne,le]=g.useState(""),[oe,de]=g.useState(!1),[ue,me]=g.useState(!1),[pe,ce]=g.useState(!1),[je,he]=g.useState(""),[fe,xe]=g.useState(!1),[Se,ge]=g.useState(""),[ve,Ee]=g.useState(),[Ne,Ae]=g.useState(!0),[Ie,_e]=g.useState(!0),[ye,Ce]=g.useState(!1),[De,Te]=g.useState(!0),[be,Le]=g.useState(""),[Re,Pe]=g.useState(""),[Ue,we]=g.useState(""),[Me,Oe]=g.useState(!1),[We,ke]=g.useState(!1),Be=async(e,t=!0)=>{const r=e||X;if(!r||!t&&(t||r===q?.fullName))return ce(!1),!0;try{return 200===await _({fullName:r})?(ce(!1),de(!1),!0):(ce(!0),xe(!1),de(!1),!1)}catch(s){return u(s),!0}},Ge=e=>{if(e){const t=x.isMobilePhone(e);return _e(t),t}return _e(!0),!0},Fe=e=>{if(e&&h(e)&&Se===r.User){const t=(I({start:e,end:new Date}).years??0)>=l.MINIMUM_AGE;return Ae(t),t}return Ae(!0),!0},He=Se===r.Supplier,qe=Se===r.User,ze=Q||t&&q&&t.type===r.Supplier&&q.type===r.User&&q.supplier===t._id;return s.jsxs(v,{onLoad:async e=>{if(e&&e.verified){me(!0);const r=new URLSearchParams(window.location.search);if(r.has("u")){const s=r.get("u");if(s&&""!==s)try{const t=await o(s);if(t){if(!(e.type===n.Admin||t.type===n.Supplier&&e._id===t._id||t.type===n.User&&e._id===t.supplier))return me(!1),void K(!0);H(e),z(t),J(d(e)),ge(t.type||""),te(t.email||""),he(t.avatar||""),Z(t.fullName||""),se(t.phone||""),ie(t.location||""),le(t.bio||""),Ee(t&&t.birthDate?new Date(t.birthDate):void 0),Ce(t.payLater||!1),Te(t.licenseRequired||!1),Le(t.minimumRentalDays?.toString()||""),Pe(t.priceChangeRate?.toString()||""),we(t.supplierDressLimit?.toString()||""),Oe(!!t.notifyAdminOnNewDress),ke(!!t.blacklisted),Y(!0),me(!1)}else me(!1),K(!0)}catch(t){u(t),me(!1),Y(!1)}else me(!1),K(!0)}else me(!1),K(!0)}},strict:!0,children:[t&&q&&V&&s.jsx("div",{className:"update-user",children:s.jsxs(L,{className:"user-form user-form-wrapper",elevation:10,children:[s.jsx("h1",{className:"user-form-title",children:F.UPDATE_USER_HEADING}),s.jsxs("form",{onSubmit:async e=>{try{if(e.preventDefault(),!q)return void u();if(Se===r.Supplier){if(!(await Be(X,!1)))return}else ce(!1);if(!Ge(re))return;if(!Fe(ve))return;if(Se===r.Supplier&&!je)return xe(!0),void de(!1);const t=m(),s={_id:q._id,phone:re,location:ae,bio:ne,fullName:X,language:t,type:Se,avatar:je,birthDate:ve,minimumRentalDays:be?Number(be):void 0,priceChangeRate:Re?Number(Re):void 0,supplierDressLimit:Ue?Number(Ue):void 0,notifyAdminOnNewDress:Se===r.Supplier?Me:void 0,blacklisted:We};if(Se===r.Supplier&&(s.payLater=ye,s.licenseRequired=De),200===await p(s)){const e=c(q);e.fullName=X,e.type=Se,z(e),j(a.UPDATED)}else u(),de(!1)}catch(t){u(t)}},children:[s.jsx(T,{type:Se,mode:"update",record:q,size:"large",readonly:!1,onBeforeUpload:()=>{me(!0)},onChange:e=>{if(t&&q&&t._id===q._id){const r=c(t);r.avatar=e,H(r)}const s=c(q);s.avatar=e,me(!1),z(s),he(e),null!==e&&Se===r.Supplier&&xe(!1)},color:"disabled",className:"avatar-ctn",hideDelete:Se===r.Supplier}),He&&s.jsxs("div",{className:"info",children:[s.jsx(R,{}),s.jsx("span",{children:E.RECOMMENDED_IMAGE_SIZE})]}),Q&&s.jsxs(P,{fullWidth:!0,margin:"dense",style:{marginTop:He?0:39},children:[s.jsx(U,{className:"required",children:a.TYPE}),s.jsxs(w,{label:a.TYPE,value:Se,onChange:async e=>{const t=e.target.value;ge(e.target.value),t===r.Supplier?await Be(X):ce(!1)},variant:"standard",required:!0,fullWidth:!0,children:[s.jsx(M,{value:r.Admin,children:i(n.Admin)}),s.jsx(M,{value:r.Supplier,children:i(n.Supplier)}),s.jsx(M,{value:r.User,children:i(n.User)})]})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(U,{className:"required",children:a.FULL_NAME}),s.jsx(O,{id:"full-name",type:"text",error:pe,required:!0,onBlur:async e=>{Se===r.Supplier?await Be(e.target.value):ce(!1)},onChange:e=>{Z(e.target.value),e.target.value||ce(!1)},autoComplete:"off",value:X}),s.jsx(W,{error:pe,children:pe&&E.INVALID_SUPPLIER_NAME||""})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(U,{className:"required",children:a.EMAIL}),s.jsx(O,{id:"email",type:"text",value:ee,disabled:!0})]}),s.jsx(P,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(B,{checked:We,onChange:e=>{ke(e.target.checked)},color:"primary"}),label:a.BLACKLISTED,title:a.BLACKLISTED_TOOLTIP})}),qe&&s.jsxs(s.Fragment,{children:[s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(b,{label:N.BIRTH_DATE,value:ve,required:!0,onChange:e=>{if(e){const t=Fe(e);Ee(e),Ae(t)}},language:q&&q.language||l.DEFAULT_LANGUAGE}),s.jsx(W,{error:!Ne,children:!Ne&&a.BIRTH_DATE_NOT_VALID||""})]}),s.jsx(A,{user:q,className:"driver-license-field"})]}),He&&s.jsxs(s.Fragment,{children:[s.jsx(P,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(B,{checked:ye,onChange:e=>{Ce(e.target.checked)},color:"primary"}),label:a.PAY_LATER})}),s.jsx(P,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(B,{checked:De,onChange:e=>{Te(e.target.checked)},color:"primary"}),label:a.LICENSE_REQUIRED})}),s.jsx(P,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(B,{checked:Me,disabled:t?.type===n.Supplier,onChange:e=>{Oe(e.target.checked)},color:"primary"}),label:a.NOTIFY_ADMIN_ON_NEW_CAR})}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(U,{children:a.SUPPLIER_CAR_LIMIT}),s.jsx(O,{type:"text",onChange:e=>{we(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^\\d+$"}},value:Ue,disabled:t?.type===n.Supplier})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(U,{children:a.MIN_RENTAL_DAYS}),s.jsx(O,{type:"text",onChange:e=>{Le(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^\\d+$"}},value:be})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(U,{children:a.PRICE_CHANGE_RATE}),s.jsx(O,{type:"text",onChange:e=>{Pe(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^-?\\d+(\\.\\d+)?$"}},value:Re,disabled:t?.type===n.Supplier})]})]}),s.jsxs("div",{className:"info",children:[s.jsx(R,{}),s.jsx("span",{children:a.OPTIONAL})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(U,{children:a.PHONE}),s.jsx(O,{id:"phone",type:"text",onChange:e=>{se(e.target.value),e.target.value||_e(!0)},onBlur:e=>{Ge(e.target.value)},autoComplete:"off",value:re,error:!Ie}),s.jsx(W,{error:!Ie,children:!Ie&&a.PHONE_NOT_VALID||""})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(U,{children:a.LOCATION}),s.jsx(O,{id:"location",type:"text",onChange:e=>{ie(e.target.value)},autoComplete:"off",value:ae})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(U,{children:a.BIO}),s.jsx(O,{id:"bio",type:"text",onChange:e=>{le(e.target.value)},autoComplete:"off",value:ne})]}),ze&&s.jsx(P,{fullWidth:!0,margin:"dense",className:"resend-activation-link",children:s.jsx(G,{variant:"outlined",onClick:async()=>{try{200===await f(ee,!1,Se===r.User?"frontend":"backend")?j(a.ACTIVATION_EMAIL_SENT):u()}catch(e){u(e)}},children:a.RESEND_ACTIVATION_LINK})}),s.jsxs("div",{className:"buttons",children:[s.jsx(G,{variant:"contained",className:"btn-primary btn-margin btn-margin-bottom",size:"small",onClick:()=>e(`/change-password?u=${q._id}`),children:a.RESET_PASSWORD}),s.jsx(G,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",children:a.SAVE}),s.jsx(G,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{e("/users")},children:a.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[oe&&s.jsx(C,{message:a.GENERIC_ERROR}),fe&&s.jsx(C,{message:a.IMAGE_REQUIRED})]})]})]})}),ue&&s.jsx(D,{text:a.PLEASE_WAIT}),$&&s.jsx(y,{hideHeader:!0})]})};export{H as default};
