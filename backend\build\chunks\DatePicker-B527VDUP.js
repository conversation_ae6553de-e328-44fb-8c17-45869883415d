import{c as e,j as a}from"../entries/index-CEzJO5Xy.js";import{r}from"./router-BtYqujaw.js";import{e as t,D as o}from"./DatePicker-CyKPL9FL.js";import{L as s,A as n}from"./useMobilePicker-Cpitw7qm.js";import{f as l}from"./fr-CaQg1DLH.js";import{e as i}from"./format-4arn0GRM.js";const c=c=>{const m=e.c(29),{value:f,label:u,minDate:d,maxDate:p,required:j,language:D,variant:x,readOnly:h,onChange:v,onError:y}=c,[b,_]=r.useState(null),[g,E]=r.useState();let P,S,k,q;m[0]!==f?(P=()=>{_(f||null)},S=[f],m[0]=f,m[1]=P,m[2]=S):(P=m[1],S=m[2]),r.useEffect(P,S),m[3]!==d?(k=()=>{if(d){const e=new Date(d);e.setHours(10,0,0,0),E(e)}else E(void 0)},q=[d],m[3]=d,m[4]=k,m[5]=q):(k=m[4],q=m[5]),r.useEffect(k,q);const w="fr"===D?l:"es"===D?t:i;let A,C;m[6]===Symbol.for("react.memo_cache_sentinel")?(A=["year","month","day"],m[6]=A):A=m[6],m[7]!==g||m[8]!==v||m[9]!==y?(C=e=>{e&&e.setHours(10,0,0,0),_(e),v&&v(e),e&&g&&e<g&&y&&y("minDate",e)},m[7]=g,m[8]=v,m[9]=y,m[10]=C):C=m[10];const H=x||"standard";let L,O,B,F,M;return m[11]!==j||m[12]!==H?(L={variant:H,required:j},m[11]=j,m[12]=H,m[13]=L):L=m[13],m[14]===Symbol.for("react.memo_cache_sentinel")?(O={actions:["accept","cancel","clear"]},m[14]=O):O=m[14],m[15]!==L?(B={textField:L,actionBar:O},m[15]=L,m[16]=B):B=m[16],m[17]!==u||m[18]!==p||m[19]!==g||m[20]!==y||m[21]!==h||m[22]!==B||m[23]!==C||m[24]!==b?(F=a.jsx(o,{label:u,views:A,value:b,readOnly:h,onChange:C,onError:y,minDate:g,maxDate:p,slotProps:B}),m[17]=u,m[18]=p,m[19]=g,m[20]=y,m[21]=h,m[22]=B,m[23]=C,m[24]=b,m[25]=F):F=m[25],m[26]!==F||m[27]!==w?(M=a.jsx(s,{adapterLocale:w,dateAdapter:n,children:F}),m[26]=F,m[27]=w,m[28]=M):M=m[28],M};export{c as D};
