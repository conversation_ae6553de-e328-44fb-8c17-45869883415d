import{b as e,s,a,aK as t,j as r,aL as o,e as i,z as n,aM as l,B as m,c}from"../entries/index-CEzJO5Xy.js";import{d as E,r as S}from"./router-BtYqujaw.js";import{L as d}from"./Layout-BQBjg4Lf.js";import{z as u,u as j,s as C}from"./zod-4O8Zwsja.js";import{P as N}from"./Paper-CcwAvfvc.js";import{F as A,I as b}from"./InputLabel-BbcIE26O.js";import{O as p}from"./OutlinedInput-g8mR4MM3.js";import{F as f}from"./FormHelperText-DFSsjBsL.js";import{B as x,C as T}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-CtA82Ni6.js";const h=new e({fr:{CONTACT_HEADING:"Contact",SUBJECT:"Objet",MESSAGE:"Message",SEND:"Envoyer",MESSAGE_SENT:"Message envoyé"},en:{CONTACT_HEADING:"Contact",SUBJECT:"Subject",MESSAGE:"Message",SEND:"Send",MESSAGE_SENT:"Message sent"},es:{CONTACT_HEADING:"Contacto",SUBJECT:"Asunto",MESSAGE:"Mensaje",SEND:"Enviar",MESSAGE_SENT:"Mensaje enviado"},ar:{CONTACT_HEADING:"اتصل بنا",SUBJECT:"الموضوع",MESSAGE:"الرسالة",SEND:"إرسال",MESSAGE_SENT:"تم إرسال الرسالة"}});s(h);const g=u.object({email:u.string().email({message:a.EMAIL_NOT_VALID}),subject:u.string(),message:u.string()}),M=({user:e,className:s})=>{const c=E(),{reCaptchaLoaded:d,generateReCaptchaToken:u}=t(),[M,G]=S.useState(!1),{register:_,setValue:I,handleSubmit:L,reset:y,formState:{errors:D,isSubmitting:v},clearErrors:B}=j({resolver:C(g),mode:"onSubmit"}),O=S.useCallback((e=>{e&&(G(!0),I("email",e.email))}),[I]);return S.useEffect((()=>{O(e)}),[O,e]),r.jsxs(N,{className:(s?`${s} `:"")+"contact-form",elevation:10,children:[r.jsx("h1",{className:"contact-form-title",children:h.CONTACT_HEADING}),r.jsxs("form",{onSubmit:L((async s=>{try{console.log("boo");let a="";d&&(a=await u(),await o(a)||(a="")),i.RECAPTCHA_ENABLED;const t={from:s.email,to:i.CONTACT_EMAIL,subject:s.subject,message:s.message,isContactForm:!0};200===await l(t)?(y(),O(e),m(h.MESSAGE_SENT)):n()}catch(a){n(a)}})),children:[!M&&r.jsxs(A,{fullWidth:!0,margin:"dense",error:!!D.email,children:[r.jsx(b,{className:"required",children:a.EMAIL}),r.jsx(p,{type:"text",..._("email"),label:a.EMAIL,required:!0,autoComplete:"off",onChange:()=>B()}),r.jsx(f,{error:!!D.email,children:D.email?.message||""})]}),r.jsxs(A,{fullWidth:!0,margin:"dense",children:[r.jsx(b,{className:"required",children:h.SUBJECT}),r.jsx(p,{type:"text",..._("subject"),label:h.SUBJECT,required:!0,autoComplete:"off"})]}),r.jsxs(A,{fullWidth:!0,margin:"dense",children:[r.jsx(b,{className:"required",children:h.MESSAGE}),r.jsx(p,{type:"text",label:h.MESSAGE,..._("message"),autoComplete:"off",required:!0,multiline:!0,minRows:7,maxRows:7})]}),r.jsxs("div",{className:"buttons",children:[r.jsx(x,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom btn","aria-label":"Send",disabled:v,children:v?r.jsx(T,{color:"inherit",size:24}):h.SEND}),r.jsx(x,{variant:"outlined",color:"primary",className:"btn-margin-bottom btn","aria-label":"Cancel",onClick:()=>{c("/")},children:a.CANCEL})]})]})]})},G=()=>{const e=c.c(3),[s,a]=S.useState();let t;e[0]===Symbol.for("react.memo_cache_sentinel")?(t=e=>{a(e)},e[0]=t):t=e[0];const o=t;let i;return e[1]!==s?(i=r.jsx(d,{onLoad:o,strict:!0,children:r.jsx("div",{className:"contact",children:r.jsx(M,{user:s,className:"form"})})}),e[1]=s,e[2]=i):i=e[2],i};export{G as default};
