import{F as s,j as e,R as i,G as a,e as t,$ as r,V as l,a1 as o,W as n,S as c,a4 as m,a as d,h as p,g as j,a5 as x}from"../entries/index-xsXxT3-W.js";import{d as h,r as N}from"./router-BtYqujaw.js";import{L as u}from"./Layout-DaeN7D4t.js";import{D as f,a as S,d as L}from"./DressService-DkS6e_O5.js";import{S as v}from"./SimpleBackdrop-CqsJhYJ4.js";import E from"./NoMatch-DMPclUW6.js";import{E as T}from"./Error-FiYP5RHa.js";import{A as b}from"./Avatar-CvDHTACZ.js";import{B as C}from"./BookingList-XbPSaIIt.js";import{T as A}from"./Tooltip-CKMkVqOx.js";import{C as g,L as D}from"./Straighten-Isz6BfHc.js";import{V as I}from"./Check-BO6X9Q-4.js";import{C as _}from"./Clear-CDOl64hX.js";import{B}from"./Button-BeKLLPpp.js";import{D as O,a as y,b as R,d as w}from"./Grow-Cp8xsNYl.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Czag2Ija.js";import"./useSlot-DiTut-u0.js";import"./Paper-C-atefOs.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Box-Dm2ZtwWL.js";import"./Avatar-Dvwllg8p.js";import"./cars-xqBbVU4C.js";import"./BookingStatus-BaSj8uqV.js";import"./InputLabel-C8rcdOGQ.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";import"./TextField-D_yQOTzE.js";import"./OutlinedInput-BX8yFQbF.js";import"./Input-D1AdR9CM.js";import"./FormHelperText-DDZ4BMA4.js";import"./Menu-C_-X8cS7.js";import"./mergeSlotProps-DEridHif.js";import"./MenuItem-P0BnGnrT.js";import"./listItemTextClasses-BcbgzvlE.js";import"./Link-sHEcszvT.js";import"./fr-DJt_zj3p.js";import"./DataGrid-DJUEcXft.js";import"./getThemeProps-DSP27jpP.js";import"./Switch-C5asfh_w.js";import"./SwitchBase-DrUkTXjH.js";import"./IconButton-CxOCoGF3.js";import"./Toolbar-BTa0QYME.js";import"./KeyboardArrowRight-LSgfnPVa.js";import"./Chip-MGF1mKZa.js";import"./Autocomplete-CWN5GAd4.js";import"./ListItemText-DUhWzkV9.js";import"./Checkbox-F0AjCtoF.js";import"./Edit-Bc0UCPtn.js";import"./Delete-BfnPAJno.js";const k=()=>{const k=h(),[M,U]=N.useState(),[z,P]=N.useState(!0),[F,G]=N.useState(!1),[V,$]=N.useState(!1),[H,W]=N.useState(!1),[K,Z]=N.useState(),[J,q]=N.useState(!1),[Q,X]=N.useState([]),[Y,ss]=N.useState([]),[es,is]=N.useState(0);N.useEffect((()=>{const s=document.querySelector(".dress-sec");s&&is(s.clientHeight+75)}),[K]);const as=M&&K&&(s(M)||K.supplier._id===M._id);return e.jsxs(u,{onLoad:async()=>{P(!0),$(!1),G(!1);try{const s=new URLSearchParams(window.location.search);if(s.has("dr")){const e=s.get("dr");if(e&&""!==e){const s=p();if(s){const i=await j(s._id);if(i){U(i);const s=await S(e);if(s){Z(s),W(!0);const e=[s.supplier._id];X(e.filter((s=>void 0!==s)));const i=x().map((s=>s.value));ss(i)}else G(!0)}else G(!0)}else G(!0)}else G(!0)}else G(!0)}catch(s){$(!0)}P(!1)},strict:!0,children:[H&&K&&K.supplier&&e.jsxs("div",{className:"dress",children:[e.jsxs("div",{className:"col-1",children:[e.jsxs("section",{className:"dress-sec",children:[e.jsx("div",{className:"name",children:e.jsx("h2",{children:K.name})}),e.jsxs("div",{className:"dress-img",children:[e.jsx(b,{type:i.Dress,mode:"update",record:K,size:"large",readonly:!as,hideDelete:!0,onBeforeUpload:()=>{P(!0)},onChange:()=>{P(!1)},color:"disabled",className:"avatar-ctn"}),e.jsxs("div",{className:"dress-supplier",children:[e.jsx("span",{className:"dress-supplier-logo",children:e.jsx("img",{src:a(t.CDN_USERS,K.supplier.avatar),alt:K.supplier.fullName})}),e.jsx("span",{className:"dress-supplier-info",children:K.supplier.fullName})]})]}),e.jsx("div",{className:"dress-info",children:e.jsxs("ul",{className:"dress-info-list",children:[e.jsx("li",{className:"dress-type",children:e.jsx(A,{title:r(K.type),placement:"top",children:e.jsxs("div",{className:"dress-info-list-item",children:[e.jsx(f,{}),e.jsx("span",{className:"dress-info-list-text",children:l(K.type)})]})})}),e.jsx("li",{className:"dress-size",children:e.jsx(A,{title:o(K.size),placement:"top",children:e.jsx("div",{className:"dress-info-list-item",children:e.jsx("span",{className:"dress-info-list-text",children:n(K.size)})})})}),e.jsx("li",{className:"dress-color",children:e.jsx(A,{title:c.COLOR||"Color",placement:"top",children:e.jsxs("div",{className:"dress-info-list-item",children:[e.jsx(g,{}),e.jsx("span",{className:"dress-info-list-text",children:K.color})]})})}),e.jsx("li",{className:"dress-length",children:e.jsx(A,{title:c.LENGTH||"Length",placement:"top",children:e.jsxs("div",{className:"dress-info-list-item",children:[e.jsx(D,{}),e.jsx("span",{className:"dress-info-list-text",children:`${K.length} ${c.CM||"cm"}`})]})})}),e.jsx("li",{className:"dress-material",children:e.jsx(A,{title:c.MATERIAL||"Material",placement:"top",children:e.jsx("div",{className:"dress-info-list-item",children:e.jsx("span",{className:"dress-info-list-text",children:m(K.material)})})})}),e.jsx("li",{className:"dress-rentals",children:e.jsx(A,{title:c.RENTALS_COUNT||"Times rented",placement:"top",children:e.jsx("div",{className:"dress-info-list-item",children:e.jsx("span",{className:"dress-info-list-text",children:`${c.RENTALS_COUNT||"Times rented"}: ${K.rentals||0}`})})})}),e.jsx("li",{className:K.available?"dress-available":"dress-unavailable",children:e.jsx(A,{title:K.available?c.DRESS_AVAILABLE_TOOLTIP:c.DRESS_UNAVAILABLE_TOOLTIP,children:e.jsxs("div",{className:"dress-info-list-item",children:[K.available?e.jsx(I,{}):e.jsx(_,{}),K.available?e.jsx("span",{className:"dress-info-list-text",children:c.DRESS_AVAILABLE}):e.jsx("span",{className:"dress-info-list-text",children:c.DRESS_UNAVAILABLE})]})})}),K.customizable&&e.jsx("li",{className:"dress-customizable",children:e.jsx(A,{title:c.CUSTOMIZABLE_TOOLTIP||"This dress can be customized",placement:"top",children:e.jsxs("div",{className:"dress-info-list-item",children:[e.jsx(I,{className:"available"}),e.jsx("span",{className:"dress-info-list-text",children:c.CUSTOMIZABLE||"Customizable"})]})})})]})})]}),as&&e.jsxs("section",{className:"buttons action",children:[e.jsx(B,{variant:"contained",className:"btn-primary btn-margin btn-margin-bottom",size:"small",onClick:()=>k(`/update-dress?dr=${K._id}`),children:d.UPDATE}),e.jsx(B,{variant:"contained",className:"btn-margin-bottom",color:"error",size:"small",onClick:()=>{q(!0)},children:d.DELETE})]})]}),e.jsx("div",{className:"col-2",children:e.jsx(C,{containerClassName:"dress",offset:es,loggedUser:M,suppliers:Q,statuses:Y,dress:K._id,hideSupplierColumn:!0,hideDressColumn:!0,hideDates:t.isMobile,checkboxSelection:!t.isMobile})})]}),e.jsxs(O,{disableEscapeKeyDown:!0,maxWidth:"xs",open:J,children:[e.jsx(y,{className:"dialog-header",children:d.CONFIRM_TITLE}),e.jsx(R,{children:c.DELETE_DRESS}),e.jsxs(w,{className:"dialog-actions",children:[e.jsx(B,{onClick:()=>{q(!1)},variant:"contained",className:"btn-secondary",children:d.CANCEL}),e.jsx(B,{onClick:async()=>{try{K&&K._id?(q(!1),P(!0),200===await L(K._id)?k("/dresses"):($(!0),P(!1))):($(!0),P(!1))}catch(s){$(!0),P(!1)}},variant:"contained",color:"error",children:d.DELETE})]})]}),z&&e.jsx(v,{text:d.LOADING}),V&&e.jsx(T,{}),F&&e.jsx(E,{})]})};export{k as default};
