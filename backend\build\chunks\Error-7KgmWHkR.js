import{c as s,j as e,a as r}from"../entries/index-CEzJO5Xy.js";import{B as a}from"./Button-DGZYUY3P.js";import{d as t}from"./router-BtYqujaw.js";const n=n=>{const i=s.c(11),{message:o,style:c,homeLink:l}=n,m=t();let d,j,x,h;return i[0]!==c?(d=c||{},i[0]=c,i[1]=d):d=i[1],i[2]!==o?(j=e.jsx("div",{className:"error",children:e.jsx("span",{className:"message",children:o})}),i[2]=o,i[3]=j):j=i[3],i[4]!==l||i[5]!==m?(x=l&&e.jsx("p",{children:e.jsx(a,{variant:"text",onClick:()=>m("/"),className:"btn-lnk",children:r.GO_TO_HOME})}),i[4]=l,i[5]=m,i[6]=x):x=i[6],i[7]!==d||i[8]!==j||i[9]!==x?(h=e.jsxs("div",{style:d,children:[j,x]}),i[7]=d,i[8]=j,i[9]=x,i[10]=h):h=i[10],h};export{n as E};
