import{F as s,j as e,R as i,G as t,e as a,$ as r,V as l,a1 as o,W as n,S as c,a4 as m,a as d,h as p,g as j,a5 as x}from"../entries/index-CEzJO5Xy.js";import{d as h,r as N}from"./router-BtYqujaw.js";import{L as u}from"./Layout-BQBjg4Lf.js";import{a as f,d as S}from"./DressService-J0XavNJj.js";import{S as v}from"./SimpleBackdrop-Bf3qjF13.js";import L from"./NoMatch-jvHCs4x8.js";import{E as T}from"./Error-koMug0_G.js";import{A as E}from"./Avatar-BtfxKR-8.js";import{B as C}from"./BookingList-Dn-WOL9Q.js";import{T as b}from"./Tooltip-BkJF6Mu0.js";import{D as g}from"./Checkroom-Bt6MiDKF.js";import{C as A,L as D}from"./Straighten-6ibCitj5.js";import{V as I}from"./Check-D745pofy.js";import{C as _}from"./Clear-BpXDeTL8.js";import{B}from"./Button-DGZYUY3P.js";import{D as O,a as y,b as R}from"./Grow-CjOKj0i1.js";import{D as k}from"./DialogTitle-BZXwroUN.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./Paper-CcwAvfvc.js";import"./LocationService-BtQFgoWL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Box-CHHh9iS3.js";import"./Avatar-Dix3YM8x.js";import"./BookingService-BJ4R0IJT.js";import"./InputLabel-BbcIE26O.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";import"./TextField-BAse--ht.js";import"./OutlinedInput-g8mR4MM3.js";import"./Input-BQdee9z7.js";import"./FormHelperText-DFSsjBsL.js";import"./Menu-ZU0DMgjT.js";import"./mergeSlotProps-Cay5TZBz.js";import"./MenuItem-suKfXYI2.js";import"./listItemTextClasses-DFwCkkgK.js";import"./BookingStatus-Bg6x_fQB.js";import"./Link-B-UCzRRJ.js";import"./format-4arn0GRM.js";import"./DataGrid-DM8uCtAG.js";import"./getThemeProps-gt86ccpv.js";import"./Switch-BWPUOSX1.js";import"./SwitchBase-BIeqtL5F.js";import"./IconButton-CnBvmeAK.js";import"./Toolbar-CNUITE_K.js";import"./KeyboardArrowRight-BV-h2cWM.js";import"./Chip-CAtDqtgp.js";import"./Autocomplete-CviOU_ku.js";import"./ListItemText-DBn_RuMq.js";import"./Checkbox-CDqupZJG.js";import"./Edit-DIF9Bumd.js";import"./Delete-CnqjtpsJ.js";import"./fr-CaQg1DLH.js";const w=()=>{const w=h(),[U,M]=N.useState(),[z,P]=N.useState(!0),[V,$]=N.useState(!1),[F,G]=N.useState(!1),[H,K]=N.useState(!1),[W,Z]=N.useState(),[J,Q]=N.useState(!1),[q,X]=N.useState([]),[Y,ss]=N.useState([]),[es,is]=N.useState(0);N.useEffect((()=>{const s=document.querySelector(".dress-sec");s&&is(s.clientHeight+75)}),[W]);const ts=U&&W&&(s(U)||W.supplier._id===U._id);return e.jsxs(u,{onLoad:async()=>{P(!0),G(!1),$(!1);try{const s=new URLSearchParams(window.location.search);if(s.has("dr")){const e=s.get("dr");if(e&&""!==e){const s=p();if(s){const i=await j(s._id);if(i){M(i);const s=await f(e);if(s){Z(s),K(!0);const e=[s.supplier._id];X(e.filter((s=>void 0!==s)));const i=x().map((s=>s.value));ss(i)}else $(!0)}else $(!0)}else $(!0)}else $(!0)}else $(!0)}catch(s){G(!0)}P(!1)},strict:!0,children:[H&&W&&W.supplier&&e.jsxs("div",{className:"dress",children:[e.jsxs("div",{className:"col-1",children:[e.jsxs("section",{className:"dress-sec",children:[e.jsx("div",{className:"name",children:e.jsx("h2",{children:W.name})}),e.jsxs("div",{className:"dress-img",children:[e.jsx(E,{type:i.Dress,mode:"update",record:W,size:"large",readonly:!ts,hideDelete:!0,onBeforeUpload:()=>{P(!0)},onChange:()=>{P(!1)},color:"disabled",className:"avatar-ctn"}),e.jsxs("div",{className:"dress-supplier",children:[e.jsx("span",{className:"dress-supplier-logo",children:e.jsx("img",{src:t(a.CDN_USERS,W.supplier.avatar),alt:W.supplier.fullName})}),e.jsx("span",{className:"dress-supplier-info",children:W.supplier.fullName})]})]}),e.jsx("div",{className:"dress-info",children:e.jsxs("ul",{className:"dress-info-list",children:[e.jsx("li",{className:"dress-type",children:e.jsx(b,{title:r(W.type),placement:"top",children:e.jsxs("div",{className:"dress-info-list-item",children:[e.jsx(g,{}),e.jsx("span",{className:"dress-info-list-text",children:l(W.type)})]})})}),e.jsx("li",{className:"dress-size",children:e.jsx(b,{title:o(W.size),placement:"top",children:e.jsx("div",{className:"dress-info-list-item",children:e.jsx("span",{className:"dress-info-list-text",children:n(W.size)})})})}),e.jsx("li",{className:"dress-color",children:e.jsx(b,{title:c.COLOR||"Color",placement:"top",children:e.jsxs("div",{className:"dress-info-list-item",children:[e.jsx(A,{}),e.jsx("span",{className:"dress-info-list-text",children:W.color})]})})}),e.jsx("li",{className:"dress-length",children:e.jsx(b,{title:c.LENGTH||"Length",placement:"top",children:e.jsxs("div",{className:"dress-info-list-item",children:[e.jsx(D,{}),e.jsx("span",{className:"dress-info-list-text",children:`${W.length} ${c.CM||"cm"}`})]})})}),e.jsx("li",{className:"dress-material",children:e.jsx(b,{title:c.MATERIAL||"Material",placement:"top",children:e.jsx("div",{className:"dress-info-list-item",children:e.jsx("span",{className:"dress-info-list-text",children:m(W.material)})})})}),e.jsx("li",{className:"dress-rentals",children:e.jsx(b,{title:c.RENTALS_COUNT||"Times rented",placement:"top",children:e.jsx("div",{className:"dress-info-list-item",children:e.jsx("span",{className:"dress-info-list-text",children:`${c.RENTALS_COUNT||"Times rented"}: ${W.rentals||0}`})})})}),e.jsx("li",{className:W.available?"dress-available":"dress-unavailable",children:e.jsx(b,{title:W.available?c.DRESS_AVAILABLE_TOOLTIP:c.DRESS_UNAVAILABLE_TOOLTIP,children:e.jsxs("div",{className:"dress-info-list-item",children:[W.available?e.jsx(I,{}):e.jsx(_,{}),W.available?e.jsx("span",{className:"dress-info-list-text",children:c.DRESS_AVAILABLE}):e.jsx("span",{className:"dress-info-list-text",children:c.DRESS_UNAVAILABLE})]})})}),W.customizable&&e.jsx("li",{className:"dress-customizable",children:e.jsx(b,{title:c.CUSTOMIZABLE_TOOLTIP||"This dress can be customized",placement:"top",children:e.jsxs("div",{className:"dress-info-list-item",children:[e.jsx(I,{className:"available"}),e.jsx("span",{className:"dress-info-list-text",children:c.CUSTOMIZABLE||"Customizable"})]})})})]})})]}),ts&&e.jsxs("section",{className:"buttons action",children:[e.jsx(B,{variant:"contained",className:"btn-primary btn-margin btn-margin-bottom",size:"small",onClick:()=>w(`/update-dress?dr=${W._id}`),children:d.UPDATE}),e.jsx(B,{variant:"contained",className:"btn-margin-bottom",color:"error",size:"small",onClick:()=>{Q(!0)},children:d.DELETE})]})]}),e.jsx("div",{className:"col-2",children:e.jsx(C,{containerClassName:"dress",offset:es,loggedUser:U,suppliers:q,statuses:Y,dress:W._id,hideSupplierColumn:!0,hideDressColumn:!0,hideDates:a.isMobile,checkboxSelection:!a.isMobile})})]}),e.jsxs(O,{disableEscapeKeyDown:!0,maxWidth:"xs",open:J,children:[e.jsx(k,{className:"dialog-header",children:d.CONFIRM_TITLE}),e.jsx(y,{children:c.DELETE_DRESS}),e.jsxs(R,{className:"dialog-actions",children:[e.jsx(B,{onClick:()=>{Q(!1)},variant:"contained",className:"btn-secondary",children:d.CANCEL}),e.jsx(B,{onClick:async()=>{try{W&&W._id?(Q(!1),P(!0),200===await S(W._id)?w("/dresses"):(G(!0),P(!1))):(G(!0),P(!1))}catch(s){G(!0),P(!1)}},variant:"contained",color:"error",children:d.DELETE})]})]}),z&&e.jsx(v,{text:d.LOADING}),F&&e.jsx(T,{}),V&&e.jsx(L,{})]})};export{w as default};
