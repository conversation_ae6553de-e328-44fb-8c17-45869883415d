import{b as e,s as a,c as i,j as n,a as r,u as s,h as t,f as l,R as o,b9 as c,B as d,z as u}from"../entries/index-xsXxT3-W.js";import{d as A,r as E}from"./router-BtYqujaw.js";import{B as v}from"./Button-BeKLLPpp.js";const I=new e({fr:{VALIDATE_EMAIL:"Un e-mail de validation a été envoyé à votre adresse e-mail. Veuillez vérifier votre boîte aux lettres et valider votre compte en cliquant sur le lien dans l'e-mail. Il expirera au bout d'un jour. Si vous n'avez pas reçu d'e-mail de validation, cliquez sur renvoyer.",RESEND:"Renvoyer",VALIDATION_EMAIL_SENT:"E-mail de validation envoyé.",VALIDATION_EMAIL_ERROR:"Une erreur s'est produite lors de l'envoi de l'e-mail de validation."},en:{VALIDATE_EMAIL:"A validation email has been sent to your email address. Please check your mailbox and validate your account by clicking the link in the email. It will be expire after one day. If you didn't receive the validation email click on resend.",RESEND:"Resend",VALIDATION_EMAIL_SENT:"Validation email sent.",VALIDATION_EMAIL_ERROR:"An error occurred while sending validation email."},es:{VALIDATE_EMAIL:"Se ha enviado un correo electrónico de validación a su dirección de correo electrónico. Por favor, revise su bandeja de entrada y valide su cuenta haciendo clic en el enlace del correo electrónico. Expirará después de un día. Si no recibió el correo electrónico de validación, haga clic en reenviar.",RESEND:"Reenviar",VALIDATION_EMAIL_SENT:"Correo electrónico de validación enviado.",VALIDATION_EMAIL_ERROR:"Se produjo un error al enviar el correo electrónico de validación."}});a(I);const m=new e({fr:{UNAUTHORIZED:"Accès non autorisé"},en:{UNAUTHORIZED:"Unauthorized access"},es:{UNAUTHORIZED:"Acceso no autorizado"}});a(m);const L=e=>{const a=i.c(8),{style:s}=e,t=A();let l,o,c,d;return a[0]!==s?(l=s||{},a[0]=s,a[1]=l):l=a[1],a[2]===Symbol.for("react.memo_cache_sentinel")?(o=n.jsx("h2",{children:m.UNAUTHORIZED}),a[2]=o):o=a[2],a[3]!==t?(c=n.jsx("p",{children:n.jsx(v,{variant:"text",onClick:()=>t("/"),className:"btn-lnk",children:r.GO_TO_HOME})}),a[3]=t,a[4]=c):c=a[4],a[5]!==l||a[6]!==c?(d=n.jsxs("div",{className:"msg",style:l,children:[o,c]}),a[5]=l,a[6]=c,a[7]=d):d=a[7],d},R=e=>{const a=i.c(32),{strict:r,admin:A,style:m,children:R,onLoad:h}=e,{user:N,userLoaded:_,setUnauthorized:D,unauthorized:T}=s(),[y,O]=E.useState(!0);let f,p,x,j,V;a[0]!==h||a[1]!==r||a[2]!==N||a[3]!==_?(f=()=>{!t()&&r?l(!0):_&&(O(!1),h&&h(N||void 0))},a[0]=h,a[1]=r,a[2]=N,a[3]=_,a[4]=f):f=a[4],a[5]!==r||a[6]!==N||a[7]!==_?(p=[N,_,r],a[5]=r,a[6]=N,a[7]=_,a[8]=p):p=a[8],E.useEffect(f,p),a[9]!==A||a[10]!==D||a[11]!==N?(x=()=>{A&&N&&N.type!==o.Admin&&(D(!0),O(!1))},a[9]=A,a[10]=D,a[11]=N,a[12]=x):x=a[12],a[13]!==A||a[14]!==N?(j=[N,A],a[13]=A,a[14]=N,a[15]=j):j=a[15],E.useEffect(x,j),a[16]!==N?(V=async e=>{e.preventDefault();try{if(N){const e={email:N.email};200===await c(e)?d(I.VALIDATION_EMAIL_SENT):u(null,I.VALIDATION_EMAIL_ERROR)}else u()}catch(a){u(a,I.VALIDATION_EMAIL_ERROR)}},a[16]=N,a[17]=V):V=a[17];const b=V;let M,S,U;return a[18]!==R||a[19]!==b||a[20]!==y||a[21]!==r||a[22]!==m||a[23]!==T||a[24]!==N||a[25]!==_?(M=!(!N&&!y||N&&N.verified)&&r||T?!y&&!T&&n.jsxs("div",{className:"validate-email",children:[n.jsx("span",{children:I.VALIDATE_EMAIL}),n.jsx(v,{type:"button",variant:"contained",size:"small",className:"btn-primary btn-resend",onClick:b,children:I.RESEND})]}):n.jsx("div",{className:"content",style:m||{},children:_&&R}),a[18]=R,a[19]=b,a[20]=y,a[21]=r,a[22]=m,a[23]=T,a[24]=N,a[25]=_,a[26]=M):M=a[26],a[27]!==T?(S=T&&n.jsx(L,{}),a[27]=T,a[28]=S):S=a[28],a[29]!==M||a[30]!==S?(U=n.jsxs(n.Fragment,{children:[M,S]}),a[29]=M,a[30]=S,a[31]=U):U=a[31],U};export{R as L,I as s};
