import{r as e}from"./router-BtYqujaw.js";import{i as t,aq as a,j as r,k as i,ao as o}from"../entries/index-xsXxT3-W.js";import{a as s,g as n,u as l,s as d,c,o as p,r as u,m}from"./Button-BeKLLPpp.js";import{b as g}from"./Menu-C_-X8cS7.js";import{l as v}from"./listItemTextClasses-BcbgzvlE.js";function b(e){return n("MuiDivider",e)}const y=s("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);function f(e){return n("MuiListItemIcon",e)}const h=s("MuiListItemIcon",["root","alignItemsFlexStart"]);function $(e){return n("MuiMenuItem",e)}const x=s("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),C=d(p,{shouldForwardProp:e=>u(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.root,a.dense&&t.dense,a.divider&&t.divider,!a.disableGutters&&t.gutters]}})(m((({theme:e})=>({...e.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${x.selected}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:o(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${x.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:o(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}},[`&.${x.selected}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:o(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:o(e.palette.primary.main,e.palette.action.selectedOpacity)}},[`&.${x.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${x.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},[`& + .${y.root}`]:{marginTop:e.spacing(1),marginBottom:e.spacing(1)},[`& + .${y.inset}`]:{marginLeft:52},[`& .${v.root}`]:{marginTop:0,marginBottom:0},[`& .${v.inset}`]:{paddingLeft:36},[`& .${h.root}`]:{minWidth:36},variants:[{props:({ownerState:e})=>!e.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:e})=>e.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:e})=>!e.dense,style:{[e.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:e})=>e.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...e.typography.body2,[`& .${h.root} svg`]:{fontSize:"1.25rem"}}}]})))),M=e.forwardRef((function(o,s){const n=t({props:o,name:"MuiMenuItem"}),{autoFocus:d=!1,component:p="li",dense:u=!1,divider:m=!1,disableGutters:v=!1,focusVisibleClassName:b,role:y="menuitem",tabIndex:f,className:h,...x}=n,M=e.useContext(g),w=e.useMemo((()=>({dense:u||M.dense||!1,disableGutters:v})),[M.dense,u,v]),I=e.useRef(null);a((()=>{d&&I.current&&I.current.focus()}),[d]);const O={...n,dense:w.dense,divider:m,disableGutters:v},k=(e=>{const{disabled:t,dense:a,divider:r,disableGutters:i,selected:o,classes:s}=e,n=c({root:["root",a&&"dense",t&&"disabled",!i&&"gutters",r&&"divider",o&&"selected"]},$,s);return{...s,...n}})(n),S=l(I,s);let j;return n.disabled||(j=void 0!==f?f:-1),r.jsx(g.Provider,{value:w,children:r.jsx(C,{ref:S,role:y,tabIndex:j,component:p,focusVisibleClassName:i(k.focusVisible,b),className:i(k.root,h),...x,ownerState:O,classes:k})})}));export{M,b as a,f as g};
