import { Schema, model } from 'mongoose';
import * as logger from "../common/logger.js";
const countrySchema = new Schema({
  values: {
    type: [Schema.Types.ObjectId],
    ref: 'LocationValue',
    required: [true, "can't be blank"],
    validate: value => Array.isArray(value)
  },
  supplier: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  strict: true,
  collection: 'Country'
});
// Add custom indexes
countrySchema.index({
  values: 1
});
const Country = model('Country', countrySchema);
// Create indexes manually and handle potential errors
Country.syncIndexes().catch(err => {
  logger.error('Error creating Country indexes:', err);
});
export default Country;