import{b as e,s as t,c as s,j as c,a as n,G as a,e as r,J as l,a7 as o,a5 as i}from"../entries/index-xsXxT3-W.js";import{r as u}from"./router-BtYqujaw.js";import{A as h}from"./Accordion-Z5bnZGK6.js";import{B as d}from"./BookingStatus-BaSj8uqV.js";const m=new e({fr:{NEW_BOOKING:"Nouvelle réservation",DRESS:"Robe"},en:{NEW_BOOKING:"New Booking",DRESS:"Dress"},es:{NEW_BOOKING:"Nueva reserva",DRESS:"Vestido"}});t(m);const g=e=>{const t=s.c(25),{suppliers:i,collapse:d,className:m,onChange:g}=e;let N;t[0]===Symbol.for("react.memo_cache_sentinel")?(N=[],t[0]=N):N=t[0];const[_,x]=u.useState(N);let C;t[1]===Symbol.for("react.memo_cache_sentinel")?(C=[],t[1]=C):C=t[1];const[S,k]=u.useState(C),[O,j]=u.useState(!1);let b;t[2]===Symbol.for("react.memo_cache_sentinel")?(b=[],t[2]=b):b=t[2];const I=u.useRef(b);let E,L,T;t[3]!==i?(E=()=>{x(i)},L=[i],t[3]=i,t[4]=E,t[5]=L):(E=t[4],L=t[5]),u.useEffect(E,L),t[6]!==i||t[7]!==S||t[8]!==g||t[9]!==_.length?(T=e=>{const t=e.currentTarget.getAttribute("data-id");if("checked"in e.currentTarget&&e.currentTarget.checked)S.push(t),S.length===_.length&&j(!0);else{const e=S.indexOf(t);S.splice(e,1),0===S.length&&j(!1)}k(S),g&&(0===S.length?g(l(o(i))):g(l(S)))},t[6]=i,t[7]=S,t[8]=g,t[9]=_.length,t[10]=T):T=t[10];const v=T;let A;t[11]!==O||t[12]!==g||t[13]!==_?(A=()=>{if(O)I.current.forEach(f),j(!1),k([]);else{I.current.forEach(p);const e=o(_);j(!0),k(e),g&&g(l(e))}},t[11]=O,t[12]=g,t[13]=_,t[14]=A):A=t[14];const P=A;let R;t[15]!==v?(R=e=>{const t=e.currentTarget.previousSibling;t.checked=!t.checked;const s=e;s.currentTarget=t,v(s)},t[15]=v,t[16]=R):R=t[16];const K=R;let D;return t[17]!==O||t[18]!==m||t[19]!==d||t[20]!==v||t[21]!==K||t[22]!==P||t[23]!==_?(D=_.length>1&&c.jsxs(h,{title:n.SUPPLIER,collapse:d,offsetHeight:Math.floor(_.length/2*r.SUPPLIER_IMAGE_HEIGHT),className:(m?`${m} `:"")+"supplier-filter",children:[c.jsx("ul",{className:"supplier-list",children:_.map(((e,t)=>c.jsxs("li",{children:[c.jsx("input",{ref:e=>{I.current[t]=e},type:"checkbox","data-id":e._id,className:"supplier-checkbox",onChange:v}),c.jsx("span",{role:"button",tabIndex:0,onClick:K,className:"supplier",children:c.jsx("img",{src:a(r.CDN_USERS,e.avatar),alt:e.fullName,title:e.fullName})}),!!e.dressCount&&c.jsx("span",{className:"dress-count",children:`(${e.dressCount})`})]},e._id)))}),c.jsx("div",{className:"filter-actions",children:c.jsx("span",{onClick:P,className:"uncheckall",role:"button",tabIndex:0,children:O?n.UNCHECK_ALL:n.CHECK_ALL})})]})||c.jsx(c.Fragment,{}),t[17]=O,t[18]=m,t[19]=d,t[20]=v,t[21]=K,t[22]=P,t[23]=_,t[24]=D):D=t[24],D};function f(e){e&&(e.checked=!1)}function p(e){e&&(e.checked=!0)}const N=i(),_=N.map((e=>e.value)),x=e=>{const t=s.c(19),{className:a,collapse:r,onChange:o}=e;let i;t[0]===Symbol.for("react.memo_cache_sentinel")?(i=[],t[0]=i):i=t[0];const[m,g]=u.useState(i),[f,p]=u.useState(!1);let x;t[1]===Symbol.for("react.memo_cache_sentinel")?(x=[],t[1]=x):x=t[1];const k=u.useRef(x);let O;t[2]!==o?(O=e=>{o&&o(0===e.length?_:l(e))},t[2]=o,t[3]=O):O=t[3];const j=O;let b;t[4]!==m||t[5]!==j?(b=e=>{const t=e.currentTarget.getAttribute("data-value");if("checked"in e.currentTarget&&e.currentTarget.checked)m.push(t),m.length===_.length&&p(!0);else{const e=m.findIndex((e=>e===t));m.splice(e,1),0===m.length&&p(!1)}g(m),j(m)},t[4]=m,t[5]=j,t[6]=b):b=t[6];const I=b;let E;t[7]!==I?(E=e=>{const t=e.currentTarget.previousSibling;t.checked=!t.checked;const s=e;s.currentTarget=t,I(s)},t[7]=I,t[8]=E):E=t[8];const L=E;let T;t[9]!==f||t[10]!==j?(T=()=>{f?(k.current.forEach(C),p(!1),g([])):(k.current.forEach(S),p(!0),g(_),j(_))},t[9]=f,t[10]=j,t[11]=T):T=t[11];const v=T;let A;return t[12]!==f||t[13]!==a||t[14]!==r||t[15]!==I||t[16]!==L||t[17]!==v?(A=_.length>0&&c.jsxs(h,{title:n.STATUS,collapse:r,className:(a?`${a} `:"")+"status-filter",children:[c.jsx("ul",{className:"status-list",children:N.map(((e,t)=>c.jsxs("li",{children:[c.jsx("input",{ref:e=>{k.current[t]=e},type:"checkbox","data-value":e.value,className:"status-checkbox",onChange:I}),c.jsx(d,{value:e.value,onClick:L})]},e.value)))}),c.jsx("div",{className:"filter-actions",children:c.jsx("span",{onClick:v,className:"uncheckall",role:"button",tabIndex:0,children:f?n.UNCHECK_ALL:n.CHECK_ALL})})]})||c.jsx(c.Fragment,{}),t[12]=f,t[13]=a,t[14]=r,t[15]=I,t[16]=L,t[17]=v,t[18]=A):A=t[18],A};function C(e){e&&(e.checked=!1)}function S(e){e&&(e.checked=!0)}const k=new e({fr:{PICK_UP_LOCATION:"Lieu de prise en charge",DROP_OFF_LOCATION:"Lieu de restitution"},en:{PICK_UP_LOCATION:"Pick-up location",DROP_OFF_LOCATION:"Drop-off location"},es:{PICK_UP_LOCATION:"Lugar de recogida",DROP_OFF_LOCATION:"Lugar de entrega"}});t(k);export{g as S,m as a,x as b,k as s};
