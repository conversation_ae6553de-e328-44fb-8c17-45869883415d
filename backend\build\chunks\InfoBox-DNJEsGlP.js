import{c as s,j as e}from"../entries/index-xsXxT3-W.js";import{I as o}from"./Info-CNP9gYBt.js";const a=a=>{const n=s.c(6),{className:c,value:r}=a,i=`info-box${c?" ":""}${c||""}`;let t,m,l;return n[0]===Symbol.for("react.memo_cache_sentinel")?(t=e.jsx(o,{className:"info-box-icon"}),n[0]=t):t=n[0],n[1]!==r?(m=e.jsx("span",{className:"info-box-text",children:r}),n[1]=r,n[2]=m):m=n[2],n[3]!==i||n[4]!==m?(l=e.jsxs("div",{className:i,children:[t,m]}),n[3]=i,n[4]=m,n[5]=l):l=n[5],l};export{a as I};
