import{b as s,s as e,c as r,u as a,j as t,a as o,e as i,w as n,d as c,g as d,x as m,z as l,A as p,B as A,C as E}from"../entries/index-CEzJO5Xy.js";import{d as u,r as h}from"./router-BtYqujaw.js";import{u as j,z as f,s as w}from"./zod-4O8Zwsja.js";import{s as T,L as N}from"./Layout-BQBjg4Lf.js";import{s as x}from"./change-password-CyUFObAm.js";import{s as I}from"./reset-password-CKyLhs8Q.js";import S from"./NoMatch-jvHCs4x8.js";import{E as _}from"./Error-koMug0_G.js";import{P as C}from"./Paper-CcwAvfvc.js";import{B as P}from"./Button-DGZYUY3P.js";import{F as v,I as g}from"./InputLabel-BbcIE26O.js";import{I as b}from"./Input-BQdee9z7.js";import{F as D}from"./FormHelperText-DFSsjBsL.js";import"./vendor-dblfw9z9.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const R=new s({fr:{ACTIVATE_HEADING:"Activation du compte",TOKEN_EXPIRED:"Votre lien d'activation du compte a expiré.",ACTIVATE:"Activer"},en:{ACTIVATE_HEADING:"Account Activation",TOKEN_EXPIRED:"Your account activation link expired.",ACTIVATE:"Activate"},es:{ACTIVATE_HEADING:"Activación de cuenta",TOKEN_EXPIRED:"Su enlace de activación de cuenta ha expirado.",ACTIVATE:"Activar"},ar:{ACTIVATE_HEADING:"تفعيل الحساب",TOKEN_EXPIRED:"انتهت صلاحية رابط تفعيل حسابك.",ACTIVATE:"تفعيل"}});e(R);const O=f.object({password:f.string().min(i.PASSWORD_MIN_LENGTH,{message:o.PASSWORD_ERROR}),confirmPassword:f.string()}).refine((s=>s.password===s.confirmPassword),{path:["confirmPassword"],message:o.PASSWORDS_DONT_MATCH}),y=()=>{const s=r.c(38),e=u(),{setUser:i,setUserLoaded:f}=a(),[y,H]=h.useState(""),[V,L]=h.useState(""),[W,G]=h.useState(""),[k,F]=h.useState(!1),[K,M]=h.useState(!1),[X,q]=h.useState(!1),[U,z]=h.useState(!1),[B,J]=h.useState(!1);let Y;s[0]===Symbol.for("react.memo_cache_sentinel")?(Y={resolver:w(O),mode:"onSubmit"},s[0]=Y):Y=s[0];const{register:Q,handleSubmit:Z,formState:$,setError:ss,clearErrors:es}=j(Y),{errors:rs,isSubmitting:as}=$;let ts;s[1]!==V||s[2]!==e||s[3]!==i||s[4]!==f||s[5]!==W||s[6]!==y?(ts=async s=>{const{password:r}=s;try{const s={userId:y,token:W,password:r};if(200===await n(s)){const s=await c({email:V,password:r});if(200===s.status){const r=await d(s.data._id);J(!0),i(r),f(!0),200===await m(y)?e("/"):l()}else l()}else l()}catch(a){l(a)}},s[1]=V,s[2]=e,s[3]=i,s[4]=f,s[5]=W,s[6]=y,s[7]=ts):ts=s[7];const os=ts;let is;s[8]!==V?(is=async()=>{try{200===await p(V,!1)?A(o.ACTIVATION_EMAIL_SENT):l()}catch(s){l(s)}},s[8]=V,s[9]=is):is=s[9];const ns=is;let cs;s[10]!==ss?(cs=async s=>{if(s)q(!0);else{const s=new URLSearchParams(window.location.search);if(s.has("u")&&s.has("e")&&s.has("t")){const r=s.get("u"),a=s.get("e"),t=s.get("t");if(r&&a&&t)try{const e=await E(r,a,t);if(200===e){if(H(r),L(a),G(t),F(!0),s.has("r")){const e="true"===s.get("r");z(e)}}else 204===e?(L(a),M(!0)):q(!0)}catch(e){const s=e;console.error(s),ss("root",{})}else q(!0)}else q(!0)}},s[10]=ss,s[11]=cs):cs=s[11];const ds=cs;let ms,ls,ps,As,Es;return s[12]!==ns||s[13]!==e||s[14]!==K?(ms=K&&t.jsx("div",{className:"resend",children:t.jsxs(C,{className:"resend-form",elevation:10,children:[t.jsx("h1",{children:R.ACTIVATE_HEADING}),t.jsxs("div",{className:"resend-form-content",children:[t.jsx("span",{children:R.TOKEN_EXPIRED}),t.jsx(P,{type:"button",variant:"contained",size:"small",className:"btn-primary btn-resend",onClick:ns,children:T.RESEND}),t.jsx("p",{className:"go-to-home",children:t.jsx(P,{variant:"text",onClick:()=>e("/"),className:"btn-lnk",children:o.GO_TO_HOME})})]})]})}),s[12]=ns,s[13]=e,s[14]=K,s[15]=ms):ms=s[15],s[16]!==es||s[17]!==rs.confirmPassword||s[18]!==rs.password||s[19]!==Z||s[20]!==as||s[21]!==e||s[22]!==os||s[23]!==Q||s[24]!==U||s[25]!==k?(ls=k&&t.jsx("div",{className:"activate",children:t.jsxs(C,{className:"activate-form",elevation:10,children:[t.jsx("h1",{children:U?I.RESET_PASSWORD_HEADING:R.ACTIVATE_HEADING}),t.jsxs("form",{onSubmit:Z(os),children:[t.jsxs(v,{fullWidth:!0,margin:"dense",error:!!rs.password,children:[t.jsx(g,{className:"required",children:x.NEW_PASSWORD}),t.jsx(b,{...Q("password"),type:"password",required:!0,autoComplete:"new-password",onChange:()=>es()}),t.jsx(D,{error:!!rs.password,children:rs.password?.message||""})]}),t.jsxs(v,{fullWidth:!0,margin:"dense",error:!!rs.confirmPassword,children:[t.jsx(g,{className:"required",children:o.CONFIRM_PASSWORD}),t.jsx(b,{...Q("confirmPassword"),type:"password",required:!0,autoComplete:"new-password",onChange:()=>es()}),t.jsx(D,{error:!!rs.confirmPassword,children:rs.confirmPassword?.message||""})]}),t.jsxs("div",{className:"buttons",children:[t.jsx(P,{type:"submit",className:"btn-primary btn-margin btn-margin-bottom",variant:"contained",disabled:as,children:U?o.UPDATE:R.ACTIVATE}),t.jsx(P,{variant:"outlined",color:"primary",className:"btn-margin-bottom",onClick:()=>e("/"),children:o.CANCEL})]})]})]})}),s[16]=es,s[17]=rs.confirmPassword,s[18]=rs.password,s[19]=Z,s[20]=as,s[21]=e,s[22]=os,s[23]=Q,s[24]=U,s[25]=k,s[26]=ls):ls=s[26],s[27]!==B||s[28]!==X?(ps=!B&&X&&t.jsx(S,{hideHeader:!0}),s[27]=B,s[28]=X,s[29]=ps):ps=s[29],s[30]!==rs.root?(As=rs.root&&t.jsx(_,{}),s[30]=rs.root,s[31]=As):As=s[31],s[32]!==ds||s[33]!==ms||s[34]!==ls||s[35]!==ps||s[36]!==As?(Es=t.jsxs(N,{onLoad:ds,strict:!1,children:[ms,ls,ps,As]}),s[32]=ds,s[33]=ms,s[34]=ls,s[35]=ps,s[36]=As,s[37]=Es):Es=s[37],Es};export{y as default};
