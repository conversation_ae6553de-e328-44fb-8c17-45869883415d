import { Request } from 'express';
export type SessionData = {
    id: string;
};
/**
 * Sign and return the JWT.
 *
 * @async
 * @param {SessionData} payload
 * @param {?boolean} [stayConnected]
 * @returns {Promise<string>}
 */
export declare const encryptJWT: (payload: SessionData, stayConnected?: boolean) => Promise<string>;
/**
 * Verify the JWT format, verify the JWS signature, validate the JWT Claims Set.
 *
 * @async
 * @param {string} input
 * @returns {Promise<SessionData>}
 */
export declare const decryptJWT: (input: string) => Promise<SessionData>;
/**
 * Check whether the request is from the backend or not.
 *
 * @export
 * @param {Request} req
 * @returns {boolean}
 */
export declare const isBackend: (req: Request) => boolean;
/**
 * Check whether the request is from the frontend or not.
 *
 * @export
 * @param {Request} req
 * @returns {boolean}
 */
export declare const isFrontend: (req: Request) => boolean;
/**
 * Get authentification cookie name.
 *
 * @param {Request} req
 * @returns {string}
 */
export declare const getAuthCookieName: (req: Request) => string;
