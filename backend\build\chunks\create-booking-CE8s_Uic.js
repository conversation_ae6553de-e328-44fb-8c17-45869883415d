import{b as e,s as a,Q as t,j as r,R as s,e as i,aa as n,z as o,G as l,Y as E}from"../entries/index-CEzJO5Xy.js";import{r as c}from"./router-BtYqujaw.js";import{M as d}from"./MultipleSelect-C7xTvWe9.js";import{g as u}from"./DressService-J0XavNJj.js";import{F as N,I}from"./InputLabel-BbcIE26O.js";import{S as O}from"./TextField-BAse--ht.js";import{M as m}from"./MenuItem-suKfXYI2.js";import{L as D}from"./ListItemAvatar-Bv6onK36.js";import{A}from"./Avatar-Dix3YM8x.js";import{L as S}from"./ListItemText-DBn_RuMq.js";import{F as p}from"./FormHelperText-DFSsjBsL.js";const C=new e({fr:{NEW_BOOKING_HEADING:"Nouvelle réservation",NEW_BOOKING:"Nouvelle réservation",DRESS:"Robe",DRIVER:"Client",PICKUP_LOCATION:"Lieu de prise en charge",DROP_OFF_LOCATION:"Lieu de retour",FROM:"De",TO:"À",STATUS:"Statut",PRICE:"Prix",CANCELLATION:"Annulation",AMENDMENTS:"Modifications",PENDING:"En attente",DEPOSIT:"Acompte",PAID:"Payé",RESERVED:"Réservé",CANCELLED:"Annulé"},en:{NEW_BOOKING_HEADING:"New booking",NEW_BOOKING:"New Booking",DRESS:"Dress",DRIVER:"Customer",PICKUP_LOCATION:"Pickup Location",DROP_OFF_LOCATION:"Drop-off Location",FROM:"From",TO:"To",STATUS:"Status",PRICE:"Price",CANCELLATION:"Cancellation",AMENDMENTS:"Amendments",PENDING:"Pending",DEPOSIT:"Deposit",PAID:"Paid",RESERVED:"Reserved",CANCELLED:"Cancelled"},es:{NEW_BOOKING_HEADING:"Nueva reserva",NEW_BOOKING:"Nueva reserva",DRESS:"Vestido",DRIVER:"Cliente",PICKUP_LOCATION:"Lugar de recogida",DROP_OFF_LOCATION:"Lugar de entrega",FROM:"Desde",TO:"Hasta",STATUS:"Estado",PRICE:"Precio",CANCELLATION:"Cancelación",AMENDMENTS:"Modificaciones",PENDING:"Pendiente",DEPOSIT:"Depósito",PAID:"Pagado",RESERVED:"Reservado",CANCELLED:"Cancelado"},ar:{NEW_BOOKING_HEADING:"حجز جديد",NEW_BOOKING:"حجز جديد",DRESS:"الفستان",DRIVER:"العميل",PICKUP_LOCATION:"موقع الاستلام",DROP_OFF_LOCATION:"موقع التسليم",FROM:"من",TO:"إلى",STATUS:"الحالة",PRICE:"السعر",CANCELLATION:"الإلغاء",AMENDMENTS:"التعديلات",PENDING:"في الانتظار",DEPOSIT:"عربون",PAID:"مدفوع",RESERVED:"محجوز",CANCELLED:"ملغي"}});a(C);const f=({multiple:e,value:a,label:l,required:E,variant:u,onChange:N})=>{const[I,O]=c.useState(!1),[m,D]=c.useState(!1),[A,S]=c.useState([]),[p,C]=c.useState(!1),[f,P]=c.useState(1),[R,T]=c.useState(""),[_,L]=c.useState([]);c.useEffect((()=>{const r=e?a:[a];a&&!t(_,r)&&L(r)}),[e,a,_]);const g=async(e,a,t)=>{try{D(!0);const r=await n(a,e,i.PAGE_SIZE),s=r&&r.length>0?r[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!s)return void o();const l=s.resultData.map((e=>{const{_id:a,fullName:t,avatar:r}=e;return{_id:a,name:t,image:r}})),E=1===e?l:[...A,...l];S(E),C(l.length>0),t&&t()}catch(r){o(r)}finally{D(!1)}};return r.jsx(d,{loading:m,label:l||"",callbackFromMultipleSelect:e=>{N&&N(e)},options:A,selectedOptions:_,required:E||!1,multiple:e,type:s.User,variant:u||"standard",ListboxProps:{onScroll:e=>{const a=e.currentTarget;if(p&&!m&&a.scrollTop+a.clientHeight>=a.scrollHeight-i.PAGE_OFFSET){const e=f+1;P(e),g(e,R)}}},onFocus:()=>{if(!I){const e=1;P(e),S([]),g(e,R,(()=>{O(!0)}))}},onInputChange:e=>{const a=e&&e.target&&"value"in e.target&&e.target.value||"";a!==R&&(S([]),P(1),T(a),g(1,a))},onClear:()=>{S([]),P(1),T(""),C(!0),g(1,"")}})},P=({value:e,label:a,required:t,multiple:s,variant:n,supplier:o,onChange:d})=>{const[C,f]=c.useState(s?[]:""),[P,R]=c.useState([]),[T,_]=c.useState(!0);return c.useEffect((()=>{(async()=>{try{_(!0);const e={suppliers:o?[o]:[],availability:[E.Available]},a=await u("",e,1,1e3),t=a&&a.length>0?a[0]:{pageInfo:{totalRecord:0},resultData:[]};t&&t.resultData&&R(t.resultData)}catch(e){console.error("Error fetching dresses:",e)}finally{_(!1)}})()}),[o]),c.useEffect((()=>{void 0!==e&&f(e)}),[e]),r.jsxs(N,{fullWidth:!0,margin:"dense",variant:n||"standard",children:[r.jsx(I,{className:t?"required":"",children:a}),r.jsx(O,{value:C,onChange:e=>{const a=e.target.value;if(f(a),d)if(s){const e=(Array.isArray(a)?a:[a]).map((e=>P.find((a=>a._id===e)))).filter(Boolean).map((e=>({_id:e._id,name:e.name})));d(e)}else{const e=P.find((e=>e._id===a));d(e?[{_id:e._id,name:e.name}]:[])}},multiple:s,required:t,disabled:T,renderValue:e=>{if(s)return(Array.isArray(e)?e:[]).map((e=>P.find((a=>a._id===e))?.name)).filter(Boolean).join(", ");{const a=P.find((a=>a._id===e));return a?.name||""}},children:P.map((e=>r.jsxs(m,{value:e._id,children:[r.jsx(D,{children:r.jsx(A,{src:e.image?l(i.CDN_DRESSES,e.image):void 0,sx:{width:32,height:32}})}),r.jsx(S,{primary:e.name,secondary:`${e.type} - ${e.size} - ${e.color}`})]},e._id)))}),T&&r.jsx(p,{children:"Loading dresses..."})]})};export{P as D,f as U,C as s};
