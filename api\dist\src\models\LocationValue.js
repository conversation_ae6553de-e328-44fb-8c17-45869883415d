import { Schema, model } from 'mongoose';
import * as logger from "../common/logger.js";
const locationValueSchema = new Schema({
  language: {
    type: String,
    required: [true, "can't be blank"],
    index: true,
    trim: true,
    lowercase: true,
    minLength: 2,
    maxLength: 2
  },
  value: {
    type: String,
    required: [true, "can't be blank"],
    index: true,
    trim: true
  }
}, {
  timestamps: true,
  strict: true,
  collection: 'LocationValue'
});
// Add custom indexes
locationValueSchema.index({
  language: 1,
  value: 1
});
locationValueSchema.index({
  value: 'text'
});
const LocationValue = model('LocationValue', locationValueSchema);
// Create indexes manually and handle potential errors
LocationValue.syncIndexes().catch(err => {
  logger.error('Error creating LocationValue indexes:', err);
});
export default LocationValue;