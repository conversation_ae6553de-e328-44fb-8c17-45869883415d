{"version": 3, "sources": ["../../date-fns/add.js", "../../date-fns/isSaturday.js", "../../date-fns/isSunday.js", "../../date-fns/isWeekend.js", "../../date-fns/addBusinessDays.js", "../../date-fns/setISOWeekYear.js", "../../date-fns/addISOWeekYears.js", "../../date-fns/addQuarters.js", "../../date-fns/areIntervalsOverlapping.js", "../../date-fns/max.js", "../../date-fns/min.js", "../../date-fns/clamp.js", "../../date-fns/closestIndexTo.js", "../../date-fns/closestTo.js", "../../date-fns/compareAsc.js", "../../date-fns/compareDesc.js", "../../date-fns/constructNow.js", "../../date-fns/daysToWeeks.js", "../../date-fns/differenceInBusinessDays.js", "../../date-fns/differenceInCalendarISOWeekYears.js", "../../date-fns/differenceInCalendarISOWeeks.js", "../../date-fns/differenceInCalendarMonths.js", "../../date-fns/getQuarter.js", "../../date-fns/differenceInCalendarQuarters.js", "../../date-fns/differenceInCalendarWeeks.js", "../../date-fns/differenceInCalendarYears.js", "../../date-fns/differenceInDays.js", "../../date-fns/_lib/getRoundingMethod.js", "../../date-fns/differenceInHours.js", "../../date-fns/subISOWeekYears.js", "../../date-fns/differenceInISOWeekYears.js", "../../date-fns/differenceInMilliseconds.js", "../../date-fns/differenceInMinutes.js", "../../date-fns/isLastDayOfMonth.js", "../../date-fns/differenceInMonths.js", "../../date-fns/differenceInQuarters.js", "../../date-fns/differenceInSeconds.js", "../../date-fns/differenceInWeeks.js", "../../date-fns/differenceInYears.js", "../../date-fns/_lib/normalizeInterval.js", "../../date-fns/eachDayOfInterval.js", "../../date-fns/eachHourOfInterval.js", "../../date-fns/eachMinuteOfInterval.js", "../../date-fns/eachMonthOfInterval.js", "../../date-fns/startOfQuarter.js", "../../date-fns/eachQuarterOfInterval.js", "../../date-fns/eachWeekOfInterval.js", "../../date-fns/eachWeekendOfInterval.js", "../../date-fns/eachWeekendOfMonth.js", "../../date-fns/eachWeekendOfYear.js", "../../date-fns/eachYearOfInterval.js", "../../date-fns/endOfDecade.js", "../../date-fns/endOfHour.js", "../../date-fns/endOfISOWeek.js", "../../date-fns/endOfISOWeekYear.js", "../../date-fns/endOfMinute.js", "../../date-fns/endOfQuarter.js", "../../date-fns/endOfSecond.js", "../../date-fns/endOfToday.js", "../../date-fns/endOfTomorrow.js", "../../date-fns/endOfYesterday.js", "../../date-fns/formatDistance.js", "../../date-fns/formatDistanceStrict.js", "../../date-fns/formatDistanceToNow.js", "../../date-fns/formatDistanceToNowStrict.js", "../../date-fns/formatDuration.js", "../../date-fns/formatISO.js", "../../date-fns/formatISO9075.js", "../../date-fns/formatISODuration.js", "../../date-fns/formatRFC3339.js", "../../date-fns/formatRFC7231.js", "../../date-fns/formatRelative.js", "../../date-fns/fromUnixTime.js", "../../date-fns/getDay.js", "../../date-fns/isLeapYear.js", "../../date-fns/getDaysInYear.js", "../../date-fns/getDecade.js", "../../date-fns/getISOWeeksInYear.js", "../../date-fns/getOverlappingDaysInIntervals.js", "../../date-fns/getTime.js", "../../date-fns/getUnixTime.js", "../../date-fns/getWeekOfMonth.js", "../../date-fns/lastDayOfMonth.js", "../../date-fns/getWeeksInMonth.js", "../../date-fns/hoursToMilliseconds.js", "../../date-fns/hoursToMinutes.js", "../../date-fns/hoursToSeconds.js", "../../date-fns/interval.js", "../../date-fns/intervalToDuration.js", "../../date-fns/intlFormat.js", "../../date-fns/intlFormatDistance.js", "../../date-fns/isExists.js", "../../date-fns/isFirstDayOfMonth.js", "../../date-fns/isFriday.js", "../../date-fns/isFuture.js", "../../date-fns/isMatch.js", "../../date-fns/isMonday.js", "../../date-fns/isPast.js", "../../date-fns/isSameISOWeek.js", "../../date-fns/isSameISOWeekYear.js", "../../date-fns/startOfMinute.js", "../../date-fns/isSameMinute.js", "../../date-fns/isSameQuarter.js", "../../date-fns/startOfSecond.js", "../../date-fns/isSameSecond.js", "../../date-fns/isThisHour.js", "../../date-fns/isThisISOWeek.js", "../../date-fns/isThisMinute.js", "../../date-fns/isThisMonth.js", "../../date-fns/isThisQuarter.js", "../../date-fns/isThisSecond.js", "../../date-fns/isThisWeek.js", "../../date-fns/isThisYear.js", "../../date-fns/isThursday.js", "../../date-fns/isToday.js", "../../date-fns/isTomorrow.js", "../../date-fns/isTuesday.js", "../../date-fns/isWednesday.js", "../../date-fns/subDays.js", "../../date-fns/isYesterday.js", "../../date-fns/lastDayOfDecade.js", "../../date-fns/lastDayOfWeek.js", "../../date-fns/lastDayOfISOWeek.js", "../../date-fns/lastDayOfISOWeekYear.js", "../../date-fns/lastDayOfQuarter.js", "../../date-fns/lastDayOfYear.js", "../../date-fns/lightFormat.js", "../../date-fns/milliseconds.js", "../../date-fns/millisecondsToHours.js", "../../date-fns/millisecondsToMinutes.js", "../../date-fns/millisecondsToSeconds.js", "../../date-fns/minutesToHours.js", "../../date-fns/minutesToMilliseconds.js", "../../date-fns/minutesToSeconds.js", "../../date-fns/monthsToQuarters.js", "../../date-fns/monthsToYears.js", "../../date-fns/nextDay.js", "../../date-fns/nextFriday.js", "../../date-fns/nextMonday.js", "../../date-fns/nextSaturday.js", "../../date-fns/nextSunday.js", "../../date-fns/nextThursday.js", "../../date-fns/nextTuesday.js", "../../date-fns/nextWednesday.js", "../../date-fns/parseISO.js", "../../date-fns/parseJSON.js", "../../date-fns/previousDay.js", "../../date-fns/previousFriday.js", "../../date-fns/previousMonday.js", "../../date-fns/previousSaturday.js", "../../date-fns/previousSunday.js", "../../date-fns/previousThursday.js", "../../date-fns/previousTuesday.js", "../../date-fns/previousWednesday.js", "../../date-fns/quartersToMonths.js", "../../date-fns/quartersToYears.js", "../../date-fns/roundToNearestHours.js", "../../date-fns/roundToNearestMinutes.js", "../../date-fns/secondsToHours.js", "../../date-fns/secondsToMilliseconds.js", "../../date-fns/secondsToMinutes.js", "../../date-fns/set.js", "../../date-fns/setDayOfYear.js", "../../date-fns/setDefaultOptions.js", "../../date-fns/setQuarter.js", "../../date-fns/setWeekYear.js", "../../date-fns/startOfDecade.js", "../../date-fns/startOfToday.js", "../../date-fns/startOfTomorrow.js", "../../date-fns/startOfYesterday.js", "../../date-fns/subMonths.js", "../../date-fns/sub.js", "../../date-fns/subBusinessDays.js", "../../date-fns/subHours.js", "../../date-fns/subMilliseconds.js", "../../date-fns/subMinutes.js", "../../date-fns/subQuarters.js", "../../date-fns/subSeconds.js", "../../date-fns/subWeeks.js", "../../date-fns/subYears.js", "../../date-fns/weeksToDays.js", "../../date-fns/yearsToDays.js", "../../date-fns/yearsToMonths.js", "../../date-fns/yearsToQuarters.js"], "sourcesContent": ["import { addDays } from \"./addDays.js\";\nimport { addMonths } from \"./addMonths.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link add} function options.\n */\n\n/**\n * @name add\n * @category Common Helpers\n * @summary Add the specified years, months, weeks, days, hours, minutes, and seconds to the given date.\n *\n * @description\n * Add the specified years, months, weeks, days, hours, minutes, and seconds to the given date.\n *\n * @typeParam DateType - The `Date` type the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param duration - The object with years, months, weeks, days, hours, minutes, and seconds to be added.\n * @param options - An object with options\n *\n * @returns The new date with the seconds added\n *\n * @example\n * // Add the following duration to 1 September 2014, 10:19:50\n * const result = add(new Date(2014, 8, 1, 10, 19, 50), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30,\n * })\n * //=> Thu Jun 15 2017 15:29:20\n */\nexport function add(date, duration, options) {\n  const {\n    years = 0,\n    months = 0,\n    weeks = 0,\n    days = 0,\n    hours = 0,\n    minutes = 0,\n    seconds = 0,\n  } = duration;\n\n  // Add years and months\n  const _date = toDate(date, options?.in);\n  const dateWithMonths =\n    months || years ? addMonths(_date, months + years * 12) : _date;\n\n  // Add weeks and days\n  const dateWithDays =\n    days || weeks ? addDays(dateWithMonths, days + weeks * 7) : dateWithMonths;\n\n  // Add days, hours, minutes, and seconds\n  const minutesToAdd = minutes + hours * 60;\n  const secondsToAdd = seconds + minutesToAdd * 60;\n  const msToAdd = secondsToAdd * 1000;\n\n  return constructFrom(options?.in || date, +dateWithDays + msToAdd);\n}\n\n// Fallback for modularized imports:\nexport default add;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isSaturday} function options.\n */\n\n/**\n * @name isSaturday\n * @category Weekday Helpers\n * @summary Is the given date Saturday?\n *\n * @description\n * Is the given date Saturday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Saturday\n *\n * @example\n * // Is 27 September 2014 Saturday?\n * const result = isSaturday(new Date(2014, 8, 27))\n * //=> true\n */\nexport function isSaturday(date, options) {\n  return toDate(date, options?.in).getDay() === 6;\n}\n\n// Fallback for modularized imports:\nexport default isSaturday;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isSunday} function options.\n */\n\n/**\n * @name isSunday\n * @category Weekday Helpers\n * @summary Is the given date Sunday?\n *\n * @description\n * Is the given date Sunday?\n *\n * @param date - The date to check\n * @param options - The options object\n *\n * @returns The date is Sunday\n *\n * @example\n * // Is 21 September 2014 Sunday?\n * const result = isSunday(new Date(2014, 8, 21))\n * //=> true\n */\nexport function isSunday(date, options) {\n  return toDate(date, options?.in).getDay() === 0;\n}\n\n// Fallback for modularized imports:\nexport default isSunday;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWeekend} function options.\n */\n\n/**\n * @name isWeekend\n * @category Weekday Helpers\n * @summary Does the given date fall on a weekend?\n *\n * @description\n * Does the given date fall on a weekend? A weekend is either Saturday (`6`) or Sunday (`0`).\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date falls on a weekend\n *\n * @example\n * // Does 5 October 2014 fall on a weekend?\n * const result = isWeekend(new Date(2014, 9, 5))\n * //=> true\n */\nexport function isWeekend(date, options) {\n  const day = toDate(date, options?.in).getDay();\n  return day === 0 || day === 6;\n}\n\n// Fallback for modularized imports:\nexport default isWeekend;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { isSaturday } from \"./isSaturday.js\";\nimport { isSunday } from \"./isSunday.js\";\nimport { isWeekend } from \"./isWeekend.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addBusinessDays} function options.\n */\n\n/**\n * @name addBusinessDays\n * @category Day Helpers\n * @summary Add the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Add the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of business days to be added.\n * @param options - An object with options\n *\n * @returns The new date with the business days added\n *\n * @example\n * // Add 10 business days to 1 September 2014:\n * const result = addBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Sep 15 2014 00:00:00 (skipped weekend days)\n */\nexport function addBusinessDays(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  const startedOnWeekend = isWeekend(_date, options);\n\n  if (isNaN(amount)) return constructFrom(options?.in, NaN);\n\n  const hours = _date.getHours();\n  const sign = amount < 0 ? -1 : 1;\n  const fullWeeks = Math.trunc(amount / 5);\n\n  _date.setDate(_date.getDate() + fullWeeks * 7);\n\n  // Get remaining days not part of a full week\n  let restDays = Math.abs(amount % 5);\n\n  // Loops over remaining days\n  while (restDays > 0) {\n    _date.setDate(_date.getDate() + sign);\n    if (!isWeekend(_date, options)) restDays -= 1;\n  }\n\n  // If the date is a weekend day and we reduce a dividable of\n  // 5 from it, we land on a weekend date.\n  // To counter this, we add days accordingly to land on the next business day\n  if (startedOnWeekend && isWeekend(_date, options) && amount !== 0) {\n    // If we're reducing days, we want to add days until we land on a weekday\n    // If we're adding days we want to reduce days until we land on a weekday\n    if (isSaturday(_date, options))\n      _date.setDate(_date.getDate() + (sign < 0 ? 2 : -1));\n    if (isSunday(_date, options))\n      _date.setDate(_date.getDate() + (sign < 0 ? 1 : -2));\n  }\n\n  // Restore hours to avoid DST lag\n  _date.setHours(hours);\n\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addBusinessDays;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISOWeekYear} function options.\n */\n\n/**\n * @name setISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Set the ISO week-numbering year to the given date.\n *\n * @description\n * Set the ISO week-numbering year to the given date,\n * saving the week number and the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param weekYear - The ISO week-numbering year of the new date\n * @param options - An object with options\n *\n * @returns The new date with the ISO week-numbering year set\n *\n * @example\n * // Set ISO week-numbering year 2007 to 29 December 2008:\n * const result = setISOWeekYear(new Date(2008, 11, 29), 2007)\n * //=> Mon Jan 01 2007 00:00:00\n */\nexport function setISOWeekYear(date, weekYear, options) {\n  let _date = toDate(date, options?.in);\n  const diff = differenceInCalendarDays(\n    _date,\n    startOfISOWeekYear(_date, options),\n  );\n  const fourthOfJanuary = constructFrom(options?.in || date, 0);\n  fourthOfJanuary.setFullYear(weekYear, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  _date = startOfISOWeekYear(fourthOfJanuary);\n  _date.setDate(_date.getDate() + diff);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setISOWeekYear;\n", "import { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { setISOWeekYear } from \"./setISOWeekYear.js\";\n\n/**\n * The {@link addISOWeekYears} function options.\n */\n\n/**\n * @name addISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Add the specified number of ISO week-numbering years to the given date.\n *\n * @description\n * Add the specified number of ISO week-numbering years to the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of ISO week-numbering years to be added.\n * @param options - An object with options\n *\n * @returns The new date with the ISO week-numbering years added\n *\n * @example\n * // Add 5 ISO week-numbering years to 2 July 2010:\n * const result = addISOWeekYears(new Date(2010, 6, 2), 5)\n * //=> Fri Jun 26 2015 00:00:00\n */\nexport function addISOWeekYears(date, amount, options) {\n  return setISOWeekYear(date, getISOWeekYear(date, options) + amount, options);\n}\n\n// Fallback for modularized imports:\nexport default addISOWeekYears;\n", "import { addMonths } from \"./addMonths.js\";\n\n/**\n * The {@link addQuarters} function options.\n */\n\n/**\n * @name addQuarters\n * @category Quarter Helpers\n * @summary Add the specified number of year quarters to the given date.\n *\n * @description\n * Add the specified number of year quarters to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of quarters to be added.\n * @param options - An object with options\n *\n * @returns The new date with the quarters added\n *\n * @example\n * // Add 1 quarter to 1 September 2014:\n * const result = addQuarters(new Date(2014, 8, 1), 1)\n * //=; Mon Dec 01 2014 00:00:00\n */\nexport function addQuarters(date, amount, options) {\n  return addMonths(date, amount * 3, options);\n}\n\n// Fallback for modularized imports:\nexport default addQuarters;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link areIntervalsOverlapping} function options.\n */\n\n/**\n * @name areIntervalsOverlapping\n * @category Interval Helpers\n * @summary Is the given time interval overlapping with another time interval?\n *\n * @description\n * Is the given time interval overlapping with another time interval? Adjacent intervals do not count as overlapping unless `inclusive` is set to `true`.\n *\n * @param intervalLeft - The first interval to compare.\n * @param intervalRight - The second interval to compare.\n * @param options - The object with options\n *\n * @returns Whether the time intervals are overlapping\n *\n * @example\n * // For overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> true\n *\n * @example\n * // For non-overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> false\n *\n * @example\n * // For adjacent time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 30) }\n * )\n * //=> false\n *\n * @example\n * // Using the inclusive option:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) },\n *   { inclusive: true }\n * )\n * //=> true\n */\nexport function areIntervalsOverlapping(intervalLeft, intervalRight, options) {\n  const [leftStartTime, leftEndTime] = [\n    +toDate(intervalLeft.start, options?.in),\n    +toDate(intervalLeft.end, options?.in),\n  ].sort((a, b) => a - b);\n  const [rightStartTime, rightEndTime] = [\n    +toDate(intervalRight.start, options?.in),\n    +toDate(intervalRight.end, options?.in),\n  ].sort((a, b) => a - b);\n\n  if (options?.inclusive)\n    return leftStartTime <= rightEndTime && rightStartTime <= leftEndTime;\n\n  return leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n}\n\n// Fallback for modularized imports:\nexport default areIntervalsOverlapping;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link max} function options.\n */\n\n/**\n * @name max\n * @category Common Helpers\n * @summary Return the latest of the given dates.\n *\n * @description\n * Return the latest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dates - The dates to compare\n *\n * @returns The latest of the dates\n *\n * @example\n * // Which of these dates is the latest?\n * const result = max([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Sun Jul 02 1995 00:00:00\n */\nexport function max(dates, options) {\n  let result;\n  let context = options?.in;\n\n  dates.forEach((date) => {\n    // Use the first date object as the context function\n    if (!context && typeof date === \"object\")\n      context = constructFrom.bind(null, date);\n\n    const date_ = toDate(date, context);\n    if (!result || result < date_ || isNaN(+date_)) result = date_;\n  });\n\n  return constructFrom(context, result || NaN);\n}\n\n// Fallback for modularized imports:\nexport default max;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link min} function options.\n */\n\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dates - The dates to compare\n *\n * @returns The earliest of the dates\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport function min(dates, options) {\n  let result;\n  let context = options?.in;\n\n  dates.forEach((date) => {\n    // Use the first date object as the context function\n    if (!context && typeof date === \"object\")\n      context = constructFrom.bind(null, date);\n\n    const date_ = toDate(date, context);\n    if (!result || result > date_ || isNaN(+date_)) result = date_;\n  });\n\n  return constructFrom(context, result || NaN);\n}\n\n// Fallback for modularized imports:\nexport default min;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { max } from \"./max.js\";\nimport { min } from \"./min.js\";\n\n/**\n * The {@link clamp} function options.\n */\n\n/**\n * The {@link clamp} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name clamp\n * @category Interval Helpers\n * @summary Return a date bounded by the start and the end of the given interval.\n *\n * @description\n * Clamps a date to the lower bound with the start of the interval and the upper\n * bound with the end of the interval.\n *\n * - When the date is less than the start of the interval, the start is returned.\n * - When the date is greater than the end of the interval, the end is returned.\n * - Otherwise the date is returned.\n *\n * @typeParam DateType - Date argument type.\n * @typeParam IntervalType - Interval argument type.\n * @typeParam Options - Options type.\n *\n * @param date - The date to be bounded\n * @param interval - The interval to bound to\n * @param options - An object with options\n *\n * @returns The date bounded by the start and the end of the interval\n *\n * @example\n * // What is Mar 21, 2021 bounded to an interval starting at Mar 22, 2021 and ending at Apr 01, 2021\n * const result = clamp(new Date(2021, 2, 21), {\n *   start: new Date(2021, 2, 22),\n *   end: new Date(2021, 3, 1),\n * })\n * //=> Mon Mar 22 2021 00:00:00\n */\nexport function clamp(date, interval, options) {\n  const [date_, start, end] = normalizeDates(\n    options?.in,\n    date,\n    interval.start,\n    interval.end,\n  );\n\n  return min([max([date_, start], options), end], options);\n}\n\n// Fallback for modularized imports:\nexport default clamp;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns An index of the date closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * const dateToCompare = new Date(2015, 8, 6)\n * const datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * const result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\nexport function closestIndexTo(dateToCompare, dates) {\n  // [TODO] It would be better to return -1 here rather than undefined, as this\n  // is how JS behaves, but it would be a breaking change, so we need\n  // to consider it for v4.\n  const timeToCompare = +toDate(dateToCompare);\n\n  if (isNaN(timeToCompare)) return NaN;\n\n  let result;\n  let minDistance;\n  dates.forEach((date, index) => {\n    const date_ = toDate(date);\n\n    if (isNaN(+date_)) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n\n    const distance = Math.abs(timeToCompare - +date_);\n    if (result == null || distance < minDistance) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default closestIndexTo;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { closestIndexTo } from \"./closestIndexTo.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link closestTo} function options.\n */\n\n/**\n * The {@link closestTo} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * @typeParam DateToCompare - Date to compare argument type.\n * @typeParam DatesType - Dates array argument type.\n * @typeParam Options - Options type.\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns The date from the array closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * const dateToCompare = new Date(2015, 8, 6)\n * const result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\nexport function closestTo(dateToCompare, dates, options) {\n  const [dateToCompare_, ...dates_] = normalizeDates(\n    options?.in,\n    dateToCompare,\n    ...dates,\n  );\n\n  const index = closestIndexTo(dateToCompare_, dates_);\n\n  if (typeof index === \"number\" && isNaN(index))\n    return constructFrom(dateToCompare_, NaN);\n\n  if (index !== undefined) return dates_[index];\n}\n\n// Fallback for modularized imports:\nexport default closestTo;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name compareAsc\n * @category Common Helpers\n * @summary Compare the two dates and return -1, 0 or 1.\n *\n * @description\n * Compare the two dates and return 1 if the first date is after the second,\n * -1 if the first date is before the second or 0 if dates are equal.\n *\n * @param dateLeft - The first date to compare\n * @param dateRight - The second date to compare\n *\n * @returns The result of the comparison\n *\n * @example\n * // Compare 11 February 1987 and 10 July 1989:\n * const result = compareAsc(new Date(1987, 1, 11), new Date(1989, 6, 10))\n * //=> -1\n *\n * @example\n * // Sort the array of dates:\n * const result = [\n *   new Date(1995, 6, 2),\n *   new Date(1987, 1, 11),\n *   new Date(1989, 6, 10)\n * ].sort(compareAsc)\n * //=> [\n * //   Wed Feb 11 1987 00:00:00,\n * //   Mon Jul 10 1989 00:00:00,\n * //   Sun Jul 02 1995 00:00:00\n * // ]\n */\nexport function compareAsc(dateLeft, dateRight) {\n  const diff = +toDate(dateLeft) - +toDate(dateRight);\n\n  if (diff < 0) return -1;\n  else if (diff > 0) return 1;\n\n  // Return 0 if diff is 0; return NaN if diff is NaN\n  return diff;\n}\n\n// Fallback for modularized imports:\nexport default compareAsc;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name compareDesc\n * @category Common Helpers\n * @summary Compare the two dates reverse chronologically and return -1, 0 or 1.\n *\n * @description\n * Compare the two dates and return -1 if the first date is after the second,\n * 1 if the first date is before the second or 0 if dates are equal.\n *\n * @param dateLeft - The first date to compare\n * @param dateRight - The second date to compare\n *\n * @returns The result of the comparison\n *\n * @example\n * // Compare 11 February 1987 and 10 July 1989 reverse chronologically:\n * const result = compareDesc(new Date(1987, 1, 11), new Date(1989, 6, 10))\n * //=> 1\n *\n * @example\n * // Sort the array of dates in reverse chronological order:\n * const result = [\n *   new Date(1995, 6, 2),\n *   new Date(1987, 1, 11),\n *   new Date(1989, 6, 10)\n * ].sort(compareDesc)\n * //=> [\n * //   Sun Jul 02 1995 00:00:00,\n * //   Mon Jul 10 1989 00:00:00,\n * //   Wed Feb 11 1987 00:00:00\n * // ]\n */\nexport function compareDesc(dateLeft, dateRight) {\n  const diff = +toDate(dateLeft) - +toDate(dateRight);\n\n  if (diff > 0) return -1;\n  else if (diff < 0) return 1;\n\n  // Return 0 if diff is 0; return NaN if diff is NaN\n  return diff;\n}\n\n// Fallback for modularized imports:\nexport default compareDesc;\n", "import { constructFrom } from \"./constructFrom.js\";\n\n/**\n * @name constructNow\n * @category Generic Helpers\n * @summary Constructs a new current date using the passed value constructor.\n * @pure false\n *\n * @description\n * The function constructs a new current date using the constructor from\n * the reference date. It helps to build generic functions that accept date\n * extensions and use the current date.\n *\n * It defaults to `Date` if the passed reference date is a number or a string.\n *\n * @param date - The reference date to take constructor from\n *\n * @returns Current date initialized using the given date constructor\n *\n * @example\n * import { constructNow, isSameDay } from 'date-fns'\n *\n * function isToday<DateType extends Date>(\n *   date: DateArg<DateType>,\n * ): boolean {\n *   // If we were to use `new Date()` directly, the function would  behave\n *   // differently in different timezones and return false for the same date.\n *   return isSameDay(date, constructNow(date));\n * }\n */\nexport function constructNow(date) {\n  return constructFrom(date, Date.now());\n}\n\n// Fallback for modularized imports:\nexport default constructNow;\n", "import { daysInWeek } from \"./constants.js\";\n\n/**\n * @name daysToWeeks\n * @category Conversion Helpers\n * @summary Convert days to weeks.\n *\n * @description\n * Convert a number of days to a full number of weeks.\n *\n * @param days - The number of days to be converted\n *\n * @returns The number of days converted in weeks\n *\n * @example\n * // Convert 14 days to weeks:\n * const result = daysToWeeks(14)\n * //=> 2\n *\n * @example\n * // It uses trunc rounding:\n * const result = daysToWeeks(13)\n * //=> 1\n */\nexport function daysToWeeks(days) {\n  const result = Math.trunc(days / daysInWeek);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default daysToWeeks;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { addDays } from \"./addDays.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { isSameDay } from \"./isSameDay.js\";\nimport { isValid } from \"./isValid.js\";\nimport { isWeekend } from \"./isWeekend.js\";\n\n/**\n * The {@link differenceInBusinessDays} function options.\n */\n\n/**\n * @name differenceInBusinessDays\n * @category Day Helpers\n * @summary Get the number of business days between the given dates.\n *\n * @description\n * Get the number of business day periods between the given dates.\n * Business days being days that aren't in the weekend.\n * Like `differenceInCalendarDays`, the function removes the times from\n * the dates before calculating the difference.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of business days\n *\n * @example\n * // How many business days are between\n * // 10 January 2014 and 20 July 2014?\n * const result = differenceInBusinessDays(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 0, 10)\n * )\n * //=> 136\n *\n * // How many business days are between\n * // 30 November 2021 and 1 November 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 30),\n *   new Date(2021, 10, 1)\n * )\n * //=> 21\n *\n * // How many business days are between\n * // 1 November 2021 and 1 December 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 11, 1)\n * )\n * //=> -22\n *\n * // How many business days are between\n * // 1 November 2021 and 1 November 2021 ?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 10, 1)\n * )\n * //=> 0\n */\nexport function differenceInBusinessDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  if (!isValid(laterDate_) || !isValid(earlierDate_)) return NaN;\n\n  const diff = differenceInCalendarDays(laterDate_, earlierDate_);\n  const sign = diff < 0 ? -1 : 1;\n  const weeks = Math.trunc(diff / 7);\n\n  let result = weeks * 5;\n  let movingDate = addDays(earlierDate_, weeks * 7);\n\n  // the loop below will run at most 6 times to account for the remaining days that don't makeup a full week\n  while (!isSameDay(laterDate_, movingDate)) {\n    // sign is used to account for both negative and positive differences\n    result += isWeekend(movingDate, options) ? 0 : sign;\n    movingDate = addDays(movingDate, sign);\n  }\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInBusinessDays;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\n\n/**\n * The {@link differenceInCalendarISOWeekYears} function options.\n */\n\n/**\n * @name differenceInCalendarISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of calendar ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of calendar ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar ISO week-numbering years\n *\n * @example\n * // How many calendar ISO week-numbering years are 1 January 2010 and 1 January 2012?\n * const result = differenceInCalendarISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 2\n */\nexport function differenceInCalendarISOWeekYears(\n  laterDate,\n  earlierDate,\n  options,\n) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return (\n    getISOWeekYear(laterDate_, options) - getISOWeekYear(earlierDate_, options)\n  );\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarISOWeekYears;\n", "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link differenceInCalendarISOWeeks} function options.\n */\n\n/**\n * @name differenceInCalendarISOWeeks\n * @category ISO Week Helpers\n * @summary Get the number of calendar ISO weeks between the given dates.\n *\n * @description\n * Get the number of calendar ISO weeks between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar ISO weeks\n *\n * @example\n * // How many calendar ISO weeks are between 6 July 2014 and 21 July 2014?\n * const result = differenceInCalendarISOWeeks(\n *   new Date(2014, 6, 21),\n *   new Date(2014, 6, 6),\n * );\n * //=> 3\n */\nexport function differenceInCalendarISOWeeks(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const startOfISOWeekLeft = startOfISOWeek(laterDate_);\n  const startOfISOWeekRight = startOfISOWeek(earlierDate_);\n\n  const timestampLeft =\n    +startOfISOWeekLeft - getTimezoneOffsetInMilliseconds(startOfISOWeekLeft);\n  const timestampRight =\n    +startOfISOWeekRight - getTimezoneOffsetInMilliseconds(startOfISOWeekRight);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((timestampLeft - timestampRight) / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarISOWeeks;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link differenceInCalendarMonths} function options.\n */\n\n/**\n * @name differenceInCalendarMonths\n * @category Month Helpers\n * @summary Get the number of calendar months between the given dates.\n *\n * @description\n * Get the number of calendar months between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar months\n *\n * @example\n * // How many calendar months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInCalendarMonths(\n *   new Date(2014, 8, 1),\n *   new Date(2014, 0, 31)\n * )\n * //=> 8\n */\nexport function differenceInCalendarMonths(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const yearsDiff = laterDate_.getFullYear() - earlierDate_.getFullYear();\n  const monthsDiff = laterDate_.getMonth() - earlierDate_.getMonth();\n\n  return yearsDiff * 12 + monthsDiff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarMonths;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getQuarter} function options.\n */\n\n/**\n * @name getQuarter\n * @category Quarter Helpers\n * @summary Get the year quarter of the given date.\n *\n * @description\n * Get the year quarter of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The quarter\n *\n * @example\n * // Which quarter is 2 July 2014?\n * const result = getQuarter(new Date(2014, 6, 2));\n * //=> 3\n */\nexport function getQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const quarter = Math.trunc(_date.getMonth() / 3) + 1;\n  return quarter;\n}\n\n// Fallback for modularized imports:\nexport default getQuarter;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { getQuarter } from \"./getQuarter.js\";\n\n/**\n * The {@link differenceInCalendarQuarters} function options.\n */\n\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar quarters\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\nexport function differenceInCalendarQuarters(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const yearsDiff = laterDate_.getFullYear() - earlierDate_.getFullYear();\n  const quartersDiff = getQuarter(laterDate_) - getQuarter(earlierDate_);\n\n  return yearsDiff * 4 + quartersDiff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarQuarters;\n", "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link differenceInCalendarWeeks} function options.\n */\n\n/**\n * @name differenceInCalendarWeeks\n * @category Week Helpers\n * @summary Get the number of calendar weeks between the given dates.\n *\n * @description\n * Get the number of calendar weeks between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of calendar weeks\n *\n * @example\n * // How many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5)\n * )\n * //=> 3\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5),\n *   { weekStartsOn: 1 }\n * )\n * //=> 2\n */\nexport function differenceInCalendarWeeks(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const laterStartOfWeek = startOfWeek(laterDate_, options);\n  const earlierStartOfWeek = startOfWeek(earlierDate_, options);\n\n  const laterTimestamp =\n    +laterStartOfWeek - getTimezoneOffsetInMilliseconds(laterStartOfWeek);\n  const earlierTimestamp =\n    +earlierStartOfWeek - getTimezoneOffsetInMilliseconds(earlierStartOfWeek);\n\n  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarWeeks;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link differenceInCalendarYears} function options.\n */\n\n/**\n * @name differenceInCalendarYears\n * @category Year Helpers\n * @summary Get the number of calendar years between the given dates.\n *\n * @description\n * Get the number of calendar years between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n\n * @returns The number of calendar years\n *\n * @example\n * // How many calendar years are between 31 December 2013 and 11 February 2015?\n * const result = differenceInCalendarYears(\n *   new Date(2015, 1, 11),\n *   new Date(2013, 11, 31)\n * );\n * //=> 2\n */\nexport function differenceInCalendarYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return laterDate_.getFullYear() - earlierDate_.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarYears;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\n\n/**\n * The {@link differenceInDays} function options.\n */\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.trunc(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full days according to the local timezone\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n *\n * @example\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n * //=> 92\n */\nexport function differenceInDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const sign = compareLocalAsc(laterDate_, earlierDate_);\n  const difference = Math.abs(\n    differenceInCalendarDays(laterDate_, earlierDate_),\n  );\n\n  laterDate_.setDate(laterDate_.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  const isLastDayNotFull = Number(\n    compareLocalAsc(laterDate_, earlierDate_) === -sign,\n  );\n\n  const result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(laterDate, earlierDate) {\n  const diff =\n    laterDate.getFullYear() - earlierDate.getFullYear() ||\n    laterDate.getMonth() - earlierDate.getMonth() ||\n    laterDate.getDate() - earlierDate.getDate() ||\n    laterDate.getHours() - earlierDate.getHours() ||\n    laterDate.getMinutes() - earlierDate.getMinutes() ||\n    laterDate.getSeconds() - earlierDate.getSeconds() ||\n    laterDate.getMilliseconds() - earlierDate.getMilliseconds();\n\n  if (diff < 0) return -1;\n  if (diff > 0) return 1;\n\n  // Return 0 if diff is 0; return NaN if diff is NaN\n  return diff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInDays;\n", "export function getRoundingMethod(method) {\n  return (number) => {\n    const round = method ? Math[method] : Math.trunc;\n    const result = round(number);\n    // Prevent negative zero\n    return result === 0 ? 0 : result;\n  };\n}\n", "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInHour } from \"./constants.js\";\n\n/**\n * The {@link differenceInHours} function options.\n */\n\n/**\n * @name differenceInHours\n * @category Hour Helpers\n * @summary Get the number of hours between the given dates.\n *\n * @description\n * Get the number of hours between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of hours\n *\n * @example\n * // How many hours are between 2 July 2014 06:50:00 and 2 July 2014 19:00:00?\n * const result = differenceInHours(\n *   new Date(2014, 6, 2, 19, 0),\n *   new Date(2014, 6, 2, 6, 50)\n * )\n * //=> 12\n */\nexport function differenceInHours(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  const diff = (+laterDate_ - +earlierDate_) / millisecondsInHour;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInHours;\n", "import { addISOWeekYears } from \"./addISOWeekYears.js\";\n\n/**\n * The {@link subISOWeekYears} function options.\n */\n\n/**\n * @name subISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Subtract the specified number of ISO week-numbering years from the given date.\n *\n * @description\n * Subtract the specified number of ISO week-numbering years from the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of ISO week-numbering years to be subtracted.\n * @param options - The options\n *\n * @returns The new date with the ISO week-numbering years subtracted\n *\n * @example\n * // Subtract 5 ISO week-numbering years from 1 September 2014:\n * const result = subISOWeekYears(new Date(2014, 8, 1), 5)\n * //=> Mon Aug 31 2009 00:00:00\n */\nexport function subISOWeekYears(date, amount, options) {\n  return addISOWeekYears(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subISOWeekYears;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarISOWeekYears } from \"./differenceInCalendarISOWeekYears.js\";\nimport { subISOWeekYears } from \"./subISOWeekYears.js\";\n\n/**\n * The {@link differenceInISOWeekYears} function options.\n */\n\n/**\n * @name differenceInISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of full ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of full ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - The options\n *\n * @returns The number of full ISO week-numbering years\n *\n * @example\n * // How many full ISO week-numbering years are between 1 January 2010 and 1 January 2012?\n * const result = differenceInISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * // => 1\n */\nexport function differenceInISOWeekYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const sign = compareAsc(laterDate_, earlierDate_);\n  const diff = Math.abs(\n    differenceInCalendarISOWeekYears(laterDate_, earlierDate_, options),\n  );\n\n  const adjustedDate = subISOWeekYears(laterDate_, sign * diff, options);\n\n  const isLastISOWeekYearNotFull = Number(\n    compareAsc(adjustedDate, earlierDate_) === -sign,\n  );\n  const result = sign * (diff - isLastISOWeekYearNotFull);\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInISOWeekYears;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name differenceInMilliseconds\n * @category Millisecond Helpers\n * @summary Get the number of milliseconds between the given dates.\n *\n * @description\n * Get the number of milliseconds between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n *\n * @returns The number of milliseconds\n *\n * @example\n * // How many milliseconds are between\n * // 2 July 2014 12:30:20.600 and 2 July 2014 12:30:21.700?\n * const result = differenceInMilliseconds(\n *   new Date(2014, 6, 2, 12, 30, 21, 700),\n *   new Date(2014, 6, 2, 12, 30, 20, 600)\n * )\n * //=> 1100\n */\nexport function differenceInMilliseconds(laterDate, earlierDate) {\n  return +toDate(laterDate) - +toDate(earlierDate);\n}\n\n// Fallback for modularized imports:\nexport default differenceInMilliseconds;\n", "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { millisecondsInMinute } from \"./constants.js\";\nimport { differenceInMilliseconds } from \"./differenceInMilliseconds.js\";\n\n/**\n * The {@link differenceInMinutes} function options.\n */\n\n/**\n * @name differenceInMinutes\n * @category Minute Helpers\n * @summary Get the number of minutes between the given dates.\n *\n * @description\n * Get the signed number of full (rounded towards 0) minutes between the given dates.\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of minutes\n *\n * @example\n * // How many minutes are between 2 July 2014 12:07:59 and 2 July 2014 12:20:00?\n * const result = differenceInMinutes(\n *   new Date(2014, 6, 2, 12, 20, 0),\n *   new Date(2014, 6, 2, 12, 7, 59)\n * )\n * //=> 12\n *\n * @example\n * // How many minutes are between 10:01:59 and 10:00:00\n * const result = differenceInMinutes(\n *   new Date(2000, 0, 1, 10, 0, 0),\n *   new Date(2000, 0, 1, 10, 1, 59)\n * )\n * //=> -1\n */\nexport function differenceInMinutes(dateLeft, dateRight, options) {\n  const diff =\n    differenceInMilliseconds(dateLeft, dateRight) / millisecondsInMinute;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInMinutes;\n", "import { endOfDay } from \"./endOfDay.js\";\nimport { endOfMonth } from \"./endOfMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * @name isLastDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the last day of a month?\n *\n * @description\n * Is the given date the last day of a month?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is the last day of a month\n *\n * @example\n * // Is 28 February 2014 the last day of a month?\n * const result = isLastDayOfMonth(new Date(2014, 1, 28))\n * //=> true\n */\nexport function isLastDayOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  return +endOfDay(_date, options) === +endOfMonth(_date, options);\n}\n\n// Fallback for modularized imports:\nexport default isLastDayOfMonth;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarMonths } from \"./differenceInCalendarMonths.js\";\nimport { isLastDayOfMonth } from \"./isLastDayOfMonth.js\";\n\n/**\n * The {@link differenceInMonths} function options.\n */\n\n/**\n * @name differenceInMonths\n * @category Month Helpers\n * @summary Get the number of full months between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full months\n *\n * @example\n * // How many full months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInMonths(new Date(2014, 8, 1), new Date(2014, 0, 31))\n * //=> 7\n */\nexport function differenceInMonths(laterDate, earlierDate, options) {\n  const [laterDate_, workingLaterDate, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    laterDate,\n    earlierDate,\n  );\n\n  const sign = compareAsc(workingLaterDate, earlierDate_);\n  const difference = Math.abs(\n    differenceInCalendarMonths(workingLaterDate, earlierDate_),\n  );\n\n  if (difference < 1) return 0;\n\n  if (workingLaterDate.getMonth() === 1 && workingLaterDate.getDate() > 27)\n    workingLaterDate.setDate(30);\n\n  workingLaterDate.setMonth(workingLaterDate.getMonth() - sign * difference);\n\n  let isLastMonthNotFull = compareAsc(workingLaterDate, earlierDate_) === -sign;\n\n  if (\n    isLastDayOfMonth(laterDate_) &&\n    difference === 1 &&\n    compareAsc(laterDate_, earlierDate_) === 1\n  ) {\n    isLastMonthNotFull = false;\n  }\n\n  const result = sign * (difference - +isLastMonthNotFull);\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInMonths;\n", "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\n\n/**\n * The {@link differenceInQuarters} function options.\n */\n\n/**\n * @name differenceInQuarters\n * @category Quarter Helpers\n * @summary Get the number of quarters between the given dates.\n *\n * @description\n * Get the number of quarters between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of full quarters\n *\n * @example\n * // How many full quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInQuarters(new Date(2014, 6, 2), new Date(2013, 11, 31))\n * //=> 2\n */\nexport function differenceInQuarters(laterDate, earlierDate, options) {\n  const diff = differenceInMonths(laterDate, earlierDate, options) / 3;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInQuarters;\n", "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { differenceInMilliseconds } from \"./differenceInMilliseconds.js\";\n\n/**\n * The {@link differenceInSeconds} function options.\n */\n\n/**\n * @name differenceInSeconds\n * @category Second Helpers\n * @summary Get the number of seconds between the given dates.\n *\n * @description\n * Get the number of seconds between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of seconds\n *\n * @example\n * // How many seconds are between\n * // 2 July 2014 12:30:07.999 and 2 July 2014 12:30:20.000?\n * const result = differenceInSeconds(\n *   new Date(2014, 6, 2, 12, 30, 20, 0),\n *   new Date(2014, 6, 2, 12, 30, 7, 999)\n * )\n * //=> 12\n */\nexport function differenceInSeconds(laterDate, earlierDate, options) {\n  const diff = differenceInMilliseconds(laterDate, earlierDate) / 1000;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInSeconds;\n", "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { differenceInDays } from \"./differenceInDays.js\";\n\n/**\n * The {@link differenceInWeeks} function options.\n */\n\n/**\n * @name differenceInWeeks\n * @category Week Helpers\n * @summary Get the number of full weeks between the given dates.\n *\n * @description\n * Get the number of full weeks between two dates. Fractional weeks are\n * truncated towards zero by default.\n *\n * One \"full week\" is the distance between a local time in one day to the same\n * local time 7 days earlier or later. A full week can sometimes be less than\n * or more than 7*24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 7*24-hour periods, use this instead:\n * `Math.trunc(differenceInHours(dateLeft, dateRight)/(7*24))|0`.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full weeks\n *\n * @example\n * // How many full weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInWeeks(new Date(2014, 6, 20), new Date(2014, 6, 5))\n * //=> 2\n *\n * @example\n * // How many full weeks are between\n * // 1 March 2020 0:00 and 6 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 8 weeks (54 days),\n * // even if DST starts and the period has\n * // only 54*24-1 hours.\n * const result = differenceInWeeks(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 6)\n * )\n * //=> 8\n */\nexport function differenceInWeeks(laterDate, earlierDate, options) {\n  const diff = differenceInDays(laterDate, earlierDate, options) / 7;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInWeeks;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarYears } from \"./differenceInCalendarYears.js\";\n\n/**\n * The {@link differenceInYears} function options.\n */\n\n/**\n * @name differenceInYears\n * @category Year Helpers\n * @summary Get the number of full years between the given dates.\n *\n * @description\n * Get the number of full years between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full years\n *\n * @example\n * // How many full years are between 31 December 2013 and 11 February 2015?\n * const result = differenceInYears(new Date(2015, 1, 11), new Date(2013, 11, 31))\n * //=> 1\n */\nexport function differenceInYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  // -1 if the left date is earlier than the right date\n  // 2023-12-31 - 2024-01-01 = -1\n  const sign = compareAsc(laterDate_, earlierDate_);\n\n  // First calculate the difference in calendar years\n  // 2024-01-01 - 2023-12-31 = 1 year\n  const diff = Math.abs(differenceInCalendarYears(laterDate_, earlierDate_));\n\n  // Now we need to calculate if the difference is full. To do that we set\n  // both dates to the same year and check if the both date's month and day\n  // form a full year.\n  laterDate_.setFullYear(1584);\n  earlierDate_.setFullYear(1584);\n\n  // For it to be true, when the later date is indeed later than the earlier date\n  // (2026-02-01 - 2023-12-10 = 3 years), the difference is full if\n  // the normalized later date is also later than the normalized earlier date.\n  // In our example, 1584-02-01 is earlier than 1584-12-10, so the difference\n  // is partial, hence we need to subtract 1 from the difference 3 - 1 = 2.\n  const partial = compareAsc(laterDate_, earlierDate_) === -sign;\n\n  const result = sign * (diff - +partial);\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInYears;\n", "import { normalizeDates } from \"./normalizeDates.js\";\n\nexport function normalizeInterval(context, interval) {\n  const [start, end] = normalizeDates(context, interval.start, interval.end);\n  return { start, end };\n}\n", "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachDayOfInterval} function options.\n */\n\n/**\n * The {@link eachDayOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachDayOfInterval\n * @category Interval Helpers\n * @summary Return the array of dates within the specified time interval.\n *\n * @description\n * Return the array of dates within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of days from the day of the interval start to the day of the interval end\n *\n * @example\n * // Each day between 6 October 2014 and 10 October 2014:\n * const result = eachDayOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 9, 10)\n * })\n * //=> [\n * //   Mon Oct 06 2014 00:00:00,\n * //   Tue Oct 07 2014 00:00:00,\n * //   Wed Oct 08 2014 00:00:00,\n * //   Thu Oct 09 2014 00:00:00,\n * //   Fri Oct 10 2014 00:00:00\n * // ]\n */\nexport function eachDayOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  const date = reversed ? end : start;\n  date.setHours(0, 0, 0, 0);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date.setDate(date.getDate() + step);\n    date.setHours(0, 0, 0, 0);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachDayOfInterval;\n", "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachHourOfInterval} function options.\n */\n\n/**\n * The {@link eachHourOfInterval} function result type.\n * Resolves to the appropriate date type based on inputs.\n */\n\n/**\n * @name eachHourOfInterval\n * @category Interval Helpers\n * @summary Return the array of hours within the specified time interval.\n *\n * @description\n * Return the array of hours within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of hours from the hour of the interval start to the hour of the interval end\n *\n * @example\n * // Each hour between 6 October 2014, 12:00 and 6 October 2014, 15:00\n * const result = eachHourOfInterval({\n *   start: new Date(2014, 9, 6, 12),\n *   end: new Date(2014, 9, 6, 15)\n * });\n * //=> [\n * //   Mon Oct 06 2014 12:00:00,\n * //   Mon Oct 06 2014 13:00:00,\n * //   Mon Oct 06 2014 14:00:00,\n * //   Mon Oct 06 2014 15:00:00\n * // ]\n */\nexport function eachHourOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  const date = reversed ? end : start;\n  date.setMinutes(0, 0, 0);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date.setHours(date.getHours() + step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachHourOfInterval;\n", "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addMinutes } from \"./addMinutes.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachMinuteOfInterval} function options.\n */\n\n/**\n * The {@link eachMinuteOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of minutes from the minute of the interval start to the minute of the interval end\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nexport function eachMinuteOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n  // Set to the start of the minute\n  start.setSeconds(0, 0);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  let date = reversed ? end : start;\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date = addMinutes(date, step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMinuteOfInterval;\n", "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachMonthOfInterval} function options.\n */\n\n/**\n * The {@link eachMonthOfInterval} function result type. It resolves the proper data type.\n */\n\n/**\n * @name eachMonthOfInterval\n * @category Interval Helpers\n * @summary Return the array of months within the specified time interval.\n *\n * @description\n * Return the array of months within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of months from the month of the interval start to the month of the interval end\n *\n * @example\n * // Each month between 6 February 2014 and 10 August 2014:\n * const result = eachMonthOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Sat Feb 01 2014 00:00:00,\n * //   Sat Mar 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Thu May 01 2014 00:00:00,\n * //   Sun Jun 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * //   Fri Aug 01 2014 00:00:00\n * // ]\n */\nexport function eachMonthOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  const date = reversed ? end : start;\n  date.setHours(0, 0, 0, 0);\n  date.setDate(1);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date.setMonth(date.getMonth() + step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMonthOfInterval;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfQuarter} function options.\n */\n\n/**\n * @name startOfQuarter\n * @category Quarter Helpers\n * @summary Return the start of a year quarter for the given date.\n *\n * @description\n * Return the start of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a quarter\n *\n * @example\n * // The start of a quarter for 2 September 2014 11:55:00:\n * const result = startOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Jul 01 2014 00:00:00\n */\nexport function startOfQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const currentMonth = _date.getMonth();\n  const month = currentMonth - (currentMonth % 3);\n  _date.setMonth(month, 1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfQuarter;\n", "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addQuarters } from \"./addQuarters.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfQuarter } from \"./startOfQuarter.js\";\n\n/**\n * The {@link eachQuarterOfInterval} function options.\n */\n\n/**\n * The {@link eachQuarterOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachQuarterOfInterval\n * @category Interval Helpers\n * @summary Return the array of quarters within the specified time interval.\n *\n * @description\n * Return the array of quarters within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval\n * @param options - An object with options\n *\n * @returns The array with starts of quarters from the quarter of the interval start to the quarter of the interval end\n *\n * @example\n * // Each quarter within interval 6 February 2014 - 10 August 2014:\n * const result = eachQuarterOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10),\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * // ]\n */\nexport function eachQuarterOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +startOfQuarter(start) : +startOfQuarter(end);\n  let date = reversed ? startOfQuarter(end) : startOfQuarter(start);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date = addQuarters(date, step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachQuarterOfInterval;\n", "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addWeeks } from \"./addWeeks.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link eachWeekOfInterval} function options.\n */\n\n/**\n * The {@link eachWeekOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the interval start date,\n * then the end interval date. If a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of weeks from the week of the interval start to the week of the interval end\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\nexport function eachWeekOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const startDateWeek = reversed\n    ? startOfWeek(end, options)\n    : startOfWeek(start, options);\n  const endDateWeek = reversed\n    ? startOfWeek(start, options)\n    : startOfWeek(end, options);\n\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n\n  const endTime = +endDateWeek.getTime();\n  let currentDate = startDateWeek;\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    currentDate.setHours(0);\n    dates.push(constructFrom(start, currentDate));\n    currentDate = addWeeks(currentDate, step);\n    currentDate.setHours(15);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekOfInterval;\n", "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { eachDayOfInterval } from \"./eachDayOfInterval.js\";\nimport { isWeekend } from \"./isWeekend.js\";\n\n/**\n * The {@link eachWeekendOfInterval} function options.\n */\n\n/**\n * The {@link eachWeekendOfInterval} function result type.\n */\n\n/**\n * @name eachWeekendOfInterval\n * @category Interval Helpers\n * @summary List all the Saturdays and Sundays in the given date interval.\n *\n * @description\n * Get all the Saturdays and Sundays in the given date interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The given interval\n * @param options - An object with options\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the given date interval\n * const result = eachWeekendOfInterval({\n *   start: new Date(2018, 8, 17),\n *   end: new Date(2018, 8, 30)\n * })\n * //=> [\n * //   Sat Sep 22 2018 00:00:00,\n * //   Sun Sep 23 2018 00:00:00,\n * //   Sat Sep 29 2018 00:00:00,\n * //   Sun Sep 30 2018 00:00:00\n * // ]\n */\nexport function eachWeekendOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n  const dateInterval = eachDayOfInterval({ start, end }, options);\n  const weekends = [];\n  let index = 0;\n  while (index < dateInterval.length) {\n    const date = dateInterval[index++];\n    if (isWeekend(date)) weekends.push(constructFrom(start, date));\n  }\n  return weekends;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfInterval;\n", "import { eachWeekendOfInterval } from \"./eachWeekendOfInterval.js\";\nimport { endOfMonth } from \"./endOfMonth.js\";\nimport { startOfMonth } from \"./startOfMonth.js\";\n\n/**\n * The {@link eachWeekendOfMonth} function options.\n */\n\n/**\n * @name eachWeekendOfMonth\n * @category Month Helpers\n * @summary List all the Saturdays and Sundays in the given month.\n *\n * @description\n * Get all the Saturdays and Sundays in the given month.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The given month\n * @param options - An object with options\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the given month\n * const result = eachWeekendOfMonth(new Date(2022, 1, 1))\n * //=> [\n * //   Sat Feb 05 2022 00:00:00,\n * //   Sun Feb 06 2022 00:00:00,\n * //   Sat Feb 12 2022 00:00:00,\n * //   Sun Feb 13 2022 00:00:00,\n * //   Sat Feb 19 2022 00:00:00,\n * //   Sun Feb 20 2022 00:00:00,\n * //   Sat Feb 26 2022 00:00:00,\n * //   Sun Feb 27 2022 00:00:00\n * // ]\n */\nexport function eachWeekendOfMonth(date, options) {\n  const start = startOfMonth(date, options);\n  const end = endOfMonth(date, options);\n  return eachWeekendOfInterval({ start, end }, options);\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfMonth;\n", "import { eachWeekendOfInterval } from \"./eachWeekendOfInterval.js\";\nimport { endOfYear } from \"./endOfYear.js\";\nimport { startOfYear } from \"./startOfYear.js\";\n\n/**\n * The {@link eachWeekendOfYear} function options.\n */\n\n/**\n * @name eachWeekendOfYear\n * @category Year Helpers\n * @summary List all the Saturdays and Sundays in the year.\n *\n * @description\n * Get all the Saturdays and Sundays in the year.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The given year\n * @param options - An object with options\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the year\n * const result = eachWeekendOfYear(new Date(2020, 1, 1))\n * //=> [\n * //   Sat Jan 03 2020 00:00:00,\n * //   Sun Jan 04 2020 00:00:00,\n * //   ...\n * //   Sun Dec 27 2020 00:00:00\n * // ]\n * ]\n */\nexport function eachWeekendOfYear(date, options) {\n  const start = startOfYear(date, options);\n  const end = endOfYear(date, options);\n  return eachWeekendOfInterval({ start, end }, options);\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfYear;\n", "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachYearOfInterval} function options.\n */\n\n/**\n * The {@link eachYearOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachYearOfInterval\n * @category Interval Helpers\n * @summary Return the array of yearly timestamps within the specified time interval.\n *\n * @description\n * Return the array of yearly timestamps within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of yearly timestamps from the month of the interval start to the month of the interval end\n *\n * @example\n * // Each year between 6 February 2014 and 10 August 2017:\n * const result = eachYearOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2017, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Thu Jan 01 2015 00:00:00,\n * //   Fri Jan 01 2016 00:00:00,\n * //   Sun Jan 01 2017 00:00:00\n * // ]\n */\nexport function eachYearOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  const date = reversed ? end : start;\n  date.setHours(0, 0, 0, 0);\n  date.setMonth(0, 1);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date.setFullYear(date.getFullYear() + step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachYearOfInterval;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfDecade} function options.\n */\n\n/**\n * @name endOfDecade\n * @category Decade Helpers\n * @summary Return the end of a decade for the given date.\n *\n * @description\n * Return the end of a decade for the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a decade\n *\n * @example\n * // The end of a decade for 12 May 1984 00:00:00:\n * const result = endOfDecade(new Date(1984, 4, 12, 00, 00, 00))\n * //=> Dec 31 1989 23:59:59.999\n */\nexport function endOfDecade(date, options) {\n  // TODO: Switch to more technical definition in of decades that start with 1\n  // end with 0. I.e. 2001-2010 instead of current 2000-2009. It's a breaking\n  // change, so it can only be done in 4.0.\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = 9 + Math.floor(year / 10) * 10;\n  _date.setFullYear(decade, 11, 31);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfDecade;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfHour} function options.\n */\n\n/**\n * @name endOfHour\n * @category Hour Helpers\n * @summary Return the end of an hour for the given date.\n *\n * @description\n * Return the end of an hour for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of an hour\n *\n * @example\n * // The end of an hour for 2 September 2014 11:55:00:\n * const result = endOfHour(new Date(2014, 8, 2, 11, 55))\n * //=> Tue Sep 02 2014 11:59:59.999\n */\nexport function endOfHour(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMinutes(59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfHour;\n", "import { endOfWeek } from \"./endOfWeek.js\";\n\n/**\n * The {@link endOfISOWeek} function options.\n */\n\n/**\n * @name endOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the end of an ISO week for the given date.\n *\n * @description\n * Return the end of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of an ISO week\n *\n * @example\n * // The end of an ISO week for 2 September 2014 11:55:00:\n * const result = endOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfISOWeek(date, options) {\n  return endOfWeek(date, { ...options, weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default endOfISOWeek;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link endOfISOWeekYear} function options.\n */\n\n/**\n * @name endOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the end of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the end of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The end of an ISO week-numbering year\n *\n * @example\n * // The end of an ISO week-numbering year for 2 July 2005:\n * const result = endOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Sun Jan 01 2006 23:59:59.999\n */\nexport function endOfISOWeekYear(date, options) {\n  const year = getISOWeekYear(date, options);\n  const fourthOfJanuaryOfNextYear = constructFrom(options?.in || date, 0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  const _date = startOfISOWeek(fourthOfJanuaryOfNextYear, options);\n  _date.setMilliseconds(_date.getMilliseconds() - 1);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfISOWeekYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfMinute} function options.\n */\n\n/**\n * @name endOfMinute\n * @category Minute Helpers\n * @summary Return the end of a minute for the given date.\n *\n * @description\n * Return the end of a minute for the given date.\n * The result will be in the local timezone or the provided context.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a minute\n *\n * @example\n * // The end of a minute for 1 December 2014 22:15:45.400:\n * const result = endOfMinute(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:59.999\n */\nexport function endOfMinute(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setSeconds(59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfMinute;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfQuarter} function options.\n */\n\n/**\n * @name endOfQuarter\n * @category Quarter Helpers\n * @summary Return the end of a year quarter for the given date.\n *\n * @description\n * Return the end of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a quarter\n *\n * @example\n * // The end of a quarter for 2 September 2014 11:55:00:\n * const result = endOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\nexport function endOfQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const currentMonth = _date.getMonth();\n  const month = currentMonth - (currentMonth % 3) + 3;\n  _date.setMonth(month, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfQuarter;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfSecond} function options.\n */\n\n/**\n * @name endOfSecond\n * @category Second Helpers\n * @summary Return the end of a second for the given date.\n *\n * @description\n * Return the end of a second for the given date.\n * The result will be in the local timezone if no `in` option is specified.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a second\n *\n * @example\n * // The end of a second for 1 December 2014 22:15:45.400:\n * const result = endOfSecond(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:45.999\n */\nexport function endOfSecond(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMilliseconds(999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfSecond;\n", "import { endOfDay } from \"./endOfDay.js\";\n\n/**\n * The {@link endOfToday} function options.\n */\n\n/**\n * @name endOfToday\n * @category Day Helpers\n * @summary Return the end of today.\n * @pure false\n *\n * @description\n * Return the end of today.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param options - The options\n *\n * @returns The end of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfToday()\n * //=> Mon Oct 6 2014 23:59:59.999\n */\nexport function endOfToday(options) {\n  return endOfDay(Date.now(), options);\n}\n\n// Fallback for modularized imports:\nexport default endOfToday;\n", "import { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link endOfTomorrow} function options.\n */\n\n/**\n * @name endOfTomorrow\n * @category Day Helpers\n * @summary Return the end of tomorrow.\n * @pure false\n *\n * @description\n * Return the end of tomorrow.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param options - The options\n * @returns The end of tomorrow\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfTomorrow()\n * //=> Tue Oct 7 2014 23:59:59.999\n */\nexport function endOfTomorrow(options) {\n  const now = constructNow(options?.in);\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n\n  const date = constructNow(options?.in);\n  date.setFullYear(year, month, day + 1);\n  date.setHours(23, 59, 59, 999);\n  return options?.in ? options.in(date) : date;\n}\n\n// Fallback for modularized imports:\nexport default endOfTomorrow;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link endOfYesterday} function options.\n */\n\n/**\n * @name endOfYesterday\n * @category Day Helpers\n * @summary Return the end of yesterday.\n * @pure false\n *\n * @description\n * Return the end of yesterday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @returns The end of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfYesterday()\n * //=> Sun Oct 5 2014 23:59:59.999\n */\nexport function endOfYesterday(options) {\n  const now = constructNow(options?.in);\n  const date = constructFrom(options?.in, 0);\n  date.setFullYear(now.getFullYear(), now.getMonth(), now.getDate() - 1);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default endOfYesterday;\n", "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { minutesInDay, minutesInMonth } from \"./constants.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\n\n/**\n * The {@link formatDistance} function options.\n */\n\n/**\n * @name formatDistance\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words.\n *\n * | Distance between dates                                            | Result              |\n * |-------------------------------------------------------------------|---------------------|\n * | 0 ... 30 secs                                                     | less than a minute  |\n * | 30 secs ... 1 min 30 secs                                         | 1 minute            |\n * | 1 min 30 secs ... 44 mins 30 secs                                 | [2..44] minutes     |\n * | 44 mins ... 30 secs ... 89 mins 30 secs                           | about 1 hour        |\n * | 89 mins 30 secs ... 23 hrs 59 mins 30 secs                        | about [2..24] hours |\n * | 23 hrs 59 mins 30 secs ... 41 hrs 59 mins 30 secs                 | 1 day               |\n * | 41 hrs 59 mins 30 secs ... 29 days 23 hrs 59 mins 30 secs         | [2..30] days        |\n * | 29 days 23 hrs 59 mins 30 secs ... 44 days 23 hrs 59 mins 30 secs | about 1 month       |\n * | 44 days 23 hrs 59 mins 30 secs ... 59 days 23 hrs 59 mins 30 secs | about 2 months      |\n * | 59 days 23 hrs 59 mins 30 secs ... 1 yr                           | [2..12] months      |\n * | 1 yr ... 1 yr 3 months                                            | about 1 year        |\n * | 1 yr 3 months ... 1 yr 9 month s                                  | over 1 year         |\n * | 1 yr 9 months ... 2 yrs                                           | almost 2 years      |\n * | N yrs ... N yrs 3 months                                          | about N years       |\n * | N yrs 3 months ... N yrs 9 months                                 | over N years        |\n * | N yrs 9 months ... N+1 yrs                                        | almost N+1 years    |\n *\n * With `options.includeSeconds == true`:\n * | Distance between dates | Result               |\n * |------------------------|----------------------|\n * | 0 secs ... 5 secs      | less than 5 seconds  |\n * | 5 secs ... 10 secs     | less than 10 seconds |\n * | 10 secs ... 20 secs    | less than 20 seconds |\n * | 20 secs ... 40 secs    | half a minute        |\n * | 40 secs ... 60 secs    | less than a minute   |\n * | 60 secs ... 90 secs    | 1 minute             |\n *\n * @param laterDate - The date\n * @param earlierDate - The date to compare with\n * @param options - An object with options\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistance(new Date(2014, 6, 2), new Date(2015, 0, 1))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00, including seconds?\n * const result = formatDistance(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0),\n *   { includeSeconds: true }\n * )\n * //=> 'less than 20 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistance(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> 'about 1 year ago'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistance(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> 'pli ol 1 jaro'\n */\nexport function formatDistance(laterDate, earlierDate, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const minutesInAlmostTwoDays = 2520;\n\n  const comparison = compareAsc(laterDate, earlierDate);\n\n  if (isNaN(comparison)) throw new RangeError(\"Invalid time value\");\n\n  const localizeOptions = Object.assign({}, options, {\n    addSuffix: options?.addSuffix,\n    comparison: comparison,\n  });\n\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    ...(comparison > 0 ? [earlierDate, laterDate] : [laterDate, earlierDate]),\n  );\n\n  const seconds = differenceInSeconds(earlierDate_, laterDate_);\n  const offsetInSeconds =\n    (getTimezoneOffsetInMilliseconds(earlierDate_) -\n      getTimezoneOffsetInMilliseconds(laterDate_)) /\n    1000;\n  const minutes = Math.round((seconds - offsetInSeconds) / 60);\n  let months;\n\n  // 0 up to 2 mins\n  if (minutes < 2) {\n    if (options?.includeSeconds) {\n      if (seconds < 5) {\n        return locale.formatDistance(\"lessThanXSeconds\", 5, localizeOptions);\n      } else if (seconds < 10) {\n        return locale.formatDistance(\"lessThanXSeconds\", 10, localizeOptions);\n      } else if (seconds < 20) {\n        return locale.formatDistance(\"lessThanXSeconds\", 20, localizeOptions);\n      } else if (seconds < 40) {\n        return locale.formatDistance(\"halfAMinute\", 0, localizeOptions);\n      } else if (seconds < 60) {\n        return locale.formatDistance(\"lessThanXMinutes\", 1, localizeOptions);\n      } else {\n        return locale.formatDistance(\"xMinutes\", 1, localizeOptions);\n      }\n    } else {\n      if (minutes === 0) {\n        return locale.formatDistance(\"lessThanXMinutes\", 1, localizeOptions);\n      } else {\n        return locale.formatDistance(\"xMinutes\", minutes, localizeOptions);\n      }\n    }\n\n    // 2 mins up to 0.75 hrs\n  } else if (minutes < 45) {\n    return locale.formatDistance(\"xMinutes\", minutes, localizeOptions);\n\n    // 0.75 hrs up to 1.5 hrs\n  } else if (minutes < 90) {\n    return locale.formatDistance(\"aboutXHours\", 1, localizeOptions);\n\n    // 1.5 hrs up to 24 hrs\n  } else if (minutes < minutesInDay) {\n    const hours = Math.round(minutes / 60);\n    return locale.formatDistance(\"aboutXHours\", hours, localizeOptions);\n\n    // 1 day up to 1.75 days\n  } else if (minutes < minutesInAlmostTwoDays) {\n    return locale.formatDistance(\"xDays\", 1, localizeOptions);\n\n    // 1.75 days up to 30 days\n  } else if (minutes < minutesInMonth) {\n    const days = Math.round(minutes / minutesInDay);\n    return locale.formatDistance(\"xDays\", days, localizeOptions);\n\n    // 1 month up to 2 months\n  } else if (minutes < minutesInMonth * 2) {\n    months = Math.round(minutes / minutesInMonth);\n    return locale.formatDistance(\"aboutXMonths\", months, localizeOptions);\n  }\n\n  months = differenceInMonths(earlierDate_, laterDate_);\n\n  // 2 months up to 12 months\n  if (months < 12) {\n    const nearestMonth = Math.round(minutes / minutesInMonth);\n    return locale.formatDistance(\"xMonths\", nearestMonth, localizeOptions);\n\n    // 1 year up to max Date\n  } else {\n    const monthsSinceStartOfYear = months % 12;\n    const years = Math.trunc(months / 12);\n\n    // N years up to 1 years 3 months\n    if (monthsSinceStartOfYear < 3) {\n      return locale.formatDistance(\"aboutXYears\", years, localizeOptions);\n\n      // N years 3 months up to N years 9 months\n    } else if (monthsSinceStartOfYear < 9) {\n      return locale.formatDistance(\"overXYears\", years, localizeOptions);\n\n      // N years 9 months up to N year 12 months\n    } else {\n      return locale.formatDistance(\"almostXYears\", years + 1, localizeOptions);\n    }\n  }\n}\n\n// Fallback for modularized imports:\nexport default formatDistance;\n", "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport {\n  millisecondsInMinute,\n  minutesInDay,\n  minutesInMonth,\n  minutesInYear,\n} from \"./constants.js\";\n\n/**\n * The {@link formatDistanceStrict} function options.\n */\n\n/**\n * The unit used to format the distance in {@link formatDistanceStrict}.\n */\n\n/**\n * @name formatDistanceStrict\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words, using strict units.\n * This is like `formatDistance`, but does not use helpers like 'almost', 'over',\n * 'less than' and the like.\n *\n * | Distance between dates | Result              |\n * |------------------------|---------------------|\n * | 0 ... 59 secs          | [0..59] seconds     |\n * | 1 ... 59 mins          | [1..59] minutes     |\n * | 1 ... 23 hrs           | [1..23] hours       |\n * | 1 ... 29 days          | [1..29] days        |\n * | 1 ... 11 months        | [1..11] months      |\n * | 1 ... N years          | [1..N]  years       |\n *\n * @param laterDate - The date\n * @param earlierDate - The date to compare with\n * @param options - An object with options\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.unit` must be 'second', 'minute', 'hour', 'day', 'month' or 'year'\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistanceStrict(new Date(2014, 6, 2), new Date(2015, 0, 2))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00?\n * const result = formatDistanceStrict(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0)\n * )\n * //=> '15 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistanceStrict(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> '1 year ago'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, in minutes?\n * const result = formatDistanceStrict(new Date(2016, 0, 1), new Date(2015, 0, 1), {\n *   unit: 'minute'\n * })\n * //=> '525600 minutes'\n *\n * @example\n * // What is the distance from 1 January 2015\n * // to 28 January 2015, in months, rounded up?\n * const result = formatDistanceStrict(new Date(2015, 0, 28), new Date(2015, 0, 1), {\n *   unit: 'month',\n *   roundingMethod: 'ceil'\n * })\n * //=> '1 month'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistanceStrict(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> '1 jaro'\n */\n\nexport function formatDistanceStrict(laterDate, earlierDate, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n\n  const comparison = compareAsc(laterDate, earlierDate);\n\n  if (isNaN(comparison)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const localizeOptions = Object.assign({}, options, {\n    addSuffix: options?.addSuffix,\n    comparison: comparison,\n  });\n\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    ...(comparison > 0 ? [earlierDate, laterDate] : [laterDate, earlierDate]),\n  );\n\n  const roundingMethod = getRoundingMethod(options?.roundingMethod ?? \"round\");\n\n  const milliseconds = earlierDate_.getTime() - laterDate_.getTime();\n  const minutes = milliseconds / millisecondsInMinute;\n\n  const timezoneOffset =\n    getTimezoneOffsetInMilliseconds(earlierDate_) -\n    getTimezoneOffsetInMilliseconds(laterDate_);\n\n  // Use DST-normalized difference in minutes for years, months and days;\n  // use regular difference in minutes for hours, minutes and seconds.\n  const dstNormalizedMinutes =\n    (milliseconds - timezoneOffset) / millisecondsInMinute;\n\n  const defaultUnit = options?.unit;\n  let unit;\n  if (!defaultUnit) {\n    if (minutes < 1) {\n      unit = \"second\";\n    } else if (minutes < 60) {\n      unit = \"minute\";\n    } else if (minutes < minutesInDay) {\n      unit = \"hour\";\n    } else if (dstNormalizedMinutes < minutesInMonth) {\n      unit = \"day\";\n    } else if (dstNormalizedMinutes < minutesInYear) {\n      unit = \"month\";\n    } else {\n      unit = \"year\";\n    }\n  } else {\n    unit = defaultUnit;\n  }\n\n  // 0 up to 60 seconds\n  if (unit === \"second\") {\n    const seconds = roundingMethod(milliseconds / 1000);\n    return locale.formatDistance(\"xSeconds\", seconds, localizeOptions);\n\n    // 1 up to 60 mins\n  } else if (unit === \"minute\") {\n    const roundedMinutes = roundingMethod(minutes);\n    return locale.formatDistance(\"xMinutes\", roundedMinutes, localizeOptions);\n\n    // 1 up to 24 hours\n  } else if (unit === \"hour\") {\n    const hours = roundingMethod(minutes / 60);\n    return locale.formatDistance(\"xHours\", hours, localizeOptions);\n\n    // 1 up to 30 days\n  } else if (unit === \"day\") {\n    const days = roundingMethod(dstNormalizedMinutes / minutesInDay);\n    return locale.formatDistance(\"xDays\", days, localizeOptions);\n\n    // 1 up to 12 months\n  } else if (unit === \"month\") {\n    const months = roundingMethod(dstNormalizedMinutes / minutesInMonth);\n    return months === 12 && defaultUnit !== \"month\"\n      ? locale.formatDistance(\"xYears\", 1, localizeOptions)\n      : locale.formatDistance(\"xMonths\", months, localizeOptions);\n\n    // 1 year up to max Date\n  } else {\n    const years = roundingMethod(dstNormalizedMinutes / minutesInYear);\n    return locale.formatDistance(\"xYears\", years, localizeOptions);\n  }\n}\n\n// Fallback for modularized imports:\nexport default formatDistanceStrict;\n", "import { constructNow } from \"./constructNow.js\";\n\nimport { formatDistance } from \"./formatDistance.js\";\n\n/**\n * The {@link formatDistanceToNow} function options.\n */\n\n/**\n * @name formatDistanceToNow\n * @category Common Helpers\n * @summary Return the distance between the given date and now in words.\n * @pure false\n *\n * @description\n * Return the distance between the given date and now in words.\n *\n * | Distance to now                                                   | Result              |\n * |-------------------------------------------------------------------|---------------------|\n * | 0 ... 30 secs                                                     | less than a minute  |\n * | 30 secs ... 1 min 30 secs                                         | 1 minute            |\n * | 1 min 30 secs ... 44 mins 30 secs                                 | [2..44] minutes     |\n * | 44 mins ... 30 secs ... 89 mins 30 secs                           | about 1 hour        |\n * | 89 mins 30 secs ... 23 hrs 59 mins 30 secs                        | about [2..24] hours |\n * | 23 hrs 59 mins 30 secs ... 41 hrs 59 mins 30 secs                 | 1 day               |\n * | 41 hrs 59 mins 30 secs ... 29 days 23 hrs 59 mins 30 secs         | [2..30] days        |\n * | 29 days 23 hrs 59 mins 30 secs ... 44 days 23 hrs 59 mins 30 secs | about 1 month       |\n * | 44 days 23 hrs 59 mins 30 secs ... 59 days 23 hrs 59 mins 30 secs | about 2 months      |\n * | 59 days 23 hrs 59 mins 30 secs ... 1 yr                           | [2..12] months      |\n * | 1 yr ... 1 yr 3 months                                            | about 1 year        |\n * | 1 yr 3 months ... 1 yr 9 month s                                  | over 1 year         |\n * | 1 yr 9 months ... 2 yrs                                           | almost 2 years      |\n * | N yrs ... N yrs 3 months                                          | about N years       |\n * | N yrs 3 months ... N yrs 9 months                                 | over N years        |\n * | N yrs 9 months ... N+1 yrs                                        | almost N+1 years    |\n *\n * With `options.includeSeconds == true`:\n * | Distance to now     | Result               |\n * |---------------------|----------------------|\n * | 0 secs ... 5 secs   | less than 5 seconds  |\n * | 5 secs ... 10 secs  | less than 10 seconds |\n * | 10 secs ... 20 secs | less than 20 seconds |\n * | 20 secs ... 40 secs | half a minute        |\n * | 40 secs ... 60 secs | less than a minute   |\n * | 60 secs ... 90 secs | 1 minute             |\n *\n * @param date - The given date\n * @param options - The object with options\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // If today is 1 January 2015, what is the distance to 2 July 2014?\n * const result = formatDistanceToNow(\n *   new Date(2014, 6, 2)\n * )\n * //=> '6 months'\n *\n * @example\n * // If now is 1 January 2015 00:00:00,\n * // what is the distance to 1 January 2015 00:00:15, including seconds?\n * const result = formatDistanceToNow(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   {includeSeconds: true}\n * )\n * //=> 'less than 20 seconds'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016, with a suffix?\n * const result = formatDistanceToNow(\n *   new Date(2016, 0, 1),\n *   {addSuffix: true}\n * )\n * //=> 'in about 1 year'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 August 2016 in Esperanto?\n * const eoLocale = require('date-fns/locale/eo')\n * const result = formatDistanceToNow(\n *   new Date(2016, 7, 1),\n *   {locale: eoLocale}\n * )\n * //=> 'pli ol 1 jaro'\n */\nexport function formatDistanceToNow(date, options) {\n  return formatDistance(date, constructNow(date), options);\n}\n\n// Fallback for modularized imports:\nexport default formatDistanceToNow;\n", "import { constructNow } from \"./constructNow.js\";\n\nimport { formatDistanceStrict } from \"./formatDistanceStrict.js\";\n\n/**\n * The {@link formatDistanceToNowStrict} function options.\n */\n\n/**\n * @name formatDistanceToNowStrict\n * @category Common Helpers\n * @summary Return the distance between the given date and now in words.\n * @pure false\n *\n * @description\n * Return the distance between the given dates in words, using strict units.\n * This is like `formatDistance`, but does not use helpers like 'almost', 'over',\n * 'less than' and the like.\n *\n * | Distance between dates | Result              |\n * |------------------------|---------------------|\n * | 0 ... 59 secs          | [0..59] seconds     |\n * | 1 ... 59 mins          | [1..59] minutes     |\n * | 1 ... 23 hrs           | [1..23] hours       |\n * | 1 ... 29 days          | [1..29] days        |\n * | 1 ... 11 months        | [1..11] months      |\n * | 1 ... N years          | [1..N]  years       |\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // If today is 1 January 2015, what is the distance to 2 July 2014?\n * const result = formatDistanceToNowStrict(\n *   new Date(2014, 6, 2)\n * )\n * //=> '6 months'\n *\n * @example\n * // If now is 1 January 2015 00:00:00,\n * // what is the distance to 1 January 2015 00:00:15, including seconds?\n * const result = formatDistanceToNowStrict(\n *   new Date(2015, 0, 1, 0, 0, 15)\n * )\n * //=> '15 seconds'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016, with a suffix?\n * const result = formatDistanceToNowStrict(\n *   new Date(2016, 0, 1),\n *   {addSuffix: true}\n * )\n * //=> 'in 1 year'\n *\n * @example\n * // If today is 28 January 2015,\n * // what is the distance to 1 January 2015, in months, rounded up??\n * const result = formatDistanceToNowStrict(new Date(2015, 0, 1), {\n *   unit: 'month',\n *   roundingMethod: 'ceil'\n * })\n * //=> '1 month'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016 in Esperanto?\n * const eoLocale = require('date-fns/locale/eo')\n * const result = formatDistanceToNowStrict(\n *   new Date(2016, 0, 1),\n *   {locale: eoLocale}\n * )\n * //=> '1 jaro'\n */\nexport function formatDistanceToNowStrict(date, options) {\n  return formatDistanceStrict(date, constructNow(date), options);\n}\n\n// Fallback for modularized imports:\nexport default formatDistanceToNowStrict;\n", "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\n\n/**\n * The {@link formatDuration} function options.\n */\n\nconst defaultFormat = [\n  \"years\",\n  \"months\",\n  \"weeks\",\n  \"days\",\n  \"hours\",\n  \"minutes\",\n  \"seconds\",\n];\n\n/**\n * @name formatDuration\n * @category Common Helpers\n * @summary Formats a duration in human-readable format\n *\n * @description\n * Return human-readable duration string i.e. \"9 months 2 days\"\n *\n * @param duration - The duration to format\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @example\n * // Format full duration\n * formatDuration({\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> '2 years 9 months 1 week 7 days 5 hours 9 minutes 30 seconds'\n *\n * @example\n * // Format partial duration\n * formatDuration({ months: 9, days: 2 })\n * //=> '9 months 2 days'\n *\n * @example\n * // Customize the format\n * formatDuration(\n *   {\n *     years: 2,\n *     months: 9,\n *     weeks: 1,\n *     days: 7,\n *     hours: 5,\n *     minutes: 9,\n *     seconds: 30\n *   },\n *   { format: ['months', 'weeks'] }\n * ) === '9 months 1 week'\n *\n * @example\n * // Customize the zeros presence\n * formatDuration({ years: 0, months: 9 })\n * //=> '9 months'\n * formatDuration({ years: 0, months: 9 }, { zero: true })\n * //=> '0 years 9 months'\n *\n * @example\n * // Customize the delimiter\n * formatDuration({ years: 2, months: 9, weeks: 3 }, { delimiter: ', ' })\n * //=> '2 years, 9 months, 3 weeks'\n */\nexport function formatDuration(duration, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const format = options?.format ?? defaultFormat;\n  const zero = options?.zero ?? false;\n  const delimiter = options?.delimiter ?? \" \";\n\n  if (!locale.formatDistance) {\n    return \"\";\n  }\n\n  const result = format\n    .reduce((acc, unit) => {\n      const token = `x${unit.replace(/(^.)/, (m) => m.toUpperCase())}`;\n      const value = duration[unit];\n      if (value !== undefined && (zero || duration[unit])) {\n        return acc.concat(locale.formatDistance(token, value));\n      }\n      return acc;\n    }, [])\n    .join(delimiter);\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatDuration;\n", "import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatISO} function options.\n */\n\n/**\n * @name formatISO\n * @category Common Helpers\n * @summary Format the date according to the ISO 8601 standard (https://support.sas.com/documentation/cdl/en/lrdict/64316/HTML/default/viewer.htm#a003169814.htm).\n *\n * @description\n * Return the formatted date string in ISO 8601 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string (in local time zone)\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601, short format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918T190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, date only:\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, time only (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52Z'\n */\nexport function formatISO(date, options) {\n  const date_ = toDate(date, options?.in);\n\n  if (isNaN(+date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const format = options?.format ?? \"extended\";\n  const representation = options?.representation ?? \"complete\";\n\n  let result = \"\";\n  let tzOffset = \"\";\n\n  const dateDelimiter = format === \"extended\" ? \"-\" : \"\";\n  const timeDelimiter = format === \"extended\" ? \":\" : \"\";\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== \"time\") {\n    const day = addLeadingZeros(date_.getDate(), 2);\n    const month = addLeadingZeros(date_.getMonth() + 1, 2);\n    const year = addLeadingZeros(date_.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = `${year}${dateDelimiter}${month}${dateDelimiter}${day}`;\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== \"date\") {\n    // Add the timezone.\n    const offset = date_.getTimezoneOffset();\n\n    if (offset !== 0) {\n      const absoluteOffset = Math.abs(offset);\n      const hourOffset = addLeadingZeros(Math.trunc(absoluteOffset / 60), 2);\n      const minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n      // If less than 0, the sign is +, because it is ahead of time.\n      const sign = offset < 0 ? \"+\" : \"-\";\n\n      tzOffset = `${sign}${hourOffset}:${minuteOffset}`;\n    } else {\n      tzOffset = \"Z\";\n    }\n\n    const hour = addLeadingZeros(date_.getHours(), 2);\n    const minute = addLeadingZeros(date_.getMinutes(), 2);\n    const second = addLeadingZeros(date_.getSeconds(), 2);\n\n    // If there's also date, separate it with time with 'T'\n    const separator = result === \"\" ? \"\" : \"T\";\n\n    // Creates a time string consisting of hour, minute, and second, separated by delimiters, if defined.\n    const time = [hour, minute, second].join(timeDelimiter);\n\n    // HHmmss or HH:mm:ss.\n    result = `${result}${separator}${time}${tzOffset}`;\n  }\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatISO;\n", "import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatISO9075} function options.\n */\n\n/**\n * @name formatISO9075\n * @category Common Helpers\n * @summary Format the date according to the ISO 9075 standard (https://dev.mysql.com/doc/refman/5.7/en/date-and-time-functions.html#function_get-format).\n *\n * @description\n * Return the formatted date string in ISO 9075 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18 19:00:52'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075, short format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918 190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, date only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, time only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52'\n */\nexport function formatISO9075(date, options) {\n  const date_ = toDate(date, options?.in);\n\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const format = options?.format ?? \"extended\";\n  const representation = options?.representation ?? \"complete\";\n\n  let result = \"\";\n\n  const dateDelimiter = format === \"extended\" ? \"-\" : \"\";\n  const timeDelimiter = format === \"extended\" ? \":\" : \"\";\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== \"time\") {\n    const day = addLeadingZeros(date_.getDate(), 2);\n    const month = addLeadingZeros(date_.getMonth() + 1, 2);\n    const year = addLeadingZeros(date_.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = `${year}${dateDelimiter}${month}${dateDelimiter}${day}`;\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== \"date\") {\n    const hour = addLeadingZeros(date_.getHours(), 2);\n    const minute = addLeadingZeros(date_.getMinutes(), 2);\n    const second = addLeadingZeros(date_.getSeconds(), 2);\n\n    // If there's also date, separate it with time with a space\n    const separator = result === \"\" ? \"\" : \" \";\n\n    // HHmmss or HH:mm:ss.\n    result = `${result}${separator}${hour}${timeDelimiter}${minute}${timeDelimiter}${second}`;\n  }\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatISO9075;\n", "/**\n * @name formatISODuration\n * @category Common Helpers\n * @summary Format a duration object according as ISO 8601 duration string\n *\n * @description\n * Format a duration object according to the ISO 8601 duration standard (https://www.digi.com/resources/documentation/digidocs//90001488-13/reference/r_iso_8601_duration_format.htm)\n *\n * @param duration - The duration to format\n *\n * @returns The ISO 8601 duration string\n *\n * @example\n * // Format the given duration as ISO 8601 string\n * const result = formatISODuration({\n *   years: 39,\n *   months: 2,\n *   days: 20,\n *   hours: 7,\n *   minutes: 5,\n *   seconds: 0\n * })\n * //=> 'P39Y2M20DT0H0M0S'\n */\nexport function formatISODuration(duration) {\n  const {\n    years = 0,\n    months = 0,\n    days = 0,\n    hours = 0,\n    minutes = 0,\n    seconds = 0,\n  } = duration;\n\n  return `P${years}Y${months}M${days}DT${hours}H${minutes}M${seconds}S`;\n}\n\n// Fallback for modularized imports:\nexport default formatISODuration;\n", "import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatRFC3339} function options.\n */\n\n/**\n * @name formatRFC3339\n * @category Common Helpers\n * @summary Format the date according to the RFC 3339 standard (https://tools.ietf.org/html/rfc3339#section-5.6).\n *\n * @description\n * Return the formatted date string in RFC 3339 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format:\n * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 3 digits of second fraction\n * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), {\n *   fractionDigits: 3\n * })\n * //=> '2019-09-18T19:00:52.234Z'\n */\nexport function formatRFC3339(date, options) {\n  const date_ = toDate(date, options?.in);\n\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const fractionDigits = options?.fractionDigits ?? 0;\n\n  const day = addLeadingZeros(date_.getDate(), 2);\n  const month = addLeadingZeros(date_.getMonth() + 1, 2);\n  const year = date_.getFullYear();\n\n  const hour = addLeadingZeros(date_.getHours(), 2);\n  const minute = addLeadingZeros(date_.getMinutes(), 2);\n  const second = addLeadingZeros(date_.getSeconds(), 2);\n\n  let fractionalSecond = \"\";\n  if (fractionDigits > 0) {\n    const milliseconds = date_.getMilliseconds();\n    const fractionalSeconds = Math.trunc(\n      milliseconds * Math.pow(10, fractionDigits - 3),\n    );\n    fractionalSecond = \".\" + addLeadingZeros(fractionalSeconds, fractionDigits);\n  }\n\n  let offset = \"\";\n  const tzOffset = date_.getTimezoneOffset();\n\n  if (tzOffset !== 0) {\n    const absoluteOffset = Math.abs(tzOffset);\n    const hourOffset = addLeadingZeros(Math.trunc(absoluteOffset / 60), 2);\n    const minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n    // If less than 0, the sign is +, because it is ahead of time.\n    const sign = tzOffset < 0 ? \"+\" : \"-\";\n\n    offset = `${sign}${hourOffset}:${minuteOffset}`;\n  } else {\n    offset = \"Z\";\n  }\n\n  return `${year}-${month}-${day}T${hour}:${minute}:${second}${fractionalSecond}${offset}`;\n}\n\n// Fallback for modularized imports:\nexport default formatRFC3339;\n", "import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\nconst days = [\"<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Sat\"];\n\nconst months = [\n  \"Jan\",\n  \"Feb\",\n  \"Mar\",\n  \"Apr\",\n  \"May\",\n  \"Jun\",\n  \"Jul\",\n  \"Aug\",\n  \"Sep\",\n  \"Oct\",\n  \"Nov\",\n  \"Dec\",\n];\n\n/**\n * @name formatRFC7231\n * @category Common Helpers\n * @summary Format the date according to the RFC 7231 standard (https://tools.ietf.org/html/rfc7231#section-*******).\n *\n * @description\n * Return the formatted date string in RFC 7231 format.\n * The result will always be in UTC timezone.\n *\n * @param date - The original date\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 7231 format:\n * const result = formatRFC7231(new Date(2019, 8, 18, 19, 0, 52))\n * //=> 'Wed, 18 Sep 2019 19:00:52 GMT'\n */\nexport function formatRFC7231(date) {\n  const _date = toDate(date);\n\n  if (!isValid(_date)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const dayName = days[_date.getUTCDay()];\n  const dayOfMonth = addLeadingZeros(_date.getUTCDate(), 2);\n  const monthName = months[_date.getUTCMonth()];\n  const year = _date.getUTCFullYear();\n\n  const hour = addLeadingZeros(_date.getUTCHours(), 2);\n  const minute = addLeadingZeros(_date.getUTCMinutes(), 2);\n  const second = addLeadingZeros(_date.getUTCSeconds(), 2);\n\n  // Result variables.\n  return `${dayName}, ${dayOfMonth} ${monthName} ${year} ${hour}:${minute}:${second} GMT`;\n}\n\n// Fallback for modularized imports:\nexport default formatRFC7231;\n", "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { format } from \"./format.js\";\n\n/**\n * The {@link formatRelative} function options.\n */\n\n/**\n * @name formatRelative\n * @category Common Helpers\n * @summary Represent the date in words relative to the given base date.\n *\n * @description\n * Represent the date in words relative to the given base date.\n *\n * | Distance to the base date | Result                    |\n * |---------------------------|---------------------------|\n * | Previous 6 days           | last Sunday at 04:30 AM   |\n * | Last day                  | yesterday at 04:30 AM     |\n * | Same day                  | today at 04:30 AM         |\n * | Next day                  | tomorrow at 04:30 AM      |\n * | Next 6 days               | Sunday at 04:30 AM        |\n * | Other                     | 12/31/2017                |\n *\n * @param date - The date to format\n * @param baseDate - The date to compare with\n * @param options - An object with options\n *\n * @returns The date in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.locale` must contain `localize` property\n * @throws `options.locale` must contain `formatLong` property\n * @throws `options.locale` must contain `formatRelative` property\n *\n * @example\n * // Represent the date of 6 days ago in words relative to the given base date. In this example, today is Wednesday\n * const result = formatRelative(subDays(new Date(), 6), new Date())\n * //=> \"last Thursday at 12:45 AM\"\n */\nexport function formatRelative(date, baseDate, options) {\n  const [date_, baseDate_] = normalizeDates(options?.in, date, baseDate);\n\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const diff = differenceInCalendarDays(date_, baseDate_);\n\n  if (isNaN(diff)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  let token;\n  if (diff < -6) {\n    token = \"other\";\n  } else if (diff < -1) {\n    token = \"lastWeek\";\n  } else if (diff < 0) {\n    token = \"yesterday\";\n  } else if (diff < 1) {\n    token = \"today\";\n  } else if (diff < 2) {\n    token = \"tomorrow\";\n  } else if (diff < 7) {\n    token = \"nextWeek\";\n  } else {\n    token = \"other\";\n  }\n\n  const formatStr = locale.formatRelative(token, date_, baseDate_, {\n    locale,\n    weekStartsOn,\n  });\n  return format(date_, formatStr, { locale, weekStartsOn });\n}\n\n// Fallback for modularized imports:\nexport default formatRelative;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link fromUnixTime} function options.\n */\n\n/**\n * @name fromUnixTime\n * @category Timestamp Helpers\n * @summary Create a date from a Unix timestamp.\n *\n * @description\n * Create a date from a Unix timestamp (in seconds). Decimal values will be discarded.\n *\n * @param unixTime - The given Unix timestamp (in seconds)\n * @param options - An object with options. Allows to pass a context.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @returns The date\n *\n * @example\n * // Create the date 29 February 2012 11:45:05:\n * const result = fromUnixTime(1330515905)\n * //=> Wed Feb 29 2012 11:45:05\n */\nexport function fromUnixTime(unixTime, options) {\n  return toDate(unixTime * 1000, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default fromUnixTime;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDay} function options.\n */\n\n/**\n * @name getDay\n * @category Weekday Helpers\n * @summary Get the day of the week of the given date.\n *\n * @description\n * Get the day of the week of the given date.\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The day of week, 0 represents Sunday\n *\n * @example\n * // Which day of the week is 29 February 2012?\n * const result = getDay(new Date(2012, 1, 29))\n * //=> 3\n */\nexport function getDay(date, options) {\n  return toDate(date, options?.in).getDay();\n}\n\n// Fallback for modularized imports:\nexport default getDay;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isLeapYear\n * @category Year Helpers\n * @summary Is the given date in the leap year?\n *\n * @description\n * Is the given date in the leap year?\n *\n * @param date - The date to check\n * @param options - The options object\n *\n * @returns The date is in the leap year\n *\n * @example\n * // Is 1 September 2012 in the leap year?\n * const result = isLeapYear(new Date(2012, 8, 1))\n * //=> true\n */\nexport function isLeapYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\n\n// Fallback for modularized imports:\nexport default isLeapYear;\n", "import { isLeapYear } from \"./isLeapYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDaysInYear} function options.\n */\n\n/**\n * @name getDaysInYear\n * @category Year Helpers\n * @summary Get the number of days in a year of the given date.\n *\n * @description\n * Get the number of days in a year of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of days in a year\n *\n * @example\n * // How many days are in 2012?\n * const result = getDaysInYear(new Date(2012, 0, 1))\n * //=> 366\n */\nexport function getDaysInYear(date, options) {\n  const _date = toDate(date, options?.in);\n  if (Number.isNaN(+_date)) return NaN;\n  return isLeapYear(_date) ? 366 : 365;\n}\n\n// Fallback for modularized imports:\nexport default getDaysInYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDecade} function options.\n */\n\n/**\n * @name getDecade\n * @category Decade Helpers\n * @summary Get the decade of the given date.\n *\n * @description\n * Get the decade of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The year of decade\n *\n * @example\n * // Which decade belongs 27 November 1942?\n * const result = getDecade(new Date(1942, 10, 27))\n * //=> 1940\n */\nexport function getDecade(date, options) {\n  // TODO: Switch to more technical definition in of decades that start with 1\n  // end with 0. I.e. 2001-2010 instead of current 2000-2009. It's a breaking\n  // change, so it can only be done in 4.0.\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = Math.floor(year / 10) * 10;\n  return decade;\n}\n\n// Fallback for modularized imports:\nexport default getDecade;\n", "import { addWeeks } from \"./addWeeks.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\n\n/**\n * The {@link getISOWeeksInYear} function options.\n */\n\n/**\n * @name getISOWeeksInYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * @description\n * Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of ISO weeks in a year\n *\n * @example\n * // How many weeks are in ISO week-numbering year 2015?\n * const result = getISOWeeksInYear(new Date(2015, 1, 11))\n * //=> 53\n */\nexport function getISOWeeksInYear(date, options) {\n  const thisYear = startOfISOWeekYear(date, options);\n  const nextYear = startOfISOWeekYear(addWeeks(thisYear, 60));\n  const diff = +nextYear - +thisYear;\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default getISOWeeksInYear;\n", "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { millisecondsInDay } from \"./constants.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * @name getOverlappingDaysInIntervals\n * @category Interval Helpers\n * @summary Get the number of days that overlap in two time intervals\n *\n * @description\n * Get the number of days that overlap in two time intervals. It uses the time\n * between dates to calculate the number of days, rounding it up to include\n * partial days.\n *\n * Two equal 0-length intervals will result in 0. Two equal 1ms intervals will\n * result in 1.\n *\n * @param intervalLeft - The first interval to compare.\n * @param intervalRight - The second interval to compare.\n * @param options - An object with options\n *\n * @returns The number of days that overlap in two time intervals\n *\n * @example\n * // For overlapping time intervals adds 1 for each started overlapping day:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> 3\n *\n * @example\n * // For non-overlapping time intervals returns 0:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> 0\n */\n\nexport function getOverlappingDaysInIntervals(intervalLeft, intervalRight) {\n  const [leftStart, leftEnd] = [\n    +toDate(intervalLeft.start),\n    +toDate(intervalLeft.end),\n  ].sort((a, b) => a - b);\n  const [rightStart, rightEnd] = [\n    +toDate(intervalRight.start),\n    +toDate(intervalRight.end),\n  ].sort((a, b) => a - b);\n\n  // Prevent NaN result if intervals don't overlap at all.\n  const isOverlapping = leftStart < rightEnd && rightStart < leftEnd;\n  if (!isOverlapping) return 0;\n\n  // Remove the timezone offset to negate the DST effect on calculations.\n  const overlapLeft = rightStart < leftStart ? leftStart : rightStart;\n  const left = overlapLeft - getTimezoneOffsetInMilliseconds(overlapLeft);\n  const overlapRight = rightEnd > leftEnd ? leftEnd : rightEnd;\n  const right = overlapRight - getTimezoneOffsetInMilliseconds(overlapRight);\n\n  // Ceil the number to include partial days too.\n  return Math.ceil((right - left) / millisecondsInDay);\n}\n\n// Fallback for modularized imports:\nexport default getOverlappingDaysInIntervals;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name getTime\n * @category Timestamp Helpers\n * @summary Get the milliseconds timestamp of the given date.\n *\n * @description\n * Get the milliseconds timestamp of the given date.\n *\n * @param date - The given date\n *\n * @returns The timestamp\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05.123:\n * const result = getTime(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 1330515905123\n */\nexport function getTime(date) {\n  return +toDate(date);\n}\n\n// Fallback for modularized imports:\nexport default getTime;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name getUnixTime\n * @category Timestamp Helpers\n * @summary Get the seconds timestamp of the given date.\n *\n * @description\n * Get the seconds timestamp of the given date.\n *\n * @param date - The given date\n *\n * @returns The timestamp\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05 CET:\n * const result = getUnixTime(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 1330512305\n */\nexport function getUnixTime(date) {\n  return Math.trunc(+toDate(date) / 1000);\n}\n\n// Fallback for modularized imports:\nexport default getUnixTime;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { getDate } from \"./getDate.js\";\nimport { getDay } from \"./getDay.js\";\nimport { startOfMonth } from \"./startOfMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeekOfMonth} function options.\n */\n\n/**\n * @name getWeekOfMonth\n * @category Week Helpers\n * @summary Get the week of the month of the given date.\n *\n * @description\n * Get the week of the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The week of month\n *\n * @example\n * // Which week of the month is 9 November 2017?\n * const result = getWeekOfMonth(new Date(2017, 10, 9))\n * //=> 2\n */\nexport function getWeekOfMonth(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const currentDayOfMonth = getDate(toDate(date, options?.in));\n  if (isNaN(currentDayOfMonth)) return NaN;\n\n  const startWeekDay = getDay(startOfMonth(date, options));\n\n  let lastDayOfFirstWeek = weekStartsOn - startWeekDay;\n  if (lastDayOfFirstWeek <= 0) lastDayOfFirstWeek += 7;\n\n  const remainingDaysAfterFirstWeek = currentDayOfMonth - lastDayOfFirstWeek;\n  return Math.ceil(remainingDaysAfterFirstWeek / 7) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getWeekOfMonth;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfMonth} function options.\n */\n\n/**\n * @name lastDayOfMonth\n * @category Month Helpers\n * @summary Return the last day of a month for the given date.\n *\n * @description\n * Return the last day of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The last day of a month\n *\n * @example\n * // The last day of a month for 2 September 2014 11:55:00:\n * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(0, 0, 0, 0);\n  return toDate(_date, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfMonth;\n", "import { differenceInCalendarWeeks } from \"./differenceInCalendarWeeks.js\";\nimport { lastDayOfMonth } from \"./lastDayOfMonth.js\";\nimport { startOfMonth } from \"./startOfMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeeksInMonth} function options.\n */\n\n/**\n * @name getWeeksInMonth\n * @category Week Helpers\n * @summary Get the number of calendar weeks a month spans.\n *\n * @description\n * Get the number of calendar weeks the month in the given date spans.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The number of calendar weeks\n *\n * @example\n * // How many calendar weeks does February 2015 span?\n * const result = getWeeksInMonth(new Date(2015, 1, 8))\n * //=> 4\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks does July 2017 span?\n * const result = getWeeksInMonth(new Date(2017, 6, 5), { weekStartsOn: 1 })\n * //=> 6\n */\nexport function getWeeksInMonth(date, options) {\n  const contextDate = toDate(date, options?.in);\n  return (\n    differenceInCalendarWeeks(\n      lastDayOfMonth(contextDate, options),\n      startOfMonth(contextDate, options),\n      options,\n    ) + 1\n  );\n}\n\n// Fallback for modularized imports:\nexport default getWeeksInMonth;\n", "import { millisecondsInHour } from \"./constants.js\";\n\n/**\n * @name hoursToMilliseconds\n * @category  Conversion Helpers\n * @summary Convert hours to milliseconds.\n *\n * @description\n * Convert a number of hours to a full number of milliseconds.\n *\n * @param hours - number of hours to be converted\n *\n * @returns The number of hours converted to milliseconds\n *\n * @example\n * // Convert 2 hours to milliseconds:\n * const result = hoursToMilliseconds(2)\n * //=> 7200000\n */\nexport function hoursToMilliseconds(hours) {\n  return Math.trunc(hours * millisecondsInHour);\n}\n\n// Fallback for modularized imports:\nexport default hoursToMilliseconds;\n", "import { minutesInHour } from \"./constants.js\";\n\n/**\n * @name hoursToMinutes\n * @category Conversion Helpers\n * @summary Convert hours to minutes.\n *\n * @description\n * Convert a number of hours to a full number of minutes.\n *\n * @param hours - number of hours to be converted\n *\n * @returns The number of hours converted in minutes\n *\n * @example\n * // Convert 2 hours to minutes:\n * const result = hoursToMinutes(2)\n * //=> 120\n */\nexport function hoursToMinutes(hours) {\n  return Math.trunc(hours * minutesInHour);\n}\n\n// Fallback for modularized imports:\nexport default hoursToMinutes;\n", "import { secondsInHour } from \"./constants.js\";\n\n/**\n * @name hoursToSeconds\n * @category Conversion Helpers\n * @summary Convert hours to seconds.\n *\n * @description\n * Convert a number of hours to a full number of seconds.\n *\n * @param hours - The number of hours to be converted\n *\n * @returns The number of hours converted in seconds\n *\n * @example\n * // Convert 2 hours to seconds:\n * const result = hoursToSeconds(2)\n * //=> 7200\n */\nexport function hoursToSeconds(hours) {\n  return Math.trunc(hours * secondsInHour);\n}\n\n// Fallback for modularized imports:\nexport default hoursToSeconds;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link interval} function options.\n */\n\n/**\n * The {@link interval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the start argument,\n * then the end interval date. If a context function is passed, it uses the context\n * function return type.\n */\n\n/**\n * @name interval\n * @category Interval Helpers\n * @summary Creates an interval object and validates its values.\n *\n * @description\n * Creates a normalized interval object and validates its values. If the interval is invalid, an exception is thrown.\n *\n * @typeParam StartDate - Start date type.\n * @typeParam EndDate - End date type.\n * @typeParam Options - Options type.\n *\n * @param start - The start of the interval.\n * @param end - The end of the interval.\n * @param options - The options object.\n *\n * @throws `Start date is invalid` when `start` is invalid.\n * @throws `End date is invalid` when `end` is invalid.\n * @throws `End date must be after start date` when end is before `start` and `options.assertPositive` is true.\n *\n * @returns The normalized and validated interval object.\n */\nexport function interval(start, end, options) {\n  const [_start, _end] = normalizeDates(options?.in, start, end);\n\n  if (isNaN(+_start)) throw new TypeError(\"Start date is invalid\");\n  if (isNaN(+_end)) throw new TypeError(\"End date is invalid\");\n\n  if (options?.assertPositive && +_start > +_end)\n    throw new TypeError(\"End date must be after start date\");\n\n  return { start: _start, end: _end };\n}\n\n// Fallback for modularized imports:\nexport default interval;\n", "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { add } from \"./add.js\";\nimport { differenceInDays } from \"./differenceInDays.js\";\nimport { differenceInHours } from \"./differenceInHours.js\";\nimport { differenceInMinutes } from \"./differenceInMinutes.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\nimport { differenceInYears } from \"./differenceInYears.js\";\n\n/**\n * The {@link intervalToDuration} function options.\n */\n\n/**\n * @name intervalToDuration\n * @category Common Helpers\n * @summary Convert interval to duration\n *\n * @description\n * Convert an interval object to a duration object.\n *\n * @param interval - The interval to convert to duration\n * @param options - The context options\n *\n * @returns The duration object\n *\n * @example\n * // Get the duration between January 15, 1929 and April 4, 1968.\n * intervalToDuration({\n *   start: new Date(1929, 0, 15, 12, 0, 0),\n *   end: new Date(1968, 3, 4, 19, 5, 0)\n * });\n * //=> { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }\n */\nexport function intervalToDuration(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n  const duration = {};\n\n  const years = differenceInYears(end, start);\n  if (years) duration.years = years;\n\n  const remainingMonths = add(start, { years: duration.years });\n  const months = differenceInMonths(end, remainingMonths);\n  if (months) duration.months = months;\n\n  const remainingDays = add(remainingMonths, { months: duration.months });\n  const days = differenceInDays(end, remainingDays);\n  if (days) duration.days = days;\n\n  const remainingHours = add(remainingDays, { days: duration.days });\n  const hours = differenceInHours(end, remainingHours);\n  if (hours) duration.hours = hours;\n\n  const remainingMinutes = add(remainingHours, { hours: duration.hours });\n  const minutes = differenceInMinutes(end, remainingMinutes);\n  if (minutes) duration.minutes = minutes;\n\n  const remainingSeconds = add(remainingMinutes, { minutes: duration.minutes });\n  const seconds = differenceInSeconds(end, remainingSeconds);\n  if (seconds) duration.seconds = seconds;\n\n  return duration;\n}\n\n// Fallback for modularized imports:\nexport default intervalToDuration;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The locale string (see: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locales_argument).\n * @deprecated\n *\n * [TODO] Remove in v4\n */\n\n/**\n * The format options (see: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#options)\n */\n\n/**\n * The locale options.\n */\n\n/**\n * @name intlFormat\n * @category Common Helpers\n * @summary Format the date with Intl.DateTimeFormat (https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat).\n *\n * @description\n * Return the formatted date string in the given format.\n * The method uses [`Intl.DateTimeFormat`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat) inside.\n * formatOptions are the same as [`Intl.DateTimeFormat` options](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat#using_options)\n *\n * > ⚠️ Please note that before Node version 13.0.0, only the locale data for en-US is available by default.\n *\n * @param date - The date to format\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019 in middle-endian format:\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456))\n * //=> 10/4/2019\n */\n\n/**\n * @param date - The date to format\n * @param localeOptions - An object with locale\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019 in Korean.\n * // Convert the date with locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *   locale: 'ko-KR',\n * })\n * //=> 2019. 10. 4.\n */\n\n/**\n * @param date - The date to format\n * @param formatOptions - The format options\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019.\n * // Convert the date with format's options.\n * const result = intlFormat.default(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *   year: 'numeric',\n *   month: 'numeric',\n *   day: 'numeric',\n *   hour: 'numeric',\n * })\n * //=> 10/4/2019, 12 PM\n */\n\n/**\n * @param date - The date to format\n * @param formatOptions - The format options\n * @param localeOptions - An object with locale\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019 in German.\n * // Convert the date with format's options and locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *   weekday: 'long',\n *   year: 'numeric',\n *   month: 'long',\n *   day: 'numeric',\n * }, {\n *   locale: 'de-DE',\n * })\n * //=> Freitag, 4. Oktober 2019\n */\n\nexport function intlFormat(date, formatOrLocale, localeOptions) {\n  let formatOptions;\n\n  if (isFormatOptions(formatOrLocale)) {\n    formatOptions = formatOrLocale;\n  } else {\n    localeOptions = formatOrLocale;\n  }\n\n  return new Intl.DateTimeFormat(localeOptions?.locale, formatOptions).format(\n    toDate(date),\n  );\n}\n\nfunction isFormatOptions(opts) {\n  return opts !== undefined && !(\"locale\" in opts);\n}\n\n// Fallback for modularized imports:\nexport default intlFormat;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport {\n  secondsInDay,\n  secondsInHour,\n  secondsInMinute,\n  secondsInMonth,\n  secondsInQuarter,\n  secondsInWeek,\n  secondsInYear,\n} from \"./constants.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { differenceInCalendarMonths } from \"./differenceInCalendarMonths.js\";\nimport { differenceInCalendarQuarters } from \"./differenceInCalendarQuarters.js\";\nimport { differenceInCalendarWeeks } from \"./differenceInCalendarWeeks.js\";\nimport { differenceInCalendarYears } from \"./differenceInCalendarYears.js\";\nimport { differenceInHours } from \"./differenceInHours.js\";\nimport { differenceInMinutes } from \"./differenceInMinutes.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\n\n/**\n * The {@link intlFormatDistance} function options.\n */\n\n/**\n * The unit used to format the distance in {@link intlFormatDistance}.\n */\n\n/**\n * @name intlFormatDistance\n * @category Common Helpers\n * @summary Formats distance between two dates in a human-readable format\n * @description\n * The function calculates the difference between two dates and formats it as a human-readable string.\n *\n * The function will pick the most appropriate unit depending on the distance between dates. For example, if the distance is a few hours, it might return `x hours`. If the distance is a few months, it might return `x months`.\n *\n * You can also specify a unit to force using it regardless of the distance to get a result like `123456 hours`.\n *\n * See the table below for the unit picking logic:\n *\n * | Distance between dates | Result (past)  | Result (future) |\n * | ---------------------- | -------------- | --------------- |\n * | 0 seconds              | now            | now             |\n * | 1-59 seconds           | X seconds ago  | in X seconds    |\n * | 1-59 minutes           | X minutes ago  | in X minutes    |\n * | 1-23 hours             | X hours ago    | in X hours      |\n * | 1 day                  | yesterday      | tomorrow        |\n * | 2-6 days               | X days ago     | in X days       |\n * | 7 days                 | last week      | next week       |\n * | 8 days-1 month         | X weeks ago    | in X weeks      |\n * | 1 month                | last month     | next month      |\n * | 2-3 months             | X months ago   | in X months     |\n * | 1 quarter              | last quarter   | next quarter    |\n * | 2-3 quarters           | X quarters ago | in X quarters   |\n * | 1 year                 | last year      | next year       |\n * | 2+ years               | X years ago    | in X years      |\n *\n * @param laterDate - The date\n * @param earlierDate - The date to compare with.\n * @param options - An object with options.\n * See MDN for details [Locale identification and negotiation](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locale_identification_and_negotiation)\n * The narrow one could be similar to the short one for some locales.\n *\n * @returns The distance in words according to language-sensitive relative time formatting.\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.unit` must not be invalid Unit\n * @throws `options.locale` must not be invalid locale\n * @throws `options.localeMatcher` must not be invalid localeMatcher\n * @throws `options.numeric` must not be invalid numeric\n * @throws `options.style` must not be invalid style\n *\n * @example\n * // What is the distance between the dates when the fist date is after the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0)\n * )\n * //=> 'in 1 hour'\n *\n * // What is the distance between the dates when the fist date is before the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0)\n * )\n * //=> '1 hour ago'\n *\n * @example\n * // Use the unit option to force the function to output the result in quarters. Without setting it, the example would return \"next year\"\n * intlFormatDistance(\n *   new Date(1987, 6, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { unit: 'quarter' }\n * )\n * //=> 'in 5 quarters'\n *\n * @example\n * // Use the locale option to get the result in Spanish. Without setting it, the example would return \"in 1 hour\".\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { locale: 'es' }\n * )\n * //=> 'dentro de 1 hora'\n *\n * @example\n * // Use the numeric option to force the function to use numeric values. Without setting it, the example would return \"tomorrow\".\n * intlFormatDistance(\n *   new Date(1986, 3, 5, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { numeric: 'always' }\n * )\n * //=> 'in 1 day'\n *\n * @example\n * // Use the style option to force the function to use short values. Without setting it, the example would return \"in 2 years\".\n * intlFormatDistance(\n *   new Date(1988, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { style: 'short' }\n * )\n * //=> 'in 2 yr'\n */\nexport function intlFormatDistance(laterDate, earlierDate, options) {\n  let value = 0;\n  let unit;\n\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  if (!options?.unit) {\n    // Get the unit based on diffInSeconds calculations if no unit is specified\n    const diffInSeconds = differenceInSeconds(laterDate_, earlierDate_); // The smallest unit\n\n    if (Math.abs(diffInSeconds) < secondsInMinute) {\n      value = differenceInSeconds(laterDate_, earlierDate_);\n      unit = \"second\";\n    } else if (Math.abs(diffInSeconds) < secondsInHour) {\n      value = differenceInMinutes(laterDate_, earlierDate_);\n      unit = \"minute\";\n    } else if (\n      Math.abs(diffInSeconds) < secondsInDay &&\n      Math.abs(differenceInCalendarDays(laterDate_, earlierDate_)) < 1\n    ) {\n      value = differenceInHours(laterDate_, earlierDate_);\n      unit = \"hour\";\n    } else if (\n      Math.abs(diffInSeconds) < secondsInWeek &&\n      (value = differenceInCalendarDays(laterDate_, earlierDate_)) &&\n      Math.abs(value) < 7\n    ) {\n      unit = \"day\";\n    } else if (Math.abs(diffInSeconds) < secondsInMonth) {\n      value = differenceInCalendarWeeks(laterDate_, earlierDate_);\n      unit = \"week\";\n    } else if (Math.abs(diffInSeconds) < secondsInQuarter) {\n      value = differenceInCalendarMonths(laterDate_, earlierDate_);\n      unit = \"month\";\n    } else if (Math.abs(diffInSeconds) < secondsInYear) {\n      if (differenceInCalendarQuarters(laterDate_, earlierDate_) < 4) {\n        // To filter out cases that are less than a year but match 4 quarters\n        value = differenceInCalendarQuarters(laterDate_, earlierDate_);\n        unit = \"quarter\";\n      } else {\n        value = differenceInCalendarYears(laterDate_, earlierDate_);\n        unit = \"year\";\n      }\n    } else {\n      value = differenceInCalendarYears(laterDate_, earlierDate_);\n      unit = \"year\";\n    }\n  } else {\n    // Get the value if unit is specified\n    unit = options?.unit;\n    if (unit === \"second\") {\n      value = differenceInSeconds(laterDate_, earlierDate_);\n    } else if (unit === \"minute\") {\n      value = differenceInMinutes(laterDate_, earlierDate_);\n    } else if (unit === \"hour\") {\n      value = differenceInHours(laterDate_, earlierDate_);\n    } else if (unit === \"day\") {\n      value = differenceInCalendarDays(laterDate_, earlierDate_);\n    } else if (unit === \"week\") {\n      value = differenceInCalendarWeeks(laterDate_, earlierDate_);\n    } else if (unit === \"month\") {\n      value = differenceInCalendarMonths(laterDate_, earlierDate_);\n    } else if (unit === \"quarter\") {\n      value = differenceInCalendarQuarters(laterDate_, earlierDate_);\n    } else if (unit === \"year\") {\n      value = differenceInCalendarYears(laterDate_, earlierDate_);\n    }\n  }\n\n  const rtf = new Intl.RelativeTimeFormat(options?.locale, {\n    numeric: \"auto\",\n    ...options,\n  });\n\n  return rtf.format(value, unit);\n}\n\n// Fallback for modularized imports:\nexport default intlFormatDistance;\n", "/**\n * @name isExists\n * @category Common Helpers\n * @summary Is the given date exists?\n *\n * @description\n * Checks if the given arguments convert to an existing date.\n *\n * @param year - The year of the date to check\n * @param month - The month of the date to check\n * @param day - The day of the date to check\n *\n * @returns `true` if the date exists\n *\n * @example\n * // For the valid date:\n * const result = isExists(2018, 0, 31)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isExists(2018, 1, 31)\n * //=> false\n */\nexport function isExists(year, month, day) {\n  const date = new Date(year, month, day);\n  return (\n    date.getFullYear() === year &&\n    date.getMonth() === month &&\n    date.getDate() === day\n  );\n}\n\n// Fallback for modularized imports:\nexport default isExists;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isFirstDayOfMonth} function options.\n */\n\n/**\n * @name isFirstDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the first day of a month?\n *\n * @description\n * Is the given date the first day of a month?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is the first day of a month\n *\n * @example\n * // Is 1 September 2014 the first day of a month?\n * const result = isFirstDayOfMonth(new Date(2014, 8, 1))\n * //=> true\n */\nexport function isFirstDayOfMonth(date, options) {\n  return toDate(date, options?.in).getDate() === 1;\n}\n\n// Fallback for modularized imports:\nexport default isFirstDayOfMonth;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isFriday} function options.\n */\n\n/**\n * @name isFriday\n * @category Weekday Helpers\n * @summary Is the given date Friday?\n *\n * @description\n * Is the given date Friday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Friday\n *\n * @example\n * // Is 26 September 2014 Friday?\n * const result = isFriday(new Date(2014, 8, 26))\n * //=> true\n */\nexport function isFriday(date, options) {\n  return toDate(date, options?.in).getDay() === 5;\n}\n\n// Fallback for modularized imports:\nexport default isFriday;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isFuture\n * @category Common Helpers\n * @summary Is the given date in the future?\n * @pure false\n *\n * @description\n * Is the given date in the future?\n *\n * @param date - The date to check\n *\n * @returns The date is in the future\n *\n * @example\n * // If today is 6 October 2014, is 31 December 2014 in the future?\n * const result = isFuture(new Date(2014, 11, 31))\n * //=> true\n */\nexport function isFuture(date) {\n  return +toDate(date) > Date.now();\n}\n\n// Fallback for modularized imports:\nexport default isFuture;\n", "import { isValid } from \"./isValid.js\";\nimport { parse } from \"./parse.js\";\n\n/**\n * The {@link isMatch} function options.\n */\n\n/**\n * @name isMatch\n * @category Common Helpers\n * @summary validates the date string against given formats\n *\n * @description\n * Return the true if given date is string correct against the given format else\n * will return false.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters in the format string wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the format string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 5 below the table).\n *\n * Not all tokens are compatible. Combinations that don't make sense or could lead to bugs are prohibited\n * and will throw `RangeError`. For example usage of 24-hour format token with AM/PM token will throw an exception:\n *\n * ```javascript\n * isMatch('23 AM', 'HH a')\n * //=> RangeError: The format string mustn't contain `HH` and `a` at the same time\n * ```\n *\n * See the compatibility table: https://docs.google.com/spreadsheets/d/e/2PACX-1vQOPU3xUhplll6dyoMmVUXHKl_8CRDs6_ueLmex3SoqwhuolkuN3O05l4rqx5h1dKX8eb46Ul-CCSrq/pubhtml?gid=0&single=true\n *\n * Accepted format string patterns:\n * | Unit                            |Prior| Pattern | Result examples                   | Notes |\n * |---------------------------------|-----|---------|-----------------------------------|-------|\n * | Era                             | 140 | G..GGG  | AD, BC                            |       |\n * |                                 |     | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 |     | GGGGG   | A, B                              |       |\n * | Calendar year                   | 130 | y       | 44, 1, 1900, 2017, 9999           | 4     |\n * |                                 |     | yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | yy      | 44, 01, 00, 17                    | 4     |\n * |                                 |     | yyy     | 044, 001, 123, 999                | 4     |\n * |                                 |     | yyyy    | 0044, 0001, 1900, 2017            | 4     |\n * |                                 |     | yyyyy   | ...                               | 2,4   |\n * | Local week-numbering year       | 130 | Y       | 44, 1, 1900, 2017, 9000           | 4     |\n * |                                 |     | Yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | YY      | 44, 01, 00, 17                    | 4,6   |\n * |                                 |     | YYY     | 044, 001, 123, 999                | 4     |\n * |                                 |     | YYYY    | 0044, 0001, 1900, 2017            | 4,6   |\n * |                                 |     | YYYYY   | ...                               | 2,4   |\n * | ISO week-numbering year         | 130 | R       | -43, 1, 1900, 2017, 9999, -9999   | 4,5   |\n * |                                 |     | RR      | -43, 01, 00, 17                   | 4,5   |\n * |                                 |     | RRR     | -043, 001, 123, 999, -999         | 4,5   |\n * |                                 |     | RRRR    | -0043, 0001, 2017, 9999, -9999    | 4,5   |\n * |                                 |     | RRRRR   | ...                               | 2,4,5 |\n * | Extended year                   | 130 | u       | -43, 1, 1900, 2017, 9999, -999    | 4     |\n * |                                 |     | uu      | -43, 01, 99, -99                  | 4     |\n * |                                 |     | uuu     | -043, 001, 123, 999, -999         | 4     |\n * |                                 |     | uuuu    | -0043, 0001, 2017, 9999, -9999    | 4     |\n * |                                 |     | uuuuu   | ...                               | 2,4   |\n * | Quarter (formatting)            | 120 | Q       | 1, 2, 3, 4                        |       |\n * |                                 |     | Qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | QQ      | 01, 02, 03, 04                    |       |\n * |                                 |     | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | 120 | q       | 1, 2, 3, 4                        |       |\n * |                                 |     | qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | qq      | 01, 02, 03, 04                    |       |\n * |                                 |     | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | qqqqq   | 1, 2, 3, 4                        | 3     |\n * | Month (formatting)              | 110 | M       | 1, 2, ..., 12                     |       |\n * |                                 |     | Mo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | MM      | 01, 02, ..., 12                   |       |\n * |                                 |     | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | MMMM    | January, February, ..., December  | 2     |\n * |                                 |     | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | 110 | L       | 1, 2, ..., 12                     |       |\n * |                                 |     | Lo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | LL      | 01, 02, ..., 12                   |       |\n * |                                 |     | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | LLLL    | January, February, ..., December  | 2     |\n * |                                 |     | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | 100 | w       | 1, 2, ..., 53                     |       |\n * |                                 |     | wo      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | 100 | I       | 1, 2, ..., 53                     | 5     |\n * |                                 |     | Io      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | II      | 01, 02, ..., 53                   | 5     |\n * | Day of month                    |  90 | d       | 1, 2, ..., 31                     |       |\n * |                                 |     | do      | 1st, 2nd, ..., 31st               | 5     |\n * |                                 |     | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     |  90 | D       | 1, 2, ..., 365, 366               | 7     |\n * |                                 |     | Do      | 1st, 2nd, ..., 365th, 366th       | 5     |\n * |                                 |     | DD      | 01, 02, ..., 365, 366             | 7     |\n * |                                 |     | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 |     | DDDD    | ...                               | 2     |\n * | Day of week (formatting)        |  90 | E..EEE  | Mon, Tue, Wed, ..., Su            |       |\n * |                                 |     | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 |     | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    |  90 | i       | 1, 2, 3, ..., 7                   | 5     |\n * |                                 |     | io      | 1st, 2nd, ..., 7th                | 5     |\n * |                                 |     | ii      | 01, 02, ..., 07                   | 5     |\n * |                                 |     | iii     | Mon, Tue, Wed, ..., Su            | 5     |\n * |                                 |     | iiii    | Monday, Tuesday, ..., Sunday      | 2,5   |\n * |                                 |     | iiiii   | M, T, W, T, F, S, S               | 5     |\n * |                                 |     | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 5     |\n * | Local day of week (formatting)  |  90 | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | eo      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | ee      | 02, 03, ..., 01                   |       |\n * |                                 |     | eee     | Mon, Tue, Wed, ..., Su            |       |\n * |                                 |     | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 |     | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) |  90 | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | co      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | cc      | 02, 03, ..., 01                   |       |\n * |                                 |     | ccc     | Mon, Tue, Wed, ..., Su            |       |\n * |                                 |     | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 |     | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          |  80 | a..aaa  | AM, PM                            |       |\n * |                                 |     | aaaa    | a.m., p.m.                        | 2     |\n * |                                 |     | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          |  80 | b..bbb  | AM, PM, noon, midnight            |       |\n * |                                 |     | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 |     | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             |  80 | B..BBB  | at night, in the morning, ...     |       |\n * |                                 |     | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 |     | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     |  70 | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 |     | ho      | 1st, 2nd, ..., 11th, 12th         | 5     |\n * |                                 |     | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     |  70 | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 |     | Ho      | 0th, 1st, 2nd, ..., 23rd          | 5     |\n * |                                 |     | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     |  70 | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 |     | Ko      | 1st, 2nd, ..., 11th, 0th          | 5     |\n * |                                 |     | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     |  70 | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 |     | ko      | 24th, 1st, 2nd, ..., 23rd         | 5     |\n * |                                 |     | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          |  60 | m       | 0, 1, ..., 59                     |       |\n * |                                 |     | mo      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | mm      | 00, 01, ..., 59                   |       |\n * | Second                          |  50 | s       | 0, 1, ..., 59                     |       |\n * |                                 |     | so      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | ss      | 00, 01, ..., 59                   |       |\n * | Seconds timestamp               |  40 | t       | 512969520                         |       |\n * |                                 |     | tt      | ...                               | 2     |\n * | Fraction of second              |  30 | S       | 0, 1, ..., 9                      |       |\n * |                                 |     | SS      | 00, 01, ..., 99                   |       |\n * |                                 |     | SSS     | 000, 001, ..., 999                |       |\n * |                                 |     | SSSS    | ...                               | 2     |\n * | Milliseconds timestamp          |  20 | T       | 512969520900                      |       |\n * |                                 |     | TT      | ...                               | 2     |\n * | Timezone (ISO-8601 w/ Z)        |  10 | X       | -08, +0530, Z                     |       |\n * |                                 |     | XX      | -0800, +0530, Z                   |       |\n * |                                 |     | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 |     | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 |     | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       |  10 | x       | -08, +0530, +00                   |       |\n * |                                 |     | xx      | -0800, +0530, +0000               |       |\n * |                                 |     | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 |     | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 |     | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Long localized date             |  NA | P       | 05/29/1453                        | 5,8   |\n * |                                 |     | PP      | May 29, 1453                      |       |\n * |                                 |     | PPP     | May 29th, 1453                    |       |\n * |                                 |     | PPPP    | Sunday, May 29th, 1453            | 2,5,8 |\n * | Long localized time             |  NA | p       | 12:00 AM                          | 5,8   |\n * |                                 |     | pp      | 12:00:00 AM                       |       |\n * | Combination of date and time    |  NA | Pp      | 05/29/1453, 12:00 AM              |       |\n * |                                 |     | PPpp    | May 29, 1453, 12:00:00 AM         |       |\n * |                                 |     | PPPpp   | May 29th, 1453 at ...             |       |\n * |                                 |     | PPPPpp  | Sunday, May 29th, 1453 at ...     | 2,5,8 |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular.\n *    In `format` function, they will produce different result:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n *    `isMatch` will try to match both formatting and stand-alone units interchangeably.\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table:\n *    - for numerical units (`yyyyyyyy`) `isMatch` will try to match a number\n *      as wide as the sequence\n *    - for text units (`MMMMMMMM`) `isMatch` will try to match the widest variation of the unit.\n *      These variations are marked with \"2\" in the last column of the table.\n *\n * 3. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 4. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` will try to guess the century of two digit year by proximity with `referenceDate`:\n *\n *    `isMatch('50', 'yy') //=> true`\n *\n *    `isMatch('75', 'yy') //=> true`\n *\n *    while `uu` will use the year as is:\n *\n *    `isMatch('50', 'uu') //=> true`\n *\n *    `isMatch('75', 'uu') //=> true`\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [setISOWeekYear](https://date-fns.org/docs/setISOWeekYear)\n *    and [setWeekYear](https://date-fns.org/docs/setWeekYear)).\n *\n * 5. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 6. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 7. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 8. `P+` tokens do not have a defined priority since they are merely aliases to other tokens based\n *    on the given locale.\n *\n *    using `en-US` locale: `P` => `MM/dd/yyyy`\n *    using `en-US` locale: `p` => `hh:mm a`\n *    using `pt-BR` locale: `P` => `dd/MM/yyyy`\n *    using `pt-BR` locale: `p` => `HH:mm`\n *\n * Values will be checked in the descending order of its unit's priority.\n * Units of an equal priority overwrite each other in the order of appearance.\n *\n * If no values of higher priority are matched (e.g. when matching string 'January 1st' without a year),\n * the values will be taken from today's using `new Date()` date which works as a context of parsing.\n *\n * The result may vary by locale.\n *\n * If `formatString` matches with `dateString` but does not provides tokens, `referenceDate` will be returned.\n *\n * @param dateStr - The date string to verify\n * @param format - The string of tokens\n * @param options - An object with options.\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * @returns Is format string a match for date string?\n *\n * @throws `options.locale` must contain `match` property\n * @throws use `yyyy` instead of `YYYY` for formatting years; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `yy` instead of `YY` for formatting years; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `d` instead of `D` for formatting days of the month; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `dd` instead of `DD` for formatting days of the month; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * // Match 11 February 2014 from middle-endian format:\n * const result = isMatch('02/11/2014', 'MM/dd/yyyy')\n * //=> true\n *\n * @example\n * // Match 28th of February in Esperanto locale in the context of 2010 year:\n * import eo from 'date-fns/locale/eo'\n * const result = isMatch('28-a de februaro', \"do 'de' MMMM\", {\n *   locale: eo\n * })\n * //=> true\n */\nexport function isMatch(dateStr, formatStr, options) {\n  return isValid(parse(dateStr, formatStr, new Date(), options));\n}\n\n// Fallback for modularized imports:\nexport default isMatch;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isMonday} function options.\n */\n\n/**\n * @name isMonday\n * @category Weekday Helpers\n * @summary Is the given date Monday?\n *\n * @description\n * Is the given date Monday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Monday\n *\n * @example\n * // Is 22 September 2014 Monday?\n * const result = isMonday(new Date(2014, 8, 22))\n * //=> true\n */\nexport function isMonday(date, options) {\n  return toDate(date, options?.in).getDay() === 1;\n}\n\n// Fallback for modularized imports:\nexport default isMonday;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isPast\n * @category Common Helpers\n * @summary Is the given date in the past?\n * @pure false\n *\n * @description\n * Is the given date in the past?\n *\n * @param date - The date to check\n *\n * @returns The date is in the past\n *\n * @example\n * // If today is 6 October 2014, is 2 July 2014 in the past?\n * const result = isPast(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isPast(date) {\n  return +toDate(date) < Date.now();\n}\n\n// Fallback for modularized imports:\nexport default isPast;\n", "import { isSameWeek } from \"./isSameWeek.js\";\n\n/**\n * The {@link isSameISOWeek} function options.\n */\n\n/**\n * @name isSameISOWeek\n * @category ISO Week Helpers\n * @summary Are the given dates in the same ISO week (and year)?\n *\n * @description\n * Are the given dates in the same ISO week (and year)?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same ISO week (and year)\n *\n * @example\n * // Are 1 September 2014 and 7 September 2014 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2014, 8, 7))\n * //=> true\n *\n * @example\n * // Are 1 September 2014 and 1 September 2015 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2015, 8, 1))\n * //=> false\n */\nexport function isSameISOWeek(laterDate, earlierDate, options) {\n  return isSameWeek(laterDate, earlierDate, { ...options, weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default isSameISOWeek;\n", "import { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\n\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameISOWeekYear} function options.\n */\n\n/**\n * @name isSameISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Are the given dates in the same ISO week-numbering year?\n *\n * @description\n * Are the given dates in the same ISO week-numbering year?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same ISO week-numbering year\n *\n * @example\n * // Are 29 December 2003 and 2 January 2005 in the same ISO week-numbering year?\n * const result = isSameISOWeekYear(new Date(2003, 11, 29), new Date(2005, 0, 2))\n * //=> true\n */\nexport function isSameISOWeekYear(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return +startOfISOWeekYear(laterDate_) === +startOfISOWeekYear(earlierDate_);\n}\n\n// Fallback for modularized imports:\nexport default isSameISOWeekYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfMinute} function options.\n */\n\n/**\n * @name startOfMinute\n * @category Minute Helpers\n * @summary Return the start of a minute for the given date.\n *\n * @description\n * Return the start of a minute for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a minute\n *\n * @example\n * // The start of a minute for 1 December 2014 22:15:45.400:\n * const result = startOfMinute(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:00\n */\nexport function startOfMinute(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setSeconds(0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfMinute;\n", "import { startOfMinute } from \"./startOfMinute.js\";\n\n/**\n * @name isSameMinute\n * @category Minute Helpers\n * @summary Are the given dates in the same minute (and hour and day)?\n *\n * @description\n * Are the given dates in the same minute (and hour and day)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n *\n * @returns The dates are in the same minute (and hour and day)\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 4 September 2014 06:30:15 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 4, 6, 30, 15)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 5 September 2014 06:30:00 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 5, 6, 30)\n * )\n * //=> false\n */\nexport function isSameMinute(laterDate, earlierDate) {\n  return +startOfMinute(laterDate) === +startOfMinute(earlierDate);\n}\n\n// Fallback for modularized imports:\nexport default isSameMinute;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfQuarter } from \"./startOfQuarter.js\";\n\n/**\n * The {@link isSameQuarter} function options.\n */\n\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same quarter (and year)\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport function isSameQuarter(laterDate, earlierDate, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return +startOfQuarter(dateLeft_) === +startOfQuarter(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameQuarter;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfSecond} function options.\n */\n\n/**\n * @name startOfSecond\n * @category Second Helpers\n * @summary Return the start of a second for the given date.\n *\n * @description\n * Return the start of a second for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a second\n *\n * @example\n * // The start of a second for 1 December 2014 22:15:45.400:\n * const result = startOfSecond(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:45.000\n */\nexport function startOfSecond(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setMilliseconds(0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfSecond;\n", "import { startOfSecond } from \"./startOfSecond.js\";\n\n/**\n * @name isSameSecond\n * @category Second Helpers\n * @summary Are the given dates in the same second (and hour and day)?\n *\n * @description\n * Are the given dates in the same second (and hour and day)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n *\n * @returns The dates are in the same second (and hour and day)\n *\n * @example\n * // Are 4 September 2014 06:30:15.000 and 4 September 2014 06:30.15.500 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 30, 15),\n *   new Date(2014, 8, 4, 6, 30, 15, 500)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 4 September 2014 06:01.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 4, 6, 1, 15)\n * )\n * //=> false\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 5 September 2014 06:00.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 5, 6, 0, 15)\n * )\n * //=> false\n */\nexport function isSameSecond(laterDate, earlierDate) {\n  return +startOfSecond(laterDate) === +startOfSecond(earlierDate);\n}\n\n// Fallback for modularized imports:\nexport default isSameSecond;\n", "import { constructNow } from \"./constructNow.js\";\nimport { isSameHour } from \"./isSameHour.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isThisHour} function options.\n */\n\n/**\n * @name isThisHour\n * @category Hour Helpers\n * @summary Is the given date in the same hour as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same hour as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this hour\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:00:00 in this hour?\n * const result = isThisHour(new Date(2014, 8, 25, 18))\n * //=> true\n */\nexport function isThisHour(date, options) {\n  return isSameHour(\n    toDate(date, options?.in),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisHour;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameISOWeek } from \"./isSameISOWeek.js\";\n\n/**\n * The {@link isThisISOWeek} function options.\n */\n\n/**\n * @name isThisISOWeek\n * @category ISO Week Helpers\n * @summary Is the given date in the same ISO week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same ISO week as the current date?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this ISO week\n *\n * @example\n * // If today is 25 September 2014, is 22 September 2014 in this ISO week?\n * const result = isThisISOWeek(new Date(2014, 8, 22))\n * //=> true\n */\nexport function isThisISOWeek(date, options) {\n  return isSameISOWeek(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisISOWeek;\n", "import { constructNow } from \"./constructNow.js\";\nimport { isSameMinute } from \"./isSameMinute.js\";\n\n/**\n * @name isThisMinute\n * @category Minute Helpers\n * @summary Is the given date in the same minute as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same minute as the current date?\n *\n * @param date - The date to check\n *\n * @returns The date is in this minute\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:00 in this minute?\n * const result = isThisMinute(new Date(2014, 8, 25, 18, 30))\n * //=> true\n */\n\nexport function isThisMinute(date) {\n  return isSameMinute(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisMinute;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameMonth } from \"./isSameMonth.js\";\n\n/**\n * The {@link isThisMonth} function options.\n */\n\n/**\n * @name isThisMonth\n * @category Month Helpers\n * @summary Is the given date in the same month as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same month as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this month\n *\n * @example\n * // If today is 25 September 2014, is 15 September 2014 in this month?\n * const result = isThisMonth(new Date(2014, 8, 15))\n * //=> true\n */\nexport function isThisMonth(date, options) {\n  return isSameMonth(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisMonth;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameQuarter } from \"./isSameQuarter.js\";\n\n/**\n * The {@link isThisQuarter} function options.\n */\n\n/**\n * @name isThisQuarter\n * @category Quarter Helpers\n * @summary Is the given date in the same quarter as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same quarter as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this quarter\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this quarter?\n * const result = isThisQuarter(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisQuarter(date, options) {\n  return isSameQuarter(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisQuarter;\n", "import { constructNow } from \"./constructNow.js\";\nimport { isSameSecond } from \"./isSameSecond.js\";\n\n/**\n * @name isThisSecond\n * @category Second Helpers\n * @summary Is the given date in the same second as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same second as the current date?\n *\n * @param date - The date to check\n *\n * @returns The date is in this second\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:15.000 in this second?\n * const result = isThisSecond(new Date(2014, 8, 25, 18, 30, 15))\n * //=> true\n */\nexport function isThisSecond(date) {\n  return isSameSecond(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisSecond;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameWeek } from \"./isSameWeek.js\";\n\n/**\n * The {@link isThisWeek} function options.\n */\n\n/**\n * @name isThisWeek\n * @category Week Helpers\n * @summary Is the given date in the same week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same week as the current date?\n *\n * @param date - The date to check\n * @param options - The object with options\n *\n * @returns The date is in this week\n *\n * @example\n * // If today is 25 September 2014, is 21 September 2014 in this week?\n * const result = isThisWeek(new Date(2014, 8, 21))\n * //=> true\n *\n * @example\n * // If today is 25 September 2014 and week starts with Monday\n * // is 21 September 2014 in this week?\n * const result = isThisWeek(new Date(2014, 8, 21), { weekStartsOn: 1 })\n * //=> false\n */\nexport function isThisWeek(date, options) {\n  return isSameWeek(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n    options,\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisWeek;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameYear } from \"./isSameYear.js\";\n\n/**\n * The {@link isThisYear} function options.\n */\n\n/**\n * @name isThisYear\n * @category Year Helpers\n * @summary Is the given date in the same year as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same year as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this year\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this year?\n * const result = isThisYear(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisYear(date, options) {\n  return isSameYear(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isThursday} function options.\n */\n\n/**\n * @name isThursday\n * @category Weekday Helpers\n * @summary Is the given date Thursday?\n *\n * @description\n * Is the given date Thursday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Thursday\n *\n * @example\n * // Is 25 September 2014 Thursday?\n * const result = isThursday(new Date(2014, 8, 25))\n * //=> true\n */\nexport function isThursday(date, options) {\n  return toDate(date, options?.in).getDay() === 4;\n}\n\n// Fallback for modularized imports:\nexport default isThursday;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\n\n/**\n * The {@link isToday} function options.\n */\n\n/**\n * @name isToday\n * @category Day Helpers\n * @summary Is the given date today?\n * @pure false\n *\n * @description\n * Is the given date today?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is today\n *\n * @example\n * // If today is 6 October 2014, is 6 October 14:00:00 today?\n * const result = isToday(new Date(2014, 9, 6, 14, 0))\n * //=> true\n */\nexport function isToday(date, options) {\n  return isSameDay(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isToday;\n", "import { addDays } from \"./addDays.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\n\n/**\n * The {@link isTomorrow} function options.\n */\n\n/**\n * @name isTomorrow\n * @category Day Helpers\n * @summary Is the given date tomorrow?\n * @pure false\n *\n * @description\n * Is the given date tomorrow?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is tomorrow\n *\n * @example\n * // If today is 6 October 2014, is 7 October 14:00:00 tomorrow?\n * const result = isTomorrow(new Date(2014, 9, 7, 14, 0))\n * //=> true\n */\nexport function isTomorrow(date, options) {\n  return isSameDay(\n    date,\n    addDays(constructNow(options?.in || date), 1),\n    options,\n  );\n}\n\n// Fallback for modularized imports:\nexport default isTomorrow;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isTuesday} function options.\n */\n\n/**\n * @name isTuesday\n * @category Weekday Helpers\n * @summary Is the given date Tuesday?\n *\n * @description\n * Is the given date Tuesday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Tuesday\n *\n * @example\n * // Is 23 September 2014 Tuesday?\n * const result = isTuesday(new Date(2014, 8, 23))\n * //=> true\n */\nexport function isTuesday(date, options) {\n  return toDate(date, options?.in).getDay() === 2;\n}\n\n// Fallback for modularized imports:\nexport default isTuesday;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWednesday} function options.\n */\n\n/**\n * @name isWednesday\n * @category Weekday Helpers\n * @summary Is the given date Wednesday?\n *\n * @description\n * Is the given date Wednesday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Wednesday\n *\n * @example\n * // Is 24 September 2014 Wednesday?\n * const result = isWednesday(new Date(2014, 8, 24))\n * //=> true\n */\nexport function isWednesday(date, options) {\n  return toDate(date, options?.in).getDay() === 3;\n}\n\n// Fallback for modularized imports:\nexport default isWednesday;\n", "import { addDays } from \"./addDays.js\";\n\n/**\n * The {@link subDays} function options.\n */\n\n/**\n * @name subDays\n * @category Day Helpers\n * @summary Subtract the specified number of days from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the days subtracted\n *\n * @example\n * // Subtract 10 days from 1 September 2014:\n * const result = subDays(new Date(2014, 8, 1), 10)\n * //=> Fri Aug 22 2014 00:00:00\n */\nexport function subDays(date, amount, options) {\n  return addDays(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subDays;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\nimport { subDays } from \"./subDays.js\";\n\n/**\n * The {@link isYesterday} function options.\n */\n\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is yesterday\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * const result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\nexport function isYesterday(date, options) {\n  return isSameDay(\n    constructFrom(options?.in || date, date),\n    subDays(constructNow(options?.in || date), 1),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isYesterday;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfDecade} function options.\n */\n\n/**\n * @name lastDayOfDecade\n * @category Decade Helpers\n * @summary Return the last day of a decade for the given date.\n *\n * @description\n * Return the last day of a decade for the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type; inferred from arguments or specified by context.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The last day of a decade\n *\n * @example\n * // The last day of a decade for 21 December 2012 21:12:00:\n * const result = lastDayOfDecade(new Date(2012, 11, 21, 21, 12, 00))\n * //=> Wed Dec 31 2019 00:00:00\n */\nexport function lastDayOfDecade(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = 9 + Math.floor(year / 10) * 10;\n  _date.setFullYear(decade + 1, 0, 0);\n  _date.setHours(0, 0, 0, 0);\n  return toDate(_date, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfDecade;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfWeek} function options.\n */\n\n/**\n * @name lastDayOfWeek\n * @category Week Helpers\n * @summary Return the last day of a week for the given date.\n *\n * @description\n * Return the last day of a week for the given date.\n * The result will be in the local timezone unless a context is specified.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The last day of a week\n */\nexport function lastDayOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date, options?.in);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n\n  _date.setHours(0, 0, 0, 0);\n  _date.setDate(_date.getDate() + diff);\n\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfWeek;\n", "import { lastDayOfWeek } from \"./lastDayOfWeek.js\";\n\n/**\n * The {@link lastDayOfISOWeek} function options.\n */\n\n/**\n * @name lastDayOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the last day of an ISO week for the given date.\n *\n * @description\n * Return the last day of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The Date type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [UTCDate](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The last day of an ISO week\n *\n * @example\n * // The last day of an ISO week for 2 September 2014 11:55:00:\n * const result = lastDayOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport function lastDayOfISOWeek(date, options) {\n  return lastDayOfWeek(date, { ...options, weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfISOWeek;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link lastDayOfISOWeekYear} function options.\n */\n\n/**\n * @name lastDayOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the last day of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the last day of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of an ISO week-numbering year\n *\n * @example\n * // The last day of an ISO week-numbering year for 2 July 2005:\n * const result = lastDayOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Sun Jan 01 2006 00:00:00\n */\nexport function lastDayOfISOWeekYear(date, options) {\n  const year = getISOWeekYear(date, options);\n  const fourthOfJanuary = constructFrom(options?.in || date, 0);\n  fourthOfJanuary.setFullYear(year + 1, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n\n  const date_ = startOfISOWeek(fourthOfJanuary, options);\n  date_.setDate(date_.getDate() - 1);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfISOWeekYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfQuarter} function options.\n */\n\n/**\n * @name lastDayOfQuarter\n * @category Quarter Helpers\n * @summary Return the last day of a year quarter for the given date.\n *\n * @description\n * Return the last day of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The last day of a quarter\n *\n * @example\n * // The last day of a quarter for 2 September 2014 11:55:00:\n * const result = lastDayOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfQuarter(date, options) {\n  const date_ = toDate(date, options?.in);\n  const currentMonth = date_.getMonth();\n  const month = currentMonth - (currentMonth % 3) + 3;\n  date_.setMonth(month, 0);\n  date_.setHours(0, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfQuarter;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfYear} function options.\n */\n\n/**\n * @name lastDayOfYear\n * @category Year Helpers\n * @summary Return the last day of a year for the given date.\n *\n * @description\n * Return the last day of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The last day of a year\n *\n * @example\n * // The last day of a year for 2 September 2014 11:55:00:\n * const result = lastDayOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Dec 31 2014 00:00:00\n */\nexport function lastDayOfYear(date, options) {\n  const date_ = toDate(date, options?.in);\n  const year = date_.getFullYear();\n  date_.setFullYear(year + 1, 0, 0);\n  date_.setHours(0, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfYear;\n", "import { lightFormatters } from \"./_lib/format/lightFormatters.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { lightFormatters };\n\n// This RegExp consists of three parts separated by `|`:\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp = /(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @private\n */\n\n/**\n * @name lightFormat\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. Unlike `format`,\n * `lightFormat` doesn't use locales and outputs date using the most popular tokens.\n *\n * > ⚠️ Please note that the `lightFormat` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   |\n * |---------------------------------|---------|-----------------------------------|\n * | AM, PM                          | a..aaa  | AM, PM                            |\n * |                                 | aaaa    | a.m., p.m.                        |\n * |                                 | aaaaa   | a, p                              |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 |\n * |                                 | yy      | 44, 01, 00, 17                    |\n * |                                 | yyy     | 044, 001, 000, 017                |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |\n * |                                 | MM      | 01, 02, ..., 12                   |\n * | Day of month                    | d       | 1, 2, ..., 31                     |\n * |                                 | dd      | 01, 02, ..., 31                   |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |\n * |                                 | hh      | 01, 02, ..., 11, 12               |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |\n * |                                 | HH      | 00, 01, 02, ..., 23               |\n * | Minute                          | m       | 0, 1, ..., 59                     |\n * |                                 | mm      | 00, 01, ..., 59                   |\n * | Second                          | s       | 0, 1, ..., 59                     |\n * |                                 | ss      | 00, 01, ..., 59                   |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |\n * |                                 | SS      | 00, 01, ..., 99                   |\n * |                                 | SSS     | 000, 001, ..., 999                |\n * |                                 | SSSS    | ...                               |\n *\n * @param date - The original date\n * @param format - The string of tokens\n *\n * @returns The formatted date string\n *\n * @throws `Invalid time value` if the date is invalid\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * const result = lightFormat(new Date(2014, 1, 11), 'yyyy-MM-dd')\n * //=> '2014-02-11'\n */\nexport function lightFormat(date, formatStr) {\n  const date_ = toDate(date);\n\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const tokens = formatStr.match(formattingTokensRegExp);\n\n  // The only case when formattingTokensRegExp doesn't match the string is when it's empty\n  if (!tokens) return \"\";\n\n  const result = tokens\n    .map((substring) => {\n      // Replace two single quote characters with one single quote character\n      if (substring === \"''\") {\n        return \"'\";\n      }\n\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"'\") {\n        return cleanEscapedString(substring);\n      }\n\n      const formatter = lightFormatters[firstCharacter];\n      if (formatter) {\n        return formatter(date_, substring);\n      }\n\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\n          \"Format string contains an unescaped latin alphabet character `\" +\n            firstCharacter +\n            \"`\",\n        );\n      }\n\n      return substring;\n    })\n    .join(\"\");\n\n  return result;\n}\n\nfunction cleanEscapedString(input) {\n  const matches = input.match(escapedStringRegExp);\n  if (!matches) return input;\n  return matches[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default lightFormat;\n", "import { daysInYear } from \"./constants.js\";\n\n/**\n * @name milliseconds\n * @category Millisecond Helpers\n * @summary\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * @description\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occurs every 4 years, except for years that are divisible by 100 and not divisible by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n *\n * One month is a year divided by 12.\n *\n * @param duration - The object with years, months, weeks, days, hours, minutes and seconds to be added.\n *\n * @returns The milliseconds\n *\n * @example\n * // 1 year in milliseconds\n * milliseconds({ years: 1 })\n * //=> 31556952000\n *\n * // 3 months in milliseconds\n * milliseconds({ months: 3 })\n * //=> 7889238000\n */\nexport function milliseconds({\n  years,\n  months,\n  weeks,\n  days,\n  hours,\n  minutes,\n  seconds,\n}) {\n  let totalDays = 0;\n\n  if (years) totalDays += years * daysInYear;\n  if (months) totalDays += months * (daysInYear / 12);\n  if (weeks) totalDays += weeks * 7;\n  if (days) totalDays += days;\n\n  let totalSeconds = totalDays * 24 * 60 * 60;\n\n  if (hours) totalSeconds += hours * 60 * 60;\n  if (minutes) totalSeconds += minutes * 60;\n  if (seconds) totalSeconds += seconds;\n\n  return Math.trunc(totalSeconds * 1000);\n}\n\n// Fallback for modularized imports:\nexport default milliseconds;\n", "import { millisecondsInHour } from \"./constants.js\";\n\n/**\n * @name millisecondsToHours\n * @category Conversion Helpers\n * @summary Convert milliseconds to hours.\n *\n * @description\n * Convert a number of milliseconds to a full number of hours.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in hours\n *\n * @example\n * // Convert 7200000 milliseconds to hours:\n * const result = millisecondsToHours(7200000)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToHours(7199999)\n * //=> 1\n */\nexport function millisecondsToHours(milliseconds) {\n  const hours = milliseconds / millisecondsInHour;\n  return Math.trunc(hours);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToHours;\n", "import { millisecondsInMinute } from \"./constants.js\";\n\n/**\n * @name millisecondsToMinutes\n * @category Conversion Helpers\n * @summary Convert milliseconds to minutes.\n *\n * @description\n * Convert a number of milliseconds to a full number of minutes.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in minutes\n *\n * @example\n * // Convert 60000 milliseconds to minutes:\n * const result = millisecondsToMinutes(60000)\n * //=> 1\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToMinutes(119999)\n * //=> 1\n */\nexport function millisecondsToMinutes(milliseconds) {\n  const minutes = milliseconds / millisecondsInMinute;\n  return Math.trunc(minutes);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToMinutes;\n", "import { millisecondsInSecond } from \"./constants.js\";\n\n/**\n * @name millisecondsToSeconds\n * @category Conversion Helpers\n * @summary Convert milliseconds to seconds.\n *\n * @description\n * Convert a number of milliseconds to a full number of seconds.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in seconds\n *\n * @example\n * // Convert 1000 milliseconds to seconds:\n * const result = millisecondsToSeconds(1000)\n * //=> 1\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToSeconds(1999)\n * //=> 1\n */\nexport function millisecondsToSeconds(milliseconds) {\n  const seconds = milliseconds / millisecondsInSecond;\n  return Math.trunc(seconds);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToSeconds;\n", "import { minutesInHour } from \"./constants.js\";\n\n/**\n * @name minutesToHours\n * @category Conversion Helpers\n * @summary Convert minutes to hours.\n *\n * @description\n * Convert a number of minutes to a full number of hours.\n *\n * @param minutes - The number of minutes to be converted\n *\n * @returns The number of minutes converted in hours\n *\n * @example\n * // Convert 140 minutes to hours:\n * const result = minutesToHours(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = minutesToHours(179)\n * //=> 2\n */\nexport function minutesToHours(minutes) {\n  const hours = minutes / minutesInHour;\n  return Math.trunc(hours);\n}\n\n// Fallback for modularized imports:\nexport default minutesToHours;\n", "import { millisecondsInMinute } from \"./constants.js\";\n\n/**\n * @name minutesToMilliseconds\n * @category Conversion Helpers\n * @summary Convert minutes to milliseconds.\n *\n * @description\n * Convert a number of minutes to a full number of milliseconds.\n *\n * @param minutes - The number of minutes to be converted\n *\n * @returns The number of minutes converted in milliseconds\n *\n * @example\n * // Convert 2 minutes to milliseconds\n * const result = minutesToMilliseconds(2)\n * //=> 120000\n */\nexport function minutesToMilliseconds(minutes) {\n  return Math.trunc(minutes * millisecondsInMinute);\n}\n\n// Fallback for modularized imports:\nexport default minutesToMilliseconds;\n", "import { secondsInMinute } from \"./constants.js\";\n\n/**\n * @name minutesToSeconds\n * @category Conversion Helpers\n * @summary Convert minutes to seconds.\n *\n * @description\n * Convert a number of minutes to a full number of seconds.\n *\n * @param minutes - The number of minutes to be converted\n *\n * @returns The number of minutes converted in seconds\n *\n * @example\n * // Convert 2 minutes to seconds\n * const result = minutesToSeconds(2)\n * //=> 120\n */\nexport function minutesToSeconds(minutes) {\n  return Math.trunc(minutes * secondsInMinute);\n}\n\n// Fallback for modularized imports:\nexport default minutesToSeconds;\n", "import { monthsInQuarter } from \"./constants.js\";\n\n/**\n * @name monthsToQuarters\n * @category Conversion Helpers\n * @summary Convert number of months to quarters.\n *\n * @description\n * Convert a number of months to a full number of quarters.\n *\n * @param months - The number of months to be converted.\n *\n * @returns The number of months converted in quarters\n *\n * @example\n * // Convert 6 months to quarters:\n * const result = monthsToQuarters(6)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = monthsToQuarters(7)\n * //=> 2\n */\nexport function monthsToQuarters(months) {\n  const quarters = months / monthsInQuarter;\n  return Math.trunc(quarters);\n}\n\n// Fallback for modularized imports:\nexport default monthsToQuarters;\n", "import { monthsInYear } from \"./constants.js\";\n\n/**\n * @name monthsToYears\n * @category Conversion Helpers\n * @summary Convert number of months to years.\n *\n * @description\n * Convert a number of months to a full number of years.\n *\n * @param months - The number of months to be converted\n *\n * @returns The number of months converted in years\n *\n * @example\n * // Convert 36 months to years:\n * const result = monthsToYears(36)\n * //=> 3\n *\n * // It uses floor rounding:\n * const result = monthsToYears(40)\n * //=> 3\n */\nexport function monthsToYears(months) {\n  const years = months / monthsInYear;\n  return Math.trunc(years);\n}\n\n// Fallback for modularized imports:\nexport default monthsToYears;\n", "import { addDays } from \"./addDays.js\";\nimport { getDay } from \"./getDay.js\";\n\n/**\n * The {@link nextDay} function options.\n */\n\n/**\n * @name nextDay\n * @category Weekday Helpers\n * @summary When is the next day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to check\n * @param day - Day of the week\n * @param options - An object with options\n *\n * @returns The date is the next day of the week\n *\n * @example\n * // When is the next Monday after Mar, 20, 2020?\n * const result = nextDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 23 2020 00:00:00\n *\n * @example\n * // When is the next Tuesday after Mar, 21, 2020?\n * const result = nextDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 24 2020 00:00:00\n */\nexport function nextDay(date, day, options) {\n  let delta = day - getDay(date, options);\n  if (delta <= 0) delta += 7;\n\n  return addDays(date, delta, options);\n}\n\n// Fallback for modularized imports:\nexport default nextDay;\n", "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextFriday} function options.\n */\n\n/**\n * @name nextFriday\n * @category Weekday Helpers\n * @summary When is the next Friday?\n *\n * @description\n * When is the next Friday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Friday\n *\n * @example\n * // When is the next Friday after Mar, 22, 2020?\n * const result = nextFriday(new Date(2020, 2, 22))\n * //=> Fri Mar 27 2020 00:00:00\n */\nexport function nextFriday(date, options) {\n  return nextDay(date, 5, options);\n}\n\n// Fallback for modularized imports:\nexport default nextFriday;\n", "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextMonday} function options.\n */\n\n/**\n * @name nextMonday\n * @category Weekday Helpers\n * @summary When is the next Monday?\n *\n * @description\n * When is the next Monday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, returned from the context function if passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Monday\n *\n * @example\n * // When is the next Monday after Mar, 22, 2020?\n * const result = nextMonday(new Date(2020, 2, 22))\n * //=> Mon Mar 23 2020 00:00:00\n */\nexport function nextMonday(date, options) {\n  return nextDay(date, 1, options);\n}\n\n// Fallback for modularized imports:\nexport default nextMonday;\n", "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextSaturday} function options.\n */\n\n/**\n * @name nextSaturday\n * @category Weekday Helpers\n * @summary When is the next Saturday?\n *\n * @description\n * When is the next Saturday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Saturday\n *\n * @example\n * // When is the next Saturday after Mar, 22, 2020?\n * const result = nextSaturday(new Date(2020, 2, 22))\n * //=> Sat Mar 28 2020 00:00:00\n */\nexport function nextSaturday(date, options) {\n  return nextDay(date, 6, options);\n}\n\n// Fallback for modularized imports:\nexport default nextSaturday;\n", "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextSunday} function options.\n */\n\n/**\n * @name nextSunday\n * @category Weekday Helpers\n * @summary When is the next Sunday?\n *\n * @description\n * When is the next Sunday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned if a context is provided.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Sunday\n *\n * @example\n * // When is the next Sunday after March 22, 2020?\n * const result = nextSunday(new Date(2020, 2, 22))\n * //=> Sun Mar 29 2020 00:00:00\n */\nexport function nextSunday(date, options) {\n  return nextDay(date, 0, options);\n}\n\n// Fallback for modularized imports:\nexport default nextSunday;\n", "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextThursday} function options.\n */\n\n/**\n * @name nextThursday\n * @category Weekday Helpers\n * @summary When is the next Thursday?\n *\n * @description\n * When is the next Thursday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Thursday\n *\n * @example\n * // When is the next Thursday after Mar, 22, 2020?\n * const result = nextThursday(new Date(2020, 2, 22))\n * //=> Thur Mar 26 2020 00:00:00\n */\nexport function nextThursday(date, options) {\n  return nextDay(date, 4, options);\n}\n\n// Fallback for modularized imports:\nexport default nextThursday;\n", "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextTuesday} function options.\n */\n\n/**\n * @name nextTuesday\n * @category Weekday Helpers\n * @summary When is the next Tuesday?\n *\n * @description\n * When is the next Tuesday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Tuesday\n *\n * @example\n * // When is the next Tuesday after Mar, 22, 2020?\n * const result = nextTuesday(new Date(2020, 2, 22))\n * //=> Tue Mar 24 2020 00:00:00\n */\nexport function nextTuesday(date, options) {\n  return nextDay(date, 2, options);\n}\n\n// Fallback for modularized imports:\nexport default nextTuesday;\n", "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextWednesday} function options.\n */\n\n/**\n * @name nextWednesday\n * @category Weekday Helpers\n * @summary When is the next Wednesday?\n *\n * @description\n * When is the next Wednesday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Wednesday\n *\n * @example\n * // When is the next Wednesday after Mar, 22, 2020?\n * const result = nextWednesday(new Date(2020, 2, 22))\n * //=> Wed Mar 25 2020 00:00:00\n */\nexport function nextWednesday(date, options) {\n  return nextDay(date, 3, options);\n}\n\n// Fallback for modularized imports:\nexport default nextWednesday;\n", "import {\n  millisecondsInHour,\n  millisecondsInMinute,\n} from \"./constants.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link parseISO} function options.\n */\n\n/**\n * @name parseISO\n * @category Common Helpers\n * @summary Parse ISO string\n *\n * @description\n * Parse the given string in ISO 8601 format and return an instance of Date.\n *\n * Function accepts complete ISO 8601 formats as well as partial implementations.\n * ISO 8601: http://en.wikipedia.org/wiki/ISO_8601\n *\n * If the argument isn't a string, the function cannot parse the string or\n * the values are invalid, it returns Invalid Date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param argument - The value to convert\n * @param options - An object with options\n *\n * @returns The parsed date in the local time zone\n *\n * @example\n * // Convert string '2014-02-11T11:30:30' to date:\n * const result = parseISO('2014-02-11T11:30:30')\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert string '+02014101' to date,\n * // if the additional number of digits in the extended year format is 1:\n * const result = parseISO('+02014101', { additionalDigits: 1 })\n * //=> Fri Apr 11 2014 00:00:00\n */\nexport function parseISO(argument, options) {\n  const invalidDate = () => constructFrom(options?.in, NaN);\n\n  const additionalDigits = options?.additionalDigits ?? 2;\n  const dateStrings = splitDateString(argument);\n\n  let date;\n  if (dateStrings.date) {\n    const parseYearResult = parseYear(dateStrings.date, additionalDigits);\n    date = parseDate(parseYearResult.restDateString, parseYearResult.year);\n  }\n\n  if (!date || isNaN(+date)) return invalidDate();\n\n  const timestamp = +date;\n  let time = 0;\n  let offset;\n\n  if (dateStrings.time) {\n    time = parseTime(dateStrings.time);\n    if (isNaN(time)) return invalidDate();\n  }\n\n  if (dateStrings.timezone) {\n    offset = parseTimezone(dateStrings.timezone);\n    if (isNaN(offset)) return invalidDate();\n  } else {\n    const tmpDate = new Date(timestamp + time);\n    const result = toDate(0, options?.in);\n    result.setFullYear(\n      tmpDate.getUTCFullYear(),\n      tmpDate.getUTCMonth(),\n      tmpDate.getUTCDate(),\n    );\n    result.setHours(\n      tmpDate.getUTCHours(),\n      tmpDate.getUTCMinutes(),\n      tmpDate.getUTCSeconds(),\n      tmpDate.getUTCMilliseconds(),\n    );\n    return result;\n  }\n\n  return toDate(timestamp + time + offset, options?.in);\n}\n\nconst patterns = {\n  dateTimeDelimiter: /[T ]/,\n  timeZoneDelimiter: /[Z ]/i,\n  timezone: /([Z+-].*)$/,\n};\n\nconst dateRegex =\n  /^-?(?:(\\d{3})|(\\d{2})(?:-?(\\d{2}))?|W(\\d{2})(?:-?(\\d{1}))?|)$/;\nconst timeRegex =\n  /^(\\d{2}(?:[.,]\\d*)?)(?::?(\\d{2}(?:[.,]\\d*)?))?(?::?(\\d{2}(?:[.,]\\d*)?))?$/;\nconst timezoneRegex = /^([+-])(\\d{2})(?::?(\\d{2}))?$/;\n\nfunction splitDateString(dateString) {\n  const dateStrings = {};\n  const array = dateString.split(patterns.dateTimeDelimiter);\n  let timeString;\n\n  // The regex match should only return at maximum two array elements.\n  // [date], [time], or [date, time].\n  if (array.length > 2) {\n    return dateStrings;\n  }\n\n  if (/:/.test(array[0])) {\n    timeString = array[0];\n  } else {\n    dateStrings.date = array[0];\n    timeString = array[1];\n    if (patterns.timeZoneDelimiter.test(dateStrings.date)) {\n      dateStrings.date = dateString.split(patterns.timeZoneDelimiter)[0];\n      timeString = dateString.substr(\n        dateStrings.date.length,\n        dateString.length,\n      );\n    }\n  }\n\n  if (timeString) {\n    const token = patterns.timezone.exec(timeString);\n    if (token) {\n      dateStrings.time = timeString.replace(token[1], \"\");\n      dateStrings.timezone = token[1];\n    } else {\n      dateStrings.time = timeString;\n    }\n  }\n\n  return dateStrings;\n}\n\nfunction parseYear(dateString, additionalDigits) {\n  const regex = new RegExp(\n    \"^(?:(\\\\d{4}|[+-]\\\\d{\" +\n      (4 + additionalDigits) +\n      \"})|(\\\\d{2}|[+-]\\\\d{\" +\n      (2 + additionalDigits) +\n      \"})$)\",\n  );\n\n  const captures = dateString.match(regex);\n  // Invalid ISO-formatted year\n  if (!captures) return { year: NaN, restDateString: \"\" };\n\n  const year = captures[1] ? parseInt(captures[1]) : null;\n  const century = captures[2] ? parseInt(captures[2]) : null;\n\n  // either year or century is null, not both\n  return {\n    year: century === null ? year : century * 100,\n    restDateString: dateString.slice((captures[1] || captures[2]).length),\n  };\n}\n\nfunction parseDate(dateString, year) {\n  // Invalid ISO-formatted year\n  if (year === null) return new Date(NaN);\n\n  const captures = dateString.match(dateRegex);\n  // Invalid ISO-formatted string\n  if (!captures) return new Date(NaN);\n\n  const isWeekDate = !!captures[4];\n  const dayOfYear = parseDateUnit(captures[1]);\n  const month = parseDateUnit(captures[2]) - 1;\n  const day = parseDateUnit(captures[3]);\n  const week = parseDateUnit(captures[4]);\n  const dayOfWeek = parseDateUnit(captures[5]) - 1;\n\n  if (isWeekDate) {\n    if (!validateWeekDate(year, week, dayOfWeek)) {\n      return new Date(NaN);\n    }\n    return dayOfISOWeekYear(year, week, dayOfWeek);\n  } else {\n    const date = new Date(0);\n    if (\n      !validateDate(year, month, day) ||\n      !validateDayOfYearDate(year, dayOfYear)\n    ) {\n      return new Date(NaN);\n    }\n    date.setUTCFullYear(year, month, Math.max(dayOfYear, day));\n    return date;\n  }\n}\n\nfunction parseDateUnit(value) {\n  return value ? parseInt(value) : 1;\n}\n\nfunction parseTime(timeString) {\n  const captures = timeString.match(timeRegex);\n  if (!captures) return NaN; // Invalid ISO-formatted time\n\n  const hours = parseTimeUnit(captures[1]);\n  const minutes = parseTimeUnit(captures[2]);\n  const seconds = parseTimeUnit(captures[3]);\n\n  if (!validateTime(hours, minutes, seconds)) {\n    return NaN;\n  }\n\n  return (\n    hours * millisecondsInHour + minutes * millisecondsInMinute + seconds * 1000\n  );\n}\n\nfunction parseTimeUnit(value) {\n  return (value && parseFloat(value.replace(\",\", \".\"))) || 0;\n}\n\nfunction parseTimezone(timezoneString) {\n  if (timezoneString === \"Z\") return 0;\n\n  const captures = timezoneString.match(timezoneRegex);\n  if (!captures) return 0;\n\n  const sign = captures[1] === \"+\" ? -1 : 1;\n  const hours = parseInt(captures[2]);\n  const minutes = (captures[3] && parseInt(captures[3])) || 0;\n\n  if (!validateTimezone(hours, minutes)) {\n    return NaN;\n  }\n\n  return sign * (hours * millisecondsInHour + minutes * millisecondsInMinute);\n}\n\nfunction dayOfISOWeekYear(isoWeekYear, week, day) {\n  const date = new Date(0);\n  date.setUTCFullYear(isoWeekYear, 0, 4);\n  const fourthOfJanuaryDay = date.getUTCDay() || 7;\n  const diff = (week - 1) * 7 + day + 1 - fourthOfJanuaryDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}\n\n// Validation functions\n\n// February is null to handle the leap year (using ||)\nconst daysInMonths = [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\n\nfunction isLeapYearIndex(year) {\n  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\n\nfunction validateDate(year, month, date) {\n  return (\n    month >= 0 &&\n    month <= 11 &&\n    date >= 1 &&\n    date <= (daysInMonths[month] || (isLeapYearIndex(year) ? 29 : 28))\n  );\n}\n\nfunction validateDayOfYearDate(year, dayOfYear) {\n  return dayOfYear >= 1 && dayOfYear <= (isLeapYearIndex(year) ? 366 : 365);\n}\n\nfunction validateWeekDate(_year, week, day) {\n  return week >= 1 && week <= 53 && day >= 0 && day <= 6;\n}\n\nfunction validateTime(hours, minutes, seconds) {\n  if (hours === 24) {\n    return minutes === 0 && seconds === 0;\n  }\n\n  return (\n    seconds >= 0 &&\n    seconds < 60 &&\n    minutes >= 0 &&\n    minutes < 60 &&\n    hours >= 0 &&\n    hours < 25\n  );\n}\n\nfunction validateTimezone(_hours, minutes) {\n  return minutes >= 0 && minutes <= 59;\n}\n\n// Fallback for modularized imports:\nexport default parseISO;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link parseJSON} function options.\n */\n\n/**\n * Converts a complete ISO date string in UTC time, the typical format for transmitting\n * a date in JSON, to a JavaScript `Date` instance.\n *\n * This is a minimal implementation for converting dates retrieved from a JSON API to\n * a `Date` instance which can be used with other functions in the `date-fns` library.\n * The following formats are supported:\n *\n * - `2000-03-15T05:20:10.123Z`: The output of `.toISOString()` and `JSON.stringify(new Date())`\n * - `2000-03-15T05:20:10Z`: Without milliseconds\n * - `2000-03-15T05:20:10+00:00`: With a zero offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+05:45`: With a positive or negative offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+0000`: With a zero offset without a colon\n * - `2000-03-15T05:20:10`: Without a trailing 'Z' symbol\n * - `2000-03-15T05:20:10.1234567`: Up to 7 digits in milliseconds field. Only first 3 are taken into account since JS does not allow fractional milliseconds\n * - `2000-03-15 05:20:10`: With a space instead of a 'T' separator for APIs returning a SQL date without reformatting\n *\n * For convenience and ease of use these other input types are also supported\n * via [toDate](https://date-fns.org/docs/toDate):\n *\n * - A `Date` instance will be cloned\n * - A `number` will be treated as a timestamp\n *\n * Any other input type or invalid date strings will return an `Invalid Date`.\n *\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dateStr - A fully formed ISO8601 date string to convert\n * @param options - An object with options\n *\n * @returns The parsed date in the local time zone\n */\nexport function parseJSON(dateStr, options) {\n  const parts = dateStr.match(\n    /(\\d{4})-(\\d{2})-(\\d{2})[T ](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{0,7}))?(?:Z|(.)(\\d{2}):?(\\d{2})?)?/,\n  );\n\n  if (!parts) return toDate(NaN, options?.in);\n\n  return toDate(\n    Date.UTC(\n      +parts[1],\n      +parts[2] - 1,\n      +parts[3],\n      +parts[4] - (+parts[9] || 0) * (parts[8] == \"-\" ? -1 : 1),\n      +parts[5] - (+parts[10] || 0) * (parts[8] == \"-\" ? -1 : 1),\n      +parts[6],\n      +((parts[7] || \"0\") + \"00\").substring(0, 3),\n    ),\n    options?.in,\n  );\n}\n\n// Fallback for modularized imports:\nexport default parseJSON;\n", "import { getDay } from \"./getDay.js\";\nimport { subDays } from \"./subDays.js\";\n\n/**\n * The {@link previousDay} function options.\n */\n\n/**\n * @name previousDay\n * @category Weekday Helpers\n * @summary When is the previous day of the week?\n *\n * @description\n * When is the previous day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to check\n * @param day - The day of the week\n * @param options - An object with options\n *\n * @returns The date is the previous day of week\n *\n * @example\n * // When is the previous Monday before Mar, 20, 2020?\n * const result = previousDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 16 2020 00:00:00\n *\n * @example\n * // When is the previous Tuesday before Mar, 21, 2020?\n * const result = previousDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 17 2020 00:00:00\n */\nexport function previousDay(date, day, options) {\n  let delta = getDay(date, options) - day;\n  if (delta <= 0) delta += 7;\n\n  return subDays(date, delta, options);\n}\n\n// Fallback for modularized imports:\nexport default previousDay;\n", "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousFriday} function options.\n */\n\n/**\n * @name previousFriday\n * @category Weekday Helpers\n * @summary When is the previous Friday?\n *\n * @description\n * When is the previous Friday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [UTCDate](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - The options\n *\n * @returns The previous Friday\n *\n * @example\n * // When is the previous Friday before Jun, 19, 2021?\n * const result = previousFriday(new Date(2021, 5, 19))\n * //=> Fri June 18 2021 00:00:00\n */\nexport function previousFriday(date, options) {\n  return previousDay(date, 5, options);\n}\n\n// Fallback for modularized imports:\nexport default previousFriday;\n", "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousMonday} function options.\n */\n\n/**\n * @name previousMonday\n * @category Weekday Helpers\n * @summary When is the previous Monday?\n *\n * @description\n * When is the previous Monday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The previous Monday\n *\n * @example\n * // When is the previous Monday before Jun, 18, 2021?\n * const result = previousMonday(new Date(2021, 5, 18))\n * //=> Mon June 14 2021 00:00:00\n */\nexport function previousMonday(date, options) {\n  return previousDay(date, 1, options);\n}\n\n// Fallback for modularized imports:\nexport default previousMonday;\n", "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousSaturday} function options.\n */\n\n/**\n * @name previousSaturday\n * @category Weekday Helpers\n * @summary When is the previous Saturday?\n *\n * @description\n * When is the previous Saturday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - The options\n *\n * @returns The previous Saturday\n *\n * @example\n * // When is the previous Saturday before Jun, 20, 2021?\n * const result = previousSaturday(new Date(2021, 5, 20))\n * //=> Sat June 19 2021 00:00:00\n */\nexport function previousSaturday(date, options) {\n  return previousDay(date, 6, options);\n}\n\n// Fallback for modularized imports:\nexport default previousSaturday;\n", "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousSunday} function options.\n */\n\n/**\n * @name previousSunday\n * @category Weekday Helpers\n * @summary When is the previous Sunday?\n *\n * @description\n * When is the previous Sunday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - The options\n *\n * @returns The previous Sunday\n *\n * @example\n * // When is the previous Sunday before Jun, 21, 2021?\n * const result = previousSunday(new Date(2021, 5, 21))\n * //=> Sun June 20 2021 00:00:00\n */\nexport function previousSunday(date, options) {\n  return previousDay(date, 0, options);\n}\n\n// Fallback for modularized imports:\nexport default previousSunday;\n", "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousThursday} function options.\n */\n\n/**\n * @name previousThursday\n * @category Weekday Helpers\n * @summary When is the previous Thursday?\n *\n * @description\n * When is the previous Thursday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The previous Thursday\n *\n * @example\n * // When is the previous Thursday before Jun, 18, 2021?\n * const result = previousThursday(new Date(2021, 5, 18))\n * //=> Thu June 17 2021 00:00:00\n */\nexport function previousThursday(date, options) {\n  return previousDay(date, 4, options);\n}\n\n// Fallback for modularized imports:\nexport default previousThursday;\n", "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousTuesday} function options.\n */\n\n/**\n * @name previousTuesday\n * @category Weekday Helpers\n * @summary When is the previous Tuesday?\n *\n * @description\n * When is the previous Tuesday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The previous Tuesday\n *\n * @example\n * // When is the previous Tuesday before Jun, 18, 2021?\n * const result = previousTuesday(new Date(2021, 5, 18))\n * //=> Tue June 15 2021 00:00:00\n */\nexport function previousTuesday(date, options) {\n  return previousDay(date, 2, options);\n}\n\n// Fallback for modularized imports:\nexport default previousTuesday;\n", "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousWednesday} function options.\n */\n\n/**\n * @name previousWednesday\n * @category Weekday Helpers\n * @summary When is the previous Wednesday?\n *\n * @description\n * When is the previous Wednesday?\n *\n * @typeParam DateType - The Date type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [UTCDate](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The previous Wednesday\n *\n * @example\n * // When is the previous Wednesday before Jun, 18, 2021?\n * const result = previousWednesday(new Date(2021, 5, 18))\n * //=> Wed June 16 2021 00:00:00\n */\nexport function previousWednesday(date, options) {\n  return previousDay(date, 3, options);\n}\n\n// Fallback for modularized imports:\nexport default previousWednesday;\n", "import { monthsInQuarter } from \"./constants.js\";\n\n/**\n * @name quartersToMonths\n * @category Conversion Helpers\n * @summary Convert number of quarters to months.\n *\n * @description\n * Convert a number of quarters to a full number of months.\n *\n * @param quarters - The number of quarters to be converted\n *\n * @returns The number of quarters converted in months\n *\n * @example\n * // Convert 2 quarters to months\n * const result = quartersToMonths(2)\n * //=> 6\n */\nexport function quartersToMonths(quarters) {\n  return Math.trunc(quarters * monthsInQuarter);\n}\n\n// Fallback for modularized imports:\nexport default quartersToMonths;\n", "import { quartersInYear } from \"./constants.js\";\n\n/**\n * @name quartersToYears\n * @category Conversion Helpers\n * @summary Convert number of quarters to years.\n *\n * @description\n * Convert a number of quarters to a full number of years.\n *\n * @param quarters - The number of quarters to be converted\n *\n * @returns The number of quarters converted in years\n *\n * @example\n * // Convert 8 quarters to years\n * const result = quartersToYears(8)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = quartersToYears(11)\n * //=> 2\n */\nexport function quartersToYears(quarters) {\n  const years = quarters / quartersInYear;\n  return Math.trunc(years);\n}\n\n// Fallback for modularized imports:\nexport default quartersToYears;\n", "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link roundToNearestHours} function options.\n */\n\n/**\n * @name roundToNearestHours\n * @category Hour Helpers\n * @summary Rounds the given date to the nearest hour\n *\n * @description\n * Rounds the given date to the nearest hour (or number of hours).\n * Rounds up when the given date is exactly between the nearest round hours.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to round\n * @param options - An object with options.\n *\n * @returns The new date rounded to the closest hour\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56))\n * //=> Thu Jul 10 2014 13:00:00\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest half hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { nearestTo: 6 })\n * //=> Thu Jul 10 2014 12:00:00\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest half hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { nearestTo: 8 })\n * //=> Thu Jul 10 2014 16:00:00\n *\n * @example\n * // Floor (rounds down) 10 July 2014 12:34:56 to nearest hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 1, 23, 45), { roundingMethod: 'ceil' })\n * //=> Thu Jul 10 2014 02:00:00\n *\n * @example\n * // Ceil (rounds up) 10 July 2014 12:34:56 to nearest quarter hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { roundingMethod: 'floor', nearestTo: 8 })\n * //=> Thu Jul 10 2014 08:00:00\n */\nexport function roundToNearestHours(date, options) {\n  const nearestTo = options?.nearestTo ?? 1;\n\n  if (nearestTo < 1 || nearestTo > 12)\n    return constructFrom(options?.in || date, NaN);\n\n  const date_ = toDate(date, options?.in);\n  const fractionalMinutes = date_.getMinutes() / 60;\n  const fractionalSeconds = date_.getSeconds() / 60 / 60;\n  const fractionalMilliseconds = date_.getMilliseconds() / 1000 / 60 / 60;\n  const hours =\n    date_.getHours() +\n    fractionalMinutes +\n    fractionalSeconds +\n    fractionalMilliseconds;\n\n  const method = options?.roundingMethod ?? \"round\";\n  const roundingMethod = getRoundingMethod(method);\n\n  const roundedHours = roundingMethod(hours / nearestTo) * nearestTo;\n\n  date_.setHours(roundedHours, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default roundToNearestHours;\n", "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link roundToNearestMinutes} function options.\n */\n\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to round\n * @param options - An object with options.\n *\n * @returns The new date rounded to the closest minute\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest quarter hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * //=> Thu Jul 10 2014 12:15:00\n *\n * @example\n * // Floor (rounds down) 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'floor' })\n * //=> Thu Jul 10 2014 12:12:00\n *\n * @example\n * // Ceil (rounds up) 10 July 2014 12:12:34 to nearest half hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'ceil', nearestTo: 30 })\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function roundToNearestMinutes(date, options) {\n  const nearestTo = options?.nearestTo ?? 1;\n\n  if (nearestTo < 1 || nearestTo > 30) return constructFrom(date, NaN);\n\n  const date_ = toDate(date, options?.in);\n  const fractionalSeconds = date_.getSeconds() / 60;\n  const fractionalMilliseconds = date_.getMilliseconds() / 1000 / 60;\n  const minutes =\n    date_.getMinutes() + fractionalSeconds + fractionalMilliseconds;\n\n  const method = options?.roundingMethod ?? \"round\";\n  const roundingMethod = getRoundingMethod(method);\n\n  const roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;\n\n  date_.setMinutes(roundedMinutes, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default roundToNearestMinutes;\n", "import { secondsInHour } from \"./constants.js\";\n\n/**\n * @name secondsToHours\n * @category Conversion Helpers\n * @summary Convert seconds to hours.\n *\n * @description\n * Convert a number of seconds to a full number of hours.\n *\n * @param seconds - The number of seconds to be converted\n *\n * @returns The number of seconds converted in hours\n *\n * @example\n * // Convert 7200 seconds into hours\n * const result = secondsToHours(7200)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToHours(7199)\n * //=> 1\n */\nexport function secondsToHours(seconds) {\n  const hours = seconds / secondsInHour;\n  return Math.trunc(hours);\n}\n\n// Fallback for modularized imports:\nexport default secondsToHours;\n", "import { millisecondsInSecond } from \"./constants.js\";\n\n/**\n * @name secondsToMilliseconds\n * @category Conversion Helpers\n * @summary Convert seconds to milliseconds.\n *\n * @description\n * Convert a number of seconds to a full number of milliseconds.\n *\n * @param seconds - The number of seconds to be converted\n *\n * @returns The number of seconds converted in milliseconds\n *\n * @example\n * // Convert 2 seconds into milliseconds\n * const result = secondsToMilliseconds(2)\n * //=> 2000\n */\nexport function secondsToMilliseconds(seconds) {\n  return seconds * millisecondsInSecond;\n}\n\n// Fallback for modularized imports:\nexport default secondsToMilliseconds;\n", "import { secondsInMinute } from \"./constants.js\";\n\n/**\n * @name secondsToMinutes\n * @category Conversion Helpers\n * @summary Convert seconds to minutes.\n *\n * @description\n * Convert a number of seconds to a full number of minutes.\n *\n * @param seconds - The number of seconds to be converted\n *\n * @returns The number of seconds converted in minutes\n *\n * @example\n * // Convert 120 seconds into minutes\n * const result = secondsToMinutes(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToMinutes(119)\n * //=> 1\n */\nexport function secondsToMinutes(seconds) {\n  const minutes = seconds / secondsInMinute;\n  return Math.trunc(minutes);\n}\n\n// Fallback for modularized imports:\nexport default secondsToMinutes;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { setMonth } from \"./setMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link set} function options.\n */\n\n/**\n * @name set\n * @category Common Helpers\n * @summary Set date values to a given date.\n *\n * @description\n * Set date values to a given date.\n *\n * Sets time values to date from object `values`.\n * A value is not set if it is undefined or null or doesn't exist in `values`.\n *\n * Note about bundle size: `set` does not internally use `setX` functions from date-fns but instead opts\n * to use native `Date#setX` methods. If you use this function, you may not want to include the\n * other `setX` functions that date-fns provides if you are concerned about the bundle size.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param values - The date values to be set\n * @param options - The options\n *\n * @returns The new date with options set\n *\n * @example\n * // Transform 1 September 2014 into 20 October 2015 in a single line:\n * const result = set(new Date(2014, 8, 20), { year: 2015, month: 9, date: 20 })\n * //=> Tue Oct 20 2015 00:00:00\n *\n * @example\n * // Set 12 PM to 1 September 2014 01:23:45 to 1 September 2014 12:00:00:\n * const result = set(new Date(2014, 8, 1, 1, 23, 45), { hours: 12 })\n * //=> Mon Sep 01 2014 12:23:45\n */\nexport function set(date, values, options) {\n  let _date = toDate(date, options?.in);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(+_date)) return constructFrom(options?.in || date, NaN);\n\n  if (values.year != null) _date.setFullYear(values.year);\n  if (values.month != null) _date = setMonth(_date, values.month);\n  if (values.date != null) _date.setDate(values.date);\n  if (values.hours != null) _date.setHours(values.hours);\n  if (values.minutes != null) _date.setMinutes(values.minutes);\n  if (values.seconds != null) _date.setSeconds(values.seconds);\n  if (values.milliseconds != null) _date.setMilliseconds(values.milliseconds);\n\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default set;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setDayOfYear} function options.\n */\n\n/**\n * @name setDayOfYear\n * @category Day Helpers\n * @summary Set the day of the year to the given date.\n *\n * @description\n * Set the day of the year to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param dayOfYear - The day of the year of the new date\n * @param options - An object with options\n *\n * @returns The new date with the day of the year set\n *\n * @example\n * // Set the 2nd day of the year to 2 July 2014:\n * const result = setDayOfYear(new Date(2014, 6, 2), 2)\n * //=> Thu Jan 02 2014 00:00:00\n */\nexport function setDayOfYear(date, dayOfYear, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setMonth(0);\n  date_.setDate(dayOfYear);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default setDayOfYear;\n", "import {\n  getDefaultOptions,\n  setDefaultOptions as setInternalDefaultOptions,\n} from \"./_lib/defaultOptions.js\";\n\n/**\n * @name setDefaultOptions\n * @category Common Helpers\n * @summary Set default options including locale.\n * @pure false\n *\n * @description\n * Sets the defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * @param options - An object with options\n *\n * @example\n * // Set global locale:\n * import { es } from 'date-fns/locale'\n * setDefaultOptions({ locale: es })\n * const result = format(new Date(2014, 8, 2), 'PPPP')\n * //=> 'martes, 2 de septiembre de 2014'\n *\n * @example\n * // Start of the week for 2 September 2014:\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Start of the week for 2 September 2014,\n * // when we set that week starts on Monday by default:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Mon Sep 01 2014 00:00:00\n *\n * @example\n * // Manually set options take priority over default options:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2), { weekStartsOn: 0 })\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Remove the option by setting it to `undefined`:\n * setDefaultOptions({ weekStartsOn: 1 })\n * setDefaultOptions({ weekStartsOn: undefined })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n */\nexport function setDefaultOptions(options) {\n  const result = {};\n  const defaultOptions = getDefaultOptions();\n\n  for (const property in defaultOptions) {\n    if (Object.prototype.hasOwnProperty.call(defaultOptions, property)) {\n      // [TODO] I challenge you to fix the type\n      result[property] = defaultOptions[property];\n    }\n  }\n\n  for (const property in options) {\n    if (Object.prototype.hasOwnProperty.call(options, property)) {\n      if (options[property] === undefined) {\n        // [TODO] I challenge you to fix the type\n        delete result[property];\n      } else {\n        // [TODO] I challenge you to fix the type\n        result[property] = options[property];\n      }\n    }\n  }\n\n  setInternalDefaultOptions(result);\n}\n\n// Fallback for modularized imports:\nexport default setDefaultOptions;\n", "import { setMonth } from \"./setMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setQuarter} function options.\n */\n\n/**\n * @name setQuarter\n * @category Quarter Helpers\n * @summary Set the year quarter to the given date.\n *\n * @description\n * Set the year quarter to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param quarter - The quarter of the new date\n * @param options - The options\n *\n * @returns The new date with the quarter set\n *\n * @example\n * // Set the 2nd quarter to 2 July 2014:\n * const result = setQuarter(new Date(2014, 6, 2), 2)\n * //=> Wed Apr 02 2014 00:00:00\n */\nexport function setQuarter(date, quarter, options) {\n  const date_ = toDate(date, options?.in);\n  const oldQuarter = Math.trunc(date_.getMonth() / 3) + 1;\n  const diff = quarter - oldQuarter;\n  return setMonth(date_, date_.getMonth() + diff * 3);\n}\n\n// Fallback for modularized imports:\nexport default setQuarter;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { startOfWeekYear } from \"./startOfWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setWeekYear} function options.\n */\n\n/**\n * @name setWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Set the local week-numbering year to the given date.\n *\n * @description\n * Set the local week-numbering year to the given date,\n * saving the week number and the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param weekYear - The local week-numbering year of the new date\n * @param options - An object with options\n *\n * @returns The new date with the local week-numbering year set\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010 with default options:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004)\n * //=> Sat Jan 03 2004 00:00:00\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010,\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport function setWeekYear(date, weekYear, options) {\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const diff = differenceInCalendarDays(\n    toDate(date, options?.in),\n    startOfWeekYear(date, options),\n    options,\n  );\n\n  const firstWeek = constructFrom(options?.in || date, 0);\n  firstWeek.setFullYear(weekYear, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n\n  const date_ = startOfWeekYear(firstWeek, options);\n  date_.setDate(date_.getDate() + diff);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default setWeekYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfDecade} options.\n */\n\n/**\n * @name startOfDecade\n * @category Decade Helpers\n * @summary Return the start of a decade for the given date.\n *\n * @description\n * Return the start of a decade for the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a decade\n *\n * @example\n * // The start of a decade for 21 October 2015 00:00:00:\n * const result = startOfDecade(new Date(2015, 9, 21, 00, 00, 00))\n * //=> Jan 01 2010 00:00:00\n */\nexport function startOfDecade(date, options) {\n  // TODO: Switch to more technical definition in of decades that start with 1\n  // end with 0. I.e. 2001-2010 instead of current 2000-2009. It's a breaking\n  // change, so it can only be done in 4.0.\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = Math.floor(year / 10) * 10;\n  _date.setFullYear(decade, 0, 1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfDecade;\n", "import { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link startOfToday} function options.\n */\n\n/**\n * @name startOfToday\n * @category Day Helpers\n * @summary Return the start of today.\n * @pure false\n *\n * @description\n * Return the start of today.\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @returns The start of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfToday()\n * //=> Mon Oct 6 2014 00:00:00\n */\nexport function startOfToday(options) {\n  return startOfDay(Date.now(), options);\n}\n\n// Fallback for modularized imports:\nexport default startOfToday;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link startOfTomorrow} function options.\n */\n\n/**\n * @name startOfTomorrow\n * @category Day Helpers\n * @summary Return the start of tomorrow.\n * @pure false\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @returns The start of tomorrow\n *\n * @description\n * Return the start of tomorrow.\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfTomorrow()\n * //=> Tue Oct 7 2014 00:00:00\n */\nexport function startOfTomorrow(options) {\n  const now = constructNow(options?.in);\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n\n  const date = constructFrom(options?.in, 0);\n  date.setFullYear(year, month, day + 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfTomorrow;\n", "import { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link startOfYesterday} function options.\n */\n\n/**\n * @name startOfYesterday\n * @category Day Helpers\n * @summary Return the start of yesterday.\n * @pure false\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @description\n * Return the start of yesterday.\n *\n * @returns The start of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfYesterday()\n * //=> Sun Oct 5 2014 00:00:00\n */\nexport function startOfYesterday(options) {\n  const now = constructNow(options?.in);\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n\n  const date = constructNow(options?.in);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfYesterday;\n", "import { addMonths } from \"./addMonths.js\";\n\n/**\n * The subMonths function options.\n */\n\n/**\n * @name subMonths\n * @category Month Helpers\n * @summary Subtract the specified number of months from the given date.\n *\n * @description\n * Subtract the specified number of months from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the months subtracted\n *\n * @example\n * // Subtract 5 months from 1 February 2015:\n * const result = subMonths(new Date(2015, 1, 1), 5)\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function subMonths(date, amount, options) {\n  return addMonths(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMonths;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { subDays } from \"./subDays.js\";\nimport { subMonths } from \"./subMonths.js\";\n\n/**\n * The {@link sub} function options.\n */\n\n/**\n * @name sub\n * @category Common Helpers\n * @summary Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @description\n * Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param duration - The object with years, months, weeks, days, hours, minutes and seconds to be subtracted\n * @param options - An object with options\n *\n * | Key     | Description                        |\n * |---------|------------------------------------|\n * | years   | Amount of years to be subtracted   |\n * | months  | Amount of months to be subtracted  |\n * | weeks   | Amount of weeks to be subtracted   |\n * | days    | Amount of days to be subtracted    |\n * | hours   | Amount of hours to be subtracted   |\n * | minutes | Amount of minutes to be subtracted |\n * | seconds | Amount of seconds to be subtracted |\n *\n * All values default to 0\n *\n * @returns The new date with the seconds subtracted\n *\n * @example\n * // Subtract the following duration from 15 June 2017 15:29:20\n * const result = sub(new Date(2017, 5, 15, 15, 29, 20), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> Mon Sep 1 2014 10:19:50\n */\nexport function sub(date, duration, options) {\n  const {\n    years = 0,\n    months = 0,\n    weeks = 0,\n    days = 0,\n    hours = 0,\n    minutes = 0,\n    seconds = 0,\n  } = duration;\n\n  const withoutMonths = subMonths(date, months + years * 12, options);\n  const withoutDays = subDays(withoutMonths, days + weeks * 7, options);\n\n  const minutesToSub = minutes + hours * 60;\n  const secondsToSub = seconds + minutesToSub * 60;\n  const msToSub = secondsToSub * 1000;\n\n  return constructFrom(options?.in || date, +withoutDays - msToSub);\n}\n\n// Fallback for modularized imports:\nexport default sub;\n", "import { addBusinessDays } from \"./addBusinessDays.js\";\n\n/**\n * The {@link subBusinessDays} function options.\n */\n\n/**\n * @name subBusinessDays\n * @category Day Helpers\n * @summary Subtract the specified number of business days (mon - fri) from the given date.\n *\n * @description\n * Subtract the specified number of business days (mon - fri) from the given date, ignoring weekends.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of business days to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the business days subtracted\n *\n * @example\n * // Subtract 10 business days from 1 September 2014:\n * const result = subBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Aug 18 2014 00:00:00 (skipped weekend days)\n */\nexport function subBusinessDays(date, amount, options) {\n  return addBusinessDays(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subBusinessDays;\n", "import { addHours } from \"./addHours.js\";\n\n/**\n * The {@link subHours} function options.\n */\n\n/**\n * @name subHours\n * @category Hour Helpers\n * @summary Subtract the specified number of hours from the given date.\n *\n * @description\n * Subtract the specified number of hours from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of hours to be subtracted.\n * @param options - The options\n *\n * @returns The new date with the hours subtracted\n *\n * @example\n * // Subtract 2 hours from 11 July 2014 01:00:00:\n * const result = subHours(new Date(2014, 6, 11, 1, 0), 2)\n * //=> Thu Jul 10 2014 23:00:00\n */\nexport function subHours(date, amount, options) {\n  return addHours(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subHours;\n", "import { addMilliseconds } from \"./addMilliseconds.js\";\n\n/**\n * The {@link subMilliseconds} function options.\n */\n\n/**\n * Subtract the specified number of milliseconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of milliseconds to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the milliseconds subtracted\n */\nexport function subMilliseconds(date, amount, options) {\n  return addMilliseconds(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMilliseconds;\n", "import { addMinutes } from \"./addMinutes.js\";\n\n/**\n * The {@link subMinutes} function options.\n */\n\n/**\n * @name subMinutes\n * @category Minute Helpers\n * @summary Subtract the specified number of minutes from the given date.\n *\n * @description\n * Subtract the specified number of minutes from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of minutes to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the minutes subtracted\n *\n * @example\n * // Subtract 30 minutes from 10 July 2014 12:00:00:\n * const result = subMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 11:30:00\n */\nexport function subMinutes(date, amount, options) {\n  return addMinutes(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMinutes;\n", "import { addQuarters } from \"./addQuarters.js\";\n\n/**\n * The {@link subQuarters} function options.\n */\n\n/**\n * @name subQuarters\n * @category Quarter Helpers\n * @summary Subtract the specified number of year quarters from the given date.\n *\n * @description\n * Subtract the specified number of year quarters from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of quarters to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the quarters subtracted\n *\n * @example\n * // Subtract 3 quarters from 1 September 2014:\n * const result = subQuarters(new Date(2014, 8, 1), 3)\n * //=> Sun Dec 01 2013 00:00:00\n */\nexport function subQuarters(date, amount, options) {\n  return addQuarters(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subQuarters;\n", "import { addSeconds } from \"./addSeconds.js\";\n\n/**\n * The {@link subSeconds} function options.\n */\n\n/**\n * Subtract the specified number of seconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of seconds to be subtracted.\n * @param options - The options\n *\n * @returns The new date with the seconds subtracted\n *\n * @example\n * // Subtract 30 seconds from 10 July 2014 12:45:00:\n * const result = subSeconds(new Date(2014, 6, 10, 12, 45, 0), 30)\n * //=> Thu Jul 10 2014 12:44:30\n */\nexport function subSeconds(date, amount, options) {\n  return addSeconds(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subSeconds;\n", "import { addWeeks } from \"./addWeeks.js\";\n\n/**\n * The {@link subWeeks} function options.\n */\n\n/**\n * @name subWeeks\n * @category Week Helpers\n * @summary Subtract the specified number of weeks from the given date.\n *\n * @description\n * Subtract the specified number of weeks from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of weeks to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the weeks subtracted\n *\n * @example\n * // Subtract 4 weeks from 1 September 2014:\n * const result = subWeeks(new Date(2014, 8, 1), 4)\n * //=> Mon Aug 04 2014 00:00:00\n */\nexport function subWeeks(date, amount, options) {\n  return addWeeks(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subWeeks;\n", "import { addYears } from \"./addYears.js\";\n\n/**\n * The {@link subYears} function options.\n */\n\n/**\n * @name subYears\n * @category Year Helpers\n * @summary Subtract the specified number of years from the given date.\n *\n * @description\n * Subtract the specified number of years from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the years subtracted\n *\n * @example\n * // Subtract 5 years from 1 September 2014:\n * const result = subYears(new Date(2014, 8, 1), 5)\n * //=> Tue Sep 01 2009 00:00:00\n */\nexport function subYears(date, amount, options) {\n  return addYears(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subYears;\n", "import { daysInWeek } from \"./constants.js\";\n\n/**\n * @name weeksToDays\n * @category Conversion Helpers\n * @summary Convert weeks to days.\n *\n * @description\n * Convert a number of weeks to a full number of days.\n *\n * @param weeks - The number of weeks to be converted\n *\n * @returns The number of weeks converted in days\n *\n * @example\n * // Convert 2 weeks into days\n * const result = weeksToDays(2)\n * //=> 14\n */\nexport function weeksToDays(weeks) {\n  return Math.trunc(weeks * daysInWeek);\n}\n\n// Fallback for modularized imports:\nexport default weeksToDays;\n", "import { daysInYear } from \"./constants.js\";\n\n/**\n * @name yearsToDays\n * @category Conversion Helpers\n * @summary Convert years to days.\n *\n * @description\n * Convert a number of years to a full number of days.\n *\n * @param years - The number of years to be converted\n *\n * @returns The number of years converted in days\n *\n * @example\n * // Convert 2 years into days\n * const result = yearsToDays(2)\n * //=> 730\n */\nexport function yearsToDays(years) {\n  return Math.trunc(years * daysInYear);\n}\n\n// Fallback for modularized imports:\nexport default yearsToDays;\n", "import { monthsInYear } from \"./constants.js\";\n\n/**\n * @name yearsToMonths\n * @category Conversion Helpers\n * @summary Convert years to months.\n *\n * @description\n * Convert a number of years to a full number of months.\n *\n * @param years - The number of years to be converted\n *\n * @returns The number of years converted in months\n *\n * @example\n * // Convert 2 years into months\n * const result = yearsToMonths(2)\n * //=> 24\n */\nexport function yearsToMonths(years) {\n  return Math.trunc(years * monthsInYear);\n}\n\n// Fallback for modularized imports:\nexport default yearsToMonths;\n", "import { quartersInYear } from \"./constants.js\";\n\n/**\n * @name yearsToQuarters\n * @category Conversion Helpers\n * @summary Convert years to quarters.\n *\n * @description\n * Convert a number of years to a full number of quarters.\n *\n * @param years - The number of years to be converted\n *\n * @returns The number of years converted in quarters\n *\n * @example\n * // Convert 2 years to quarters\n * const result = yearsToQuarters(2)\n * //=> 8\n */\nexport function yearsToQuarters(years) {\n  return Math.trunc(years * quartersInYear);\n}\n\n// Fallback for modularized imports:\nexport default yearsToQuarters;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCO,SAAS,IAAI,MAAM,UAAU,SAAS;AAC3C,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,QAAAA,UAAS;AAAA,IACT,QAAQ;AAAA,IACR,MAAAC,QAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,IAAI;AAGJ,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,iBACJD,WAAU,QAAQ,UAAU,OAAOA,UAAS,QAAQ,EAAE,IAAI;AAG5D,QAAM,eACJC,SAAQ,QAAQ,QAAQ,gBAAgBA,QAAO,QAAQ,CAAC,IAAI;AAG9D,QAAM,eAAe,UAAU,QAAQ;AACvC,QAAM,eAAe,UAAU,eAAe;AAC9C,QAAM,UAAU,eAAe;AAE/B,SAAO,eAAc,mCAAS,OAAM,MAAM,CAAC,eAAe,OAAO;AACnE;;;ACzCO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO,MAAM;AAChD;;;ACFO,SAAS,SAAS,MAAM,SAAS;AACtC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO,MAAM;AAChD;;;ACFO,SAAS,UAAU,MAAM,SAAS;AACvC,QAAM,MAAM,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO;AAC7C,SAAO,QAAQ,KAAK,QAAQ;AAC9B;;;ACKO,SAAS,gBAAgB,MAAM,QAAQ,SAAS;AACrD,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,mBAAmB,UAAU,OAAO,OAAO;AAEjD,MAAI,MAAM,MAAM,EAAG,QAAO,cAAc,mCAAS,IAAI,GAAG;AAExD,QAAM,QAAQ,MAAM,SAAS;AAC7B,QAAM,OAAO,SAAS,IAAI,KAAK;AAC/B,QAAM,YAAY,KAAK,MAAM,SAAS,CAAC;AAEvC,QAAM,QAAQ,MAAM,QAAQ,IAAI,YAAY,CAAC;AAG7C,MAAI,WAAW,KAAK,IAAI,SAAS,CAAC;AAGlC,SAAO,WAAW,GAAG;AACnB,UAAM,QAAQ,MAAM,QAAQ,IAAI,IAAI;AACpC,QAAI,CAAC,UAAU,OAAO,OAAO,EAAG,aAAY;AAAA,EAC9C;AAKA,MAAI,oBAAoB,UAAU,OAAO,OAAO,KAAK,WAAW,GAAG;AAGjE,QAAI,WAAW,OAAO,OAAO;AAC3B,YAAM,QAAQ,MAAM,QAAQ,KAAK,OAAO,IAAI,IAAI,GAAG;AACrD,QAAI,SAAS,OAAO,OAAO;AACzB,YAAM,QAAQ,MAAM,QAAQ,KAAK,OAAO,IAAI,IAAI,GAAG;AAAA,EACvD;AAGA,QAAM,SAAS,KAAK;AAEpB,SAAO;AACT;;;ACnCO,SAAS,eAAe,MAAM,UAAU,SAAS;AACtD,MAAI,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACpC,QAAM,OAAO;AAAA,IACX;AAAA,IACA,mBAAmB,OAAO,OAAO;AAAA,EACnC;AACA,QAAM,kBAAkB,eAAc,mCAAS,OAAM,MAAM,CAAC;AAC5D,kBAAgB,YAAY,UAAU,GAAG,CAAC;AAC1C,kBAAgB,SAAS,GAAG,GAAG,GAAG,CAAC;AACnC,UAAQ,mBAAmB,eAAe;AAC1C,QAAM,QAAQ,MAAM,QAAQ,IAAI,IAAI;AACpC,SAAO;AACT;;;AChBO,SAAS,gBAAgB,MAAM,QAAQ,SAAS;AACrD,SAAO,eAAe,MAAM,eAAe,MAAM,OAAO,IAAI,QAAQ,OAAO;AAC7E;;;ACJO,SAAS,YAAY,MAAM,QAAQ,SAAS;AACjD,SAAO,UAAU,MAAM,SAAS,GAAG,OAAO;AAC5C;;;ACuBO,SAAS,wBAAwB,cAAc,eAAe,SAAS;AAC5E,QAAM,CAAC,eAAe,WAAW,IAAI;AAAA,IACnC,CAAC,OAAO,aAAa,OAAO,mCAAS,EAAE;AAAA,IACvC,CAAC,OAAO,aAAa,KAAK,mCAAS,EAAE;AAAA,EACvC,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACtB,QAAM,CAAC,gBAAgB,YAAY,IAAI;AAAA,IACrC,CAAC,OAAO,cAAc,OAAO,mCAAS,EAAE;AAAA,IACxC,CAAC,OAAO,cAAc,KAAK,mCAAS,EAAE;AAAA,EACxC,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAEtB,MAAI,mCAAS;AACX,WAAO,iBAAiB,gBAAgB,kBAAkB;AAE5D,SAAO,gBAAgB,gBAAgB,iBAAiB;AAC1D;;;ACnCO,SAAS,IAAI,OAAO,SAAS;AAClC,MAAI;AACJ,MAAI,UAAU,mCAAS;AAEvB,QAAM,QAAQ,CAAC,SAAS;AAEtB,QAAI,CAAC,WAAW,OAAO,SAAS;AAC9B,gBAAU,cAAc,KAAK,MAAM,IAAI;AAEzC,UAAM,QAAQ,OAAO,MAAM,OAAO;AAClC,QAAI,CAAC,UAAU,SAAS,SAAS,MAAM,CAAC,KAAK,EAAG,UAAS;AAAA,EAC3D,CAAC;AAED,SAAO,cAAc,SAAS,UAAU,GAAG;AAC7C;;;ACdO,SAAS,IAAI,OAAO,SAAS;AAClC,MAAI;AACJ,MAAI,UAAU,mCAAS;AAEvB,QAAM,QAAQ,CAAC,SAAS;AAEtB,QAAI,CAAC,WAAW,OAAO,SAAS;AAC9B,gBAAU,cAAc,KAAK,MAAM,IAAI;AAEzC,UAAM,QAAQ,OAAO,MAAM,OAAO;AAClC,QAAI,CAAC,UAAU,SAAS,SAAS,MAAM,CAAC,KAAK,EAAG,UAAS;AAAA,EAC3D,CAAC;AAED,SAAO,cAAc,SAAS,UAAU,GAAG;AAC7C;;;ACAO,SAAS,MAAM,MAAMC,WAAU,SAAS;AAC7C,QAAM,CAAC,OAAO,OAAO,GAAG,IAAI;AAAA,IAC1B,mCAAS;AAAA,IACT;AAAA,IACAA,UAAS;AAAA,IACTA,UAAS;AAAA,EACX;AAEA,SAAO,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO;AACzD;;;AC7BO,SAAS,eAAe,eAAe,OAAO;AAInD,QAAM,gBAAgB,CAAC,OAAO,aAAa;AAE3C,MAAI,MAAM,aAAa,EAAG,QAAO;AAEjC,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,UAAM,QAAQ,OAAO,IAAI;AAEzB,QAAI,MAAM,CAAC,KAAK,GAAG;AACjB,eAAS;AACT,oBAAc;AACd;AAAA,IACF;AAEA,UAAM,WAAW,KAAK,IAAI,gBAAgB,CAAC,KAAK;AAChD,QAAI,UAAU,QAAQ,WAAW,aAAa;AAC5C,eAAS;AACT,oBAAc;AAAA,IAChB;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;ACZO,SAAS,UAAU,eAAe,OAAO,SAAS;AACvD,QAAM,CAAC,gBAAgB,GAAG,MAAM,IAAI;AAAA,IAClC,mCAAS;AAAA,IACT;AAAA,IACA,GAAG;AAAA,EACL;AAEA,QAAM,QAAQ,eAAe,gBAAgB,MAAM;AAEnD,MAAI,OAAO,UAAU,YAAY,MAAM,KAAK;AAC1C,WAAO,cAAc,gBAAgB,GAAG;AAE1C,MAAI,UAAU,OAAW,QAAO,OAAO,KAAK;AAC9C;;;ACpBO,SAAS,WAAW,UAAU,WAAW;AAC9C,QAAM,OAAO,CAAC,OAAO,QAAQ,IAAI,CAAC,OAAO,SAAS;AAElD,MAAI,OAAO,EAAG,QAAO;AAAA,WACZ,OAAO,EAAG,QAAO;AAG1B,SAAO;AACT;;;ACRO,SAAS,YAAY,UAAU,WAAW;AAC/C,QAAM,OAAO,CAAC,OAAO,QAAQ,IAAI,CAAC,OAAO,SAAS;AAElD,MAAI,OAAO,EAAG,QAAO;AAAA,WACZ,OAAO,EAAG,QAAO;AAG1B,SAAO;AACT;;;ACZO,SAAS,aAAa,MAAM;AACjC,SAAO,cAAc,MAAM,KAAK,IAAI,CAAC;AACvC;;;ACRO,SAAS,YAAYC,OAAM;AAChC,QAAM,SAAS,KAAK,MAAMA,QAAO,UAAU;AAE3C,SAAO,WAAW,IAAI,IAAI;AAC5B;;;ACiCO,SAAS,yBAAyB,WAAW,aAAa,SAAS;AACxE,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,MAAI,CAAC,QAAQ,UAAU,KAAK,CAAC,QAAQ,YAAY,EAAG,QAAO;AAE3D,QAAM,OAAO,yBAAyB,YAAY,YAAY;AAC9D,QAAM,OAAO,OAAO,IAAI,KAAK;AAC7B,QAAM,QAAQ,KAAK,MAAM,OAAO,CAAC;AAEjC,MAAI,SAAS,QAAQ;AACrB,MAAI,aAAa,QAAQ,cAAc,QAAQ,CAAC;AAGhD,SAAO,CAAC,UAAU,YAAY,UAAU,GAAG;AAEzC,cAAU,UAAU,YAAY,OAAO,IAAI,IAAI;AAC/C,iBAAa,QAAQ,YAAY,IAAI;AAAA,EACvC;AAGA,SAAO,WAAW,IAAI,IAAI;AAC5B;;;ACvDO,SAAS,iCACd,WACA,aACA,SACA;AACA,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,SACE,eAAe,YAAY,OAAO,IAAI,eAAe,cAAc,OAAO;AAE9E;;;ACXO,SAAS,6BAA6B,WAAW,aAAa,SAAS;AAC5E,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,QAAM,qBAAqB,eAAe,UAAU;AACpD,QAAM,sBAAsB,eAAe,YAAY;AAEvD,QAAM,gBACJ,CAAC,qBAAqB,gCAAgC,kBAAkB;AAC1E,QAAM,iBACJ,CAAC,sBAAsB,gCAAgC,mBAAmB;AAK5E,SAAO,KAAK,OAAO,gBAAgB,kBAAkB,kBAAkB;AACzE;;;ACxBO,SAAS,2BAA2B,WAAW,aAAa,SAAS;AAC1E,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,QAAM,YAAY,WAAW,YAAY,IAAI,aAAa,YAAY;AACtE,QAAM,aAAa,WAAW,SAAS,IAAI,aAAa,SAAS;AAEjE,SAAO,YAAY,KAAK;AAC1B;;;ACfO,SAAS,WAAW,MAAM,SAAS;AACxC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,UAAU,KAAK,MAAM,MAAM,SAAS,IAAI,CAAC,IAAI;AACnD,SAAO;AACT;;;ACCO,SAAS,6BAA6B,WAAW,aAAa,SAAS;AAC5E,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,QAAM,YAAY,WAAW,YAAY,IAAI,aAAa,YAAY;AACtE,QAAM,eAAe,WAAW,UAAU,IAAI,WAAW,YAAY;AAErE,SAAO,YAAY,IAAI;AACzB;;;ACCO,SAAS,0BAA0B,WAAW,aAAa,SAAS;AACzE,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,QAAM,mBAAmB,YAAY,YAAY,OAAO;AACxD,QAAM,qBAAqB,YAAY,cAAc,OAAO;AAE5D,QAAM,iBACJ,CAAC,mBAAmB,gCAAgC,gBAAgB;AACtE,QAAM,mBACJ,CAAC,qBAAqB,gCAAgC,kBAAkB;AAE1E,SAAO,KAAK,OAAO,iBAAiB,oBAAoB,kBAAkB;AAC5E;;;AC7BO,SAAS,0BAA0B,WAAW,aAAa,SAAS;AACzE,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,SAAO,WAAW,YAAY,IAAI,aAAa,YAAY;AAC7D;;;ACyBO,SAAS,iBAAiB,WAAW,aAAa,SAAS;AAChE,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,QAAM,OAAO,gBAAgB,YAAY,YAAY;AACrD,QAAM,aAAa,KAAK;AAAA,IACtB,yBAAyB,YAAY,YAAY;AAAA,EACnD;AAEA,aAAW,QAAQ,WAAW,QAAQ,IAAI,OAAO,UAAU;AAI3D,QAAM,mBAAmB;AAAA,IACvB,gBAAgB,YAAY,YAAY,MAAM,CAAC;AAAA,EACjD;AAEA,QAAM,SAAS,QAAQ,aAAa;AAEpC,SAAO,WAAW,IAAI,IAAI;AAC5B;AAMA,SAAS,gBAAgB,WAAW,aAAa;AAC/C,QAAM,OACJ,UAAU,YAAY,IAAI,YAAY,YAAY,KAClD,UAAU,SAAS,IAAI,YAAY,SAAS,KAC5C,UAAU,QAAQ,IAAI,YAAY,QAAQ,KAC1C,UAAU,SAAS,IAAI,YAAY,SAAS,KAC5C,UAAU,WAAW,IAAI,YAAY,WAAW,KAChD,UAAU,WAAW,IAAI,YAAY,WAAW,KAChD,UAAU,gBAAgB,IAAI,YAAY,gBAAgB;AAE5D,MAAI,OAAO,EAAG,QAAO;AACrB,MAAI,OAAO,EAAG,QAAO;AAGrB,SAAO;AACT;;;ACxGO,SAAS,kBAAkB,QAAQ;AACxC,SAAO,CAAC,WAAW;AACjB,UAAM,QAAQ,SAAS,KAAK,MAAM,IAAI,KAAK;AAC3C,UAAM,SAAS,MAAM,MAAM;AAE3B,WAAO,WAAW,IAAI,IAAI;AAAA,EAC5B;AACF;;;ACuBO,SAAS,kBAAkB,WAAW,aAAa,SAAS;AACjE,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,aAAa,CAAC,gBAAgB;AAC7C,SAAO,kBAAkB,mCAAS,cAAc,EAAE,IAAI;AACxD;;;ACRO,SAAS,gBAAgB,MAAM,QAAQ,SAAS;AACrD,SAAO,gBAAgB,MAAM,CAAC,QAAQ,OAAO;AAC/C;;;ACCO,SAAS,yBAAyB,WAAW,aAAa,SAAS;AACxE,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,QAAM,OAAO,WAAW,YAAY,YAAY;AAChD,QAAM,OAAO,KAAK;AAAA,IAChB,iCAAiC,YAAY,cAAc,OAAO;AAAA,EACpE;AAEA,QAAM,eAAe,gBAAgB,YAAY,OAAO,MAAM,OAAO;AAErE,QAAM,2BAA2B;AAAA,IAC/B,WAAW,cAAc,YAAY,MAAM,CAAC;AAAA,EAC9C;AACA,QAAM,SAAS,QAAQ,OAAO;AAG9B,SAAO,WAAW,IAAI,IAAI;AAC5B;;;AC9BO,SAAS,yBAAyB,WAAW,aAAa;AAC/D,SAAO,CAAC,OAAO,SAAS,IAAI,CAAC,OAAO,WAAW;AACjD;;;ACYO,SAAS,oBAAoB,UAAU,WAAW,SAAS;AAChE,QAAM,OACJ,yBAAyB,UAAU,SAAS,IAAI;AAClD,SAAO,kBAAkB,mCAAS,cAAc,EAAE,IAAI;AACxD;;;ACpBO,SAAS,iBAAiB,MAAM,SAAS;AAC9C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,SAAO,CAAC,SAAS,OAAO,OAAO,MAAM,CAAC,WAAW,OAAO,OAAO;AACjE;;;ACAO,SAAS,mBAAmB,WAAW,aAAa,SAAS;AAClE,QAAM,CAAC,YAAY,kBAAkB,YAAY,IAAI;AAAA,IACnD,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,OAAO,WAAW,kBAAkB,YAAY;AACtD,QAAM,aAAa,KAAK;AAAA,IACtB,2BAA2B,kBAAkB,YAAY;AAAA,EAC3D;AAEA,MAAI,aAAa,EAAG,QAAO;AAE3B,MAAI,iBAAiB,SAAS,MAAM,KAAK,iBAAiB,QAAQ,IAAI;AACpE,qBAAiB,QAAQ,EAAE;AAE7B,mBAAiB,SAAS,iBAAiB,SAAS,IAAI,OAAO,UAAU;AAEzE,MAAI,qBAAqB,WAAW,kBAAkB,YAAY,MAAM,CAAC;AAEzE,MACE,iBAAiB,UAAU,KAC3B,eAAe,KACf,WAAW,YAAY,YAAY,MAAM,GACzC;AACA,yBAAqB;AAAA,EACvB;AAEA,QAAM,SAAS,QAAQ,aAAa,CAAC;AACrC,SAAO,WAAW,IAAI,IAAI;AAC5B;;;AC/BO,SAAS,qBAAqB,WAAW,aAAa,SAAS;AACpE,QAAM,OAAO,mBAAmB,WAAW,aAAa,OAAO,IAAI;AACnE,SAAO,kBAAkB,mCAAS,cAAc,EAAE,IAAI;AACxD;;;ACCO,SAAS,oBAAoB,WAAW,aAAa,SAAS;AACnE,QAAM,OAAO,yBAAyB,WAAW,WAAW,IAAI;AAChE,SAAO,kBAAkB,mCAAS,cAAc,EAAE,IAAI;AACxD;;;ACcO,SAAS,kBAAkB,WAAW,aAAa,SAAS;AACjE,QAAM,OAAO,iBAAiB,WAAW,aAAa,OAAO,IAAI;AACjE,SAAO,kBAAkB,mCAAS,cAAc,EAAE,IAAI;AACxD;;;ACvBO,SAAS,kBAAkB,WAAW,aAAa,SAAS;AACjE,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAIA,QAAM,OAAO,WAAW,YAAY,YAAY;AAIhD,QAAM,OAAO,KAAK,IAAI,0BAA0B,YAAY,YAAY,CAAC;AAKzE,aAAW,YAAY,IAAI;AAC3B,eAAa,YAAY,IAAI;AAO7B,QAAM,UAAU,WAAW,YAAY,YAAY,MAAM,CAAC;AAE1D,QAAM,SAAS,QAAQ,OAAO,CAAC;AAG/B,SAAO,WAAW,IAAI,IAAI;AAC5B;;;ACzDO,SAAS,kBAAkB,SAASC,WAAU;AACnD,QAAM,CAAC,OAAO,GAAG,IAAI,eAAe,SAASA,UAAS,OAAOA,UAAS,GAAG;AACzE,SAAO,EAAE,OAAO,IAAI;AACtB;;;ACuCO,SAAS,kBAAkBC,WAAU,SAAS;AACnD,QAAM,EAAE,OAAO,IAAI,IAAI,kBAAkB,mCAAS,IAAIA,SAAQ;AAE9D,MAAI,WAAW,CAAC,QAAQ,CAAC;AACzB,QAAM,UAAU,WAAW,CAAC,QAAQ,CAAC;AACrC,QAAM,OAAO,WAAW,MAAM;AAC9B,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAExB,MAAI,QAAO,mCAAS,SAAQ;AAC5B,MAAI,CAAC,KAAM,QAAO,CAAC;AACnB,MAAI,OAAO,GAAG;AACZ,WAAO,CAAC;AACR,eAAW,CAAC;AAAA,EACd;AAEA,QAAM,QAAQ,CAAC;AAEf,SAAO,CAAC,QAAQ,SAAS;AACvB,UAAM,KAAK,cAAc,OAAO,IAAI,CAAC;AACrC,SAAK,QAAQ,KAAK,QAAQ,IAAI,IAAI;AAClC,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B;AAEA,SAAO,WAAW,MAAM,QAAQ,IAAI;AACtC;;;AC3BO,SAAS,mBAAmBC,WAAU,SAAS;AACpD,QAAM,EAAE,OAAO,IAAI,IAAI,kBAAkB,mCAAS,IAAIA,SAAQ;AAE9D,MAAI,WAAW,CAAC,QAAQ,CAAC;AACzB,QAAM,UAAU,WAAW,CAAC,QAAQ,CAAC;AACrC,QAAM,OAAO,WAAW,MAAM;AAC9B,OAAK,WAAW,GAAG,GAAG,CAAC;AAEvB,MAAI,QAAO,mCAAS,SAAQ;AAC5B,MAAI,CAAC,KAAM,QAAO,CAAC;AACnB,MAAI,OAAO,GAAG;AACZ,WAAO,CAAC;AACR,eAAW,CAAC;AAAA,EACd;AAEA,QAAM,QAAQ,CAAC;AAEf,SAAO,CAAC,QAAQ,SAAS;AACvB,UAAM,KAAK,cAAc,OAAO,IAAI,CAAC;AACrC,SAAK,SAAS,KAAK,SAAS,IAAI,IAAI;AAAA,EACtC;AAEA,SAAO,WAAW,MAAM,QAAQ,IAAI;AACtC;;;ACpBO,SAAS,qBAAqBC,WAAU,SAAS;AACtD,QAAM,EAAE,OAAO,IAAI,IAAI,kBAAkB,mCAAS,IAAIA,SAAQ;AAE9D,QAAM,WAAW,GAAG,CAAC;AAErB,MAAI,WAAW,CAAC,QAAQ,CAAC;AACzB,QAAM,UAAU,WAAW,CAAC,QAAQ,CAAC;AACrC,MAAI,OAAO,WAAW,MAAM;AAE5B,MAAI,QAAO,mCAAS,SAAQ;AAC5B,MAAI,CAAC,KAAM,QAAO,CAAC;AACnB,MAAI,OAAO,GAAG;AACZ,WAAO,CAAC;AACR,eAAW,CAAC;AAAA,EACd;AAEA,QAAM,QAAQ,CAAC;AAEf,SAAO,CAAC,QAAQ,SAAS;AACvB,UAAM,KAAK,cAAc,OAAO,IAAI,CAAC;AACrC,WAAO,WAAW,MAAM,IAAI;AAAA,EAC9B;AAEA,SAAO,WAAW,MAAM,QAAQ,IAAI;AACtC;;;ACzBO,SAAS,oBAAoBC,WAAU,SAAS;AACrD,QAAM,EAAE,OAAO,IAAI,IAAI,kBAAkB,mCAAS,IAAIA,SAAQ;AAE9D,MAAI,WAAW,CAAC,QAAQ,CAAC;AACzB,QAAM,UAAU,WAAW,CAAC,QAAQ,CAAC;AACrC,QAAM,OAAO,WAAW,MAAM;AAC9B,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,OAAK,QAAQ,CAAC;AAEd,MAAI,QAAO,mCAAS,SAAQ;AAC5B,MAAI,CAAC,KAAM,QAAO,CAAC;AACnB,MAAI,OAAO,GAAG;AACZ,WAAO,CAAC;AACR,eAAW,CAAC;AAAA,EACd;AAEA,QAAM,QAAQ,CAAC;AAEf,SAAO,CAAC,QAAQ,SAAS;AACvB,UAAM,KAAK,cAAc,OAAO,IAAI,CAAC;AACrC,SAAK,SAAS,KAAK,SAAS,IAAI,IAAI;AAAA,EACtC;AAEA,SAAO,WAAW,MAAM,QAAQ,IAAI;AACtC;;;ACvCO,SAAS,eAAe,MAAM,SAAS;AAC5C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,eAAe,MAAM,SAAS;AACpC,QAAM,QAAQ,eAAgB,eAAe;AAC7C,QAAM,SAAS,OAAO,CAAC;AACvB,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;;;ACSO,SAAS,sBAAsBC,WAAU,SAAS;AACvD,QAAM,EAAE,OAAO,IAAI,IAAI,kBAAkB,mCAAS,IAAIA,SAAQ;AAE9D,MAAI,WAAW,CAAC,QAAQ,CAAC;AACzB,QAAM,UAAU,WAAW,CAAC,eAAe,KAAK,IAAI,CAAC,eAAe,GAAG;AACvE,MAAI,OAAO,WAAW,eAAe,GAAG,IAAI,eAAe,KAAK;AAEhE,MAAI,QAAO,mCAAS,SAAQ;AAC5B,MAAI,CAAC,KAAM,QAAO,CAAC;AACnB,MAAI,OAAO,GAAG;AACZ,WAAO,CAAC;AACR,eAAW,CAAC;AAAA,EACd;AAEA,QAAM,QAAQ,CAAC;AAEf,SAAO,CAAC,QAAQ,SAAS;AACvB,UAAM,KAAK,cAAc,OAAO,IAAI,CAAC;AACrC,WAAO,YAAY,MAAM,IAAI;AAAA,EAC/B;AAEA,SAAO,WAAW,MAAM,QAAQ,IAAI;AACtC;;;ACrBO,SAAS,mBAAmBC,WAAU,SAAS;AACpD,QAAM,EAAE,OAAO,IAAI,IAAI,kBAAkB,mCAAS,IAAIA,SAAQ;AAE9D,MAAI,WAAW,CAAC,QAAQ,CAAC;AACzB,QAAM,gBAAgB,WAClB,YAAY,KAAK,OAAO,IACxB,YAAY,OAAO,OAAO;AAC9B,QAAM,cAAc,WAChB,YAAY,OAAO,OAAO,IAC1B,YAAY,KAAK,OAAO;AAE5B,gBAAc,SAAS,EAAE;AACzB,cAAY,SAAS,EAAE;AAEvB,QAAM,UAAU,CAAC,YAAY,QAAQ;AACrC,MAAI,cAAc;AAElB,MAAI,QAAO,mCAAS,SAAQ;AAC5B,MAAI,CAAC,KAAM,QAAO,CAAC;AACnB,MAAI,OAAO,GAAG;AACZ,WAAO,CAAC;AACR,eAAW,CAAC;AAAA,EACd;AAEA,QAAM,QAAQ,CAAC;AAEf,SAAO,CAAC,eAAe,SAAS;AAC9B,gBAAY,SAAS,CAAC;AACtB,UAAM,KAAK,cAAc,OAAO,WAAW,CAAC;AAC5C,kBAAc,SAAS,aAAa,IAAI;AACxC,gBAAY,SAAS,EAAE;AAAA,EACzB;AAEA,SAAO,WAAW,MAAM,QAAQ,IAAI;AACtC;;;ACrCO,SAAS,sBAAsBC,WAAU,SAAS;AACvD,QAAM,EAAE,OAAO,IAAI,IAAI,kBAAkB,mCAAS,IAAIA,SAAQ;AAC9D,QAAM,eAAe,kBAAkB,EAAE,OAAO,IAAI,GAAG,OAAO;AAC9D,QAAM,WAAW,CAAC;AAClB,MAAI,QAAQ;AACZ,SAAO,QAAQ,aAAa,QAAQ;AAClC,UAAM,OAAO,aAAa,OAAO;AACjC,QAAI,UAAU,IAAI,EAAG,UAAS,KAAK,cAAc,OAAO,IAAI,CAAC;AAAA,EAC/D;AACA,SAAO;AACT;;;ACdO,SAAS,mBAAmB,MAAM,SAAS;AAChD,QAAM,QAAQ,aAAa,MAAM,OAAO;AACxC,QAAM,MAAM,WAAW,MAAM,OAAO;AACpC,SAAO,sBAAsB,EAAE,OAAO,IAAI,GAAG,OAAO;AACtD;;;ACPO,SAAS,kBAAkB,MAAM,SAAS;AAC/C,QAAM,QAAQ,YAAY,MAAM,OAAO;AACvC,QAAM,MAAM,UAAU,MAAM,OAAO;AACnC,SAAO,sBAAsB,EAAE,OAAO,IAAI,GAAG,OAAO;AACtD;;;ACIO,SAAS,mBAAmBC,WAAU,SAAS;AACpD,QAAM,EAAE,OAAO,IAAI,IAAI,kBAAkB,mCAAS,IAAIA,SAAQ;AAE9D,MAAI,WAAW,CAAC,QAAQ,CAAC;AACzB,QAAM,UAAU,WAAW,CAAC,QAAQ,CAAC;AACrC,QAAM,OAAO,WAAW,MAAM;AAC9B,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,OAAK,SAAS,GAAG,CAAC;AAElB,MAAI,QAAO,mCAAS,SAAQ;AAC5B,MAAI,CAAC,KAAM,QAAO,CAAC;AACnB,MAAI,OAAO,GAAG;AACZ,WAAO,CAAC;AACR,eAAW,CAAC;AAAA,EACd;AAEA,QAAM,QAAQ,CAAC;AAEf,SAAO,CAAC,QAAQ,SAAS;AACvB,UAAM,KAAK,cAAc,OAAO,IAAI,CAAC;AACrC,SAAK,YAAY,KAAK,YAAY,IAAI,IAAI;AAAA,EAC5C;AAEA,SAAO,WAAW,MAAM,QAAQ,IAAI;AACtC;;;ACxCO,SAAS,YAAY,MAAM,SAAS;AAIzC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,SAAS,IAAI,KAAK,MAAM,OAAO,EAAE,IAAI;AAC3C,QAAM,YAAY,QAAQ,IAAI,EAAE;AAChC,QAAM,SAAS,IAAI,IAAI,IAAI,GAAG;AAC9B,SAAO;AACT;;;ACTO,SAAS,UAAU,MAAM,SAAS;AACvC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,WAAW,IAAI,IAAI,GAAG;AAC5B,SAAO;AACT;;;ACFO,SAAS,aAAa,MAAM,SAAS;AAC1C,SAAO,UAAU,MAAM,EAAE,GAAG,SAAS,cAAc,EAAE,CAAC;AACxD;;;ACCO,SAAS,iBAAiB,MAAM,SAAS;AAC9C,QAAM,OAAO,eAAe,MAAM,OAAO;AACzC,QAAM,4BAA4B,eAAc,mCAAS,OAAM,MAAM,CAAC;AACtE,4BAA0B,YAAY,OAAO,GAAG,GAAG,CAAC;AACpD,4BAA0B,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7C,QAAM,QAAQ,eAAe,2BAA2B,OAAO;AAC/D,QAAM,gBAAgB,MAAM,gBAAgB,IAAI,CAAC;AACjD,SAAO;AACT;;;ACbO,SAAS,YAAY,MAAM,SAAS;AACzC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,WAAW,IAAI,GAAG;AACxB,SAAO;AACT;;;ACJO,SAAS,aAAa,MAAM,SAAS;AAC1C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,eAAe,MAAM,SAAS;AACpC,QAAM,QAAQ,eAAgB,eAAe,IAAK;AAClD,QAAM,SAAS,OAAO,CAAC;AACvB,QAAM,SAAS,IAAI,IAAI,IAAI,GAAG;AAC9B,SAAO;AACT;;;ACPO,SAAS,YAAY,MAAM,SAAS;AACzC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,gBAAgB,GAAG;AACzB,SAAO;AACT;;;ACLO,SAAS,WAAW,SAAS;AAClC,SAAO,SAAS,KAAK,IAAI,GAAG,OAAO;AACrC;;;ACHO,SAAS,cAAc,SAAS;AACrC,QAAM,MAAM,aAAa,mCAAS,EAAE;AACpC,QAAM,OAAO,IAAI,YAAY;AAC7B,QAAM,QAAQ,IAAI,SAAS;AAC3B,QAAM,MAAM,IAAI,QAAQ;AAExB,QAAM,OAAO,aAAa,mCAAS,EAAE;AACrC,OAAK,YAAY,MAAM,OAAO,MAAM,CAAC;AACrC,OAAK,SAAS,IAAI,IAAI,IAAI,GAAG;AAC7B,UAAO,mCAAS,MAAK,QAAQ,GAAG,IAAI,IAAI;AAC1C;;;ACVO,SAAS,eAAe,SAAS;AACtC,QAAM,MAAM,aAAa,mCAAS,EAAE;AACpC,QAAM,OAAO,cAAc,mCAAS,IAAI,CAAC;AACzC,OAAK,YAAY,IAAI,YAAY,GAAG,IAAI,SAAS,GAAG,IAAI,QAAQ,IAAI,CAAC;AACrE,OAAK,SAAS,IAAI,IAAI,IAAI,GAAG;AAC7B,SAAO;AACT;;;AC2DO,SAAS,eAAe,WAAW,aAAa,SAAS;AAC9D,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,UAAS,mCAAS,WAAU,eAAe,UAAU;AAC3D,QAAM,yBAAyB;AAE/B,QAAM,aAAa,WAAW,WAAW,WAAW;AAEpD,MAAI,MAAM,UAAU,EAAG,OAAM,IAAI,WAAW,oBAAoB;AAEhE,QAAM,kBAAkB,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,IACjD,WAAW,mCAAS;AAAA,IACpB;AAAA,EACF,CAAC;AAED,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT,GAAI,aAAa,IAAI,CAAC,aAAa,SAAS,IAAI,CAAC,WAAW,WAAW;AAAA,EACzE;AAEA,QAAM,UAAU,oBAAoB,cAAc,UAAU;AAC5D,QAAM,mBACH,gCAAgC,YAAY,IAC3C,gCAAgC,UAAU,KAC5C;AACF,QAAM,UAAU,KAAK,OAAO,UAAU,mBAAmB,EAAE;AAC3D,MAAIC;AAGJ,MAAI,UAAU,GAAG;AACf,QAAI,mCAAS,gBAAgB;AAC3B,UAAI,UAAU,GAAG;AACf,eAAO,OAAO,eAAe,oBAAoB,GAAG,eAAe;AAAA,MACrE,WAAW,UAAU,IAAI;AACvB,eAAO,OAAO,eAAe,oBAAoB,IAAI,eAAe;AAAA,MACtE,WAAW,UAAU,IAAI;AACvB,eAAO,OAAO,eAAe,oBAAoB,IAAI,eAAe;AAAA,MACtE,WAAW,UAAU,IAAI;AACvB,eAAO,OAAO,eAAe,eAAe,GAAG,eAAe;AAAA,MAChE,WAAW,UAAU,IAAI;AACvB,eAAO,OAAO,eAAe,oBAAoB,GAAG,eAAe;AAAA,MACrE,OAAO;AACL,eAAO,OAAO,eAAe,YAAY,GAAG,eAAe;AAAA,MAC7D;AAAA,IACF,OAAO;AACL,UAAI,YAAY,GAAG;AACjB,eAAO,OAAO,eAAe,oBAAoB,GAAG,eAAe;AAAA,MACrE,OAAO;AACL,eAAO,OAAO,eAAe,YAAY,SAAS,eAAe;AAAA,MACnE;AAAA,IACF;AAAA,EAGF,WAAW,UAAU,IAAI;AACvB,WAAO,OAAO,eAAe,YAAY,SAAS,eAAe;AAAA,EAGnE,WAAW,UAAU,IAAI;AACvB,WAAO,OAAO,eAAe,eAAe,GAAG,eAAe;AAAA,EAGhE,WAAW,UAAU,cAAc;AACjC,UAAM,QAAQ,KAAK,MAAM,UAAU,EAAE;AACrC,WAAO,OAAO,eAAe,eAAe,OAAO,eAAe;AAAA,EAGpE,WAAW,UAAU,wBAAwB;AAC3C,WAAO,OAAO,eAAe,SAAS,GAAG,eAAe;AAAA,EAG1D,WAAW,UAAU,gBAAgB;AACnC,UAAMC,QAAO,KAAK,MAAM,UAAU,YAAY;AAC9C,WAAO,OAAO,eAAe,SAASA,OAAM,eAAe;AAAA,EAG7D,WAAW,UAAU,iBAAiB,GAAG;AACvC,IAAAD,UAAS,KAAK,MAAM,UAAU,cAAc;AAC5C,WAAO,OAAO,eAAe,gBAAgBA,SAAQ,eAAe;AAAA,EACtE;AAEA,EAAAA,UAAS,mBAAmB,cAAc,UAAU;AAGpD,MAAIA,UAAS,IAAI;AACf,UAAM,eAAe,KAAK,MAAM,UAAU,cAAc;AACxD,WAAO,OAAO,eAAe,WAAW,cAAc,eAAe;AAAA,EAGvE,OAAO;AACL,UAAM,yBAAyBA,UAAS;AACxC,UAAM,QAAQ,KAAK,MAAMA,UAAS,EAAE;AAGpC,QAAI,yBAAyB,GAAG;AAC9B,aAAO,OAAO,eAAe,eAAe,OAAO,eAAe;AAAA,IAGpE,WAAW,yBAAyB,GAAG;AACrC,aAAO,OAAO,eAAe,cAAc,OAAO,eAAe;AAAA,IAGnE,OAAO;AACL,aAAO,OAAO,eAAe,gBAAgB,QAAQ,GAAG,eAAe;AAAA,IACzE;AAAA,EACF;AACF;;;AChGO,SAAS,qBAAqB,WAAW,aAAa,SAAS;AACpE,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,UAAS,mCAAS,WAAU,eAAe,UAAU;AAE3D,QAAM,aAAa,WAAW,WAAW,WAAW;AAEpD,MAAI,MAAM,UAAU,GAAG;AACrB,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,QAAM,kBAAkB,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,IACjD,WAAW,mCAAS;AAAA,IACpB;AAAA,EACF,CAAC;AAED,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT,GAAI,aAAa,IAAI,CAAC,aAAa,SAAS,IAAI,CAAC,WAAW,WAAW;AAAA,EACzE;AAEA,QAAM,iBAAiB,mBAAkB,mCAAS,mBAAkB,OAAO;AAE3E,QAAME,gBAAe,aAAa,QAAQ,IAAI,WAAW,QAAQ;AACjE,QAAM,UAAUA,gBAAe;AAE/B,QAAM,iBACJ,gCAAgC,YAAY,IAC5C,gCAAgC,UAAU;AAI5C,QAAM,wBACHA,gBAAe,kBAAkB;AAEpC,QAAM,cAAc,mCAAS;AAC7B,MAAI;AACJ,MAAI,CAAC,aAAa;AAChB,QAAI,UAAU,GAAG;AACf,aAAO;AAAA,IACT,WAAW,UAAU,IAAI;AACvB,aAAO;AAAA,IACT,WAAW,UAAU,cAAc;AACjC,aAAO;AAAA,IACT,WAAW,uBAAuB,gBAAgB;AAChD,aAAO;AAAA,IACT,WAAW,uBAAuB,eAAe;AAC/C,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AAGA,MAAI,SAAS,UAAU;AACrB,UAAM,UAAU,eAAeA,gBAAe,GAAI;AAClD,WAAO,OAAO,eAAe,YAAY,SAAS,eAAe;AAAA,EAGnE,WAAW,SAAS,UAAU;AAC5B,UAAM,iBAAiB,eAAe,OAAO;AAC7C,WAAO,OAAO,eAAe,YAAY,gBAAgB,eAAe;AAAA,EAG1E,WAAW,SAAS,QAAQ;AAC1B,UAAM,QAAQ,eAAe,UAAU,EAAE;AACzC,WAAO,OAAO,eAAe,UAAU,OAAO,eAAe;AAAA,EAG/D,WAAW,SAAS,OAAO;AACzB,UAAMC,QAAO,eAAe,uBAAuB,YAAY;AAC/D,WAAO,OAAO,eAAe,SAASA,OAAM,eAAe;AAAA,EAG7D,WAAW,SAAS,SAAS;AAC3B,UAAMC,UAAS,eAAe,uBAAuB,cAAc;AACnE,WAAOA,YAAW,MAAM,gBAAgB,UACpC,OAAO,eAAe,UAAU,GAAG,eAAe,IAClD,OAAO,eAAe,WAAWA,SAAQ,eAAe;AAAA,EAG9D,OAAO;AACL,UAAM,QAAQ,eAAe,uBAAuB,aAAa;AACjE,WAAO,OAAO,eAAe,UAAU,OAAO,eAAe;AAAA,EAC/D;AACF;;;AChGO,SAAS,oBAAoB,MAAM,SAAS;AACjD,SAAO,eAAe,MAAM,aAAa,IAAI,GAAG,OAAO;AACzD;;;ACZO,SAAS,0BAA0B,MAAM,SAAS;AACvD,SAAO,qBAAqB,MAAM,aAAa,IAAI,GAAG,OAAO;AAC/D;;;AC1EA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AA4DO,SAAS,eAAe,UAAU,SAAS;AAChD,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,UAAS,mCAAS,WAAU,eAAe,UAAU;AAC3D,QAAMC,WAAS,mCAAS,WAAU;AAClC,QAAM,QAAO,mCAAS,SAAQ;AAC9B,QAAM,aAAY,mCAAS,cAAa;AAExC,MAAI,CAAC,OAAO,gBAAgB;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,SAASA,QACZ,OAAO,CAAC,KAAK,SAAS;AACrB,UAAM,QAAQ,IAAI,KAAK,QAAQ,QAAQ,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAC9D,UAAM,QAAQ,SAAS,IAAI;AAC3B,QAAI,UAAU,WAAc,QAAQ,SAAS,IAAI,IAAI;AACnD,aAAO,IAAI,OAAO,OAAO,eAAe,OAAO,KAAK,CAAC;AAAA,IACvD;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC,EACJ,KAAK,SAAS;AAEjB,SAAO;AACT;;;ACxDO,SAAS,UAAU,MAAM,SAAS;AACvC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AAEtC,MAAI,MAAM,CAAC,KAAK,GAAG;AACjB,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,QAAMC,WAAS,mCAAS,WAAU;AAClC,QAAM,kBAAiB,mCAAS,mBAAkB;AAElD,MAAI,SAAS;AACb,MAAI,WAAW;AAEf,QAAM,gBAAgBA,YAAW,aAAa,MAAM;AACpD,QAAM,gBAAgBA,YAAW,aAAa,MAAM;AAGpD,MAAI,mBAAmB,QAAQ;AAC7B,UAAM,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CAAC;AAC9C,UAAM,QAAQ,gBAAgB,MAAM,SAAS,IAAI,GAAG,CAAC;AACrD,UAAM,OAAO,gBAAgB,MAAM,YAAY,GAAG,CAAC;AAGnD,aAAS,GAAG,IAAI,GAAG,aAAa,GAAG,KAAK,GAAG,aAAa,GAAG,GAAG;AAAA,EAChE;AAGA,MAAI,mBAAmB,QAAQ;AAE7B,UAAM,SAAS,MAAM,kBAAkB;AAEvC,QAAI,WAAW,GAAG;AAChB,YAAM,iBAAiB,KAAK,IAAI,MAAM;AACtC,YAAM,aAAa,gBAAgB,KAAK,MAAM,iBAAiB,EAAE,GAAG,CAAC;AACrE,YAAM,eAAe,gBAAgB,iBAAiB,IAAI,CAAC;AAE3D,YAAM,OAAO,SAAS,IAAI,MAAM;AAEhC,iBAAW,GAAG,IAAI,GAAG,UAAU,IAAI,YAAY;AAAA,IACjD,OAAO;AACL,iBAAW;AAAA,IACb;AAEA,UAAM,OAAO,gBAAgB,MAAM,SAAS,GAAG,CAAC;AAChD,UAAM,SAAS,gBAAgB,MAAM,WAAW,GAAG,CAAC;AACpD,UAAM,SAAS,gBAAgB,MAAM,WAAW,GAAG,CAAC;AAGpD,UAAM,YAAY,WAAW,KAAK,KAAK;AAGvC,UAAM,OAAO,CAAC,MAAM,QAAQ,MAAM,EAAE,KAAK,aAAa;AAGtD,aAAS,GAAG,MAAM,GAAG,SAAS,GAAG,IAAI,GAAG,QAAQ;AAAA,EAClD;AAEA,SAAO;AACT;;;ACzDO,SAAS,cAAc,MAAM,SAAS;AAC3C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AAEtC,MAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,QAAMC,WAAS,mCAAS,WAAU;AAClC,QAAM,kBAAiB,mCAAS,mBAAkB;AAElD,MAAI,SAAS;AAEb,QAAM,gBAAgBA,YAAW,aAAa,MAAM;AACpD,QAAM,gBAAgBA,YAAW,aAAa,MAAM;AAGpD,MAAI,mBAAmB,QAAQ;AAC7B,UAAM,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CAAC;AAC9C,UAAM,QAAQ,gBAAgB,MAAM,SAAS,IAAI,GAAG,CAAC;AACrD,UAAM,OAAO,gBAAgB,MAAM,YAAY,GAAG,CAAC;AAGnD,aAAS,GAAG,IAAI,GAAG,aAAa,GAAG,KAAK,GAAG,aAAa,GAAG,GAAG;AAAA,EAChE;AAGA,MAAI,mBAAmB,QAAQ;AAC7B,UAAM,OAAO,gBAAgB,MAAM,SAAS,GAAG,CAAC;AAChD,UAAM,SAAS,gBAAgB,MAAM,WAAW,GAAG,CAAC;AACpD,UAAM,SAAS,gBAAgB,MAAM,WAAW,GAAG,CAAC;AAGpD,UAAM,YAAY,WAAW,KAAK,KAAK;AAGvC,aAAS,GAAG,MAAM,GAAG,SAAS,GAAG,IAAI,GAAG,aAAa,GAAG,MAAM,GAAG,aAAa,GAAG,MAAM;AAAA,EACzF;AAEA,SAAO;AACT;;;AC1DO,SAAS,kBAAkB,UAAU;AAC1C,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,QAAAC,UAAS;AAAA,IACT,MAAAC,QAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,IAAI;AAEJ,SAAO,IAAI,KAAK,IAAID,OAAM,IAAIC,KAAI,KAAK,KAAK,IAAI,OAAO,IAAI,OAAO;AACpE;;;ACAO,SAAS,cAAc,MAAM,SAAS;AAC3C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AAEtC,MAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,QAAM,kBAAiB,mCAAS,mBAAkB;AAElD,QAAM,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CAAC;AAC9C,QAAM,QAAQ,gBAAgB,MAAM,SAAS,IAAI,GAAG,CAAC;AACrD,QAAM,OAAO,MAAM,YAAY;AAE/B,QAAM,OAAO,gBAAgB,MAAM,SAAS,GAAG,CAAC;AAChD,QAAM,SAAS,gBAAgB,MAAM,WAAW,GAAG,CAAC;AACpD,QAAM,SAAS,gBAAgB,MAAM,WAAW,GAAG,CAAC;AAEpD,MAAI,mBAAmB;AACvB,MAAI,iBAAiB,GAAG;AACtB,UAAMC,gBAAe,MAAM,gBAAgB;AAC3C,UAAM,oBAAoB,KAAK;AAAA,MAC7BA,gBAAe,KAAK,IAAI,IAAI,iBAAiB,CAAC;AAAA,IAChD;AACA,uBAAmB,MAAM,gBAAgB,mBAAmB,cAAc;AAAA,EAC5E;AAEA,MAAI,SAAS;AACb,QAAM,WAAW,MAAM,kBAAkB;AAEzC,MAAI,aAAa,GAAG;AAClB,UAAM,iBAAiB,KAAK,IAAI,QAAQ;AACxC,UAAM,aAAa,gBAAgB,KAAK,MAAM,iBAAiB,EAAE,GAAG,CAAC;AACrE,UAAM,eAAe,gBAAgB,iBAAiB,IAAI,CAAC;AAE3D,UAAM,OAAO,WAAW,IAAI,MAAM;AAElC,aAAS,GAAG,IAAI,GAAG,UAAU,IAAI,YAAY;AAAA,EAC/C,OAAO;AACL,aAAS;AAAA,EACX;AAEA,SAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM,GAAG,gBAAgB,GAAG,MAAM;AACxF;;;ACzEA,IAAM,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAE7D,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAsBO,SAAS,cAAc,MAAM;AAClC,QAAM,QAAQ,OAAO,IAAI;AAEzB,MAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,QAAM,UAAU,KAAK,MAAM,UAAU,CAAC;AACtC,QAAM,aAAa,gBAAgB,MAAM,WAAW,GAAG,CAAC;AACxD,QAAM,YAAY,OAAO,MAAM,YAAY,CAAC;AAC5C,QAAM,OAAO,MAAM,eAAe;AAElC,QAAM,OAAO,gBAAgB,MAAM,YAAY,GAAG,CAAC;AACnD,QAAM,SAAS,gBAAgB,MAAM,cAAc,GAAG,CAAC;AACvD,QAAM,SAAS,gBAAgB,MAAM,cAAc,GAAG,CAAC;AAGvD,SAAO,GAAG,OAAO,KAAK,UAAU,IAAI,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM;AACnF;;;ACfO,SAAS,eAAe,MAAM,UAAU,SAAS;AA5CxD;AA6CE,QAAM,CAAC,OAAO,SAAS,IAAI,eAAe,mCAAS,IAAI,MAAM,QAAQ;AAErE,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,UAAS,mCAAS,WAAU,eAAe,UAAU;AAC3D,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1B,eAAe,kBACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,QAAM,OAAO,yBAAyB,OAAO,SAAS;AAEtD,MAAI,MAAM,IAAI,GAAG;AACf,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAI;AACJ,MAAI,OAAO,IAAI;AACb,YAAQ;AAAA,EACV,WAAW,OAAO,IAAI;AACpB,YAAQ;AAAA,EACV,WAAW,OAAO,GAAG;AACnB,YAAQ;AAAA,EACV,WAAW,OAAO,GAAG;AACnB,YAAQ;AAAA,EACV,WAAW,OAAO,GAAG;AACnB,YAAQ;AAAA,EACV,WAAW,OAAO,GAAG;AACnB,YAAQ;AAAA,EACV,OAAO;AACL,YAAQ;AAAA,EACV;AAEA,QAAM,YAAY,OAAO,eAAe,OAAO,OAAO,WAAW;AAAA,IAC/D;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,WAAW,EAAE,QAAQ,aAAa,CAAC;AAC1D;;;AC1DO,SAAS,aAAa,UAAU,SAAS;AAC9C,SAAO,OAAO,WAAW,KAAM,mCAAS,EAAE;AAC5C;;;ACJO,SAAS,OAAO,MAAM,SAAS;AACpC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO;AAC1C;;;ACNO,SAAS,WAAW,MAAM,SAAS;AACxC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAC/B,SAAO,OAAO,QAAQ,KAAM,OAAO,MAAM,KAAK,OAAO,QAAQ;AAC/D;;;ACCO,SAAS,cAAc,MAAM,SAAS;AAC3C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,MAAI,OAAO,MAAM,CAAC,KAAK,EAAG,QAAO;AACjC,SAAO,WAAW,KAAK,IAAI,MAAM;AACnC;;;ACLO,SAAS,UAAU,MAAM,SAAS;AAIvC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,SAAS,KAAK,MAAM,OAAO,EAAE,IAAI;AACvC,SAAO;AACT;;;ACJO,SAAS,kBAAkB,MAAM,SAAS;AAC/C,QAAM,WAAW,mBAAmB,MAAM,OAAO;AACjD,QAAM,WAAW,mBAAmB,SAAS,UAAU,EAAE,CAAC;AAC1D,QAAM,OAAO,CAAC,WAAW,CAAC;AAK1B,SAAO,KAAK,MAAM,OAAO,kBAAkB;AAC7C;;;ACGO,SAAS,8BAA8B,cAAc,eAAe;AACzE,QAAM,CAAC,WAAW,OAAO,IAAI;AAAA,IAC3B,CAAC,OAAO,aAAa,KAAK;AAAA,IAC1B,CAAC,OAAO,aAAa,GAAG;AAAA,EAC1B,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACtB,QAAM,CAAC,YAAY,QAAQ,IAAI;AAAA,IAC7B,CAAC,OAAO,cAAc,KAAK;AAAA,IAC3B,CAAC,OAAO,cAAc,GAAG;AAAA,EAC3B,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAGtB,QAAM,gBAAgB,YAAY,YAAY,aAAa;AAC3D,MAAI,CAAC,cAAe,QAAO;AAG3B,QAAM,cAAc,aAAa,YAAY,YAAY;AACzD,QAAM,OAAO,cAAc,gCAAgC,WAAW;AACtE,QAAM,eAAe,WAAW,UAAU,UAAU;AACpD,QAAM,QAAQ,eAAe,gCAAgC,YAAY;AAGzE,SAAO,KAAK,MAAM,QAAQ,QAAQ,iBAAiB;AACrD;;;AC3CO,SAAS,QAAQ,MAAM;AAC5B,SAAO,CAAC,OAAO,IAAI;AACrB;;;ACFO,SAAS,YAAY,MAAM;AAChC,SAAO,KAAK,MAAM,CAAC,OAAO,IAAI,IAAI,GAAI;AACxC;;;ACOO,SAAS,eAAe,MAAM,SAAS;AA5B9C;AA6BE,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1B,eAAe,kBACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,QAAM,oBAAoB,QAAQ,OAAO,MAAM,mCAAS,EAAE,CAAC;AAC3D,MAAI,MAAM,iBAAiB,EAAG,QAAO;AAErC,QAAM,eAAe,OAAO,aAAa,MAAM,OAAO,CAAC;AAEvD,MAAI,qBAAqB,eAAe;AACxC,MAAI,sBAAsB,EAAG,uBAAsB;AAEnD,QAAM,8BAA8B,oBAAoB;AACxD,SAAO,KAAK,KAAK,8BAA8B,CAAC,IAAI;AACtD;;;ACnBO,SAAS,eAAe,MAAM,SAAS;AAC5C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,QAAQ,MAAM,SAAS;AAC7B,QAAM,YAAY,MAAM,YAAY,GAAG,QAAQ,GAAG,CAAC;AACnD,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO,OAAO,OAAO,mCAAS,EAAE;AAClC;;;ACDO,SAAS,gBAAgB,MAAM,SAAS;AAC7C,QAAM,cAAc,OAAO,MAAM,mCAAS,EAAE;AAC5C,SACE;AAAA,IACE,eAAe,aAAa,OAAO;AAAA,IACnC,aAAa,aAAa,OAAO;AAAA,IACjC;AAAA,EACF,IAAI;AAER;;;ACvBO,SAAS,oBAAoB,OAAO;AACzC,SAAO,KAAK,MAAM,QAAQ,kBAAkB;AAC9C;;;ACFO,SAAS,eAAe,OAAO;AACpC,SAAO,KAAK,MAAM,QAAQ,aAAa;AACzC;;;ACFO,SAAS,eAAe,OAAO;AACpC,SAAO,KAAK,MAAM,QAAQ,aAAa;AACzC;;;ACcO,SAAS,SAAS,OAAO,KAAK,SAAS;AAC5C,QAAM,CAAC,QAAQ,IAAI,IAAI,eAAe,mCAAS,IAAI,OAAO,GAAG;AAE7D,MAAI,MAAM,CAAC,MAAM,EAAG,OAAM,IAAI,UAAU,uBAAuB;AAC/D,MAAI,MAAM,CAAC,IAAI,EAAG,OAAM,IAAI,UAAU,qBAAqB;AAE3D,OAAI,mCAAS,mBAAkB,CAAC,SAAS,CAAC;AACxC,UAAM,IAAI,UAAU,mCAAmC;AAEzD,SAAO,EAAE,OAAO,QAAQ,KAAK,KAAK;AACpC;;;ACXO,SAAS,mBAAmBC,WAAU,SAAS;AACpD,QAAM,EAAE,OAAO,IAAI,IAAI,kBAAkB,mCAAS,IAAIA,SAAQ;AAC9D,QAAM,WAAW,CAAC;AAElB,QAAM,QAAQ,kBAAkB,KAAK,KAAK;AAC1C,MAAI,MAAO,UAAS,QAAQ;AAE5B,QAAM,kBAAkB,IAAI,OAAO,EAAE,OAAO,SAAS,MAAM,CAAC;AAC5D,QAAMC,UAAS,mBAAmB,KAAK,eAAe;AACtD,MAAIA,QAAQ,UAAS,SAASA;AAE9B,QAAM,gBAAgB,IAAI,iBAAiB,EAAE,QAAQ,SAAS,OAAO,CAAC;AACtE,QAAMC,QAAO,iBAAiB,KAAK,aAAa;AAChD,MAAIA,MAAM,UAAS,OAAOA;AAE1B,QAAM,iBAAiB,IAAI,eAAe,EAAE,MAAM,SAAS,KAAK,CAAC;AACjE,QAAM,QAAQ,kBAAkB,KAAK,cAAc;AACnD,MAAI,MAAO,UAAS,QAAQ;AAE5B,QAAM,mBAAmB,IAAI,gBAAgB,EAAE,OAAO,SAAS,MAAM,CAAC;AACtE,QAAM,UAAU,oBAAoB,KAAK,gBAAgB;AACzD,MAAI,QAAS,UAAS,UAAU;AAEhC,QAAM,mBAAmB,IAAI,kBAAkB,EAAE,SAAS,SAAS,QAAQ,CAAC;AAC5E,QAAM,UAAU,oBAAoB,KAAK,gBAAgB;AACzD,MAAI,QAAS,UAAS,UAAU;AAEhC,SAAO;AACT;;;ACuCO,SAAS,WAAW,MAAM,gBAAgB,eAAe;AAC9D,MAAI;AAEJ,MAAI,gBAAgB,cAAc,GAAG;AACnC,oBAAgB;AAAA,EAClB,OAAO;AACL,oBAAgB;AAAA,EAClB;AAEA,SAAO,IAAI,KAAK,eAAe,+CAAe,QAAQ,aAAa,EAAE;AAAA,IACnE,OAAO,IAAI;AAAA,EACb;AACF;AAEA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,UAAa,EAAE,YAAY;AAC7C;;;ACOO,SAAS,mBAAmB,WAAW,aAAa,SAAS;AAClE,MAAI,QAAQ;AACZ,MAAI;AAEJ,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,MAAI,EAAC,mCAAS,OAAM;AAElB,UAAM,gBAAgB,oBAAoB,YAAY,YAAY;AAElE,QAAI,KAAK,IAAI,aAAa,IAAI,iBAAiB;AAC7C,cAAQ,oBAAoB,YAAY,YAAY;AACpD,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,eAAe;AAClD,cAAQ,oBAAoB,YAAY,YAAY;AACpD,aAAO;AAAA,IACT,WACE,KAAK,IAAI,aAAa,IAAI,gBAC1B,KAAK,IAAI,yBAAyB,YAAY,YAAY,CAAC,IAAI,GAC/D;AACA,cAAQ,kBAAkB,YAAY,YAAY;AAClD,aAAO;AAAA,IACT,WACE,KAAK,IAAI,aAAa,IAAI,kBACzB,QAAQ,yBAAyB,YAAY,YAAY,MAC1D,KAAK,IAAI,KAAK,IAAI,GAClB;AACA,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,gBAAgB;AACnD,cAAQ,0BAA0B,YAAY,YAAY;AAC1D,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,kBAAkB;AACrD,cAAQ,2BAA2B,YAAY,YAAY;AAC3D,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,eAAe;AAClD,UAAI,6BAA6B,YAAY,YAAY,IAAI,GAAG;AAE9D,gBAAQ,6BAA6B,YAAY,YAAY;AAC7D,eAAO;AAAA,MACT,OAAO;AACL,gBAAQ,0BAA0B,YAAY,YAAY;AAC1D,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,cAAQ,0BAA0B,YAAY,YAAY;AAC1D,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AAEL,WAAO,mCAAS;AAChB,QAAI,SAAS,UAAU;AACrB,cAAQ,oBAAoB,YAAY,YAAY;AAAA,IACtD,WAAW,SAAS,UAAU;AAC5B,cAAQ,oBAAoB,YAAY,YAAY;AAAA,IACtD,WAAW,SAAS,QAAQ;AAC1B,cAAQ,kBAAkB,YAAY,YAAY;AAAA,IACpD,WAAW,SAAS,OAAO;AACzB,cAAQ,yBAAyB,YAAY,YAAY;AAAA,IAC3D,WAAW,SAAS,QAAQ;AAC1B,cAAQ,0BAA0B,YAAY,YAAY;AAAA,IAC5D,WAAW,SAAS,SAAS;AAC3B,cAAQ,2BAA2B,YAAY,YAAY;AAAA,IAC7D,WAAW,SAAS,WAAW;AAC7B,cAAQ,6BAA6B,YAAY,YAAY;AAAA,IAC/D,WAAW,SAAS,QAAQ;AAC1B,cAAQ,0BAA0B,YAAY,YAAY;AAAA,IAC5D;AAAA,EACF;AAEA,QAAM,MAAM,IAAI,KAAK,mBAAmB,mCAAS,QAAQ;AAAA,IACvD,SAAS;AAAA,IACT,GAAG;AAAA,EACL,CAAC;AAED,SAAO,IAAI,OAAO,OAAO,IAAI;AAC/B;;;ACnLO,SAAS,SAAS,MAAM,OAAO,KAAK;AACzC,QAAM,OAAO,IAAI,KAAK,MAAM,OAAO,GAAG;AACtC,SACE,KAAK,YAAY,MAAM,QACvB,KAAK,SAAS,MAAM,SACpB,KAAK,QAAQ,MAAM;AAEvB;;;ACPO,SAAS,kBAAkB,MAAM,SAAS;AAC/C,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,QAAQ,MAAM;AACjD;;;ACFO,SAAS,SAAS,MAAM,SAAS;AACtC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO,MAAM;AAChD;;;ACNO,SAAS,SAAS,MAAM;AAC7B,SAAO,CAAC,OAAO,IAAI,IAAI,KAAK,IAAI;AAClC;;;AC6QO,SAAS,QAAQ,SAAS,WAAW,SAAS;AACnD,SAAO,QAAQ,MAAM,SAAS,WAAW,oBAAI,KAAK,GAAG,OAAO,CAAC;AAC/D;;;AC7QO,SAAS,SAAS,MAAM,SAAS;AACtC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO,MAAM;AAChD;;;ACNO,SAAS,OAAO,MAAM;AAC3B,SAAO,CAAC,OAAO,IAAI,IAAI,KAAK,IAAI;AAClC;;;ACUO,SAAS,cAAc,WAAW,aAAa,SAAS;AAC7D,SAAO,WAAW,WAAW,aAAa,EAAE,GAAG,SAAS,cAAc,EAAE,CAAC;AAC3E;;;ACLO,SAAS,kBAAkB,WAAW,aAAa,SAAS;AACjE,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,SAAO,CAAC,mBAAmB,UAAU,MAAM,CAAC,mBAAmB,YAAY;AAC7E;;;ACRO,SAAS,cAAc,MAAM,SAAS;AAC3C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,WAAW,GAAG,CAAC;AACrB,SAAO;AACT;;;ACDO,SAAS,aAAa,WAAW,aAAa;AACnD,SAAO,CAAC,cAAc,SAAS,MAAM,CAAC,cAAc,WAAW;AACjE;;;ACFO,SAAS,cAAc,WAAW,aAAa,SAAS;AAC7D,QAAM,CAAC,WAAW,UAAU,IAAI;AAAA,IAC9B,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,SAAO,CAAC,eAAe,SAAS,MAAM,CAAC,eAAe,UAAU;AAClE;;;ACVO,SAAS,cAAc,MAAM,SAAS;AAC3C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,gBAAgB,CAAC;AACvB,SAAO;AACT;;;ACOO,SAAS,aAAa,WAAW,aAAa;AACnD,SAAO,CAAC,cAAc,SAAS,MAAM,CAAC,cAAc,WAAW;AACjE;;;ACbO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO;AAAA,IACL,OAAO,MAAM,mCAAS,EAAE;AAAA,IACxB,cAAa,mCAAS,OAAM,IAAI;AAAA,EAClC;AACF;;;ACJO,SAAS,cAAc,MAAM,SAAS;AAC3C,SAAO;AAAA,IACL,eAAc,mCAAS,OAAM,MAAM,IAAI;AAAA,IACvC,cAAa,mCAAS,OAAM,IAAI;AAAA,EAClC;AACF;;;ACXO,SAAS,aAAa,MAAM;AACjC,SAAO,aAAa,MAAM,aAAa,IAAI,CAAC;AAC9C;;;ACEO,SAAS,YAAY,MAAM,SAAS;AACzC,SAAO;AAAA,IACL,eAAc,mCAAS,OAAM,MAAM,IAAI;AAAA,IACvC,cAAa,mCAAS,OAAM,IAAI;AAAA,EAClC;AACF;;;ACLO,SAAS,cAAc,MAAM,SAAS;AAC3C,SAAO;AAAA,IACL,eAAc,mCAAS,OAAM,MAAM,IAAI;AAAA,IACvC,cAAa,mCAAS,OAAM,IAAI;AAAA,EAClC;AACF;;;ACVO,SAAS,aAAa,MAAM;AACjC,SAAO,aAAa,MAAM,aAAa,IAAI,CAAC;AAC9C;;;ACSO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO;AAAA,IACL,eAAc,mCAAS,OAAM,MAAM,IAAI;AAAA,IACvC,cAAa,mCAAS,OAAM,IAAI;AAAA,IAChC;AAAA,EACF;AACF;;;ACZO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO;AAAA,IACL,eAAc,mCAAS,OAAM,MAAM,IAAI;AAAA,IACvC,cAAa,mCAAS,OAAM,IAAI;AAAA,EAClC;AACF;;;ACRO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO,MAAM;AAChD;;;ACCO,SAAS,QAAQ,MAAM,SAAS;AACrC,SAAO;AAAA,IACL,eAAc,mCAAS,OAAM,MAAM,IAAI;AAAA,IACvC,cAAa,mCAAS,OAAM,IAAI;AAAA,EAClC;AACF;;;ACLO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,cAAa,mCAAS,OAAM,IAAI,GAAG,CAAC;AAAA,IAC5C;AAAA,EACF;AACF;;;ACTO,SAAS,UAAU,MAAM,SAAS;AACvC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO,MAAM;AAChD;;;ACFO,SAAS,YAAY,MAAM,SAAS;AACzC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO,MAAM;AAChD;;;ACDO,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC7C,SAAO,QAAQ,MAAM,CAAC,QAAQ,OAAO;AACvC;;;ACCO,SAAS,YAAY,MAAM,SAAS;AACzC,SAAO;AAAA,IACL,eAAc,mCAAS,OAAM,MAAM,IAAI;AAAA,IACvC,QAAQ,cAAa,mCAAS,OAAM,IAAI,GAAG,CAAC;AAAA,EAC9C;AACF;;;ACNO,SAAS,gBAAgB,MAAM,SAAS;AAC7C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,SAAS,IAAI,KAAK,MAAM,OAAO,EAAE,IAAI;AAC3C,QAAM,YAAY,SAAS,GAAG,GAAG,CAAC;AAClC,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO,OAAO,OAAO,mCAAS,EAAE;AAClC;;;ACVO,SAAS,cAAc,MAAM,SAAS;AAxB7C;AAyBE,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1B,eAAe,kBACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,MAAM,MAAM,OAAO;AACzB,QAAM,QAAQ,MAAM,eAAe,KAAK,KAAK,KAAK,MAAM;AAExD,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,QAAM,QAAQ,MAAM,QAAQ,IAAI,IAAI;AAEpC,SAAO;AACT;;;ACXO,SAAS,iBAAiB,MAAM,SAAS;AAC9C,SAAO,cAAc,MAAM,EAAE,GAAG,SAAS,cAAc,EAAE,CAAC;AAC5D;;;ACCO,SAAS,qBAAqB,MAAM,SAAS;AAClD,QAAM,OAAO,eAAe,MAAM,OAAO;AACzC,QAAM,kBAAkB,eAAc,mCAAS,OAAM,MAAM,CAAC;AAC5D,kBAAgB,YAAY,OAAO,GAAG,GAAG,CAAC;AAC1C,kBAAgB,SAAS,GAAG,GAAG,GAAG,CAAC;AAEnC,QAAM,QAAQ,eAAe,iBAAiB,OAAO;AACrD,QAAM,QAAQ,MAAM,QAAQ,IAAI,CAAC;AACjC,SAAO;AACT;;;ACdO,SAAS,iBAAiB,MAAM,SAAS;AAC9C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,eAAe,MAAM,SAAS;AACpC,QAAM,QAAQ,eAAgB,eAAe,IAAK;AAClD,QAAM,SAAS,OAAO,CAAC;AACvB,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;;;ACPO,SAAS,cAAc,MAAM,SAAS;AAC3C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,YAAY,OAAO,GAAG,GAAG,CAAC;AAChC,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;;;ACjBA,IAAM,yBAAyB;AAE/B,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAC1B,IAAM,gCAAgC;AA+D/B,SAAS,YAAY,MAAM,WAAW;AAC3C,QAAM,QAAQ,OAAO,IAAI;AAEzB,MAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,QAAM,SAAS,UAAU,MAAM,sBAAsB;AAGrD,MAAI,CAAC,OAAQ,QAAO;AAEpB,QAAM,SAAS,OACZ,IAAI,CAAC,cAAc;AAElB,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,UAAU,CAAC;AAClC,QAAI,mBAAmB,KAAK;AAC1B,aAAO,mBAAmB,SAAS;AAAA,IACrC;AAEA,UAAM,YAAY,gBAAgB,cAAc;AAChD,QAAI,WAAW;AACb,aAAO,UAAU,OAAO,SAAS;AAAA,IACnC;AAEA,QAAI,eAAe,MAAM,6BAA6B,GAAG;AACvD,YAAM,IAAI;AAAA,QACR,mEACE,iBACA;AAAA,MACJ;AAAA,IACF;AAEA,WAAO;AAAA,EACT,CAAC,EACA,KAAK,EAAE;AAEV,SAAO;AACT;AAEA,SAAS,mBAAmB,OAAO;AACjC,QAAM,UAAU,MAAM,MAAM,mBAAmB;AAC/C,MAAI,CAAC,QAAS,QAAO;AACrB,SAAO,QAAQ,CAAC,EAAE,QAAQ,mBAAmB,GAAG;AAClD;;;ACrGO,SAAS,aAAa;AAAA,EAC3B;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,YAAY;AAEhB,MAAI,MAAO,cAAa,QAAQ;AAChC,MAAID,QAAQ,cAAaA,WAAU,aAAa;AAChD,MAAI,MAAO,cAAa,QAAQ;AAChC,MAAIC,MAAM,cAAaA;AAEvB,MAAI,eAAe,YAAY,KAAK,KAAK;AAEzC,MAAI,MAAO,iBAAgB,QAAQ,KAAK;AACxC,MAAI,QAAS,iBAAgB,UAAU;AACvC,MAAI,QAAS,iBAAgB;AAE7B,SAAO,KAAK,MAAM,eAAe,GAAI;AACvC;;;AC9BO,SAAS,oBAAoBC,eAAc;AAChD,QAAM,QAAQA,gBAAe;AAC7B,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACHO,SAAS,sBAAsBC,eAAc;AAClD,QAAM,UAAUA,gBAAe;AAC/B,SAAO,KAAK,MAAM,OAAO;AAC3B;;;ACHO,SAAS,sBAAsBC,eAAc;AAClD,QAAM,UAAUA,gBAAe;AAC/B,SAAO,KAAK,MAAM,OAAO;AAC3B;;;ACHO,SAAS,eAAe,SAAS;AACtC,QAAM,QAAQ,UAAU;AACxB,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACRO,SAAS,sBAAsB,SAAS;AAC7C,SAAO,KAAK,MAAM,UAAU,oBAAoB;AAClD;;;ACFO,SAAS,iBAAiB,SAAS;AACxC,SAAO,KAAK,MAAM,UAAU,eAAe;AAC7C;;;ACGO,SAAS,iBAAiBC,SAAQ;AACvC,QAAM,WAAWA,UAAS;AAC1B,SAAO,KAAK,MAAM,QAAQ;AAC5B;;;ACJO,SAAS,cAAcC,SAAQ;AACpC,QAAM,QAAQA,UAAS;AACvB,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACKO,SAAS,QAAQ,MAAM,KAAK,SAAS;AAC1C,MAAI,QAAQ,MAAM,OAAO,MAAM,OAAO;AACtC,MAAI,SAAS,EAAG,UAAS;AAEzB,SAAO,QAAQ,MAAM,OAAO,OAAO;AACrC;;;ACTO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO,QAAQ,MAAM,GAAG,OAAO;AACjC;;;ACFO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO,QAAQ,MAAM,GAAG,OAAO;AACjC;;;ACFO,SAAS,aAAa,MAAM,SAAS;AAC1C,SAAO,QAAQ,MAAM,GAAG,OAAO;AACjC;;;ACFO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO,QAAQ,MAAM,GAAG,OAAO;AACjC;;;ACFO,SAAS,aAAa,MAAM,SAAS;AAC1C,SAAO,QAAQ,MAAM,GAAG,OAAO;AACjC;;;ACFO,SAAS,YAAY,MAAM,SAAS;AACzC,SAAO,QAAQ,MAAM,GAAG,OAAO;AACjC;;;ACFO,SAAS,cAAc,MAAM,SAAS;AAC3C,SAAO,QAAQ,MAAM,GAAG,OAAO;AACjC;;;ACeO,SAAS,SAAS,UAAU,SAAS;AAC1C,QAAM,cAAc,MAAM,cAAc,mCAAS,IAAI,GAAG;AAExD,QAAM,oBAAmB,mCAAS,qBAAoB;AACtD,QAAM,cAAc,gBAAgB,QAAQ;AAE5C,MAAI;AACJ,MAAI,YAAY,MAAM;AACpB,UAAM,kBAAkB,UAAU,YAAY,MAAM,gBAAgB;AACpE,WAAO,UAAU,gBAAgB,gBAAgB,gBAAgB,IAAI;AAAA,EACvE;AAEA,MAAI,CAAC,QAAQ,MAAM,CAAC,IAAI,EAAG,QAAO,YAAY;AAE9C,QAAM,YAAY,CAAC;AACnB,MAAI,OAAO;AACX,MAAI;AAEJ,MAAI,YAAY,MAAM;AACpB,WAAO,UAAU,YAAY,IAAI;AACjC,QAAI,MAAM,IAAI,EAAG,QAAO,YAAY;AAAA,EACtC;AAEA,MAAI,YAAY,UAAU;AACxB,aAAS,cAAc,YAAY,QAAQ;AAC3C,QAAI,MAAM,MAAM,EAAG,QAAO,YAAY;AAAA,EACxC,OAAO;AACL,UAAM,UAAU,IAAI,KAAK,YAAY,IAAI;AACzC,UAAM,SAAS,OAAO,GAAG,mCAAS,EAAE;AACpC,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,MACvB,QAAQ,YAAY;AAAA,MACpB,QAAQ,WAAW;AAAA,IACrB;AACA,WAAO;AAAA,MACL,QAAQ,YAAY;AAAA,MACpB,QAAQ,cAAc;AAAA,MACtB,QAAQ,cAAc;AAAA,MACtB,QAAQ,mBAAmB;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,YAAY,OAAO,QAAQ,mCAAS,EAAE;AACtD;AAEA,IAAM,WAAW;AAAA,EACf,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,UAAU;AACZ;AAEA,IAAM,YACJ;AACF,IAAM,YACJ;AACF,IAAM,gBAAgB;AAEtB,SAAS,gBAAgB,YAAY;AACnC,QAAM,cAAc,CAAC;AACrB,QAAM,QAAQ,WAAW,MAAM,SAAS,iBAAiB;AACzD,MAAI;AAIJ,MAAI,MAAM,SAAS,GAAG;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,IAAI,KAAK,MAAM,CAAC,CAAC,GAAG;AACtB,iBAAa,MAAM,CAAC;AAAA,EACtB,OAAO;AACL,gBAAY,OAAO,MAAM,CAAC;AAC1B,iBAAa,MAAM,CAAC;AACpB,QAAI,SAAS,kBAAkB,KAAK,YAAY,IAAI,GAAG;AACrD,kBAAY,OAAO,WAAW,MAAM,SAAS,iBAAiB,EAAE,CAAC;AACjE,mBAAa,WAAW;AAAA,QACtB,YAAY,KAAK;AAAA,QACjB,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,MAAI,YAAY;AACd,UAAM,QAAQ,SAAS,SAAS,KAAK,UAAU;AAC/C,QAAI,OAAO;AACT,kBAAY,OAAO,WAAW,QAAQ,MAAM,CAAC,GAAG,EAAE;AAClD,kBAAY,WAAW,MAAM,CAAC;AAAA,IAChC,OAAO;AACL,kBAAY,OAAO;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,YAAY,kBAAkB;AAC/C,QAAM,QAAQ,IAAI;AAAA,IAChB,0BACG,IAAI,oBACL,yBACC,IAAI,oBACL;AAAA,EACJ;AAEA,QAAM,WAAW,WAAW,MAAM,KAAK;AAEvC,MAAI,CAAC,SAAU,QAAO,EAAE,MAAM,KAAK,gBAAgB,GAAG;AAEtD,QAAM,OAAO,SAAS,CAAC,IAAI,SAAS,SAAS,CAAC,CAAC,IAAI;AACnD,QAAM,UAAU,SAAS,CAAC,IAAI,SAAS,SAAS,CAAC,CAAC,IAAI;AAGtD,SAAO;AAAA,IACL,MAAM,YAAY,OAAO,OAAO,UAAU;AAAA,IAC1C,gBAAgB,WAAW,OAAO,SAAS,CAAC,KAAK,SAAS,CAAC,GAAG,MAAM;AAAA,EACtE;AACF;AAEA,SAAS,UAAU,YAAY,MAAM;AAEnC,MAAI,SAAS,KAAM,QAAO,oBAAI,KAAK,GAAG;AAEtC,QAAM,WAAW,WAAW,MAAM,SAAS;AAE3C,MAAI,CAAC,SAAU,QAAO,oBAAI,KAAK,GAAG;AAElC,QAAM,aAAa,CAAC,CAAC,SAAS,CAAC;AAC/B,QAAM,YAAY,cAAc,SAAS,CAAC,CAAC;AAC3C,QAAM,QAAQ,cAAc,SAAS,CAAC,CAAC,IAAI;AAC3C,QAAM,MAAM,cAAc,SAAS,CAAC,CAAC;AACrC,QAAM,OAAO,cAAc,SAAS,CAAC,CAAC;AACtC,QAAM,YAAY,cAAc,SAAS,CAAC,CAAC,IAAI;AAE/C,MAAI,YAAY;AACd,QAAI,CAAC,iBAAiB,MAAM,MAAM,SAAS,GAAG;AAC5C,aAAO,oBAAI,KAAK,GAAG;AAAA,IACrB;AACA,WAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,EAC/C,OAAO;AACL,UAAM,OAAO,oBAAI,KAAK,CAAC;AACvB,QACE,CAAC,aAAa,MAAM,OAAO,GAAG,KAC9B,CAAC,sBAAsB,MAAM,SAAS,GACtC;AACA,aAAO,oBAAI,KAAK,GAAG;AAAA,IACrB;AACA,SAAK,eAAe,MAAM,OAAO,KAAK,IAAI,WAAW,GAAG,CAAC;AACzD,WAAO;AAAA,EACT;AACF;AAEA,SAAS,cAAc,OAAO;AAC5B,SAAO,QAAQ,SAAS,KAAK,IAAI;AACnC;AAEA,SAAS,UAAU,YAAY;AAC7B,QAAM,WAAW,WAAW,MAAM,SAAS;AAC3C,MAAI,CAAC,SAAU,QAAO;AAEtB,QAAM,QAAQ,cAAc,SAAS,CAAC,CAAC;AACvC,QAAM,UAAU,cAAc,SAAS,CAAC,CAAC;AACzC,QAAM,UAAU,cAAc,SAAS,CAAC,CAAC;AAEzC,MAAI,CAAC,aAAa,OAAO,SAAS,OAAO,GAAG;AAC1C,WAAO;AAAA,EACT;AAEA,SACE,QAAQ,qBAAqB,UAAU,uBAAuB,UAAU;AAE5E;AAEA,SAAS,cAAc,OAAO;AAC5B,SAAQ,SAAS,WAAW,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAM;AAC3D;AAEA,SAAS,cAAc,gBAAgB;AACrC,MAAI,mBAAmB,IAAK,QAAO;AAEnC,QAAM,WAAW,eAAe,MAAM,aAAa;AACnD,MAAI,CAAC,SAAU,QAAO;AAEtB,QAAM,OAAO,SAAS,CAAC,MAAM,MAAM,KAAK;AACxC,QAAM,QAAQ,SAAS,SAAS,CAAC,CAAC;AAClC,QAAM,UAAW,SAAS,CAAC,KAAK,SAAS,SAAS,CAAC,CAAC,KAAM;AAE1D,MAAI,CAAC,iBAAiB,OAAO,OAAO,GAAG;AACrC,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,QAAQ,qBAAqB,UAAU;AACxD;AAEA,SAAS,iBAAiB,aAAa,MAAM,KAAK;AAChD,QAAM,OAAO,oBAAI,KAAK,CAAC;AACvB,OAAK,eAAe,aAAa,GAAG,CAAC;AACrC,QAAM,qBAAqB,KAAK,UAAU,KAAK;AAC/C,QAAM,QAAQ,OAAO,KAAK,IAAI,MAAM,IAAI;AACxC,OAAK,WAAW,KAAK,WAAW,IAAI,IAAI;AACxC,SAAO;AACT;AAKA,IAAM,eAAe,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAEtE,SAAS,gBAAgB,MAAM;AAC7B,SAAO,OAAO,QAAQ,KAAM,OAAO,MAAM,KAAK,OAAO,QAAQ;AAC/D;AAEA,SAAS,aAAa,MAAM,OAAO,MAAM;AACvC,SACE,SAAS,KACT,SAAS,MACT,QAAQ,KACR,SAAS,aAAa,KAAK,MAAM,gBAAgB,IAAI,IAAI,KAAK;AAElE;AAEA,SAAS,sBAAsB,MAAM,WAAW;AAC9C,SAAO,aAAa,KAAK,cAAc,gBAAgB,IAAI,IAAI,MAAM;AACvE;AAEA,SAAS,iBAAiB,OAAO,MAAM,KAAK;AAC1C,SAAO,QAAQ,KAAK,QAAQ,MAAM,OAAO,KAAK,OAAO;AACvD;AAEA,SAAS,aAAa,OAAO,SAAS,SAAS;AAC7C,MAAI,UAAU,IAAI;AAChB,WAAO,YAAY,KAAK,YAAY;AAAA,EACtC;AAEA,SACE,WAAW,KACX,UAAU,MACV,WAAW,KACX,UAAU,MACV,SAAS,KACT,QAAQ;AAEZ;AAEA,SAAS,iBAAiB,QAAQ,SAAS;AACzC,SAAO,WAAW,KAAK,WAAW;AACpC;;;AC5PO,SAAS,UAAU,SAAS,SAAS;AAC1C,QAAM,QAAQ,QAAQ;AAAA,IACpB;AAAA,EACF;AAEA,MAAI,CAAC,MAAO,QAAO,OAAO,KAAK,mCAAS,EAAE;AAE1C,SAAO;AAAA,IACL,KAAK;AAAA,MACH,CAAC,MAAM,CAAC;AAAA,MACR,CAAC,MAAM,CAAC,IAAI;AAAA,MACZ,CAAC,MAAM,CAAC;AAAA,MACR,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,MAAM,MAAM,CAAC,KAAK,MAAM,KAAK;AAAA,MACvD,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,MAAM,MAAM,CAAC,KAAK,MAAM,KAAK;AAAA,MACxD,CAAC,MAAM,CAAC;AAAA,MACR,GAAG,MAAM,CAAC,KAAK,OAAO,MAAM,UAAU,GAAG,CAAC;AAAA,IAC5C;AAAA,IACA,mCAAS;AAAA,EACX;AACF;;;ACvBO,SAAS,YAAY,MAAM,KAAK,SAAS;AAC9C,MAAI,QAAQ,OAAO,MAAM,OAAO,IAAI;AACpC,MAAI,SAAS,EAAG,UAAS;AAEzB,SAAO,QAAQ,MAAM,OAAO,OAAO;AACrC;;;ACZO,SAAS,eAAe,MAAM,SAAS;AAC5C,SAAO,YAAY,MAAM,GAAG,OAAO;AACrC;;;ACFO,SAAS,eAAe,MAAM,SAAS;AAC5C,SAAO,YAAY,MAAM,GAAG,OAAO;AACrC;;;ACFO,SAAS,iBAAiB,MAAM,SAAS;AAC9C,SAAO,YAAY,MAAM,GAAG,OAAO;AACrC;;;ACFO,SAAS,eAAe,MAAM,SAAS;AAC5C,SAAO,YAAY,MAAM,GAAG,OAAO;AACrC;;;ACFO,SAAS,iBAAiB,MAAM,SAAS;AAC9C,SAAO,YAAY,MAAM,GAAG,OAAO;AACrC;;;ACFO,SAAS,gBAAgB,MAAM,SAAS;AAC7C,SAAO,YAAY,MAAM,GAAG,OAAO;AACrC;;;ACFO,SAAS,kBAAkB,MAAM,SAAS;AAC/C,SAAO,YAAY,MAAM,GAAG,OAAO;AACrC;;;ACVO,SAAS,iBAAiB,UAAU;AACzC,SAAO,KAAK,MAAM,WAAW,eAAe;AAC9C;;;ACGO,SAAS,gBAAgB,UAAU;AACxC,QAAM,QAAQ,WAAW;AACzB,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACuBO,SAAS,oBAAoB,MAAM,SAAS;AACjD,QAAM,aAAY,mCAAS,cAAa;AAExC,MAAI,YAAY,KAAK,YAAY;AAC/B,WAAO,eAAc,mCAAS,OAAM,MAAM,GAAG;AAE/C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,oBAAoB,MAAM,WAAW,IAAI;AAC/C,QAAM,oBAAoB,MAAM,WAAW,IAAI,KAAK;AACpD,QAAM,yBAAyB,MAAM,gBAAgB,IAAI,MAAO,KAAK;AACrE,QAAM,QACJ,MAAM,SAAS,IACf,oBACA,oBACA;AAEF,QAAM,UAAS,mCAAS,mBAAkB;AAC1C,QAAM,iBAAiB,kBAAkB,MAAM;AAE/C,QAAM,eAAe,eAAe,QAAQ,SAAS,IAAI;AAEzD,QAAM,SAAS,cAAc,GAAG,GAAG,CAAC;AACpC,SAAO;AACT;;;AC5BO,SAAS,sBAAsB,MAAM,SAAS;AACnD,QAAM,aAAY,mCAAS,cAAa;AAExC,MAAI,YAAY,KAAK,YAAY,GAAI,QAAO,cAAc,MAAM,GAAG;AAEnE,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,oBAAoB,MAAM,WAAW,IAAI;AAC/C,QAAM,yBAAyB,MAAM,gBAAgB,IAAI,MAAO;AAChE,QAAM,UACJ,MAAM,WAAW,IAAI,oBAAoB;AAE3C,QAAM,UAAS,mCAAS,mBAAkB;AAC1C,QAAM,iBAAiB,kBAAkB,MAAM;AAE/C,QAAM,iBAAiB,eAAe,UAAU,SAAS,IAAI;AAE7D,QAAM,WAAW,gBAAgB,GAAG,CAAC;AACrC,SAAO;AACT;;;ACvCO,SAAS,eAAe,SAAS;AACtC,QAAM,QAAQ,UAAU;AACxB,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACRO,SAAS,sBAAsB,SAAS;AAC7C,SAAO,UAAU;AACnB;;;ACGO,SAAS,iBAAiB,SAAS;AACxC,QAAM,UAAU,UAAU;AAC1B,SAAO,KAAK,MAAM,OAAO;AAC3B;;;ACeO,SAAS,IAAI,MAAM,QAAQ,SAAS;AACzC,MAAI,QAAQ,OAAO,MAAM,mCAAS,EAAE;AAGpC,MAAI,MAAM,CAAC,KAAK,EAAG,QAAO,eAAc,mCAAS,OAAM,MAAM,GAAG;AAEhE,MAAI,OAAO,QAAQ,KAAM,OAAM,YAAY,OAAO,IAAI;AACtD,MAAI,OAAO,SAAS,KAAM,SAAQ,SAAS,OAAO,OAAO,KAAK;AAC9D,MAAI,OAAO,QAAQ,KAAM,OAAM,QAAQ,OAAO,IAAI;AAClD,MAAI,OAAO,SAAS,KAAM,OAAM,SAAS,OAAO,KAAK;AACrD,MAAI,OAAO,WAAW,KAAM,OAAM,WAAW,OAAO,OAAO;AAC3D,MAAI,OAAO,WAAW,KAAM,OAAM,WAAW,OAAO,OAAO;AAC3D,MAAI,OAAO,gBAAgB,KAAM,OAAM,gBAAgB,OAAO,YAAY;AAE1E,SAAO;AACT;;;AC7BO,SAAS,aAAa,MAAM,WAAW,SAAS;AACrD,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,SAAS,CAAC;AAChB,QAAM,QAAQ,SAAS;AACvB,SAAO;AACT;;;ACiBO,SAASC,mBAAkB,SAAS;AACzC,QAAM,SAAS,CAAC;AAChB,QAAM,iBAAiB,kBAAkB;AAEzC,aAAW,YAAY,gBAAgB;AACrC,QAAI,OAAO,UAAU,eAAe,KAAK,gBAAgB,QAAQ,GAAG;AAElE,aAAO,QAAQ,IAAI,eAAe,QAAQ;AAAA,IAC5C;AAAA,EACF;AAEA,aAAW,YAAY,SAAS;AAC9B,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,QAAQ,GAAG;AAC3D,UAAI,QAAQ,QAAQ,MAAM,QAAW;AAEnC,eAAO,OAAO,QAAQ;AAAA,MACxB,OAAO;AAEL,eAAO,QAAQ,IAAI,QAAQ,QAAQ;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AAEA,oBAA0B,MAAM;AAClC;;;AC7CO,SAAS,WAAW,MAAM,SAAS,SAAS;AACjD,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,aAAa,KAAK,MAAM,MAAM,SAAS,IAAI,CAAC,IAAI;AACtD,QAAM,OAAO,UAAU;AACvB,SAAO,SAAS,OAAO,MAAM,SAAS,IAAI,OAAO,CAAC;AACpD;;;ACeO,SAAS,YAAY,MAAM,UAAU,SAAS;AAjDrD;AAkDE,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,yBACJ,mCAAS,4BACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,0BAC1B,eAAe,2BACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,0BAChC;AAEF,QAAM,OAAO;AAAA,IACX,OAAO,MAAM,mCAAS,EAAE;AAAA,IACxB,gBAAgB,MAAM,OAAO;AAAA,IAC7B;AAAA,EACF;AAEA,QAAM,YAAY,eAAc,mCAAS,OAAM,MAAM,CAAC;AACtD,YAAU,YAAY,UAAU,GAAG,qBAAqB;AACxD,YAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAE7B,QAAM,QAAQ,gBAAgB,WAAW,OAAO;AAChD,QAAM,QAAQ,MAAM,QAAQ,IAAI,IAAI;AACpC,SAAO;AACT;;;AC5CO,SAAS,cAAc,MAAM,SAAS;AAI3C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,SAAS,KAAK,MAAM,OAAO,EAAE,IAAI;AACvC,QAAM,YAAY,QAAQ,GAAG,CAAC;AAC9B,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;;;ACXO,SAAS,aAAa,SAAS;AACpC,SAAO,WAAW,KAAK,IAAI,GAAG,OAAO;AACvC;;;ACDO,SAAS,gBAAgB,SAAS;AACvC,QAAM,MAAM,aAAa,mCAAS,EAAE;AACpC,QAAM,OAAO,IAAI,YAAY;AAC7B,QAAM,QAAQ,IAAI,SAAS;AAC3B,QAAM,MAAM,IAAI,QAAQ;AAExB,QAAM,OAAO,cAAc,mCAAS,IAAI,CAAC;AACzC,OAAK,YAAY,MAAM,OAAO,MAAM,CAAC;AACrC,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACXO,SAAS,iBAAiB,SAAS;AACxC,QAAM,MAAM,aAAa,mCAAS,EAAE;AACpC,QAAM,OAAO,IAAI,YAAY;AAC7B,QAAM,QAAQ,IAAI,SAAS;AAC3B,QAAM,MAAM,IAAI,QAAQ;AAExB,QAAM,OAAO,aAAa,mCAAS,EAAE;AACrC,OAAK,YAAY,MAAM,OAAO,MAAM,CAAC;AACrC,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACRO,SAAS,UAAU,MAAM,QAAQ,SAAS;AAC/C,SAAO,UAAU,MAAM,CAAC,QAAQ,OAAO;AACzC;;;ACoBO,SAAS,IAAI,MAAM,UAAU,SAAS;AAC3C,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,QAAAC,UAAS;AAAA,IACT,QAAQ;AAAA,IACR,MAAAC,QAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,IAAI;AAEJ,QAAM,gBAAgB,UAAU,MAAMD,UAAS,QAAQ,IAAI,OAAO;AAClE,QAAM,cAAc,QAAQ,eAAeC,QAAO,QAAQ,GAAG,OAAO;AAEpE,QAAM,eAAe,UAAU,QAAQ;AACvC,QAAM,eAAe,UAAU,eAAe;AAC9C,QAAM,UAAU,eAAe;AAE/B,SAAO,eAAc,mCAAS,OAAM,MAAM,CAAC,cAAc,OAAO;AAClE;;;ACzCO,SAAS,gBAAgB,MAAM,QAAQ,SAAS;AACrD,SAAO,gBAAgB,MAAM,CAAC,QAAQ,OAAO;AAC/C;;;ACFO,SAAS,SAAS,MAAM,QAAQ,SAAS;AAC9C,SAAO,SAAS,MAAM,CAAC,QAAQ,OAAO;AACxC;;;ACZO,SAAS,gBAAgB,MAAM,QAAQ,SAAS;AACrD,SAAO,gBAAgB,MAAM,CAAC,QAAQ,OAAO;AAC/C;;;ACQO,SAAS,WAAW,MAAM,QAAQ,SAAS;AAChD,SAAO,WAAW,MAAM,CAAC,QAAQ,OAAO;AAC1C;;;ACFO,SAAS,YAAY,MAAM,QAAQ,SAAS;AACjD,SAAO,YAAY,MAAM,CAAC,QAAQ,OAAO;AAC3C;;;ACPO,SAAS,WAAW,MAAM,QAAQ,SAAS;AAChD,SAAO,WAAW,MAAM,CAAC,QAAQ,OAAO;AAC1C;;;ACGO,SAAS,SAAS,MAAM,QAAQ,SAAS;AAC9C,SAAO,SAAS,MAAM,CAAC,QAAQ,OAAO;AACxC;;;ACFO,SAAS,SAAS,MAAM,QAAQ,SAAS;AAC9C,SAAO,SAAS,MAAM,CAAC,QAAQ,OAAO;AACxC;;;ACXO,SAAS,YAAY,OAAO;AACjC,SAAO,KAAK,MAAM,QAAQ,UAAU;AACtC;;;ACFO,SAAS,YAAY,OAAO;AACjC,SAAO,KAAK,MAAM,QAAQ,UAAU;AACtC;;;ACFO,SAAS,cAAc,OAAO;AACnC,SAAO,KAAK,MAAM,QAAQ,YAAY;AACxC;;;ACFO,SAAS,gBAAgB,OAAO;AACrC,SAAO,KAAK,MAAM,QAAQ,cAAc;AAC1C;", "names": ["months", "days", "interval", "days", "interval", "interval", "interval", "interval", "interval", "interval", "interval", "interval", "interval", "months", "days", "milliseconds", "days", "months", "format", "format", "format", "months", "days", "milliseconds", "interval", "months", "days", "months", "days", "milliseconds", "milliseconds", "milliseconds", "months", "months", "setDefaultOptions", "months", "days"]}