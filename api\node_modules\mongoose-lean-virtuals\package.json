{"name": "mongoose-lean-virtuals", "version": "1.1.1", "description": "Attach virtuals to the results of mongoose queries when using `.lean()`", "main": "index.js", "scripts": {"docs": "acquit-markdown -r acquit-ignore -p './test/examples.test.js' > examples.md", "lint": "eslint .", "test": "mocha ./test/*.js", "test-integration": "mocha ./test/integration.js", "test-travis": "istanbul cover ./node_modules/mocha/bin/_mocha -- -R spec ./test/*", "test-typescript": "tsc --strict test/test.ts && rm ./test/test.js"}, "repository": {"type": "git", "url": "https://github.com/vkarpov15/mongoose-lean-virtuals.git"}, "keywords": ["mongoose", "lean", "virtuals", "mongodb"], "dependencies": {"mpath": "^0.8.4"}, "devDependencies": {"@types/node": "22.10.5", "acquit": "1.x", "acquit-ignore": "0.1.0", "acquit-markdown": "0.1.0", "co": "4.6.0", "eslint": "7.x", "istanbul": "0.4.5", "mocha": "5.2.x", "mongoose": "8.x", "typescript": "5.x"}, "peerDependencies": {"mongoose": ">=5.11.10"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "Apache 2.0", "bugs": {"url": "https://github.com/vkarpov15/mongoose-lean-virtuals/issues"}, "engines": {"node": ">=16.20.1"}, "homepage": "https://github.com/vkarpov15/mongoose-lean-virtuals", "eslintConfig": {"extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2015}, "env": {"node": true, "es6": true}, "rules": {"comma-style": "error", "consistent-this": ["error", "_this"], "indent": ["error", 2, {"SwitchCase": 1, "VariableDeclarator": 2}], "keyword-spacing": "error", "no-buffer-constructor": "warn", "no-console": "off", "no-multi-spaces": "error", "func-call-spacing": "error", "no-trailing-spaces": "error", "quotes": ["error", "single"], "semi": "error", "space-before-blocks": "error", "space-before-function-paren": ["error", "never"], "space-infix-ops": "error", "space-unary-ops": "error", "no-var": "warn", "prefer-const": "warn", "strict": ["error", "global"], "no-restricted-globals": ["error", {"name": "context", "message": "Don't use <PERSON><PERSON>'s global context"}]}}}