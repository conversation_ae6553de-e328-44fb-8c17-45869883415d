import { Schema, model } from 'mongoose';
const parkingSpotSchema = new Schema({
  longitude: {
    type: Number,
    required: [true, "can't be blank"]
  },
  latitude: {
    type: Number,
    required: [true, "can't be blank"]
  },
  values: {
    type: [Schema.Types.ObjectId],
    ref: 'LocationValue',
    required: [true, "can't be blank"],
    validate: value => Array.isArray(value)
  }
}, {
  timestamps: true,
  strict: true,
  collection: 'ParkingSpot'
});
const ParkingSpot = model('ParkingSpot', parkingSpotSchema);
export default ParkingSpot;