import{u as e,e as s,j as t,S as r,R as a,a as i,P as o,z as l}from"../entries/index-CEzJO5Xy.js";import{d as n,r as c}from"./router-BtYqujaw.js";import{L as m}from"./Layout-BQBjg4Lf.js";import{b as d,e as u}from"./DressService-J0XavNJj.js";import{E as j}from"./Error-7KgmWHkR.js";import{S as p}from"./SimpleBackdrop-Bf3qjF13.js";import{A as h}from"./Avatar-BtfxKR-8.js";import{S as x,D as f,a as S,b as g,c as C}from"./create-dress-BwzUhfal.js";import{L as N}from"./LocationSelectList-DYJOB_U9.js";import{P as b}from"./Paper-CcwAvfvc.js";import{I as E}from"./Info-C_WcR51V.js";import{F as I,I as y}from"./InputLabel-BbcIE26O.js";import{I as v}from"./Input-BQdee9z7.js";import{F as M}from"./FormHelperText-DFSsjBsL.js";import{T as R}from"./TextField-BAse--ht.js";import{F as k,S as A}from"./Switch-BWPUOSX1.js";import{B as L}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./LocationService-BtQFgoWL.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";import"./SupplierService-DSnTbAgG.js";import"./MultipleSelect-C7xTvWe9.js";import"./Autocomplete-CviOU_ku.js";import"./OutlinedInput-g8mR4MM3.js";import"./useFormControl-B7jXtRD7.js";import"./Chip-CAtDqtgp.js";import"./IconButton-CnBvmeAK.js";import"./Flag-BR6CpE1z.js";import"./MenuItem-suKfXYI2.js";import"./Menu-ZU0DMgjT.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-Cay5TZBz.js";import"./listItemTextClasses-DFwCkkgK.js";import"./SwitchBase-BIeqtL5F.js";const W=()=>{const W=n(),{user:_}=e(),[P,O]=c.useState(!1),[D,T]=c.useState(!1),[q,B]=c.useState(!1),[U,G]=c.useState(!1),[$,w]=c.useState(!1),[F,Y]=c.useState(""),[z,V]=c.useState(""),[Z,H]=c.useState(""),[J,K]=c.useState([]),[Q,X]=c.useState(""),[ee,se]=c.useState(""),[te,re]=c.useState(""),[ae,ie]=c.useState(""),[oe,le]=c.useState(""),[ne,ce]=c.useState(""),[me,de]=c.useState(""),[ue,je]=c.useState(""),[pe,he]=c.useState(!1),[xe,fe]=c.useState([]),[Se,ge]=c.useState(!0),[Ce,Ne]=c.useState(!1),[be,Ee]=c.useState(!1),[Ie,ye]=c.useState(""),[ve,Me]=c.useState(""),[Re,ke]=c.useState(""),[Ae,Le]=c.useState(""),[We,_e]=c.useState(""),[Pe,Oe]=c.useState(""),[De,Te]=c.useState(!1),[qe,Be]=c.useState(""),[Ue,Ge]=c.useState(""),[$e,we]=c.useState(String(s.MINIMUM_AGE)),[Fe,Ye]=c.useState(!0),[ze,Ve]=c.useState(!1),[Ze,He]=c.useState(""),Je=e=>{const t=Number.parseInt(e,10);return!Number.isNaN(t)&&t>=s.MINIMUM_AGE},Ke=e=>""===e?-1:Number(e),Qe=e=>e&&Number(e)||null;return t.jsxs(m,{onLoad:async()=>{T(!0),Ve(!1),G(!1),w(!1);try{const e=JSON.parse(localStorage.getItem("bc-user")||"{}");if(e){const s=o(e);O(s),s&&H(e._id),B(!0)}else Ve(!0)}catch(e){l(e)}finally{T(!1)}},strict:!0,children:[t.jsx("div",{className:"create-dress",children:t.jsxs(b,{className:"dress-form dress-form-wrapper",elevation:10,style:q?{}:{display:"none"},children:[t.jsx("h1",{className:"dress-form-title",children:r.NEW_DRESS}),t.jsxs("form",{onSubmit:async e=>{try{if(e.preventDefault(),T(!0),!Je($e))return Ve(!0),void G(!1);if(!F)return G(!0),void w(!1);const s={loggedUser:_._id,name:z,supplier:Z,locations:J.map((e=>e._id)),dailyPrice:Number(Q),discountedDailyPrice:Qe(ee),biWeeklyPrice:Qe(te),discountedBiWeeklyPrice:Qe(ae),weeklyPrice:Qe(oe),discountedWeeklyPrice:Qe(ne),monthlyPrice:Qe(me),discountedMonthlyPrice:Qe(ue),deposit:Number(Ze),available:Se,fullyBooked:Ce,comingSoon:be,type:Ie,size:ve,color:Ae,length:Number(We),material:Pe,customizable:De,image:F,cancellation:Ke(qe),amendments:Ke(Ue),isDateBasedPrice:pe,dateBasedPrices:xe,range:"",accessories:[]},t=await u(s);t&&t._id?W("/dresses"):l()}catch(s){l(s)}finally{T(!1)}},children:[t.jsx(h,{type:a.Dress,mode:"create",record:null,size:"large",readonly:!1,onBeforeUpload:()=>{T(!0)},onChange:e=>{T(!1),Y(e),G(!1),w(!1)},onValidate:e=>{e||w(!0)},color:"disabled",className:"avatar-ctn"}),t.jsxs("div",{className:"info",children:[t.jsx(E,{}),t.jsx("span",{children:r.RECOMMENDED_IMAGE_SIZE})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{className:"required",children:i.NAME}),t.jsx(v,{id:"name",type:"text",required:!0,onChange:e=>{V(e.target.value)},autoComplete:"off"})]}),!P&&t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(x,{label:i.SUPPLIER,required:!0,onChange:e=>{H(e.length>0?e[0]._id:"")}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(N,{label:r.LOCATIONS,multiple:!0,required:!0,onChange:e=>{K(e)}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(f,{label:r.DRESS_TYPE,required:!0,onChange:e=>{ye(e)}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(S,{label:r.DRESS_SIZE,required:!0,onChange:e=>{Me(e)}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(g,{label:r.DRESS_STYLE,required:!0,onChange:e=>{ke(e)}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(C,{label:r.MATERIAL,required:!0,onChange:e=>{Oe(e)}})}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{className:"required",children:r.COLOR}),t.jsx(v,{id:"color",type:"text",required:!0,onChange:e=>{Le(e.target.value)},autoComplete:"off"})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsxs(y,{className:"required",children:[r.LENGTH," (cm)"]}),t.jsx(v,{id:"length",type:"number",required:!0,onChange:e=>{_e(e.target.value)},autoComplete:"off"})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{className:"required",children:`${r.PRICE} (${i.CURRENCY})`}),t.jsx(v,{id:"price",type:"number",required:!0,onChange:e=>{X(e.target.value)},autoComplete:"off"})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{children:`${r.DEPOSIT} (${i.CURRENCY})`}),t.jsx(v,{id:"deposit",type:"number",onChange:e=>{He(e.target.value)},autoComplete:"off"})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{className:"required",children:i.MINIMUM_AGE}),t.jsx(v,{id:"minimum-age",type:"number",required:!0,error:!Fe,onChange:e=>{we(e.target.value);const s=Je(e.target.value);Ye(s),s||Ve(!0)},autoComplete:"off",value:$e}),t.jsx(M,{error:!Fe,children:!Fe&&i.MINIMUM_AGE_NOT_VALID||""})]}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(R,{label:`${r.CANCELLATION} (${i.CURRENCY})`,slotProps:{htmlInput:{inputMode:"numeric",pattern:"^\\d+(\\.\\d+)?$"}},onChange:e=>{Be(e.target.value)},variant:"standard",autoComplete:"off",value:qe})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(R,{label:`${r.AMENDMENTS} (${i.CURRENCY})`,slotProps:{htmlInput:{inputMode:"numeric",pattern:"^\\d+(\\.\\d+)?$"}},onChange:e=>{Ge(e.target.value)},variant:"standard",autoComplete:"off",value:Ue})}),t.jsx(I,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:t.jsx(k,{control:t.jsx(A,{checked:De,onChange:e=>{Te(e.target.checked)},color:"primary"}),label:r.CUSTOMIZABLE,className:"checkbox-fcl"})}),t.jsx(I,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:t.jsx(k,{control:t.jsx(A,{checked:Se,onChange:e=>{ge(e.target.checked)},color:"primary"}),label:r.AVAILABLE,className:"checkbox-fcl"})}),t.jsx(I,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:t.jsx(k,{control:t.jsx(A,{checked:Ce,onChange:e=>{Ne(e.target.checked)},color:"primary"}),label:r.FULLY_BOOKED,className:"checkbox-fcl"})}),t.jsx(I,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:t.jsx(k,{control:t.jsx(A,{checked:be,onChange:e=>{Ee(e.target.checked)},color:"primary"}),label:r.COMING_SOON,className:"checkbox-fcl"})}),t.jsxs("div",{className:"buttons",children:[t.jsx(L,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:D,children:i.CREATE}),t.jsx(L,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{F&&await d(F),W("/dresses")},children:i.CANCEL})]}),t.jsxs("div",{className:"form-error",children:[U&&t.jsx(j,{message:i.IMAGE_REQUIRED}),$&&t.jsx(j,{message:r.IMAGE_SIZE_ERROR}),ze&&t.jsx(j,{message:i.FORM_ERROR})]})]})]})}),D&&t.jsx(p,{text:i.LOADING})]})};export{W as default};
