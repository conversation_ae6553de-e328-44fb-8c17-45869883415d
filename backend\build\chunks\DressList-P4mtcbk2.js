import{e as s,F as e,j as a,S as i,G as l,Z as t,a as r,_ as n,$ as c,a0 as d,a1 as o,W as m,a2 as j,a3 as x,z as h,H as A}from"../entries/index-CEzJO5Xy.js";import{d as N,r as p}from"./router-BtYqujaw.js";import{g as S,c as u,d as E}from"./DressService-J0XavNJj.js";import{P as f}from"./Pager-C0zDCFUN.js";import{S as I}from"./SimpleBackdrop-Bf3qjF13.js";import{S as g}from"./SupplierBadge-C4yEjxHC.js";import{C as T,a as L}from"./ArrowForwardIos-BMce9t8T.js";import{T as v}from"./Backdrop-Bzn12VyM.js";import{T as O}from"./Tooltip-BkJF6Mu0.js";import{D}from"./Checkroom-Bt6MiDKF.js";import{C as b,L as w}from"./Straighten-6ibCitj5.js";import{V as y}from"./Check-D745pofy.js";import{C}from"./Clear-BpXDeTL8.js";import{I as _}from"./IconButton-CnBvmeAK.js";import{V as R}from"./Visibility-BpW589Gm.js";import{E as B}from"./Edit-DIF9Bumd.js";import{D as F}from"./Delete-CnqjtpsJ.js";import{D as G,a as M,b as P}from"./Grow-CjOKj0i1.js";import{D as k}from"./DialogTitle-BZXwroUN.js";import{B as U}from"./Button-DGZYUY3P.js";import{I as z}from"./Info-C_WcR51V.js";const V=({suppliers:V,keyword:Y,dressSpecs:Q,dressType:Z,dressSize:K,dressStyle:J,deposit:X,availability:H,reload:W,dresses:$,user:q,booking:ss,className:es,loading:as,hideSupplier:is,hidePrice:ls,language:ts,range:rs,rentalsCount:ns,onLoad:cs,onDelete:ds})=>{const os=N(),[ms,js]=p.useState(),[xs,hs]=p.useState(!0),[As,Ns]=p.useState(!1),[ps,Ss]=p.useState(!1),[us,Es]=p.useState([]),[fs,Is]=p.useState(1),[gs,Ts]=p.useState(0),[Ls,vs]=p.useState(0),[Os,Ds]=p.useState(!1),[bs,ws]=p.useState(""),[ys,Cs]=p.useState(-1),[_s,Rs]=p.useState(!1);p.useEffect((()=>{if(s.isMobile){const e=document.querySelector("body");e&&(e.onscroll=()=>{ps&&!As&&window.scrollY>0&&window.scrollY+window.innerHeight+s.INFINITE_SCROLL_OFFSET>=document.body.scrollHeight&&(Ns(!0),Is(fs+1))})}}),[ps,As,fs]);const Bs=async(e,a,i,l,t,r,n,c,d,o,m)=>{try{Ns(!0);const n={suppliers:a??[],dressSpecs:l,dressType:t,dressSize:r,deposit:c,availability:d,ranges:o},m=await S(i||"",n,e,s.DRESSES_PAGE_SIZE),j=m&&m.length>0?m[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!j)return void h();const x=Array.isArray(j.pageInfo)&&j.pageInfo.length>0?j.pageInfo[0].totalRecords:0;let N=[];N=s.PAGINATION_MODE===A.PAGINATION_MODE.INFINITE_SCROLL||s.isMobile?1===e?j.resultData:[...us,...j.resultData]:j.resultData,Es(N),Ts((e-1)*s.DRESSES_PAGE_SIZE+N.length),vs(x),Ss(j.resultData.length>0),((s.PAGINATION_MODE===A.PAGINATION_MODE.INFINITE_SCROLL||s.isMobile)&&1===e||s.PAGINATION_MODE===A.PAGINATION_MODE.CLASSIC&&!s.isMobile)&&window.scrollTo(0,0),cs&&cs({rows:j.resultData,rowCount:x})}catch(j){h(j)}finally{Ns(!1),hs(!1)}};p.useEffect((()=>{V&&(V.length>0?Bs(fs,V,Y,Q,Z,K,0,X||0,H,rs):(Es([]),Ts(0),Ss(!1),cs&&cs({rows:[],rowCount:0}),hs(!1)))}),[fs,V,Y,Q,Z,K,J,X,H,rs,ns]),p.useEffect((()=>{$&&(Es($),Ts($.length),Ss(!1),cs&&cs({rows:$,rowCount:$.length}))}),[$]),p.useEffect((()=>{Is(1)}),[V,Y,Q,Z,K,J,X,H,rs,ns]),p.useEffect((()=>{W&&(Is(1),Bs(1,V,Y,Q,Z,K,0,X,H,rs))}),[W,V,Y,Q,Z,K,J,X,H,rs,ns]),p.useEffect((()=>{js(q)}),[q]);const Fs=async s=>{try{const e=s.currentTarget.getAttribute("data-id"),a=Number(s.currentTarget.getAttribute("data-index")),i=await u(e);200===i?Rs(!0):204===i?(Ds(!0),ws(e),Cs(a)):h()}catch(e){h(e)}},Gs=(s,e)=>{let i=!1;return ss&&("cancellation"===s&&ss.cancellation&&e>0&&(i=!0),"amendments"===s&&ss.amendments&&e>0&&(i=!0)),-1===e?a.jsx(C,{className:"unavailable"}):0===e||i?a.jsx(y,{className:"available"}):a.jsx(z,{className:"extra-info"})},Ms=e(ms);return ms&&a.jsxs(a.Fragment,{children:[a.jsxs("section",{className:(es?`${es} `:"")+"dress-list",children:[0===us.length?!xs&&!As&&!as&&a.jsx(T,{variant:"outlined",className:"empty-list",children:a.jsx(L,{children:a.jsx(v,{color:"textSecondary",children:i.EMPTY_LIST})})}):us.map(((e,h)=>{const A=Ms||e.supplier._id===ms._id;return a.jsxs("article",{children:[a.jsxs("div",{className:"dress",children:[a.jsx("img",{src:l(s.CDN_DRESSES,e.image),alt:e.name,className:"dress-img"}),a.jsxs("div",{className:"dress-footer",children:[a.jsx("div",{className:"dress-footer-row1",children:a.jsxs("div",{className:"rating",children:[e.rating&&e.rating>=1&&a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"value",children:e.rating.toFixed(2)}),a.jsx("img",{alt:"Rating",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJYSURBVFhHxZdBdtMwEIZn5LQ0XeUGdd8j77FrewLSE9AdhA30BMAJaE5QeoKWBS+wKydoOQFlS7swN8iC99KX2BpmZDnvuZFaiTjpt9HItjy/ZGlmDI8N2jaa8bD7Vil8IbbW9L3dvz4zNyL5LwGTb91jInhvuwaFeLT28vfAdoOJFmBmjnBquzU0wWHsSijbBsPOP1pzjvvu+YgSILPnJi17TlL7TDBRAkJmGLsKwQICZl8RtQpBm5BO086kvfaTzRABQrY+nu7hYTayfS9OAeLwdqO1y0dtt6VgCxB7YtvboWQIdJ5r+IMIVxu3+ZVL0EyAOM03199pIjnfnfJq47AouCxoOmj3s0wuGAHj4bNUYXHBZugSL0qmabovIswmZOeyc1flXEiREhNJq1MQ+30XBpUyeaQUgGS+xyrhDWp8GgFaw4m0q6Qg/CytEdDu31xyIjEXVgFpfVIlrVoc4Ah2xqH0je0uBZkoO59FyrlAtEwRd50LcwKEZYhwORecAoTJ1+4FAfRsd1GyJ6+ut61do4oDS4Vn6T3mXgE8+8aCE6H/XU4Bky9dGdBcQiLo/C3fOYdTQI7U1Lef0Urc73QKSBJ4bs3GUIA71qzhFICEjdcDvj3lFMAPhwgYFZo+yL8A2w8mM04+zvLMI0D/sKYTrpoGXPNtb76++SQxXVOyz2MeSGjoFOmJhKZCkiK0thI8Cy6ncv77Kcupu5hxKj/mc3dgL1WMuALac43zRkJ5GSfNowRpi38+fxHiuWRNe/texsOnPSQ64J/XnXJswSvlFv3IAPwDtS/grYKzhw8AAAAASUVORK5CYII="})]}),e.rentals&&e.rentals>0&&a.jsx("span",{className:"rentals",children:`(${e.rentals} ${i.RENTALS||"rentals"})`})]})}),!is&&a.jsx(g,{supplier:e.supplier})]})]}),a.jsxs("div",{className:"dress-info",children:[a.jsxs("div",{className:"dress-info-header",children:[a.jsx("div",{className:"name",children:a.jsx("h2",{children:e.name})}),!ls&&a.jsx("div",{className:"price",children:`${t(e.dailyPrice,r.CURRENCY,ts)}${r.DAILY}`})]}),a.jsxs("ul",{className:"dress-info-list",children:[e.type!==n.Unknown&&a.jsx("li",{className:"dress-type",children:a.jsx(O,{title:c(e.type),placement:"top",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(D,{}),a.jsx("span",{className:"dress-info-list-text",children:d(e.type)})]})})}),a.jsx("li",{className:"dress-size",children:a.jsx(O,{title:o(e.size),placement:"top",children:a.jsx("div",{className:"dress-info-list-item",children:a.jsx("span",{className:"dress-info-list-text",children:m(e.size)})})})}),a.jsx("li",{className:"dress-color",children:a.jsx(O,{title:i.COLOR||"Color",placement:"top",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(b,{}),a.jsx("span",{className:"dress-info-list-text",children:e.color})]})})}),a.jsx("li",{className:"dress-length",children:a.jsx(O,{title:i.LENGTH||"Length",placement:"top",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(w,{}),a.jsx("span",{className:"dress-info-list-text",children:`${e.length} ${i.CM||"cm"}`})]})})}),e.customizable&&a.jsx("li",{className:"dress-customizable",children:a.jsx(O,{title:i.CUSTOMIZABLE_TOOLTIP||"This dress can be customized",placement:"top",children:a.jsx("div",{className:"dress-info-list-item",children:a.jsx(y,{className:"available"})})})})]}),a.jsxs("ul",{className:"extras-list",children:[A&&a.jsxs(a.Fragment,{children:[a.jsx("li",{className:e.available?"dress-available":"dress-unavailable",children:a.jsx(O,{title:e.available?i.DRESS_AVAILABLE_TOOLTIP||"This dress is available":i.DRESS_UNAVAILABLE_TOOLTIP||"This dress is unavailable",children:a.jsxs("div",{className:"dress-info-list-item",children:[e.available?a.jsx(y,{}):a.jsx(C,{}),e.available?a.jsx("span",{className:"dress-info-list-text",children:i.DRESS_AVAILABLE||"Available"}):a.jsx("span",{className:"dress-info-list-text",children:i.DRESS_UNAVAILABLE||"Unavailable"})]})})}),e.fullyBooked&&a.jsx("li",{className:"dress-unavailable",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(C,{}),a.jsx("span",{className:"dress-info-list-text",children:i.FULLY_BOOKED||"Fully Booked"})]})}),e.comingSoon&&a.jsx("li",{className:"dress-coming-soon",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(y,{}),a.jsx("span",{className:"dress-info-list-text",children:i.COMING_SOON||"Coming Soon"})]})})]}),e.cancellation>-1&&a.jsx("li",{children:a.jsx(O,{title:ss?"":e.cancellation>-1?i.CANCELLATION_TOOLTIP||"The booking can be cancelled":j(e.cancellation,ts),placement:"left",children:a.jsxs("div",{className:"dress-info-list-item",children:[Gs("cancellation",e.cancellation),a.jsx("span",{className:"dress-info-list-text",children:j(e.cancellation,ts)})]})})}),e.amendments>-1&&a.jsx("li",{children:a.jsx(O,{title:ss?"":e.amendments>-1?i.AMENDMENTS_TOOLTIP||"The booking can be modified":x(e.amendments,ts),placement:"left",children:a.jsxs("div",{className:"dress-info-list-item",children:[Gs("amendments",e.amendments),a.jsx("span",{className:"dress-info-list-text",children:x(e.amendments,ts)})]})})})]}),a.jsx("div",{className:"action",children:A&&a.jsxs(a.Fragment,{children:[a.jsx(O,{title:i.VIEW_DRESS||"View Dress",children:a.jsx(_,{onClick:()=>os(`/dress?dr=${e._id}`),children:a.jsx(R,{})})}),a.jsx(O,{title:r.UPDATE,children:a.jsx(_,{onClick:()=>os(`/update-dress?dr=${e._id}`),children:a.jsx(B,{})})}),a.jsx(O,{title:r.DELETE,children:a.jsx(_,{"data-id":e._id,"data-index":h,onClick:Fs,children:a.jsx(F,{})})})]})})]})]},e._id)})),a.jsxs(G,{disableEscapeKeyDown:!0,maxWidth:"xs",open:_s,children:[a.jsx(k,{className:"dialog-header",children:r.INFO}),a.jsx(M,{children:i.CANNOT_DELETE_DRESS||"This dress cannot be deleted because it is linked to bookings"}),a.jsx(P,{className:"dialog-actions",children:a.jsx(U,{onClick:()=>{Rs(!1)},variant:"contained",className:"btn-secondary",children:r.CLOSE})})]}),a.jsxs(G,{disableEscapeKeyDown:!0,maxWidth:"xs",open:Os,children:[a.jsx(k,{className:"dialog-header",children:r.CONFIRM_TITLE}),a.jsx(M,{children:i.DELETE_DRESS||"Are you sure you want to delete this dress?"}),a.jsxs(P,{className:"dialog-actions",children:[a.jsx(U,{onClick:()=>{Ds(!1),ws("")},variant:"contained",className:"btn-secondary",children:r.CANCEL}),a.jsx(U,{onClick:async()=>{try{if(""!==bs&&ys>-1)if(Ds(!1),200===await E(bs)){const s=gs-1;us.splice(ys,1),Es(us),Ts(s),vs(Ls-1),ws(""),Cs(-1),ds&&ds(s),Ns(!1)}else h(),ws(""),Cs(-1),Ns(!1);else h(),ws(""),Cs(-1),Ds(!1)}catch(s){h(s)}},variant:"contained",color:"error",children:r.DELETE})]})]})]}),!s.isMobile&&a.jsx(f,{page:fs,pageSize:s.DRESSES_PAGE_SIZE,rowCount:gs,totalRecords:Ls,onNext:()=>Is(fs+1),onPrevious:()=>Is(fs-1)}),As&&a.jsx(I,{text:r.LOADING})]})||a.jsx(a.Fragment,{})};export{V as D};
