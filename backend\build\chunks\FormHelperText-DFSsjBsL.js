import{r as e}from"./router-BtYqujaw.js";import{i as r,j as o,k as t,l as a}from"../entries/index-CEzJO5Xy.js";import{f as i}from"./InputLabel-BbcIE26O.js";import{u as s}from"./useFormControl-B7jXtRD7.js";import{a as n,g as l,s as d,c as m,m as c}from"./Button-DGZYUY3P.js";function p(e){return l("MuiFormHelperText",e)}const u=n("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var f;const g=d("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,o.size&&r[`size${a(o.size)}`],o.contained&&r.contained,o.filled&&r.filled]}})(c((({theme:e})=>({color:(e.vars||e).palette.text.secondary,...e.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${u.disabled}`]:{color:(e.vars||e).palette.text.disabled},[`&.${u.error}`]:{color:(e.vars||e).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:({ownerState:e})=>e.contained,style:{marginLeft:14,marginRight:14}}]})))),v=e.forwardRef((function(e,n){const l=r({props:e,name:"MuiFormHelperText"}),{children:d,className:c,component:u="p",disabled:v,error:b,filled:x,focused:z,margin:h,required:j,variant:q,...F}=l,T=s(),w=i({props:l,muiFormControl:T,states:["variant","size","disabled","error","filled","focused","required"]}),y={...l,component:u,contained:"filled"===w.variant||"outlined"===w.variant,variant:w.variant,size:w.size,disabled:w.disabled,error:w.error,filled:w.filled,focused:w.focused,required:w.required};delete y.ownerState;const M=(e=>{const{classes:r,contained:o,size:t,disabled:i,error:s,filled:n,focused:l,required:d}=e,c={root:["root",i&&"disabled",s&&"error",t&&`size${a(t)}`,o&&"contained",l&&"focused",n&&"filled",d&&"required"]};return m(c,p,r)})(y);return o.jsx(g,{as:u,className:t(M.root,c),ref:n,...F,ownerState:y,children:" "===d?f||(f=o.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):d})}));export{v as F};
