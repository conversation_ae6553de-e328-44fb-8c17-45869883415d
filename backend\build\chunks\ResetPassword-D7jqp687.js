import{c as s,u as e,j as r,a as o,e as t,w as a,d as n,g as i,x as m,z as c,C as d}from"../entries/index-CEzJO5Xy.js";import{d as l,r as p}from"./router-BtYqujaw.js";import{u as j,z as f,s as h}from"./zod-4O8Zwsja.js";import{L as w}from"./Layout-BQBjg4Lf.js";import{s as u}from"./change-password-CyUFObAm.js";import{s as S}from"./reset-password-CKyLhs8Q.js";import{E as x}from"./Error-koMug0_G.js";import b from"./NoMatch-jvHCs4x8.js";import{I as g,F as _}from"./InputLabel-BbcIE26O.js";import{I as N}from"./Input-BQdee9z7.js";import{B as P}from"./Button-DGZYUY3P.js";import{F as y}from"./FormHelperText-DFSsjBsL.js";import{P as R}from"./Paper-CcwAvfvc.js";import"./vendor-dblfw9z9.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const C=f.object({password:f.string().min(t.PASSWORD_MIN_LENGTH,{message:o.PASSWORD_ERROR}),confirmPassword:f.string()}).refine((s=>s.password===s.confirmPassword),{path:["confirmPassword"],message:o.PASSWORDS_DONT_MATCH}),E=()=>{const t=s.c(69),f=l(),{setUser:E,setUserLoaded:A}=e(),[W,I]=p.useState(""),[L,O]=p.useState(""),[v,D]=p.useState(""),[H,F]=p.useState(!1),[T,q]=p.useState(!1),[M,U]=p.useState(!1);let k;t[0]===Symbol.for("react.memo_cache_sentinel")?(k={resolver:h(C),mode:"onSubmit"},t[0]=k):k=t[0];const{register:z,handleSubmit:B,formState:G,setError:J,clearErrors:K}=j(k),{errors:V,isSubmitting:Q}=G;let X;t[1]!==L||t[2]!==f||t[3]!==E||t[4]!==A||t[5]!==v||t[6]!==W?(X=async s=>{const{password:e}=s;try{const s={userId:W,token:v,password:e};if(200===await a(s)){const s=await n({email:L,password:e});if(200===s.status){const e=await i(s.data._id);U(!0),E(e),A(!0),200===await m(W)?f("/"):c()}else c()}else c()}catch(r){c(r)}},t[1]=L,t[2]=f,t[3]=E,t[4]=A,t[5]=v,t[6]=W,t[7]=X):X=t[7];const Y=X;let Z;t[8]!==J?(Z=async s=>{if(s)q(!0);else{const s=new URLSearchParams(window.location.search);if(s.has("u")&&s.has("e")&&s.has("t")){const r=s.get("u"),o=s.get("e"),t=s.get("t");if(r&&o&&t)try{200===await d(r,o,t)?(I(r),O(o),D(t),F(!0)):q(!0)}catch(e){const s=e;console.error(s),J("root",{})}else q(!0)}else q(!0)}},t[8]=J,t[9]=Z):Z=t[9];const $=Z,ss=H?"":"hidden";let es,rs;t[10]===Symbol.for("react.memo_cache_sentinel")?(es=r.jsx("h1",{children:S.RESET_PASSWORD_HEADING}),t[10]=es):es=t[10],t[11]!==B||t[12]!==Y?(rs=B(Y),t[11]=B,t[12]=Y,t[13]=rs):rs=t[13];const os=!!V.password;let ts,as,ns,is;t[14]===Symbol.for("react.memo_cache_sentinel")?(ts=r.jsx(g,{className:"required",children:u.NEW_PASSWORD}),t[14]=ts):ts=t[14],t[15]!==z?(as=z("password"),t[15]=z,t[16]=as):as=t[16],t[17]!==K?(ns=()=>K(),t[17]=K,t[18]=ns):ns=t[18],t[19]!==ns||t[20]!==as?(is=r.jsx(N,{...as,type:"password",required:!0,autoComplete:"new-password",onChange:ns}),t[19]=ns,t[20]=as,t[21]=is):is=t[21];const ms=!!V.password,cs=V.password?.message||"";let ds,ls;t[22]!==ms||t[23]!==cs?(ds=r.jsx(y,{error:ms,children:cs}),t[22]=ms,t[23]=cs,t[24]=ds):ds=t[24],t[25]!==is||t[26]!==ds||t[27]!==os?(ls=r.jsxs(_,{fullWidth:!0,margin:"dense",error:os,children:[ts,is,ds]}),t[25]=is,t[26]=ds,t[27]=os,t[28]=ls):ls=t[28];const ps=!!V.confirmPassword;let js,fs,hs,ws;t[29]===Symbol.for("react.memo_cache_sentinel")?(js=r.jsx(g,{className:"required",children:o.CONFIRM_PASSWORD}),t[29]=js):js=t[29],t[30]!==z?(fs=z("confirmPassword"),t[30]=z,t[31]=fs):fs=t[31],t[32]!==K?(hs=()=>K(),t[32]=K,t[33]=hs):hs=t[33],t[34]!==fs||t[35]!==hs?(ws=r.jsx(N,{type:"password",...fs,required:!0,autoComplete:"new-password",onChange:hs}),t[34]=fs,t[35]=hs,t[36]=ws):ws=t[36];const us=!!V.confirmPassword,Ss=V.confirmPassword?.message||"";let xs,bs,gs,_s,Ns,Ps,ys,Rs,Cs,Es;return t[37]!==us||t[38]!==Ss?(xs=r.jsx(y,{error:us,children:Ss}),t[37]=us,t[38]=Ss,t[39]=xs):xs=t[39],t[40]!==ps||t[41]!==ws||t[42]!==xs?(bs=r.jsxs(_,{fullWidth:!0,margin:"dense",error:ps,children:[js,ws,xs]}),t[40]=ps,t[41]=ws,t[42]=xs,t[43]=bs):bs=t[43],t[44]!==Q?(gs=r.jsx(P,{type:"submit",className:"btn-primary btn-margin btn-margin-bottom",variant:"contained",disabled:Q,children:o.SAVE}),t[44]=Q,t[45]=gs):gs=t[45],t[46]!==f?(_s=r.jsx(P,{variant:"outlined",color:"primary",className:"btn-margin-bottom",onClick:()=>f("/"),children:o.CANCEL}),t[46]=f,t[47]=_s):_s=t[47],t[48]!==gs||t[49]!==_s?(Ns=r.jsxs("div",{className:"buttons",children:[gs,_s]}),t[48]=gs,t[49]=_s,t[50]=Ns):Ns=t[50],t[51]!==ls||t[52]!==bs||t[53]!==Ns||t[54]!==rs?(Ps=r.jsx("div",{className:"reset-password",children:r.jsxs(R,{className:"reset-password-form",elevation:10,children:[es,r.jsxs("form",{onSubmit:rs,children:[ls,bs,Ns]})]})}),t[51]=ls,t[52]=bs,t[53]=Ns,t[54]=rs,t[55]=Ps):Ps=t[55],t[56]!==Ps||t[57]!==ss?(ys=r.jsx("div",{className:ss,children:Ps}),t[56]=Ps,t[57]=ss,t[58]=ys):ys=t[58],t[59]!==V.root?(Rs=V.root&&r.jsx(x,{}),t[59]=V.root,t[60]=Rs):Rs=t[60],t[61]!==M||t[62]!==T?(Cs=!M&&T&&r.jsx(b,{hideHeader:!0}),t[61]=M,t[62]=T,t[63]=Cs):Cs=t[63],t[64]!==$||t[65]!==ys||t[66]!==Rs||t[67]!==Cs?(Es=r.jsxs(w,{onLoad:$,strict:!1,children:[ys,Rs,Cs]}),t[64]=$,t[65]=ys,t[66]=Rs,t[67]=Cs,t[68]=Es):Es=t[68],Es};export{E as default};
