import{b as s,s as e,c as o,u as r,j as t,a,e as i,d as n,f as c,g as m,h as l,v as d}from"../entries/index-xsXxT3-W.js";import{d as p,r as S}from"./router-BtYqujaw.js";import{u as j,z as I,s as N}from"./zod-4O8Zwsja.js";import u from"./Header-D38Z_B_q.js";import{E as _}from"./Error-DRzAdbbx.js";import{P as h}from"./Paper-C-atefOs.js";import{F as w,I as E}from"./InputLabel-C8rcdOGQ.js";import{I as g}from"./Input-D1AdR9CM.js";import{F as x}from"./FormHelperText-DDZ4BMA4.js";import{B as f}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./BankDetailsService-Cq-yeaEa.js";import"./Avatar-CvDHTACZ.js";import"./DressService-DkS6e_O5.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-DiTut-u0.js";import"./Backdrop-Czag2Ija.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./MenuItem-P0BnGnrT.js";import"./Menu-C_-X8cS7.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-DEridHif.js";import"./listItemTextClasses-BcbgzvlE.js";import"./Toolbar-BTa0QYME.js";import"./IconButton-CxOCoGF3.js";import"./Slide-B_HgMHO0.js";import"./ListItem-Bmdw8GrH.js";import"./ListItemText-DUhWzkV9.js";import"./Flag-CMGasDVj.js";import"./useFormControl-B7jXtRD7.js";const C=new s({fr:{SIGN_IN_HEADING:"Connexion",SIGN_IN:"Se connecter",ERROR_IN_SIGN_IN:"E-mail ou mot de passe incorrect.",IS_BLACKLISTED:"Votre compte est suspendu.",RESET_PASSWORD:"Mot de passe oublié ?",STAY_CONNECTED:"Rester connecté"},en:{SIGN_IN_HEADING:"Sign in",SIGN_IN:"Sign in",ERROR_IN_SIGN_IN:"Incorrect email or password.",IS_BLACKLISTED:"Your account is suspended.",RESET_PASSWORD:"Forgot password?",STAY_CONNECTED:"Stay connected"},es:{SIGN_IN_HEADING:"Iniciar sesión",SIGN_IN:"Iniciar sesión",ERROR_IN_SIGN_IN:"Correo electrónico o contraseña incorrectos.",IS_BLACKLISTED:"Tu cuenta está suspendida.",RESET_PASSWORD:"¿Olvidaste tu contraseña?",STAY_CONNECTED:"Mantenerse conectado"}});e(C);const R=I.object({email:I.string().email({message:a.EMAIL_NOT_VALID}),password:I.string().min(i.PASSWORD_MIN_LENGTH,{message:a.PASSWORD_ERROR}),stayConnected:I.boolean().optional()}),T=()=>{const s=o.c(26),i=p(),{setUser:I,setUserLoaded:T}=r(),[A,D]=S.useState(!1);let b,v;s[0]===Symbol.for("react.memo_cache_sentinel")?(b=N(R),s[0]=b):b=s[0],s[1]===Symbol.for("react.memo_cache_sentinel")?(v={resolver:b,mode:"onSubmit",defaultValues:{stayConnected:!1}},s[1]=v):v=s[1];const{register:y,setValue:G,handleSubmit:L,formState:O,setError:P,clearErrors:B}=j(v),{errors:W,isSubmitting:F}=O;let k;s[2]!==P?(k=()=>{P("root",{message:C.ERROR_IN_SIGN_IN})},s[2]=P,s[3]=k):k=s[3];const H=k;let M;s[4]!==i||s[5]!==P||s[6]!==I||s[7]!==T||s[8]!==H?(M=async s=>{const{email:e,password:o,stayConnected:r}=s;try{const s={email:e,password:o,stayConnected:r},t=await n(s);if(200===t.status)if(t.data.blacklisted)await c(!1),P("root",{message:C.IS_BLACKLISTED});else{const s=await m(t.data._id);I(s),T(!0);const e=new URLSearchParams(window.location.search);e.has("u")?i(`/user${window.location.search}`):e.has("c")?i(`/supplier${window.location.search}`):e.has("cr")?i(`/car${window.location.search}`):e.has("b")?i(`/update-booking${window.location.search}`):i("/")}else H()}catch{H()}},s[4]=i,s[5]=P,s[6]=I,s[7]=T,s[8]=H,s[9]=M):M=s[9];const Y=M;let $,K,V,U,q;return s[10]!==i?($=()=>{(async()=>{try{e(C);const s=l();s?200===await d()&&(await m(s._id)?i(`/${window.location.search}`):await c()):D(!0)}catch{await c()}})()},K=[i],s[10]=i,s[11]=$,s[12]=K):($=s[11],K=s[12]),S.useEffect($,K),s[13]===Symbol.for("react.memo_cache_sentinel")?(V=t.jsx(u,{}),s[13]=V):V=s[13],s[14]!==B||s[15]!==W||s[16]!==L||s[17]!==F||s[18]!==i||s[19]!==Y||s[20]!==y||s[21]!==G||s[22]!==A?(U=A&&t.jsx("div",{className:"signin",children:t.jsx(h,{className:"signin-form "+(A?"":"hidden"),elevation:10,children:t.jsxs("form",{onSubmit:L(Y),children:[t.jsx("h1",{className:"signin-form-title",children:C.SIGN_IN_HEADING}),t.jsxs(w,{fullWidth:!0,margin:"dense",error:!!W.email,children:[t.jsx(E,{htmlFor:"email",children:a.EMAIL}),t.jsx(g,{...y("email"),onChange:s=>{B(),G("email",s.target.value)},autoComplete:"email",required:!0}),t.jsx(x,{error:!!W.email,children:W.email?.message||""})]}),t.jsxs(w,{fullWidth:!0,margin:"dense",error:!!W.password,children:[t.jsx(E,{htmlFor:"password",children:a.PASSWORD}),t.jsx(g,{...y("password"),onChange:s=>{B(),G("password",s.target.value)},type:"password",autoComplete:"password",required:!0}),t.jsx(x,{error:!!W.password,children:W.password?.message||""})]}),t.jsxs("div",{className:"stay-connected",children:[t.jsx("input",{id:"stay-connected",type:"checkbox",onChange:s=>{G("stayConnected",s.currentTarget.checked)}}),t.jsx("label",{htmlFor:"stay-connected",children:C.STAY_CONNECTED})]}),t.jsx("div",{className:"forgot-password",children:t.jsx(f,{variant:"text",onClick:()=>i("/forgot-password"),className:"btn-lnk",children:C.RESET_PASSWORD})}),t.jsx("div",{className:"signin-buttons",children:t.jsx(f,{type:"submit",variant:"contained",size:"small",className:"btn-primary",disabled:F,children:C.SIGN_IN})}),t.jsx("div",{className:"form-error",children:W.root&&t.jsx(_,{message:W.root.message})})]})})}),s[14]=B,s[15]=W,s[16]=L,s[17]=F,s[18]=i,s[19]=Y,s[20]=y,s[21]=G,s[22]=A,s[23]=U):U=s[23],s[24]!==U?(q=t.jsxs("div",{children:[V,U]}),s[24]=U,s[25]=q):q=s[25],q};export{T as default};
