import{b as e,s,c as r,u as o,j as a,a as t,e as i,D as n,p as m,E as l,d as c,g as d}from"../entries/index-CEzJO5Xy.js";import{d as p,r as u}from"./router-BtYqujaw.js";import{u as _,z as f,s as S}from"./zod-4O8Zwsja.js";import{L as h}from"./Layout-BQBjg4Lf.js";import{E as j}from"./Error-7KgmWHkR.js";import{S as g}from"./SimpleBackdrop-Bf3qjF13.js";import{I as R,F as N}from"./InputLabel-BbcIE26O.js";import{I as P}from"./Input-BQdee9z7.js";import{B as I}from"./Button-DGZYUY3P.js";import{F as w}from"./FormHelperText-DFSsjBsL.js";import{P as E}from"./Paper-CcwAvfvc.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const x=new e({fr:{SIGN_UP_HEADING:"Inscription",TOS_SIGN_UP:"J'ai lu et j'accepte les conditions générales d'utilisation.",SIGN_UP:"S'inscrire",RECAPTCHA_ERROR:"Veuillez remplir le captcha pour continuer.",SIGN_UP_ERROR:"Une erreur s'est produite lors de l'inscription."},en:{SIGN_UP_HEADING:"Sign up",TOS_SIGN_UP:"I read and agree with the Terms of Use.",SIGN_UP:"Sign up",RECAPTCHA_ERROR:"Fill out the captcha to continue.",SIGN_UP_ERROR:"An error occurred during sign up."},es:{SIGN_UP_HEADING:"Registrarse",TOS_SIGN_UP:"He leído y acepto los Términos de uso.",SIGN_UP:"Registrarse",RECAPTCHA_ERROR:"Complete el captcha para continuar.",SIGN_UP_ERROR:"Se produjo un error durante el registro."},ar:{SIGN_UP_HEADING:"إنشاء حساب",TOS_SIGN_UP:"لقد قرأت ووافقت على شروط الاستخدام.",SIGN_UP:"إنشاء حساب",RECAPTCHA_ERROR:"يرجى ملء الكابتشا للمتابعة.",SIGN_UP_ERROR:"حدث خطأ أثناء إنشاء الحساب."}});s(x);const A=f.object({fullName:f.string().min(1),email:f.string().email({message:t.EMAIL_NOT_VALID}),password:f.string().min(i.PASSWORD_MIN_LENGTH,{message:t.PASSWORD_ERROR}),confirmPassword:f.string()}).refine((e=>e.password===e.confirmPassword),{path:["confirmPassword"],message:t.PASSWORDS_DONT_MATCH}),G=()=>{const e=r.c(99),s=p(),{setUser:i,setUserLoaded:f}=o(),[G,C]=u.useState(!1);let b;e[0]===Symbol.for("react.memo_cache_sentinel")?(b={resolver:S(A),mode:"onSubmit"},e[0]=b):b=e[0];const{register:U,handleSubmit:O,formState:y,setError:T,clearErrors:L,setValue:D}=_(b),{errors:H,isSubmitting:v}=y;let F;e[1]!==s||e[2]!==T||e[3]!==i||e[4]!==f?(F=async e=>{try{if(200!==await n({email:e.email}))return void T("email",{message:t.EMAIL_ALREADY_REGISTERED});const r={email:e.email,password:e.password,fullName:e.fullName,language:m()};if(200===await l(r)){const r=await c({email:e.email,password:e.password});if(200===r.status){const e=await d(r.data._id);i(e),f(!0),s(`/${window.location.search}`)}}}catch(r){const e=r;console.error(e),T("root",{message:x.SIGN_UP_ERROR})}},e[1]=s,e[2]=T,e[3]=i,e[4]=f,e[5]=F):F=e[5];const W=F;let M;e[6]!==s?(M=e=>{e?s("/"):C(!0)},e[6]=s,e[7]=M):M=e[7];const q=M;let z,B,k,V,J,K,Y,$,Q,X,Z;e[8]!==G?(z=G?{}:{display:"none"},e[8]=G,e[9]=z):z=e[9],e[10]===Symbol.for("react.memo_cache_sentinel")?(B=a.jsx("h1",{className:"signup-form-title",children:x.SIGN_UP_HEADING}),e[10]=B):B=e[10],e[11]!==O||e[12]!==W?(k=O(W),e[11]=O,e[12]=W,e[13]=k):k=e[13],e[14]===Symbol.for("react.memo_cache_sentinel")?(V=a.jsx(R,{htmlFor:"full-name",children:t.FULL_NAME}),e[14]=V):V=e[14],e[15]!==U?(J=U("fullName"),e[15]=U,e[16]=J):J=e[16],e[17]!==D?(K=e=>{D("fullName",e.target.value)},e[17]=D,e[18]=K):K=e[18],e[19]!==J||e[20]!==K?(Y=a.jsxs(N,{fullWidth:!0,margin:"dense",children:[V,a.jsx(P,{type:"text",...J,autoComplete:"off",onChange:K,required:!0})]}),e[19]=J,e[20]=K,e[21]=Y):Y=e[21],e[22]===Symbol.for("react.memo_cache_sentinel")?($=a.jsx(R,{htmlFor:"email",children:t.EMAIL}),e[22]=$):$=e[22],e[23]!==U?(Q=U("email"),e[23]=U,e[24]=Q):Q=e[24],e[25]!==L||e[26]!==D?(X=e=>{L("email"),D("email",e.target.value)},e[25]=L,e[26]=D,e[27]=X):X=e[27],e[28]!==Q||e[29]!==X?(Z=a.jsx(P,{type:"text",...Q,autoComplete:"off",onChange:X,required:!0}),e[28]=Q,e[29]=X,e[30]=Z):Z=e[30];const ee=!!H.email,se=H.email?.message||"";let re,oe,ae,te,ie,ne,me;e[31]!==ee||e[32]!==se?(re=a.jsx(w,{error:ee,children:se}),e[31]=ee,e[32]=se,e[33]=re):re=e[33],e[34]!==Z||e[35]!==re?(oe=a.jsxs(N,{fullWidth:!0,margin:"dense",children:[$,Z,re]}),e[34]=Z,e[35]=re,e[36]=oe):oe=e[36],e[37]===Symbol.for("react.memo_cache_sentinel")?(ae=a.jsx(R,{htmlFor:"password",children:t.PASSWORD}),e[37]=ae):ae=e[37],e[38]!==U?(te=U("password"),e[38]=U,e[39]=te):te=e[39],e[40]===Symbol.for("react.memo_cache_sentinel")?(ie={autoComplete:"new-password",form:{autoComplete:"off"}},e[40]=ie):ie=e[40],e[41]!==L||e[42]!==D?(ne=e=>{L("password"),D("password",e.target.value)},e[41]=L,e[42]=D,e[43]=ne):ne=e[43],e[44]!==te||e[45]!==ne?(me=a.jsx(P,{...te,type:"password",inputProps:ie,onChange:ne,required:!0}),e[44]=te,e[45]=ne,e[46]=me):me=e[46];const le=!!H.password,ce=H.password?.message||"";let de,pe,ue,_e,fe,Se,he;e[47]!==le||e[48]!==ce?(de=a.jsx(w,{error:le,children:ce}),e[47]=le,e[48]=ce,e[49]=de):de=e[49],e[50]!==me||e[51]!==de?(pe=a.jsxs(N,{fullWidth:!0,margin:"dense",children:[ae,me,de]}),e[50]=me,e[51]=de,e[52]=pe):pe=e[52],e[53]===Symbol.for("react.memo_cache_sentinel")?(ue=a.jsx(R,{htmlFor:"confirm-password",children:t.CONFIRM_PASSWORD}),e[53]=ue):ue=e[53],e[54]!==U?(_e=U("confirmPassword"),e[54]=U,e[55]=_e):_e=e[55],e[56]===Symbol.for("react.memo_cache_sentinel")?(fe={autoComplete:"new-password",form:{autoComplete:"off"}},e[56]=fe):fe=e[56],e[57]!==L||e[58]!==D?(Se=e=>{L("confirmPassword"),D("confirmPassword",e.target.value)},e[57]=L,e[58]=D,e[59]=Se):Se=e[59],e[60]!==_e||e[61]!==Se?(he=a.jsx(P,{..._e,type:"password",inputProps:fe,onChange:Se,required:!0}),e[60]=_e,e[61]=Se,e[62]=he):he=e[62];const je=!!H.confirmPassword,ge=H.confirmPassword?.message||"";let Re,Ne,Pe,Ie,we,Ee,xe,Ae,Ge,Ce,be,Ue;return e[63]!==je||e[64]!==ge?(Re=a.jsx(w,{error:je,children:ge}),e[63]=je,e[64]=ge,e[65]=Re):Re=e[65],e[66]!==he||e[67]!==Re?(Ne=a.jsxs(N,{fullWidth:!0,margin:"dense",children:[ue,he,Re]}),e[66]=he,e[67]=Re,e[68]=Ne):Ne=e[68],e[69]!==v?(Pe=a.jsx(I,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:v,children:x.SIGN_UP}),e[69]=v,e[70]=Pe):Pe=e[70],e[71]!==s?(Ie=a.jsx(I,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>s("/"),children:t.CANCEL}),e[71]=s,e[72]=Ie):Ie=e[72],e[73]!==Pe||e[74]!==Ie?(we=a.jsxs("div",{className:"buttons",children:[Pe,Ie]}),e[73]=Pe,e[74]=Ie,e[75]=we):we=e[75],e[76]!==Y||e[77]!==oe||e[78]!==pe||e[79]!==Ne||e[80]!==we?(Ee=a.jsxs("div",{children:[Y,oe,pe,Ne,we]}),e[76]=Y,e[77]=oe,e[78]=pe,e[79]=Ne,e[80]=we,e[81]=Ee):Ee=e[81],e[82]!==H.root?(xe=H.root&&a.jsx(j,{message:H.root.message}),e[82]=H.root,e[83]=xe):xe=e[83],e[84]!==xe?(Ae=a.jsx("div",{className:"form-error",children:xe}),e[84]=xe,e[85]=Ae):Ae=e[85],e[86]!==Ee||e[87]!==Ae||e[88]!==k?(Ge=a.jsxs("form",{onSubmit:k,children:[Ee,Ae]}),e[86]=Ee,e[87]=Ae,e[88]=k,e[89]=Ge):Ge=e[89],e[90]!==z||e[91]!==Ge?(Ce=a.jsx("div",{className:"signup",children:a.jsxs(E,{className:"signup-form",elevation:10,style:z,children:[B,Ge]})}),e[90]=z,e[91]=Ge,e[92]=Ce):Ce=e[92],e[93]!==v?(be=v&&a.jsx(g,{text:t.PLEASE_WAIT}),e[93]=v,e[94]=be):be=e[94],e[95]!==q||e[96]!==Ce||e[97]!==be?(Ue=a.jsxs(h,{strict:!1,onLoad:q,children:[Ce,be]}),e[95]=q,e[96]=Ce,e[97]=be,e[98]=Ue):Ue=e[98],Ue};export{G as default};
