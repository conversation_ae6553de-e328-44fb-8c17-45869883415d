import 'dotenv/config'
import bcrypt from 'bcrypt'
import * as bookcarsTypes from ':bookcars-types'
import * as env from '../config/env.config'
import * as logger from '../common/logger'
import * as databaseHelper from '../common/databaseHelper'
import User from '../models/User'
import Country from '../models/Country'
import Location from '../models/Location'
import LocationValue from '../models/LocationValue'
import Dress from '../models/Dress'

/**
 * Initialize dress rental data without deleting existing data
 * Creates Palestine country, Sofia Bridal & Soiree Boutique supplier, 
 * Al-Jalbouni Circle location, and sample dresses
 */
const initializeDressData = async (): Promise<boolean> => {
  try {
    logger.info('Initializing dress rental data...')

    // Check if data already exists
    const existingSupplier = await User.findOne({ fullName: 'Sofia Bridal & Soiree Boutique' })
    if (existingSupplier) {
      logger.info('Sofia Bridal & Soiree Boutique already exists, skipping initialization')
      return true
    }

    // Create Palestine country
    const country = await createPalestineCountry()
    logger.info('Palestine country created')

    // Create Sofia Bridal & Soiree Boutique supplier
    const supplier = await createSofiaBridalSupplier()
    logger.info('Sofia Bridal & Soiree Boutique supplier created')

    // Create Al-Jalbouni Circle location
    const location = await createAlJalbouniLocation(country._id!.toString(), supplier._id!.toString())
    logger.info('Al-Jalbouni Circle location created')

    // Create sample dresses
    await createSampleDresses(supplier._id!.toString(), location._id!.toString())
    logger.info('Sample dresses created')

    logger.info('Dress rental data initialization completed successfully')
    return true
  } catch (err) {
    logger.error('Error while initializing dress rental data:', err)
    return false
  }
}

/**
 * Create Sofia Bridal & Soiree Boutique supplier
 */
const createSofiaBridalSupplier = async () => {
  const salt = await bcrypt.genSalt(10)
  const passwordHash = await bcrypt.hash('supplier123', salt)

  const supplier = new User({
    email: '<EMAIL>',
    fullName: 'Sofia Bridal & Soiree Boutique',
    password: passwordHash,
    language: 'ar',
    type: bookcarsTypes.UserType.Supplier,
    active: true,
    verified: true,
    blacklisted: false,
    enableEmailNotifications: true,
    phone: '0599123456',
    location: 'Al-Jalbouni Circle, Jenin, Palestine',
    bio: 'Premium dress rental boutique in Jenin specializing in wedding and evening gowns',
    payLater: true,
    minimumRentalDays: 1,
    supplierDressLimit: 100,
    notifyAdminOnNewDress: true
  })

  await supplier.save()
  return supplier
}

/**
 * Create Palestine country with Arabic and English names
 */
const createPalestineCountry = async () => {
  // Check if Palestine already exists
  const existingCountry = await Country.findOne().populate('values')
  if (existingCountry) {
    const palestineValue = await LocationValue.findOne({ value: 'فلسطين' })
    if (palestineValue) {
      logger.info('Palestine country already exists')
      return existingCountry
    }
  }

  // Create location values for Palestine
  const palestineValues = [
    new LocationValue({
      language: 'en',
      value: 'Palestine'
    }),
    new LocationValue({
      language: 'fr',
      value: 'Palestine'
    }),
    new LocationValue({
      language: 'es',
      value: 'Palestina'
    })
  ]

  await LocationValue.insertMany(palestineValues)

  // Create country
  const country = new Country({
    values: palestineValues.map(v => v._id)
  })

  await country.save()
  return country
}

/**
 * Create Al-Jalbouni Circle, Jenin location
 */
const createAlJalbouniLocation = async (countryId: string, supplierId: string) => {
  // Create location values for Al-Jalbouni Circle, Jenin
  const locationValues = [
    new LocationValue({
      language: 'en',
      value: 'Al-Jalbouni Circle, Jenin, Palestine'
    }),
    new LocationValue({
      language: 'fr',
      value: 'Cercle Al-Jalbouni, Jenin, Palestine'
    }),
    new LocationValue({
      language: 'es',
      value: 'Círculo Al-Jalbouni, Jenin, Palestina'
    })
  ]

  await LocationValue.insertMany(locationValues)

  // Create location (Jenin coordinates)
  const location = new Location({
    country: countryId,
    latitude: 32.4603,
    longitude: 35.2957,
    values: locationValues.map(v => v._id),
    supplier: supplierId
  })

  await location.save()
  return location
}

/**
 * Create sample dresses with images
 */
const createSampleDresses = async (supplierId: string, locationId: string) => {
  const sampleDresses = [
    {
      name: 'فستان زفاف أنيق - Elegant Wedding Gown',
      supplier: supplierId,
      minimumAge: 18,
      locations: [locationId],
      dailyPrice: 500,
      deposit: 100,
      available: true,
      type: bookcarsTypes.DressType.Wedding,
      size: bookcarsTypes.DressSize.M,
      color: 'أبيض - White',
      length: 180,
      material: bookcarsTypes.DressMaterial.Silk,
      cancellation: 24,
      amendments: 24,
      range: bookcarsTypes.DressRange.Bridal,
      accessories: [bookcarsTypes.DressAccessories.Veil],
      rentals: 0,
      designerName: 'Sofia Designs',
      customizable: true,
      image: 'wedding-dress-1.jpg'
    },
    {
      name: 'فستان سهرة أسود - Black Evening Dress',
      supplier: supplierId,
      minimumAge: 18,
      locations: [locationId],
      dailyPrice: 200,
      deposit: 50,
      available: true,
      type: bookcarsTypes.DressType.Evening,
      size: bookcarsTypes.DressSize.S,
      color: 'أسود - Black',
      length: 160,
      material: bookcarsTypes.DressMaterial.Chiffon,
      cancellation: 12,
      amendments: 12,
      range: bookcarsTypes.DressRange.Evening,
      accessories: [bookcarsTypes.DressAccessories.Jewelry],
      rentals: 0,
      designerName: 'Night Elegance',
      customizable: false,
      image: 'evening-dress-1.jpg'
    },
    {
      name: 'فستان كوكتيل أحمر - Red Cocktail Dress',
      supplier: supplierId,
      minimumAge: 18,
      locations: [locationId],
      dailyPrice: 150,
      deposit: 30,
      available: true,
      type: bookcarsTypes.DressType.Cocktail,
      size: bookcarsTypes.DressSize.L,
      color: 'أحمر - Red',
      length: 120,
      material: bookcarsTypes.DressMaterial.Satin,
      cancellation: 6,
      amendments: 6,
      range: bookcarsTypes.DressRange.Cocktail,
      accessories: [bookcarsTypes.DressAccessories.Shoes],
      rentals: 0,
      designerName: 'Cocktail Couture',
      customizable: true,
      image: 'cocktail-dress-1.jpg'
    },
    {
      name: 'فستان حفلة تخرج وردي - Pink Prom Dress',
      supplier: supplierId,
      minimumAge: 16,
      locations: [locationId],
      dailyPrice: 180,
      deposit: 40,
      available: true,
      type: bookcarsTypes.DressType.Prom,
      size: bookcarsTypes.DressSize.M,
      color: 'وردي - Pink',
      length: 170,
      material: bookcarsTypes.DressMaterial.Tulle,
      cancellation: 12,
      amendments: 12,
      range: bookcarsTypes.DressRange.Evening,
      accessories: [bookcarsTypes.DressAccessories.Headpiece],
      rentals: 0,
      designerName: 'Prom Dreams',
      customizable: false,
      image: 'prom-dress-1.jpg'
    },
    {
      name: 'فستان عصري أزرق - Modern Blue Dress',
      supplier: supplierId,
      minimumAge: 18,
      locations: [locationId],
      dailyPrice: 120,
      deposit: 25,
      available: true,
      type: bookcarsTypes.DressType.Modern,
      size: bookcarsTypes.DressSize.XL,
      color: 'أزرق - Blue',
      length: 140,
      material: bookcarsTypes.DressMaterial.Cotton,
      cancellation: 6,
      amendments: 6,
      range: bookcarsTypes.DressRange.Casual,
      accessories: [],
      rentals: 0,
      designerName: 'Modern Style',
      customizable: true,
      image: 'modern-dress-1.jpg'
    }
  ]

  await Dress.insertMany(sampleDresses)
  logger.info(`Created ${sampleDresses.length} sample dresses`)
}

// Run the initialization if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    if (await databaseHelper.connect(env.DB_URI, env.DB_SSL, env.DB_DEBUG)) {
      await initializeDressData()
      await databaseHelper.close()
      logger.info('Database connection closed')
      process.exit(0)
    } else {
      logger.error('Failed to connect to database')
      process.exit(1)
    }
  })()
}

export { initializeDressData }
