import{b as e,s as t,j as s,F as a,R as o,a as r,e as i,J as n,K as l,z as u,B as m}from"../entries/index-xsXxT3-W.js";import{d as c,r as d}from"./router-BtYqujaw.js";import{L as p}from"./Layout-DaeN7D4t.js";import{C as j,s as g,P as f,a as h}from"./ParkingSpotEditList-52LgTzWs.js";import{s as v}from"./suppliers-Dwe__EFi.js";import{e as S,v as A,u as x}from"./LocationService-6NvQT9iL.js";import L from"./NoMatch-DMPclUW6.js";import{E as N}from"./Error-FiYP5RHa.js";import{S as C}from"./SimpleBackdrop-CqsJhYJ4.js";import{A as T}from"./Avatar-CvDHTACZ.js";import{S as E}from"./SupplierBadge-ehv63WPF.js";import{P as I}from"./Paper-C-atefOs.js";import{F as O,a as P,I as _}from"./InputLabel-C8rcdOGQ.js";import{I as D}from"./Input-D1AdR9CM.js";import{F as U}from"./FormHelperText-DDZ4BMA4.js";import{B as b}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./CountryService-CPWL_VJK.js";import"./MultipleSelect-DovAF4K6.js";import"./Autocomplete-CWN5GAd4.js";import"./OutlinedInput-BX8yFQbF.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-DiTut-u0.js";import"./TextField-D_yQOTzE.js";import"./Backdrop-Czag2Ija.js";import"./Menu-C_-X8cS7.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-DEridHif.js";import"./AccountCircle-DdIeIbov.js";import"./Chip-MGF1mKZa.js";import"./IconButton-CxOCoGF3.js";import"./Avatar-Dvwllg8p.js";import"./Flag-CMGasDVj.js";import"./DressService-DkS6e_O5.js";import"./Badge-zckTAo43.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";const y=new e({fr:{UPDATE_LOCATION:"Modification du lieu",LOCATION_UPDATED:"Lieu modifié avec succès."},en:{UPDATE_LOCATION:"Location update",LOCATION_UPDATED:"Location updated successfully."},es:{UPDATE_LOCATION:"Actualización del lugar",LOCATION_UPDATED:"Lugar actualizado correctamente."}});t(y);const w=()=>{const e=c(),[t,w]=d.useState(),[B,F]=d.useState(!1),[G,k]=d.useState(!1),[W,z]=d.useState([]),[M,R]=d.useState([]),[H,q]=d.useState(!1),[V,$]=d.useState(!1),[J,K]=d.useState(),[Q,X]=d.useState(),[Y,Z]=d.useState(""),[ee,te]=d.useState(""),[se,ae]=d.useState(""),[oe,re]=d.useState([]);return s.jsxs(p,{onLoad:async e=>{if(e&&e.verified){k(!0),w(e);const s=new URLSearchParams(window.location.search);if(s.has("loc")){const o=s.get("loc");if(o&&""!==o)try{const t=await S(o);if(!a(e)&&e._id!==t.supplier?._id)return k(!1),void q(!0);if(t&&t.values){i._LANGUAGES.forEach((e=>{t.values&&!t.values.some((t=>t.language===e.code))&&t.values.push({language:e.code,value:""})}));const e=t.values.map((e=>({language:e.language||"",name:e.value||""})));K(t),X(t.country),z(e),te(t.longitude&&t.longitude.toString()||""),ae(t.latitude&&t.latitude.toString()||""),re(t.parkingSpots||[]),F(!0),k(!1)}else k(!1),q(!0)}catch(t){u(t),k(!1),$(!0),F(!1)}else k(!1),q(!0)}else k(!1),q(!0)}},strict:!0,children:[!V&&!H&&J&&J.values&&s.jsx("div",{className:"update-location",children:s.jsxs(I,{className:"location-form location-form-wrapper",elevation:10,style:B?{}:{display:"none"},children:[s.jsx("h1",{className:"location-form-title",children:y.UPDATE_LOCATION}),s.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(!Q||!J||!J.values)return void u();let e=!0;const t=n(M);for(let s=0;s<M.length;s+=1)t[s]=!1;for(let s=0;s<W.length;s+=1){const a=W[s];if(a.name!==J.values[s].value){const o=200===await A({language:a.language,name:a.name});e=e&&o,o||(t[s]=!0)}}if(R(t),e){const e={country:Q._id,latitude:se?Number(se):void 0,longitude:ee?Number(ee):void 0,names:W,image:Y,parkingSpots:oe},{status:t,data:s}=await x(J._id,e);200===t?(K(s),m(y.LOCATION_UPDATED)):(k(!1),u())}}catch(t){u(t)}},children:[s.jsx(T,{type:o.Location,mode:"update",record:J,size:"large",readonly:!1,onBeforeUpload:()=>{k(!0)},onChange:e=>{k(!1),Z(e)},color:"disabled",className:"avatar-ctn"}),a(t)&&J.supplier&&s.jsxs(O,{fullWidth:!0,margin:"dense",children:[s.jsx(P,{children:v.SUPPLIER}),s.jsx(E,{supplier:J.supplier})]}),s.jsx(O,{fullWidth:!0,margin:"dense",children:s.jsx(j,{label:g.COUNTRY,variant:"standard",value:Q,onChange:e=>{if(e.length>0){const t=e[0],s={_id:t._id,name:t.name};X(s)}else X(void 0)},required:!0})}),J.values.map(((e,t)=>s.jsxs(O,{fullWidth:!0,margin:"dense",children:[s.jsx(_,{className:"required",children:`${r.NAME} (${i._LANGUAGES.filter((t=>t.code===e.language))[0].label})`}),s.jsx(D,{type:"text",value:W[t]&&W[t].name||"",error:M[t],required:!0,onChange:e=>{const s=n(W);s[t].name=e.target.value;const a=l(M);a[t]=!1,(()=>{let e=!1;if(!J||!J.values)return u(),e;for(let t=0;t<W.length;t+=1)if(W[t].name!==J.values[t].value){e=!0;break}})(),z(s),R(a)},autoComplete:"off"}),s.jsx(U,{error:M[t],children:M[t]&&g.INVALID_LOCATION||""})]},e.language))),s.jsxs(O,{fullWidth:!0,margin:"dense",children:[s.jsx(_,{children:r.LATITUDE}),s.jsx(f,{value:se,onChange:e=>{ae(e.target.value)}})]}),s.jsxs(O,{fullWidth:!0,margin:"dense",children:[s.jsx(_,{children:r.LONGITUDE}),s.jsx(f,{value:ee,onChange:e=>{te(e.target.value)}})]}),s.jsx(h,{title:g.PARKING_SPOTS,values:oe,onAdd:e=>{const t=n(oe);t.push(e),re(t)},onUpdate:(e,t)=>{const s=n(oe);s[t]=e,re(s)},onDelete:(e,t)=>{const s=n(oe);s.splice(t,1),re(s)}}),s.jsxs("div",{className:"buttons",children:[s.jsx(b,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",children:r.SAVE}),s.jsx(b,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>e("/locations"),children:r.CANCEL})]})]})]})}),G&&s.jsx(C,{text:r.PLEASE_WAIT}),V&&s.jsx(N,{}),H&&s.jsx(L,{hideHeader:!0})]})};export{w as default};
