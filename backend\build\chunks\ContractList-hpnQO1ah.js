import{z as e}from"./zod-4O8Zwsja.js";import{a as s,N as a,b as t,s as n,c as o,j as i,O as l,K as c,z as r,e as m}from"../entries/index-xsXxT3-W.js";import{r as d}from"./router-BtYqujaw.js";import{c as f,b as p,e as u,f as j}from"./SupplierService-9DC5V5ZJ.js";import{I as N}from"./IconButton-CxOCoGF3.js";import{U as g}from"./create-supplier-BpB8o_Zh.js";import{D as h}from"./Delete-BfnPAJno.js";const _=e.object({fullName:e.string(),email:e.string().email({message:s.EMAIL_NOT_VALID}),phone:e.string().refine((e=>!e||a.isMobilePhone(e)),{message:s.PHONE_NOT_VALID}).optional(),location:e.string().optional(),bio:e.string().optional(),blacklisted:e.boolean().optional(),payLater:e.boolean().optional(),licenseRequired:e.boolean().optional(),minimumRentalDays:e.string().refine((e=>!e||/^\d+$/.test(e)),{message:s.FIELD_NOT_VALID}).optional(),priceChangeRate:e.string().refine((e=>!e||/^-?\d+(\.\d+)?$/.test(e)),{message:s.FIELD_NOT_VALID}).optional(),supplierDressLimit:e.string().refine((e=>!e||/^\d+$/.test(e)),{message:s.FIELD_NOT_VALID}).optional(),notifyAdminOnNewDress:e.boolean().optional()}),T=new t({fr:{TITLE:"Contrats"},en:{TITLE:"Contracts"},es:{TITLE:"Contratos"}});n(T);const D=e=>{const s=o.c(20),{supplier:a,onUpload:t,onDelete:n}=e;let _;s[0]===Symbol.for("react.memo_cache_sentinel")?(_=[],s[0]=_):_=s[0];const[D,L]=d.useState(_),[I,b]=d.useState("");let x,C,E;s[1]!==a?.contracts?(x=()=>{const e=[];for(const s of m._LANGUAGES){const t={code:s.code,label:s.label,filename:a?.contracts?.find((e=>e.language===s.code))?.file||null};e.push(t)}L(e)},s[1]=a?.contracts,s[2]=x):x=s[2],s[3]!==a?(C=[a],s[3]=a,s[4]=C):C=s[4],d.useEffect(x,C),s[5]!==I||s[6]!==D||s[7]!==t||s[8]!==a?(E=e=>{if(!e.target.files)return void r();const s=new FileReader,n=e.target.files[0];s.onloadend=async()=>{try{let e=null;if(a){const s=await u(a._id,I,n);200===s.status?e=s.data:r()}else{const s=D.find((e=>e.code===I)).filename;s&&await p(s),e=await j(I,n)}if(e){const s=c(D);s.find((e=>e.code===I)).filename=e,L(s),t&&t(I,e)}}catch(e){r(e)}},s.readAsDataURL(n)},s[5]=I,s[6]=D,s[7]=t,s[8]=a,s[9]=E):E=s[9];const y=E;let A,O,S,w;return s[10]===Symbol.for("react.memo_cache_sentinel")?(A=i.jsx("div",{className:"title",children:T.TITLE}),s[10]=A):A=s[10],s[11]!==D||s[12]!==n||s[13]!==a?(O=D.map((e=>i.jsxs("div",{className:"contract",children:[i.jsx("span",{className:"label",children:e.label}),i.jsx("span",{className:"filename",children:e.filename?i.jsx("a",{href:`${l(a?m.CDN_CONTRACTS:m.CDN_TEMP_CONTRACTS,"/")}/${e.filename}`,children:e.filename}):"-"}),i.jsxs("div",{className:"actions",children:[i.jsx(N,{size:"small",onClick:async()=>{b(e.code);const s=document.getElementById("upload-contract");s.value="",setTimeout((()=>{s.click()}),0)},children:i.jsx(g,{className:"upload-icon"})}),e.filename&&i.jsx(N,{size:"small",onClick:async()=>{try{let s;if(s=a?await f(a._id,e.code):await p(e.filename),200===s){const s=c(D);s.find((s=>s.code===e.code)).filename=null,L(s),n&&n(e.code)}else r()}catch(s){r(s)}},children:i.jsx(h,{className:"delete-icon"})})]})]},e.code))),s[11]=D,s[12]=n,s[13]=a,s[14]=O):O=s[14],s[15]!==y?(S=i.jsx("input",{id:"upload-contract",type:"file",hidden:!0,onChange:y}),s[15]=y,s[16]=S):S=s[16],s[17]!==O||s[18]!==S?(w=i.jsxs("div",{className:"contracts",children:[A,O,S]}),s[17]=O,s[18]=S,s[19]=w):w=s[19],w};export{D as C,_ as s};
