{"name": "api", "version": "7.2.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon", "build": "rimraf dist && cross-env NODE_OPTIONS=\"--max-old-space-size=1024\" tsc --build --verbose && babel dist -d dist", "start": "npm run build && node dist/src", "test": "rimraf coverage && npm run build && cross-env NODE_OPTIONS=\"--experimental-vm-modules\" jest --coverage --no-cache", "lint": "eslint . --cache --cache-location .eslintcache", "ncu": "ncu -u", "script:db": "npm run build && node dist/scripts/db.js", "script:init-admin": "npm run build && node dist/scripts/init-admin.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "axios": "^1.9.0", "babel-jest": "^29.7.0", "babel-plugin-add-import-extension": "^1.6.0", "babel-plugin-module-resolver": "^5.0.2", "bcrypt": "^6.0.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "escape-string-regexp": "^5.0.0", "expo-server-sdk": "^3.15.0", "express": "^5.1.0", "helmet": "^8.1.0", "i18n-js": "^4.5.1", "jest": "^29.7.0", "jose": "^6.0.11", "mongoose": "^8.14.3", "mongoose-paginate-v2": "^1.9.1", "multer": "^1.4.5-lts.2", "nanoid": "^5.1.5", "nocache": "^4.0.0", "nodemailer": "^7.0.3", "rimraf": "^6.0.1", "stripe": "^18.1.0", "supertest": "^7.1.1", "typescript": "^5.8.3", "validator": "^13.15.0", "winston": "^3.17.0"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/mongoose": "^5.11.97", "@types/node": "^22.15.18", "@types/stripe": "^8.0.417", "@types/validator": "^13.15.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "globals": "^16.1.0", "nodemon": "^3.1.10", "npm-check-updates": "^18.0.1", "tsx": "^4.19.4"}}