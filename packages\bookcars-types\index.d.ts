export declare enum UserType {
    Admin = "admin",
    Supplier = "supplier",
    User = "user"
}
export declare enum AppType {
    Backend = "backend",
    Frontend = "frontend"
}
export declare enum DressType {
    Traditional = "traditional",
    Modern = "modern",
    Designer = "designer",
    Vintage = "vintage",
    Casual = "casual",
    Wedding = "wedding",
    Evening = "evening",
    Cocktail = "cocktail",
    Prom = "prom",
    Other = "other",
    Unknown = "unknown"
}
export declare enum DressRange {
    Mini = "mini",
    Midi = "midi",
    Maxi = "maxi",
    Bridal = "bridal",
    Evening = "evening",
    Cocktail = "cocktail",
    Casual = "casual"
}
export declare enum DressAccessories {
    Veil = "veil",
    Jewelry = "jewelry",
    Shoes = "shoes",
    Headpiece = "headpiece"
}
export declare enum DressSize {
    XS = "xs",
    S = "s",
    M = "m",
    L = "l",
    XL = "xl",
    XXL = "xxl",
    ExtraSmall = "extraSmall",
    Small = "small",
    Medium = "medium",
    Large = "large",
    ExtraLarge = "extraLarge",
    DoubleExtraLarge = "doubleExtraLarge",
    Custom = "custom"
}
export declare enum DressMaterial {
    Silk = "silk",
    Cotton = "cotton",
    Lace = "lace",
    Satin = "satin",
    Chiffon = "chiffon",
    Tulle = "tulle",
    Organza = "organza",
    Velvet = "velvet",
    Polyester = "polyester",
    Crepe = "crepe"
}
export declare enum DressStyle {
    Traditional = "traditional",
    Modern = "modern",
    Designer = "designer",
    Vintage = "vintage"
}
export declare enum RentalTerm {
    Limited = "limited",
    Unlimited = "unlimited"
}
export declare enum BookingStatus {
    Void = "void",
    Pending = "pending",
    Deposit = "deposit",
    Paid = "paid",
    Reserved = "reserved",
    Cancelled = "cancelled"
}
export declare enum Availablity {
    Available = "available",
    Unavailable = "unavailable"
}
export declare enum RecordType {
    Admin = "admin",
    Supplier = "supplier",
    User = "user",
    Dress = "dress",
    Location = "location",
    Country = "country"
}
export declare enum PaymentGateway {
    PayPal = "payPal",
    Stripe = "stripe",
    Visa = "visa"
}
export interface Booking {
    _id?: string;
    supplier: string | User;
    dress?: string | Dress;
    driver?: string | User;
    pickupLocation: string | Location;
    dropOffLocation: string | Location;
    from: Date;
    to: Date;
    status: BookingStatus;
    cancellation?: boolean;
    amendments?: boolean;
    _additionalDriver?: string | AdditionalDriver;
    cancelRequest?: boolean;
    price?: number;
    sessionId?: string;
    paymentIntentId?: string;
    customerId?: string;
    expireAt?: Date;
    isDeposit?: boolean;
    paypalOrderId?: string;
}
export interface CheckoutPayload {
    driver?: User;
    booking?: Booking;
    additionalDriver?: AdditionalDriver;
    payLater: boolean;
    sessionId?: string;
    paymentIntentId?: string;
    customerId?: string;
    payPal?: boolean;
    visa?: boolean;
}
export interface Filter {
    from?: Date;
    dateBetween?: Date;
    to?: Date;
    keyword?: string;
    pickupLocation?: string;
    dropOffLocation?: string;
}
export interface GetBookingsPayload {
    suppliers: string[];
    statuses: string[];
    user?: string;
    dress?: string;
    filter?: Filter;
}
export interface AdditionalDriver {
    fullName: string;
    email: string;
    phone: string;
    birthDate: Date;
}
export interface UpsertBookingPayload {
    booking: Booking;
    additionalDriver?: AdditionalDriver;
}
export interface LocationName {
    language: string;
    name: string;
}
export interface CountryName {
    language: string;
    name: string;
}
export interface UpsertLocationPayload {
    country: string;
    longitude?: number;
    latitude?: number;
    names: LocationName[];
    image?: string | null;
    parkingSpots?: ParkingSpot[];
    supplier?: string;
}
export interface UpdateSupplierPayload {
    _id: string;
    fullName: string;
    phone: string;
    location: string;
    bio: string;
    payLater: boolean;
    licenseRequired: boolean;
    minimumRentalDays?: number;
    priceChangeRate?: number;
    supplierDressLimit?: number;
    notifyAdminOnNewDress?: boolean;
    blacklisted?: boolean;
}
export interface CreateDressPayload {
    loggedUser: string;
    name: string;
    supplier: string;
    minimumAge: number;
    locations: string[];
    dailyPrice: number;
    discountedDailyPrice: number | null;
    biWeeklyPrice: number | null;
    discountedBiWeeklyPrice: number | null;
    weeklyPrice: number | null;
    discountedWeeklyPrice: number | null;
    monthlyPrice: number | null;
    discountedMonthlyPrice: number | null;
    isDateBasedPrice: boolean;
    dateBasedPrices: DateBasedPrice[];
    deposit: number;
    available: boolean;
    fullyBooked?: boolean;
    comingSoon?: boolean;
    type: string;
    size: string;
    customizable?: boolean;
    image?: string;
    color: string;
    length: number;
    material: string;
    rentals?: number;
    cancellation: number;
    amendments: number;
    range: string;
    accessories: string[];
    rating?: number;
    designerName?: string;
}
export interface UpdateDressPayload extends CreateDressPayload {
    _id: string;
}
export interface DressSpecs {
    customizable?: boolean;
    designerMade?: boolean;
    customSize?: boolean;
    premium?: boolean;
    longDress?: boolean;
    embroidered?: boolean;
}
export interface GetDressesPayload {
    suppliers?: string[];
    dressSpecs?: DressSpecs;
    dressType?: string[];
    size?: string[];
    dressSize?: string[];
    material?: string[];
    deposit?: number;
    availability?: string[];
    pickupLocation?: string;
    ranges?: string[];
    accessories?: string[];
    rating?: number;
    color?: string;
    days?: number;
    includeAlreadyBookedDresses?: boolean;
    includeComingSoonDresses?: boolean;
}
export interface SignUpPayload {
    email: string;
    password: string;
    fullName: string;
    phone?: string;
    language: string;
    active?: boolean;
    verified?: boolean;
    blacklisted?: boolean;
    type?: string;
    avatar?: string;
    birthDate?: number | Date;
}
export type Contract = {
    language: string;
    file: string | null;
};
export interface CreateUserPayload {
    email?: string;
    phone: string;
    location: string;
    bio: string;
    fullName: string;
    type?: string;
    avatar?: string;
    birthDate?: number | Date;
    language?: string;
    password?: string;
    verified?: boolean;
    blacklisted?: boolean;
    payLater?: boolean;
    supplier?: string;
    contracts?: Contract[];
    licenseRequired?: boolean;
    minimumRentalDays?: number;
    license?: string;
    priceChangeRate?: number;
    supplierDressLimit?: number;
    notifyAdminOnNewDress?: boolean;
}
export interface UpdateUserPayload extends CreateUserPayload {
    _id: string;
    enableEmailNotifications?: boolean;
}
export interface ChangePasswordPayload {
    _id: string;
    password: string;
    newPassword: string;
    strict: boolean;
}
export interface ActivatePayload {
    userId: string;
    token: string;
    password: string;
}
export interface ValidateEmailPayload {
    email: string;
    appType?: AppType;
}
export declare enum SocialSignInType {
    Facebook = "facebook",
    Apple = "apple",
    Google = "google"
}
export interface SignInPayload {
    email?: string;
    password?: string;
    stayConnected?: boolean;
    mobile?: boolean;
    fullName?: string;
    avatar?: string;
    accessToken?: string;
    socialSignInType?: SocialSignInType;
}
export interface ResendLinkPayload {
    email?: string;
}
export interface UpdateEmailNotificationsPayload {
    _id: string;
    enableEmailNotifications: boolean;
}
export interface UpdateLanguagePayload {
    id: string;
    language: string;
}
export interface ValidateSupplierPayload {
    fullName: string;
}
export interface ValidateLocationPayload {
    language: string;
    name: string;
}
export interface ValidateCountryPayload {
    language: string;
    name: string;
}
export interface UpdateStatusPayload {
    ids: string[];
    status: string;
}
export interface User {
    _id?: string;
    supplier?: User | string;
    fullName: string;
    email?: string;
    phone?: string;
    password?: string;
    birthDate?: Date;
    verified?: boolean;
    verifiedAt?: Date;
    active?: boolean;
    language?: string;
    enableEmailNotifications?: boolean;
    avatar?: string;
    bio?: string;
    location?: string;
    type?: string;
    blacklisted?: boolean;
    payLater?: boolean;
    accessToken?: string;
    checked?: boolean;
    customerId?: string;
    dressCount?: number;
    contracts?: Contract[];
    licenseRequired?: boolean;
    license?: string | null;
    minimumRentalDays?: number;
    priceChangeRate?: number;
    supplierDressLimit?: number;
    notifyAdminOnNewDress?: boolean;
}
export interface Option {
    _id: string;
    name?: string;
    image?: string;
}
export interface LocationValue {
    _id?: string;
    language: string;
    value?: string;
}
export interface ParkingSpot {
    _id?: string;
    longitude: number | string;
    latitude: number | string;
    name?: string;
    values?: LocationValue[];
}
export interface Location {
    _id: string;
    country?: Country;
    longitude?: number;
    latitude?: number;
    name?: string;
    values?: LocationValue[];
    image?: string;
    parkingSpots?: ParkingSpot[];
    supplier?: User;
}
export interface Country {
    _id: string;
    name?: string;
    values?: LocationValue[];
    supplier?: User;
}
export interface CountryInfo extends Country {
    locations?: Location[];
}
export interface UpsertCountryPayload {
    names: CountryName[];
    supplier?: string;
}
export interface DateBasedPrice {
    _id?: string;
    startDate: Date | null;
    endDate: Date | null;
    dailyPrice: number | string;
}
export interface Dress {
    _id: string;
    name: string;
    supplier: User;
    minimumAge: number;
    locations: Location[];
    dailyPrice: number;
    discountedDailyPrice: number | null;
    biWeeklyPrice: number | null;
    discountedBiWeeklyPrice: number | null;
    weeklyPrice: number | null;
    discountedWeeklyPrice: number | null;
    monthlyPrice: number | null;
    discountedMonthlyPrice: number | null;
    isDateBasedPrice: boolean;
    dateBasedPrices: DateBasedPrice[];
    deposit: number;
    available: boolean;
    fullyBooked?: boolean;
    comingSoon?: boolean;
    type: DressType;
    size: DressSize;
    customizable?: boolean;
    image?: string;
    color: string;
    length: number;
    material: DressMaterial;
    cancellation: number;
    amendments: number;
    range: string;
    accessories: DressAccessories[] | undefined;
    rating?: number;
    rentals: number;
    designerName?: string;
    [propKey: string]: any;
}
export interface Data<T> {
    rows: T[];
    rowCount: number;
}
export interface GetBookingDressesPayload {
    supplier: string;
    pickupLocation: string;
}
export interface Notification {
    _id: string;
    user: string;
    message: string;
    booking?: string;
    dress?: string;
    isRead?: boolean;
    checked?: boolean;
    createdAt?: Date;
}
export interface NotificationCounter {
    _id: string;
    user: string;
    count: number;
}
export interface ResultData<T> {
    pageInfo: {
        totalRecords: number;
    };
    resultData: T[];
}
export type Result<T> = [ResultData<T>] | [] | undefined | null;
export interface GetUsersBody {
    user: string;
    types: UserType[];
}
export interface CreatePaymentPayload {
    amount: number;
    /**
     * Three-letter ISO currency code, in lowercase.
     * Must be a supported currency: https://docs.stripe.com/currencies
     *
     * @type {string}
     */
    currency: string;
    /**
     * The IETF language tag of the locale Checkout is displayed in. If blank or auto, the browser's locale is used.
     *
     * @type {string}
     */
    locale: string;
    receiptEmail: string;
    customerName: string;
    name: string;
    description?: string;
}
export interface CreatePayPalOrderPayload {
    bookingId: string;
    amount: number;
    currency: string;
    name: string;
    description: string;
}
export interface PaymentResult {
    sessionId?: string;
    paymentIntentId?: string;
    customerId: string;
    clientSecret: string | null;
}
export interface SendEmailPayload {
    from: string;
    to: string;
    subject: string;
    message: string;
    isContactForm: boolean;
}
export interface Response<T> {
    status: number;
    data: T;
}
export interface BankDetails {
    _id: string;
    accountHolder: string;
    bankName: string;
    iban: string;
    swiftBic: string;
    showBankDetailsPage: boolean;
}
export interface UpsertBankDetailsPayload {
    _id?: string;
    accountHolder: string;
    bankName: string;
    iban: string;
    swiftBic: string;
    showBankDetailsPage: boolean;
}
export type DataEvent<T> = (data?: Data<T>) => void;
export interface StatusFilterItem {
    label: string;
    value: BookingStatus;
    checked?: boolean;
}
export interface DressFilter {
    pickupLocation: Location;
    dropOffLocation: Location;
    from: Date;
    to: Date;
}
export type DressFilterSubmitEvent = (filter: DressFilter) => void;
export interface DressOptions {
    cancellation?: boolean;
    amendments?: boolean;
    accessories?: boolean;
}
