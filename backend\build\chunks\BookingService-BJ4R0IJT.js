import{aC as t,p as a}from"../entries/index-CEzJO5Xy.js";const e=a=>t.post("/api/create-booking",a,{withCredentials:!0}).then((t=>t.data)),s=a=>t.put("/api/update-booking",a,{withCredentials:!0}).then((t=>t.status)),i=a=>t.post("/api/update-booking-status",a,{withCredentials:!0}).then((t=>t.status)),n=a=>t.post("/api/delete-bookings",a,{withCredentials:!0}).then((t=>t.status)),o=e=>t.get(`/api/booking/${encodeURIComponent(e)}/${a()}`,{withCredentials:!0}).then((t=>t.data)),p=(e,s,i)=>t.post(`/api/bookings/${s}/${i}/${a()}`,e,{withCredentials:!0}).then((t=>t.data));export{p as a,i as b,e as c,n as d,o as g,s as u};
