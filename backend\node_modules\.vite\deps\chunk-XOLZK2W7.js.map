{"version": 3, "sources": ["../../date-fns/addDays.js", "../../date-fns/addMilliseconds.js", "../../date-fns/addSeconds.js", "../../date-fns/addMinutes.js", "../../date-fns/addHours.js", "../../date-fns/addWeeks.js", "../../date-fns/addMonths.js", "../../date-fns/addYears.js", "../../date-fns/endOfDay.js", "../../date-fns/endOfWeek.js", "../../date-fns/endOfYear.js", "../../date-fns/startOfDay.js", "../../date-fns/_lib/getTimezoneOffsetInMilliseconds.js", "../../date-fns/differenceInCalendarDays.js", "../../date-fns/startOfYear.js", "../../date-fns/getDayOfYear.js", "../../date-fns/startOfISOWeek.js", "../../date-fns/getISOWeekYear.js", "../../date-fns/startOfISOWeekYear.js", "../../date-fns/getISOWeek.js", "../../date-fns/getWeekYear.js", "../../date-fns/startOfWeekYear.js", "../../date-fns/getWeek.js", "../../date-fns/_lib/addLeadingZeros.js", "../../date-fns/_lib/format/lightFormatters.js", "../../date-fns/_lib/format/formatters.js", "../../date-fns/_lib/format/longFormatters.js", "../../date-fns/isDate.js", "../../date-fns/isValid.js", "../../date-fns/_lib/protectedTokens.js", "../../date-fns/format.js", "../../date-fns/getDate.js", "../../date-fns/getDaysInMonth.js", "../../date-fns/getHours.js", "../../date-fns/getMinutes.js", "../../date-fns/getMonth.js", "../../date-fns/getSeconds.js", "../../date-fns/getMilliseconds.js", "../../date-fns/getYear.js", "../../date-fns/isAfter.js", "../../date-fns/isBefore.js", "../../date-fns/isEqual.js", "../../date-fns/isSameDay.js", "../../date-fns/isSameYear.js", "../../date-fns/isSameMonth.js", "../../date-fns/startOfHour.js", "../../date-fns/isSameHour.js", "../../date-fns/getDefaultOptions.js", "../../date-fns/transpose.js", "../../date-fns/setWeek.js", "../../date-fns/setISOWeek.js", "../../date-fns/setDay.js", "../../date-fns/getISODay.js", "../../date-fns/setISODay.js", "../../date-fns/parse/_lib/Setter.js", "../../date-fns/parse/_lib/Parser.js", "../../date-fns/parse/_lib/parsers/EraParser.js", "../../date-fns/parse/_lib/constants.js", "../../date-fns/parse/_lib/utils.js", "../../date-fns/parse/_lib/parsers/YearParser.js", "../../date-fns/parse/_lib/parsers/LocalWeekYearParser.js", "../../date-fns/parse/_lib/parsers/ISOWeekYearParser.js", "../../date-fns/parse/_lib/parsers/ExtendedYearParser.js", "../../date-fns/parse/_lib/parsers/QuarterParser.js", "../../date-fns/parse/_lib/parsers/StandAloneQuarterParser.js", "../../date-fns/parse/_lib/parsers/MonthParser.js", "../../date-fns/parse/_lib/parsers/StandAloneMonthParser.js", "../../date-fns/parse/_lib/parsers/LocalWeekParser.js", "../../date-fns/parse/_lib/parsers/ISOWeekParser.js", "../../date-fns/parse/_lib/parsers/DateParser.js", "../../date-fns/parse/_lib/parsers/DayOfYearParser.js", "../../date-fns/parse/_lib/parsers/DayParser.js", "../../date-fns/parse/_lib/parsers/LocalDayParser.js", "../../date-fns/parse/_lib/parsers/StandAloneLocalDayParser.js", "../../date-fns/parse/_lib/parsers/ISODayParser.js", "../../date-fns/parse/_lib/parsers/AMPMParser.js", "../../date-fns/parse/_lib/parsers/AMPMMidnightParser.js", "../../date-fns/parse/_lib/parsers/DayPeriodParser.js", "../../date-fns/parse/_lib/parsers/Hour1to12Parser.js", "../../date-fns/parse/_lib/parsers/Hour0to23Parser.js", "../../date-fns/parse/_lib/parsers/Hour0To11Parser.js", "../../date-fns/parse/_lib/parsers/Hour1To24Parser.js", "../../date-fns/parse/_lib/parsers/MinuteParser.js", "../../date-fns/parse/_lib/parsers/SecondParser.js", "../../date-fns/parse/_lib/parsers/FractionOfSecondParser.js", "../../date-fns/parse/_lib/parsers/ISOTimezoneWithZParser.js", "../../date-fns/parse/_lib/parsers/ISOTimezoneParser.js", "../../date-fns/parse/_lib/parsers/TimestampSecondsParser.js", "../../date-fns/parse/_lib/parsers/TimestampMillisecondsParser.js", "../../date-fns/parse/_lib/parsers.js", "../../date-fns/parse.js", "../../date-fns/setDate.js", "../../date-fns/setHours.js", "../../date-fns/setMinutes.js", "../../date-fns/setMonth.js", "../../date-fns/setSeconds.js", "../../date-fns/setMilliseconds.js", "../../date-fns/setYear.js", "../../date-fns/startOfMonth.js", "../../date-fns/endOfMonth.js", "../../date-fns/isWithinInterval.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addDays} function options.\n */\n\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be added.\n * @param options - An object with options\n *\n * @returns The new date with the days added\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\nexport function addDays(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  if (isNaN(amount)) return constructFrom(options?.in || date, NaN);\n\n  // If 0 days, no-op to avoid changing times in the hour before end of DST\n  if (!amount) return _date;\n\n  _date.setDate(_date.getDate() + amount);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addDays;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMilliseconds} function options.\n */\n\n/**\n * @name addMilliseconds\n * @category Millisecond Helpers\n * @summary Add the specified number of milliseconds to the given date.\n *\n * @description\n * Add the specified number of milliseconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of milliseconds to be added.\n * @param options - The options object\n *\n * @returns The new date with the milliseconds added\n *\n * @example\n * // Add 750 milliseconds to 10 July 2014 12:45:30.000:\n * const result = addMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:30.750\n */\nexport function addMilliseconds(date, amount, options) {\n  return constructFrom(options?.in || date, +toDate(date) + amount);\n}\n\n// Fallback for modularized imports:\nexport default addMilliseconds;\n", "import { addMilliseconds } from \"./addMilliseconds.js\";\n\n/**\n * The {@link addSeconds} function options.\n */\n\n/**\n * @name addSeconds\n * @category Second Helpers\n * @summary Add the specified number of seconds to the given date.\n *\n * @description\n * Add the specified number of seconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of seconds to be added.\n * @param options - An object with options\n *\n * @returns The new date with the seconds added\n *\n * @example\n * // Add 30 seconds to 10 July 2014 12:45:00:\n * const result = addSeconds(new Date(2014, 6, 10, 12, 45, 0), 30)\n * //=> Thu Jul 10 2014 12:45:30\n */\nexport function addSeconds(date, amount, options) {\n  return addMilliseconds(date, amount * 1000, options);\n}\n\n// Fallback for modularized imports:\nexport default addSeconds;\n", "import { millisecondsInMinute } from \"./constants.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMinutes} function options.\n */\n\n/**\n * @name addMinutes\n * @category Minute Helpers\n * @summary Add the specified number of minutes to the given date.\n *\n * @description\n * Add the specified number of minutes to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of minutes to be added.\n * @param options - An object with options\n *\n * @returns The new date with the minutes added\n *\n * @example\n * // Add 30 minutes to 10 July 2014 12:00:00:\n * const result = addMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function addMinutes(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  _date.setTime(_date.getTime() + amount * millisecondsInMinute);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addMinutes;\n", "import { addMilliseconds } from \"./addMilliseconds.js\";\nimport { millisecondsInHour } from \"./constants.js\";\n\n/**\n * The {@link addHours} function options.\n */\n\n/**\n * @name addHours\n * @category Hour Helpers\n * @summary Add the specified number of hours to the given date.\n *\n * @description\n * Add the specified number of hours to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of hours to be added\n * @param options - An object with options\n *\n * @returns The new date with the hours added\n *\n * @example\n * // Add 2 hours to 10 July 2014 23:00:00:\n * const result = addHours(new Date(2014, 6, 10, 23, 0), 2)\n * //=> Fri Jul 11 2014 01:00:00\n */\nexport function addHours(date, amount, options) {\n  return addMilliseconds(date, amount * millisecondsInHour, options);\n}\n\n// Fallback for modularized imports:\nexport default addHours;\n", "import { addDays } from \"./addDays.js\";\n\n/**\n * The {@link addWeeks} function options.\n */\n\n/**\n * @name addWeeks\n * @category Week Helpers\n * @summary Add the specified number of weeks to the given date.\n *\n * @description\n * Add the specified number of weeks to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of weeks to be added.\n * @param options - An object with options\n *\n * @returns The new date with the weeks added\n *\n * @example\n * // Add 4 weeks to 1 September 2014:\n * const result = addWeeks(new Date(2014, 8, 1), 4)\n * //=> Mon Sep 29 2014 00:00:00\n */\nexport function addWeeks(date, amount, options) {\n  return addDays(date, amount * 7, options);\n}\n\n// Fallback for modularized imports:\nexport default addWeeks;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMonths} function options.\n */\n\n/**\n * @name addMonths\n * @category Month Helpers\n * @summary Add the specified number of months to the given date.\n *\n * @description\n * Add the specified number of months to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be added.\n * @param options - The options object\n *\n * @returns The new date with the months added\n *\n * @example\n * // Add 5 months to 1 September 2014:\n * const result = addMonths(new Date(2014, 8, 1), 5)\n * //=> Sun Feb 01 2015 00:00:00\n *\n * // Add one month to 30 January 2023:\n * const result = addMonths(new Date(2023, 0, 30), 1)\n * //=> Tue Feb 28 2023 00:00:00\n */\nexport function addMonths(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  if (isNaN(amount)) return constructFrom(options?.in || date, NaN);\n  if (!amount) {\n    // If 0 months, no-op to avoid changing times in the hour before end of DST\n    return _date;\n  }\n  const dayOfMonth = _date.getDate();\n\n  // The JS Date object supports date math by accepting out-of-bounds values for\n  // month, day, etc. For example, new Date(2020, 0, 0) returns 31 Dec 2019 and\n  // new Date(2020, 13, 1) returns 1 Feb 2021.  This is *almost* the behavior we\n  // want except that dates will wrap around the end of a month, meaning that\n  // new Date(2020, 13, 31) will return 3 Mar 2021 not 28 Feb 2021 as desired. So\n  // we'll default to the end of the desired month by adding 1 to the desired\n  // month and using a date of 0 to back up one day to the end of the desired\n  // month.\n  const endOfDesiredMonth = constructFrom(options?.in || date, _date.getTime());\n  endOfDesiredMonth.setMonth(_date.getMonth() + amount + 1, 0);\n  const daysInMonth = endOfDesiredMonth.getDate();\n  if (dayOfMonth >= daysInMonth) {\n    // If we're already at the end of the month, then this is the correct date\n    // and we're done.\n    return endOfDesiredMonth;\n  } else {\n    // Otherwise, we now know that setting the original day-of-month value won't\n    // cause an overflow, so set the desired day-of-month. Note that we can't\n    // just set the date of `endOfDesiredMonth` because that object may have had\n    // its time changed in the unusual case where where a DST transition was on\n    // the last day of the month and its local time was in the hour skipped or\n    // repeated next to a DST transition.  So we use `date` instead which is\n    // guaranteed to still have the original time.\n    _date.setFullYear(\n      endOfDesiredMonth.getFullYear(),\n      endOfDesiredMonth.getMonth(),\n      dayOfMonth,\n    );\n    return _date;\n  }\n}\n\n// Fallback for modularized imports:\nexport default addMonths;\n", "import { addMonths } from \"./addMonths.js\";\n\n/**\n * The {@link addYears} function options.\n */\n\n/**\n * @name addYears\n * @category Year Helpers\n * @summary Add the specified number of years to the given date.\n *\n * @description\n * Add the specified number of years to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type.\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be added.\n * @param options - The options\n *\n * @returns The new date with the years added\n *\n * @example\n * // Add 5 years to 1 September 2014:\n * const result = addYears(new Date(2014, 8, 1), 5)\n * //=> Sun Sep 01 2019 00:00:00\n */\nexport function addYears(date, amount, options) {\n  return addMonths(date, amount * 12, options);\n}\n\n// Fallback for modularized imports:\nexport default addYears;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfDay} function options.\n */\n\n/**\n * @name endOfDay\n * @category Day Helpers\n * @summary Return the end of a day for the given date.\n *\n * @description\n * Return the end of a day for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a day\n *\n * @example\n * // The end of a day for 2 September 2014 11:55:00:\n * const result = endOfDay(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 02 2014 23:59:59.999\n */\nexport function endOfDay(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfDay;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfWeek} function options.\n */\n\n/**\n * @name endOfWeek\n * @category Week Helpers\n * @summary Return the end of a week for the given date.\n *\n * @description\n * Return the end of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a week\n *\n * @example\n * // The end of a week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sat Sep 06 2014 23:59:59.999\n *\n * @example\n * // If the week starts on Monday, the end of the week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date, options?.in);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n\n  _date.setDate(_date.getDate() + diff);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfWeek;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfYear} function options.\n */\n\n/**\n * @name endOfYear\n * @category Year Helpers\n * @summary Return the end of a year for the given date.\n *\n * @description\n * Return the end of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The end of a year\n *\n * @example\n * // The end of a year for 2 September 2014 11:55:00:\n * const result = endOfYear(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Wed Dec 31 2014 23:59:59.999\n */\nexport function endOfYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  _date.setFullYear(year + 1, 0, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfDay} function options.\n */\n\n/**\n * @name startOfDay\n * @category Day Helpers\n * @summary Return the start of a day for the given date.\n *\n * @description\n * Return the start of a day for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a day\n *\n * @example\n * // The start of a day for 2 September 2014 11:55:00:\n * const result = startOfDay(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 02 2014 00:00:00\n */\nexport function startOfDay(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfDay;\n", "import { toDate } from \"../toDate.js\";\n\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nexport function getTimezoneOffsetInMilliseconds(date) {\n  const _date = toDate(date);\n  const utcDate = new Date(\n    Date.UTC(\n      _date.getFullYear(),\n      _date.getMonth(),\n      _date.getDate(),\n      _date.getHours(),\n      _date.getMinutes(),\n      _date.getSeconds(),\n      _date.getMilliseconds(),\n    ),\n  );\n  utcDate.setUTCFullYear(_date.getFullYear());\n  return +date - +utcDate;\n}\n", "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInDay } from \"./constants.js\";\nimport { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link differenceInCalendarDays} function options.\n */\n\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - The options object\n *\n * @returns The number of calendar days\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\nexport function differenceInCalendarDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const laterStartOfDay = startOfDay(laterDate_);\n  const earlierStartOfDay = startOfDay(earlierDate_);\n\n  const laterTimestamp =\n    +laterStartOfDay - getTimezoneOffsetInMilliseconds(laterStartOfDay);\n  const earlierTimestamp =\n    +earlierStartOfDay - getTimezoneOffsetInMilliseconds(earlierStartOfDay);\n\n  // Round the number of days to the nearest integer because the number of\n  // milliseconds in a day is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInDay);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarDays;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfYear} function options.\n */\n\n/**\n * @name startOfYear\n * @category Year Helpers\n * @summary Return the start of a year for the given date.\n *\n * @description\n * Return the start of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a year\n *\n * @example\n * // The start of a year for 2 September 2014 11:55:00:\n * const result = startOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Jan 01 2014 00:00:00\n */\nexport function startOfYear(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setFullYear(date_.getFullYear(), 0, 1);\n  date_.setHours(0, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfYear;\n", "import { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { startOfYear } from \"./startOfYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDayOfYear} function options.\n */\n\n/**\n * @name getDayOfYear\n * @category Day Helpers\n * @summary Get the day of the year of the given date.\n *\n * @description\n * Get the day of the year of the given date.\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The day of year\n *\n * @example\n * // Which day of the year is 2 July 2014?\n * const result = getDayOfYear(new Date(2014, 6, 2))\n * //=> 183\n */\nexport function getDayOfYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const diff = differenceInCalendarDays(_date, startOfYear(_date));\n  const dayOfYear = diff + 1;\n  return dayOfYear;\n}\n\n// Fallback for modularized imports:\nexport default getDayOfYear;\n", "import { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link startOfISOWeek} function options.\n */\n\n/**\n * @name startOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the start of an ISO week for the given date.\n *\n * @description\n * Return the start of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of an ISO week\n *\n * @example\n * // The start of an ISO week for 2 September 2014 11:55:00:\n * const result = startOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfISOWeek(date, options) {\n  return startOfWeek(date, { ...options, weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default startOfISOWeek;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISOWeekYear} function options.\n */\n\n/**\n * @name getISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the ISO week-numbering year of the given date.\n *\n * @description\n * Get the ISO week-numbering year of the given date,\n * which always starts 3 days before the year's first Thursday.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n *\n * @returns The ISO week-numbering year\n *\n * @example\n * // Which ISO-week numbering year is 2 January 2005?\n * const result = getISOWeekYear(new Date(2005, 0, 2))\n * //=> 2004\n */\nexport function getISOWeekYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n\n  const fourthOfJanuaryOfNextYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfISOWeek(fourthOfJanuaryOfNextYear);\n\n  const fourthOfJanuaryOfThisYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfThisYear.setFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfISOWeek(fourthOfJanuaryOfThisYear);\n\n  if (_date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (_date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getISOWeekYear;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link startOfISOWeekYear} function options.\n */\n\n/**\n * @name startOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the start of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the start of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of an ISO week-numbering year\n *\n * @example\n * // The start of an ISO week-numbering year for 2 July 2005:\n * const result = startOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport function startOfISOWeekYear(date, options) {\n  const year = getISOWeekYear(date, options);\n  const fourthOfJanuary = constructFrom(options?.in || date, 0);\n  fourthOfJanuary.setFullYear(year, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  return startOfISOWeek(fourthOfJanuary);\n}\n\n// Fallback for modularized imports:\nexport default startOfISOWeekYear;\n", "import { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISOWeek} function options.\n */\n\n/**\n * @name getISOWeek\n * @category ISO Week Helpers\n * @summary Get the ISO week of the given date.\n *\n * @description\n * Get the ISO week of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The ISO week\n *\n * @example\n * // Which week of the ISO-week numbering year is 2 January 2005?\n * const result = getISOWeek(new Date(2005, 0, 2))\n * //=> 53\n */\nexport function getISOWeek(date, options) {\n  const _date = toDate(date, options?.in);\n  const diff = +startOfISOWeek(_date) - +startOfISOWeekYear(_date);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getISOWeek;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeekYear} function options.\n */\n\n/**\n * @name getWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Get the local week-numbering year of the given date.\n *\n * @description\n * Get the local week-numbering year of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The local week-numbering year\n *\n * @example\n * // Which week numbering year is 26 December 2004 with the default settings?\n * const result = getWeekYear(new Date(2004, 11, 26))\n * //=> 2005\n *\n * @example\n * // Which week numbering year is 26 December 2004 if week starts on Saturday?\n * const result = getWeekYear(new Date(2004, 11, 26), { weekStartsOn: 6 })\n * //=> 2004\n *\n * @example\n * // Which week numbering year is 26 December 2004 if the first week contains 4 January?\n * const result = getWeekYear(new Date(2004, 11, 26), { firstWeekContainsDate: 4 })\n * //=> 2004\n */\nexport function getWeekYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const firstWeekOfNextYear = constructFrom(options?.in || date, 0);\n  firstWeekOfNextYear.setFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfWeek(firstWeekOfNextYear, options);\n\n  const firstWeekOfThisYear = constructFrom(options?.in || date, 0);\n  firstWeekOfThisYear.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfWeek(firstWeekOfThisYear, options);\n\n  if (+_date >= +startOfNextYear) {\n    return year + 1;\n  } else if (+_date >= +startOfThisYear) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getWeekYear;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { getWeekYear } from \"./getWeekYear.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link startOfWeekYear} function options.\n */\n\n/**\n * @name startOfWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Return the start of a local week-numbering year for the given date.\n *\n * @description\n * Return the start of a local week-numbering year.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a week-numbering year\n *\n * @example\n * // The start of an a week-numbering year for 2 July 2005 with default settings:\n * const result = startOfWeekYear(new Date(2005, 6, 2))\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // The start of a week-numbering year for 2 July 2005\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = startOfWeekYear(new Date(2005, 6, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport function startOfWeekYear(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const year = getWeekYear(date, options);\n  const firstWeek = constructFrom(options?.in || date, 0);\n  firstWeek.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  const _date = startOfWeek(firstWeek, options);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfWeekYear;\n", "import { millisecondsInWeek } from \"./constants.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\nimport { startOfWeekYear } from \"./startOfWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeek} function options.\n */\n\n/**\n * @name getWeek\n * @category Week Helpers\n * @summary Get the local week index of the given date.\n *\n * @description\n * Get the local week index of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The week\n *\n * @example\n * // Which week of the local week numbering year is 2 January 2005 with default options?\n * const result = getWeek(new Date(2005, 0, 2))\n * //=> 2\n *\n * @example\n * // Which week of the local week numbering year is 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January?\n * const result = getWeek(new Date(2005, 0, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> 53\n */\nexport function getWeek(date, options) {\n  const _date = toDate(date, options?.in);\n  const diff = +startOfWeek(_date, options) - +startOfWeekYear(_date, options);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getWeek;\n", "export function addLeadingZeros(number, targetLength) {\n  const sign = number < 0 ? \"-\" : \"\";\n  const output = Math.abs(number).toString().padStart(targetLength, \"0\");\n  return sign + output;\n}\n", "import { addLeadingZeros } from \"../addLeadingZeros.js\";\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\n\nexport const lightFormatters = {\n  // Year\n  y(date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n\n    const signedYear = date.getFullYear();\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === \"yy\" ? year % 100 : year, token.length);\n  },\n\n  // Month\n  M(date, token) {\n    const month = date.getMonth();\n    return token === \"M\" ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n\n  // Day of the month\n  d(date, token) {\n    return addLeadingZeros(date.getDate(), token.length);\n  },\n\n  // AM or PM\n  a(date, token) {\n    const dayPeriodEnumValue = date.getHours() / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return dayPeriodEnumValue.toUpperCase();\n      case \"aaa\":\n        return dayPeriodEnumValue;\n      case \"aaaaa\":\n        return dayPeriodEnumValue[0];\n      case \"aaaa\":\n      default:\n        return dayPeriodEnumValue === \"am\" ? \"a.m.\" : \"p.m.\";\n    }\n  },\n\n  // Hour [1-12]\n  h(date, token) {\n    return addLeadingZeros(date.getHours() % 12 || 12, token.length);\n  },\n\n  // Hour [0-23]\n  H(date, token) {\n    return addLeadingZeros(date.getHours(), token.length);\n  },\n\n  // Minute\n  m(date, token) {\n    return addLeadingZeros(date.getMinutes(), token.length);\n  },\n\n  // Second\n  s(date, token) {\n    return addLeadingZeros(date.getSeconds(), token.length);\n  },\n\n  // Fraction of second\n  S(date, token) {\n    const numberOfDigits = token.length;\n    const milliseconds = date.getMilliseconds();\n    const fractionalSeconds = Math.trunc(\n      milliseconds * Math.pow(10, numberOfDigits - 3),\n    );\n    return addLeadingZeros(fractionalSeconds, token.length);\n  },\n};\n", "import { getDayOfYear } from \"../../getDayOfYear.js\";\nimport { getISOWeek } from \"../../getISOWeek.js\";\nimport { getISOWeekYear } from \"../../getISOWeekYear.js\";\nimport { getWeek } from \"../../getWeek.js\";\nimport { getWeekYear } from \"../../getWeekYear.js\";\n\nimport { addLeadingZeros } from \"../addLeadingZeros.js\";\nimport { lightFormatters } from \"./lightFormatters.js\";\n\nconst dayPeriodEnum = {\n  am: \"am\",\n  pm: \"pm\",\n  midnight: \"midnight\",\n  noon: \"noon\",\n  morning: \"morning\",\n  afternoon: \"afternoon\",\n  evening: \"evening\",\n  night: \"night\",\n};\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |\n * |  p! | Long localized time            |  P! | Long localized date            |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n * - `P` is long localized date format\n * - `p` is long localized time format\n */\n\nexport const formatters = {\n  // Era\n  G: function (date, token, localize) {\n    const era = date.getFullYear() > 0 ? 1 : 0;\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return localize.era(era, { width: \"abbreviated\" });\n      // A, B\n      case \"GGGGG\":\n        return localize.era(era, { width: \"narrow\" });\n      // Anno Domini, Before Christ\n      case \"GGGG\":\n      default:\n        return localize.era(era, { width: \"wide\" });\n    }\n  },\n\n  // Year\n  y: function (date, token, localize) {\n    // Ordinal number\n    if (token === \"yo\") {\n      const signedYear = date.getFullYear();\n      // Returns 1 for 1 BC (which is year 0 in JavaScript)\n      const year = signedYear > 0 ? signedYear : 1 - signedYear;\n      return localize.ordinalNumber(year, { unit: \"year\" });\n    }\n\n    return lightFormatters.y(date, token);\n  },\n\n  // Local week-numbering year\n  Y: function (date, token, localize, options) {\n    const signedWeekYear = getWeekYear(date, options);\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;\n\n    // Two digit year\n    if (token === \"YY\") {\n      const twoDigitYear = weekYear % 100;\n      return addLeadingZeros(twoDigitYear, 2);\n    }\n\n    // Ordinal number\n    if (token === \"Yo\") {\n      return localize.ordinalNumber(weekYear, { unit: \"year\" });\n    }\n\n    // Padding\n    return addLeadingZeros(weekYear, token.length);\n  },\n\n  // ISO week-numbering year\n  R: function (date, token) {\n    const isoWeekYear = getISOWeekYear(date);\n\n    // Padding\n    return addLeadingZeros(isoWeekYear, token.length);\n  },\n\n  // Extended year. This is a single number designating the year of this calendar system.\n  // The main difference between `y` and `u` localizers are B.C. years:\n  // | Year | `y` | `u` |\n  // |------|-----|-----|\n  // | AC 1 |   1 |   1 |\n  // | BC 1 |   1 |   0 |\n  // | BC 2 |   2 |  -1 |\n  // Also `yy` always returns the last two digits of a year,\n  // while `uu` pads single digit years to 2 characters and returns other years unchanged.\n  u: function (date, token) {\n    const year = date.getFullYear();\n    return addLeadingZeros(year, token.length);\n  },\n\n  // Quarter\n  Q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"Q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"QQ\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"Qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"QQQ\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"QQQQQ\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"QQQQ\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone quarter\n  q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"qq\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"qqq\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"qqqqq\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"qqqq\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // Month\n  M: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      case \"M\":\n      case \"MM\":\n        return lightFormatters.M(date, token);\n      // 1st, 2nd, ..., 12th\n      case \"Mo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"MMM\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // J, F, ..., D\n      case \"MMMMM\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // January, February, ..., December\n      case \"MMMM\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"formatting\" });\n    }\n  },\n\n  // Stand-alone month\n  L: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return String(month + 1);\n      // 01, 02, ..., 12\n      case \"LL\":\n        return addLeadingZeros(month + 1, 2);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // J, F, ..., D\n      case \"LLLLL\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"standalone\" });\n    }\n  },\n\n  // Local week of year\n  w: function (date, token, localize, options) {\n    const week = getWeek(date, options);\n\n    if (token === \"wo\") {\n      return localize.ordinalNumber(week, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(week, token.length);\n  },\n\n  // ISO week of year\n  I: function (date, token, localize) {\n    const isoWeek = getISOWeek(date);\n\n    if (token === \"Io\") {\n      return localize.ordinalNumber(isoWeek, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(isoWeek, token.length);\n  },\n\n  // Day of the month\n  d: function (date, token, localize) {\n    if (token === \"do\") {\n      return localize.ordinalNumber(date.getDate(), { unit: \"date\" });\n    }\n\n    return lightFormatters.d(date, token);\n  },\n\n  // Day of year\n  D: function (date, token, localize) {\n    const dayOfYear = getDayOfYear(date);\n\n    if (token === \"Do\") {\n      return localize.ordinalNumber(dayOfYear, { unit: \"dayOfYear\" });\n    }\n\n    return addLeadingZeros(dayOfYear, token.length);\n  },\n\n  // Day of week\n  E: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    switch (token) {\n      // Tue\n      case \"E\":\n      case \"EE\":\n      case \"EEE\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"EEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"EEEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"EEEE\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Local day of week\n  e: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (Nth day of week with current locale or weekStartsOn)\n      case \"e\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"ee\":\n        return addLeadingZeros(localDayOfWeek, 2);\n      // 1st, 2nd, ..., 7th\n      case \"eo\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"eee\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"eeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"eeeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"eeee\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone local day of week\n  c: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (same as in `e`)\n      case \"c\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"cc\":\n        return addLeadingZeros(localDayOfWeek, token.length);\n      // 1st, 2nd, ..., 7th\n      case \"co\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"ccc\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // T\n      case \"ccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // Tu\n      case \"cccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"standalone\",\n        });\n      // Tuesday\n      case \"cccc\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // ISO day of week\n  i: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n    switch (token) {\n      // 2\n      case \"i\":\n        return String(isoDayOfWeek);\n      // 02\n      case \"ii\":\n        return addLeadingZeros(isoDayOfWeek, token.length);\n      // 2nd\n      case \"io\":\n        return localize.ordinalNumber(isoDayOfWeek, { unit: \"day\" });\n      // Tue\n      case \"iii\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"iiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"iiiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"iiii\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM or PM\n  a: function (date, token, localize) {\n    const hours = date.getHours();\n    const dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"aaa\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"aaaaa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"aaaa\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM, PM, midnight, noon\n  b: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours === 12) {\n      dayPeriodEnumValue = dayPeriodEnum.noon;\n    } else if (hours === 0) {\n      dayPeriodEnumValue = dayPeriodEnum.midnight;\n    } else {\n      dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n    }\n\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"bbb\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"bbbbb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"bbbb\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // in the morning, in the afternoon, in the evening, at night\n  B: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours >= 17) {\n      dayPeriodEnumValue = dayPeriodEnum.evening;\n    } else if (hours >= 12) {\n      dayPeriodEnumValue = dayPeriodEnum.afternoon;\n    } else if (hours >= 4) {\n      dayPeriodEnumValue = dayPeriodEnum.morning;\n    } else {\n      dayPeriodEnumValue = dayPeriodEnum.night;\n    }\n\n    switch (token) {\n      case \"B\":\n      case \"BB\":\n      case \"BBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"BBBBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"BBBB\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Hour [1-12]\n  h: function (date, token, localize) {\n    if (token === \"ho\") {\n      let hours = date.getHours() % 12;\n      if (hours === 0) hours = 12;\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return lightFormatters.h(date, token);\n  },\n\n  // Hour [0-23]\n  H: function (date, token, localize) {\n    if (token === \"Ho\") {\n      return localize.ordinalNumber(date.getHours(), { unit: \"hour\" });\n    }\n\n    return lightFormatters.H(date, token);\n  },\n\n  // Hour [0-11]\n  K: function (date, token, localize) {\n    const hours = date.getHours() % 12;\n\n    if (token === \"Ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Hour [1-24]\n  k: function (date, token, localize) {\n    let hours = date.getHours();\n    if (hours === 0) hours = 24;\n\n    if (token === \"ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Minute\n  m: function (date, token, localize) {\n    if (token === \"mo\") {\n      return localize.ordinalNumber(date.getMinutes(), { unit: \"minute\" });\n    }\n\n    return lightFormatters.m(date, token);\n  },\n\n  // Second\n  s: function (date, token, localize) {\n    if (token === \"so\") {\n      return localize.ordinalNumber(date.getSeconds(), { unit: \"second\" });\n    }\n\n    return lightFormatters.s(date, token);\n  },\n\n  // Fraction of second\n  S: function (date, token) {\n    return lightFormatters.S(date, token);\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    if (timezoneOffset === 0) {\n      return \"Z\";\n    }\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"X\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case \"XXXX\":\n      case \"XX\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case \"XXXXX\":\n      case \"XXX\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"x\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case \"xxxx\":\n      case \"xx\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case \"xxxxx\":\n      case \"xxx\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (GMT)\n  O: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"O\":\n      case \"OO\":\n      case \"OOO\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"OOOO\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (specific non-location)\n  z: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"z\":\n      case \"zz\":\n      case \"zzz\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"zzzz\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Seconds timestamp\n  t: function (date, token, _localize) {\n    const timestamp = Math.trunc(+date / 1000);\n    return addLeadingZeros(timestamp, token.length);\n  },\n\n  // Milliseconds timestamp\n  T: function (date, token, _localize) {\n    return addLeadingZeros(+date, token.length);\n  },\n};\n\nfunction formatTimezoneShort(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = Math.trunc(absOffset / 60);\n  const minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\n\nfunction formatTimezoneWithOptionalMinutes(offset, delimiter) {\n  if (offset % 60 === 0) {\n    const sign = offset > 0 ? \"-\" : \"+\";\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, delimiter);\n}\n\nfunction formatTimezone(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = addLeadingZeros(Math.trunc(absOffset / 60), 2);\n  const minutes = addLeadingZeros(absOffset % 60, 2);\n  return sign + hours + delimiter + minutes;\n}\n", "const dateLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"P\":\n      return formatLong.date({ width: \"short\" });\n    case \"PP\":\n      return formatLong.date({ width: \"medium\" });\n    case \"PPP\":\n      return formatLong.date({ width: \"long\" });\n    case \"PPPP\":\n    default:\n      return formatLong.date({ width: \"full\" });\n  }\n};\n\nconst timeLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"p\":\n      return formatLong.time({ width: \"short\" });\n    case \"pp\":\n      return formatLong.time({ width: \"medium\" });\n    case \"ppp\":\n      return formatLong.time({ width: \"long\" });\n    case \"pppp\":\n    default:\n      return formatLong.time({ width: \"full\" });\n  }\n};\n\nconst dateTimeLongFormatter = (pattern, formatLong) => {\n  const matchResult = pattern.match(/(P+)(p+)?/) || [];\n  const datePattern = matchResult[1];\n  const timePattern = matchResult[2];\n\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n\n  let dateTimeFormat;\n\n  switch (datePattern) {\n    case \"P\":\n      dateTimeFormat = formatLong.dateTime({ width: \"short\" });\n      break;\n    case \"PP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"medium\" });\n      break;\n    case \"PPP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"long\" });\n      break;\n    case \"PPPP\":\n    default:\n      dateTimeFormat = formatLong.dateTime({ width: \"full\" });\n      break;\n  }\n\n  return dateTimeFormat\n    .replace(\"{{date}}\", dateLongFormatter(datePattern, formatLong))\n    .replace(\"{{time}}\", timeLongFormatter(timePattern, formatLong));\n};\n\nexport const longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter,\n};\n", "/**\n * @name isDate\n * @category Common Helpers\n * @summary Is the given value a date?\n *\n * @description\n * Returns true if the given value is an instance of Date. The function works for dates transferred across iframes.\n *\n * @param value - The value to check\n *\n * @returns True if the given value is a date\n *\n * @example\n * // For a valid date:\n * const result = isDate(new Date())\n * //=> true\n *\n * @example\n * // For an invalid date:\n * const result = isDate(new Date(NaN))\n * //=> true\n *\n * @example\n * // For some value:\n * const result = isDate('2014-02-31')\n * //=> false\n *\n * @example\n * // For an object:\n * const result = isDate({})\n * //=> false\n */\nexport function isDate(value) {\n  return (\n    value instanceof Date ||\n    (typeof value === \"object\" &&\n      Object.prototype.toString.call(value) === \"[object Date]\")\n  );\n}\n\n// Fallback for modularized imports:\nexport default isDate;\n", "import { isDate } from \"./isDate.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * @name isValid\n * @category Common Helpers\n * @summary Is the given date valid?\n *\n * @description\n * Returns false if argument is Invalid Date and true otherwise.\n * Argument is converted to Date using `toDate`. See [toDate](https://date-fns.org/docs/toDate)\n * Invalid Date is a Date, whose time value is NaN.\n *\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @param date - The date to check\n *\n * @returns The date is valid\n *\n * @example\n * // For the valid date:\n * const result = isValid(new Date(2014, 1, 31))\n * //=> true\n *\n * @example\n * // For the value, convertible into a date:\n * const result = isValid(1393804800000)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isValid(new Date(''))\n * //=> false\n */\nexport function isValid(date) {\n  return !((!isDate(date) && typeof date !== \"number\") || isNaN(+toDate(date)));\n}\n\n// Fallback for modularized imports:\nexport default isValid;\n", "const dayOfYearTokenRE = /^D+$/;\nconst weekYearTokenRE = /^Y+$/;\n\nconst throwTokens = [\"D\", \"DD\", \"YY\", \"YYYY\"];\n\nexport function isProtectedDayOfYearToken(token) {\n  return dayOfYearTokenRE.test(token);\n}\n\nexport function isProtectedWeekYearToken(token) {\n  return weekYearTokenRE.test(token);\n}\n\nexport function warnOrThrowProtectedError(token, format, input) {\n  const _message = message(token, format, input);\n  console.warn(_message);\n  if (throwTokens.includes(token)) throw new RangeError(_message);\n}\n\nfunction message(token, format, input) {\n  const subject = token[0] === \"Y\" ? \"years\" : \"days of the month\";\n  return `Use \\`${token.toLowerCase()}\\` instead of \\`${token}\\` (in \\`${format}\\`) for formatting ${subject} to the input \\`${input}\\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`;\n}\n", "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { formatters } from \"./_lib/format/formatters.js\";\nimport { longFormatters } from \"./_lib/format/longFormatters.js\";\nimport {\n  isProtectedDayOfYearToken,\n  isProtectedWeekYearToken,\n  warnOrThrowProtectedError,\n} from \"./_lib/protectedTokens.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { formatters, longFormatters };\n\n// This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp =\n  /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nconst longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\n\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\nexport { format as formatDate };\n\n/**\n * The {@link format} function options.\n */\n\n/**\n * @name format\n * @alias formatDate\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. The result may vary by locale.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n * (see the last example)\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 7 below the table).\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   | Notes |\n * |---------------------------------|---------|-----------------------------------|-------|\n * | Era                             | G..GGG  | AD, BC                            |       |\n * |                                 | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 | GGGGG   | A, B                              |       |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | yo      | 44th, 1st, 0th, 17th              | 5,7   |\n * |                                 | yy      | 44, 01, 00, 17                    | 5     |\n * |                                 | yyy     | 044, 001, 1900, 2017              | 5     |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            | 5     |\n * |                                 | yyyyy   | ...                               | 3,5   |\n * | Local week-numbering year       | Y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | Yo      | 44th, 1st, 1900th, 2017th         | 5,7   |\n * |                                 | YY      | 44, 01, 00, 17                    | 5,8   |\n * |                                 | YYY     | 044, 001, 1900, 2017              | 5     |\n * |                                 | YYYY    | 0044, 0001, 1900, 2017            | 5,8   |\n * |                                 | YYYYY   | ...                               | 3,5   |\n * | ISO week-numbering year         | R       | -43, 0, 1, 1900, 2017             | 5,7   |\n * |                                 | RR      | -43, 00, 01, 1900, 2017           | 5,7   |\n * |                                 | RRR     | -043, 000, 001, 1900, 2017        | 5,7   |\n * |                                 | RRRR    | -0043, 0000, 0001, 1900, 2017     | 5,7   |\n * |                                 | RRRRR   | ...                               | 3,5,7 |\n * | Extended year                   | u       | -43, 0, 1, 1900, 2017             | 5     |\n * |                                 | uu      | -43, 01, 1900, 2017               | 5     |\n * |                                 | uuu     | -043, 001, 1900, 2017             | 5     |\n * |                                 | uuuu    | -0043, 0001, 1900, 2017           | 5     |\n * |                                 | uuuuu   | ...                               | 3,5   |\n * | Quarter (formatting)            | Q       | 1, 2, 3, 4                        |       |\n * |                                 | Qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | QQ      | 01, 02, 03, 04                    |       |\n * |                                 | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | q       | 1, 2, 3, 4                        |       |\n * |                                 | qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | qq      | 01, 02, 03, 04                    |       |\n * |                                 | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | qqqqq   | 1, 2, 3, 4                        | 4     |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |       |\n * |                                 | Mo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | MM      | 01, 02, ..., 12                   |       |\n * |                                 | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 | MMMM    | January, February, ..., December  | 2     |\n * |                                 | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | L       | 1, 2, ..., 12                     |       |\n * |                                 | Lo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | LL      | 01, 02, ..., 12                   |       |\n * |                                 | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 | LLLL    | January, February, ..., December  | 2     |\n * |                                 | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | w       | 1, 2, ..., 53                     |       |\n * |                                 | wo      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | I       | 1, 2, ..., 53                     | 7     |\n * |                                 | Io      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | II      | 01, 02, ..., 53                   | 7     |\n * | Day of month                    | d       | 1, 2, ..., 31                     |       |\n * |                                 | do      | 1st, 2nd, ..., 31st               | 7     |\n * |                                 | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     | D       | 1, 2, ..., 365, 366               | 9     |\n * |                                 | Do      | 1st, 2nd, ..., 365th, 366th       | 7     |\n * |                                 | DD      | 01, 02, ..., 365, 366             | 9     |\n * |                                 | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 | DDDD    | ...                               | 3     |\n * | Day of week (formatting)        | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    | i       | 1, 2, 3, ..., 7                   | 7     |\n * |                                 | io      | 1st, 2nd, ..., 7th                | 7     |\n * |                                 | ii      | 01, 02, ..., 07                   | 7     |\n * |                                 | iii     | Mon, Tue, Wed, ..., Sun           | 7     |\n * |                                 | iiii    | Monday, Tuesday, ..., Sunday      | 2,7   |\n * |                                 | iiiii   | M, T, W, T, F, S, S               | 7     |\n * |                                 | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 7     |\n * | Local day of week (formatting)  | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 | eo      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | ee      | 02, 03, ..., 01                   |       |\n * |                                 | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 | co      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | cc      | 02, 03, ..., 01                   |       |\n * |                                 | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          | a..aa   | AM, PM                            |       |\n * |                                 | aaa     | am, pm                            |       |\n * |                                 | aaaa    | a.m., p.m.                        | 2     |\n * |                                 | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          | b..bb   | AM, PM, noon, midnight            |       |\n * |                                 | bbb     | am, pm, noon, midnight            |       |\n * |                                 | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             | B..BBB  | at night, in the morning, ...     |       |\n * |                                 | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 | ho      | 1st, 2nd, ..., 11th, 12th         | 7     |\n * |                                 | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 | Ho      | 0th, 1st, 2nd, ..., 23rd          | 7     |\n * |                                 | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 | Ko      | 1st, 2nd, ..., 11th, 0th          | 7     |\n * |                                 | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 | ko      | 24th, 1st, 2nd, ..., 23rd         | 7     |\n * |                                 | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          | m       | 0, 1, ..., 59                     |       |\n * |                                 | mo      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | mm      | 00, 01, ..., 59                   |       |\n * | Second                          | s       | 0, 1, ..., 59                     |       |\n * |                                 | so      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | ss      | 00, 01, ..., 59                   |       |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |       |\n * |                                 | SS      | 00, 01, ..., 99                   |       |\n * |                                 | SSS     | 000, 001, ..., 999                |       |\n * |                                 | SSSS    | ...                               | 3     |\n * | Timezone (ISO-8601 w/ Z)        | X       | -08, +0530, Z                     |       |\n * |                                 | XX      | -0800, +0530, Z                   |       |\n * |                                 | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       | x       | -08, +0530, +00                   |       |\n * |                                 | xx      | -0800, +0530, +0000               |       |\n * |                                 | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Timezone (GMT)                  | O...OOO | GMT-8, GMT+5:30, GMT+0            |       |\n * |                                 | OOOO    | GMT-08:00, GMT+05:30, GMT+00:00   | 2     |\n * | Timezone (specific non-locat.)  | z...zzz | GMT-8, GMT+5:30, GMT+0            | 6     |\n * |                                 | zzzz    | GMT-08:00, GMT+05:30, GMT+00:00   | 2,6   |\n * | Seconds timestamp               | t       | 512969520                         | 7     |\n * |                                 | tt      | ...                               | 3,7   |\n * | Milliseconds timestamp          | T       | 512969520900                      | 7     |\n * |                                 | TT      | ...                               | 3,7   |\n * | Long localized date             | P       | 04/29/1453                        | 7     |\n * |                                 | PP      | Apr 29, 1453                      | 7     |\n * |                                 | PPP     | April 29th, 1453                  | 7     |\n * |                                 | PPPP    | Friday, April 29th, 1453          | 2,7   |\n * | Long localized time             | p       | 12:00 AM                          | 7     |\n * |                                 | pp      | 12:00:00 AM                       | 7     |\n * |                                 | ppp     | 12:00:00 AM GMT+2                 | 7     |\n * |                                 | pppp    | 12:00:00 AM GMT+02:00             | 2,7   |\n * | Combination of date and time    | Pp      | 04/29/1453, 12:00 AM              | 7     |\n * |                                 | PPpp    | Apr 29, 1453, 12:00:00 AM         | 7     |\n * |                                 | PPPppp  | April 29th, 1453 at ...           | 7     |\n * |                                 | PPPPpppp| Friday, April 29th, 1453 at ...   | 2,7   |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table (e.g. `EEEEEEEEEEE`)\n *    the output will be the same as default pattern for this unit, usually\n *    the longest one (in case of ISO weekdays, `EEEE`). Default patterns for units\n *    are marked with \"2\" in the last column of the table.\n *\n *    `format(new Date(2017, 10, 6), 'MMM') //=> 'Nov'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMM') //=> 'N'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMMM') //=> 'November'`\n *\n * 3. Some patterns could be unlimited length (such as `yyyyyyyy`).\n *    The output will be padded with zeros to match the length of the pattern.\n *\n *    `format(new Date(2017, 10, 6), 'yyyyyyyy') //=> '00002017'`\n *\n * 4. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 5. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` always returns the last two digits of a year,\n *    while `uu` pads single digit years to 2 characters and returns other years unchanged:\n *\n *    | Year | `yy` | `uu` |\n *    |------|------|------|\n *    | 1    |   01 |   01 |\n *    | 14   |   14 |   14 |\n *    | 376  |   76 |  376 |\n *    | 1453 |   53 | 1453 |\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [getISOWeekYear](https://date-fns.org/docs/getISOWeekYear)\n *    and [getWeekYear](https://date-fns.org/docs/getWeekYear)).\n *\n * 6. Specific non-location timezones are currently unavailable in `date-fns`,\n *    so right now these tokens fall back to GMT timezones.\n *\n * 7. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `t`: seconds timestamp\n *    - `T`: milliseconds timestamp\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 8. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 9. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * @param date - The original date\n * @param format - The string of tokens\n * @param options - An object with options\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n * @throws `options.locale` must contain `localize` property\n * @throws `options.locale` must contain `formatLong` property\n * @throws use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * // Represent 11 February 2014 in middle-endian format:\n * const result = format(new Date(2014, 1, 11), 'MM/dd/yyyy')\n * //=> '02/11/2014'\n *\n * @example\n * // Represent 2 July 2014 in Esperanto:\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = format(new Date(2014, 6, 2), \"do 'de' MMMM yyyy\", {\n *   locale: eoLocale\n * })\n * //=> '2-a de julio 2014'\n *\n * @example\n * // Escape string by single quote characters:\n * const result = format(new Date(2014, 6, 2, 15), \"h 'o''clock'\")\n * //=> \"3 o'clock\"\n */\nexport function format(date, formatStr, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const originalDate = toDate(date, options?.in);\n\n  if (!isValid(originalDate)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  let parts = formatStr\n    .match(longFormattingTokensRegExp)\n    .map((substring) => {\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"p\" || firstCharacter === \"P\") {\n        const longFormatter = longFormatters[firstCharacter];\n        return longFormatter(substring, locale.formatLong);\n      }\n      return substring;\n    })\n    .join(\"\")\n    .match(formattingTokensRegExp)\n    .map((substring) => {\n      // Replace two single quote characters with one single quote character\n      if (substring === \"''\") {\n        return { isToken: false, value: \"'\" };\n      }\n\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"'\") {\n        return { isToken: false, value: cleanEscapedString(substring) };\n      }\n\n      if (formatters[firstCharacter]) {\n        return { isToken: true, value: substring };\n      }\n\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\n          \"Format string contains an unescaped latin alphabet character `\" +\n            firstCharacter +\n            \"`\",\n        );\n      }\n\n      return { isToken: false, value: substring };\n    });\n\n  // invoke localize preprocessor (only for french locales at the moment)\n  if (locale.localize.preprocessor) {\n    parts = locale.localize.preprocessor(originalDate, parts);\n  }\n\n  const formatterOptions = {\n    firstWeekContainsDate,\n    weekStartsOn,\n    locale,\n  };\n\n  return parts\n    .map((part) => {\n      if (!part.isToken) return part.value;\n\n      const token = part.value;\n\n      if (\n        (!options?.useAdditionalWeekYearTokens &&\n          isProtectedWeekYearToken(token)) ||\n        (!options?.useAdditionalDayOfYearTokens &&\n          isProtectedDayOfYearToken(token))\n      ) {\n        warnOrThrowProtectedError(token, formatStr, String(date));\n      }\n\n      const formatter = formatters[token[0]];\n      return formatter(originalDate, token, locale.localize, formatterOptions);\n    })\n    .join(\"\");\n}\n\nfunction cleanEscapedString(input) {\n  const matched = input.match(escapedStringRegExp);\n\n  if (!matched) {\n    return input;\n  }\n\n  return matched[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default format;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDate} function options.\n */\n\n/**\n * @name getDate\n * @category Day Helpers\n * @summary Get the day of the month of the given date.\n *\n * @description\n * Get the day of the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The day of month\n *\n * @example\n * // Which day of the month is 29 February 2012?\n * const result = getDate(new Date(2012, 1, 29))\n * //=> 29\n */\nexport function getDate(date, options) {\n  return toDate(date, options?.in).getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDate;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDaysInMonth} function options.\n */\n\n/**\n * @name getDaysInMonth\n * @category Month Helpers\n * @summary Get the number of days in a month of the given date.\n *\n * @description\n * Get the number of days in a month of the given date, considering the context if provided.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of days in a month\n *\n * @example\n * // How many days are in February 2000?\n * const result = getDaysInMonth(new Date(2000, 1))\n * //=> 29\n */\nexport function getDaysInMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const monthIndex = _date.getMonth();\n  const lastDayOfMonth = constructFrom(_date, 0);\n  lastDayOfMonth.setFullYear(year, monthIndex + 1, 0);\n  lastDayOfMonth.setHours(0, 0, 0, 0);\n  return lastDayOfMonth.getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDaysInMonth;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getHours} function options.\n */\n\n/**\n * @name getHours\n * @category Hour Helpers\n * @summary Get the hours of the given date.\n *\n * @description\n * Get the hours of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The hours\n *\n * @example\n * // Get the hours of 29 February 2012 11:45:00:\n * const result = getHours(new Date(2012, 1, 29, 11, 45))\n * //=> 11\n */\nexport function getHours(date, options) {\n  return toDate(date, options?.in).getHours();\n}\n\n// Fallback for modularized imports:\nexport default getHours;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getMinutes} function options.\n */\n\n/**\n * @name getMinutes\n * @category Minute Helpers\n * @summary Get the minutes of the given date.\n *\n * @description\n * Get the minutes of the given date.\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The minutes\n *\n * @example\n * // Get the minutes of 29 February 2012 11:45:05:\n * const result = getMinutes(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 45\n */\nexport function getMinutes(date, options) {\n  return toDate(date, options?.in).getMinutes();\n}\n\n// Fallback for modularized imports:\nexport default getMinutes;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getMonth} function options.\n */\n\n/**\n * @name getMonth\n * @category Month Helpers\n * @summary Get the month of the given date.\n *\n * @description\n * Get the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The month index (0-11)\n *\n * @example\n * // Which month is 29 February 2012?\n * const result = getMonth(new Date(2012, 1, 29))\n * //=> 1\n */\nexport function getMonth(date, options) {\n  return toDate(date, options?.in).getMonth();\n}\n\n// Fallback for modularized imports:\nexport default getMonth;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name getSeconds\n * @category Second Helpers\n * @summary Get the seconds of the given date.\n *\n * @description\n * Get the seconds of the given date.\n *\n * @param date - The given date\n *\n * @returns The seconds\n *\n * @example\n * // Get the seconds of 29 February 2012 11:45:05.123:\n * const result = getSeconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 5\n */\nexport function getSeconds(date) {\n  return toDate(date).getSeconds();\n}\n\n// Fallback for modularized imports:\nexport default getSeconds;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name getMilliseconds\n * @category Millisecond Helpers\n * @summary Get the milliseconds of the given date.\n *\n * @description\n * Get the milliseconds of the given date.\n *\n * @param date - The given date\n *\n * @returns The milliseconds\n *\n * @example\n * // Get the milliseconds of 29 February 2012 11:45:05.123:\n * const result = getMilliseconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 123\n */\nexport function getMilliseconds(date) {\n  return toDate(date).getMilliseconds();\n}\n\n// Fallback for modularized imports:\nexport default getMilliseconds;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getYear} function options.\n */\n\n/**\n * @name getYear\n * @category Year Helpers\n * @summary Get the year of the given date.\n *\n * @description\n * Get the year of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The year\n *\n * @example\n * // Which year is 2 July 2014?\n * const result = getYear(new Date(2014, 6, 2))\n * //=> 2014\n */\nexport function getYear(date, options) {\n  return toDate(date, options?.in).getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default getYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isAfter\n * @category Common Helpers\n * @summary Is the first date after the second one?\n *\n * @description\n * Is the first date after the second one?\n *\n * @param date - The date that should be after the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is after the second date\n *\n * @example\n * // Is 10 July 1989 after 11 February 1987?\n * const result = isAfter(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> true\n */\nexport function isAfter(date, dateToCompare) {\n  return +toDate(date) > +toDate(dateToCompare);\n}\n\n// Fallback for modularized imports:\nexport default isAfter;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isBefore\n * @category Common Helpers\n * @summary Is the first date before the second one?\n *\n * @description\n * Is the first date before the second one?\n *\n * @param date - The date that should be before the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is before the second date\n *\n * @example\n * // Is 10 July 1989 before 11 February 1987?\n * const result = isBefore(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> false\n */\nexport function isBefore(date, dateToCompare) {\n  return +toDate(date) < +toDate(dateToCompare);\n}\n\n// Fallback for modularized imports:\nexport default isBefore;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isEqual\n * @category Common Helpers\n * @summary Are the given dates equal?\n *\n * @description\n * Are the given dates equal?\n *\n * @param dateLeft - The first date to compare\n * @param dateRight - The second date to compare\n *\n * @returns The dates are equal\n *\n * @example\n * // Are 2 July 2014 06:30:45.000 and 2 July 2014 06:30:45.500 equal?\n * const result = isEqual(\n *   new Date(2014, 6, 2, 6, 30, 45, 0),\n *   new Date(2014, 6, 2, 6, 30, 45, 500)\n * )\n * //=> false\n */\nexport function isEqual(leftDate, rightDate) {\n  return +toDate(leftDate) === +toDate(rightDate);\n}\n\n// Fallback for modularized imports:\nexport default isEqual;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link isSameDay} function options.\n */\n\n/**\n * @name isSameDay\n * @category Day Helpers\n * @summary Are the given dates in the same day (and year and month)?\n *\n * @description\n * Are the given dates in the same day (and year and month)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same day (and year and month)\n *\n * @example\n * // Are 4 September 06:00:00 and 4 September 18:00:00 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 18, 0))\n * //=> true\n *\n * @example\n * // Are 4 September and 4 October in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2014, 9, 4))\n * //=> false\n *\n * @example\n * // Are 4 September, 2014 and 4 September, 2015 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2015, 8, 4))\n * //=> false\n */\nexport function isSameDay(laterDate, earlierDate, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return +startOfDay(dateLeft_) === +startOfDay(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameDay;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameYear} function options.\n */\n\n/**\n * @name isSameYear\n * @category Year Helpers\n * @summary Are the given dates in the same year?\n *\n * @description\n * Are the given dates in the same year?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same year\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same year?\n * const result = isSameYear(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n */\nexport function isSameYear(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return laterDate_.getFullYear() === earlierDate_.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default isSameYear;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameMonth} function options.\n */\n\n/**\n * @name isSameMonth\n * @category Month Helpers\n * @summary Are the given dates in the same month (and year)?\n *\n * @description\n * Are the given dates in the same month (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same month (and year)\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n *\n * @example\n * // Are 2 September 2014 and 25 September 2015 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2015, 8, 25))\n * //=> false\n */\nexport function isSameMonth(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return (\n    laterDate_.getFullYear() === earlierDate_.getFullYear() &&\n    laterDate_.getMonth() === earlierDate_.getMonth()\n  );\n}\n\n// Fallback for modularized imports:\nexport default isSameMonth;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfHour} function options.\n */\n\n/**\n * @name startOfHour\n * @category Hour Helpers\n * @summary Return the start of an hour for the given date.\n *\n * @description\n * Return the start of an hour for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of an hour\n *\n * @example\n * // The start of an hour for 2 September 2014 11:55:00:\n * const result = startOfHour(new Date(2014, 8, 2, 11, 55))\n * //=> Tue Sep 02 2014 11:00:00\n */\nexport function startOfHour(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMinutes(0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfHour;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfHour } from \"./startOfHour.js\";\n\n/**\n * The {@link isSameHour} function options.\n */\n\n/**\n * @name isSameHour\n * @category Hour Helpers\n * @summary Are the given dates in the same hour (and same day)?\n *\n * @description\n * Are the given dates in the same hour (and same day)?\n *\n * @param dateLeft - The first date to check\n * @param dateRight - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same hour (and same day)\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 4 September 06:30:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 6, 30))\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 5 September 06:00:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 5, 6, 0))\n * //=> false\n */\nexport function isSameHour(dateLeft, dateRight, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(\n    options?.in,\n    dateLeft,\n    dateRight,\n  );\n  return +startOfHour(dateLeft_) === +startOfHour(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameHour;\n", "import { getDefaultOptions as getInternalDefaultOptions } from \"./_lib/defaultOptions.js\";\n\n/**\n * @name getDefaultOptions\n * @category Common Helpers\n * @summary Get default options.\n * @pure false\n *\n * @description\n * Returns an object that contains defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * You can change these with [setDefaultOptions](https://date-fns.org/docs/setDefaultOptions).\n *\n * @returns The default options\n *\n * @example\n * const result = getDefaultOptions()\n * //=> {}\n *\n * @example\n * setDefaultOptions({ weekStarsOn: 1, firstWeekContainsDate: 4 })\n * const result = getDefaultOptions()\n * //=> { weekStarsOn: 1, firstWeekContainsDate: 4 }\n */\nexport function getDefaultOptions() {\n  return Object.assign({}, getInternalDefaultOptions());\n}\n\n// Fallback for modularized imports:\nexport default getDefaultOptions;\n", "import { constructFrom } from \"./constructFrom.js\";\n\n/**\n * @name transpose\n * @category Generic Helpers\n * @summary Transpose the date to the given constructor.\n *\n * @description\n * The function transposes the date to the given constructor. It helps you\n * to transpose the date in the system time zone to say `UTCDate` or any other\n * date extension.\n *\n * @typeParam InputDate - The input `Date` type derived from the passed argument.\n * @typeParam ResultDate - The result `Date` type derived from the passed constructor.\n *\n * @param date - The date to use values from\n * @param constructor - The date constructor to use\n *\n * @returns Date transposed to the given constructor\n *\n * @example\n * // Create July 10, 2022 00:00 in locale time zone\n * const date = new Date(2022, 6, 10)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0800 (Singapore Standard Time)'\n *\n * @example\n * // Transpose the date to July 10, 2022 00:00 in UTC\n * transpose(date, UTCDate)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0000 (Coordinated Universal Time)'\n */\nexport function transpose(date, constructor) {\n  const date_ = isConstructor(constructor)\n    ? new constructor(0)\n    : constructFrom(constructor, 0);\n  date_.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n  date_.setHours(\n    date.getHours(),\n    date.getMinutes(),\n    date.getSeconds(),\n    date.getMilliseconds(),\n  );\n  return date_;\n}\n\nfunction isConstructor(constructor) {\n  return (\n    typeof constructor === \"function\" &&\n    constructor.prototype?.constructor === constructor\n  );\n}\n\n// Fallback for modularized imports:\nexport default transpose;\n", "import { getWeek } from \"./getWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setWeek} function options.\n */\n\n/**\n * @name setWeek\n * @category Week Helpers\n * @summary Set the local week to the given date.\n *\n * @description\n * Set the local week to the given date, saving the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param week - The week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the local week set\n *\n * @example\n * // Set the 1st week to 2 January 2005 with default options:\n * const result = setWeek(new Date(2005, 0, 2), 1)\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // Set the 1st week to 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January:\n * const result = setWeek(new Date(2005, 0, 2), 1, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sun Jan 4 2004 00:00:00\n */\nexport function setWeek(date, week, options) {\n  const date_ = toDate(date, options?.in);\n  const diff = getWeek(date_, options) - week;\n  date_.setDate(date_.getDate() - diff * 7);\n  return toDate(date_, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default setWeek;\n", "import { getISOWeek } from \"./getISOWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISOWeek} function options.\n */\n\n/**\n * @name setISOWeek\n * @category ISO Week Helpers\n * @summary Set the ISO week to the given date.\n *\n * @description\n * Set the ISO week to the given date, saving the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The `Date` type of the context function.\n *\n * @param date - The date to be changed\n * @param week - The ISO week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the ISO week set\n *\n * @example\n * // Set the 53rd ISO week to 7 August 2004:\n * const result = setISOWeek(new Date(2004, 7, 7), 53)\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport function setISOWeek(date, week, options) {\n  const _date = toDate(date, options?.in);\n  const diff = getISOWeek(_date, options) - week;\n  _date.setDate(_date.getDate() - diff * 7);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setISOWeek;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { addDays } from \"./addDays.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setDay} function options.\n */\n\n/**\n * @name setDay\n * @category Weekday Helpers\n * @summary Set the day of the week to the given date.\n *\n * @description\n * Set the day of the week to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param day - The day of the week of the new date\n * @param options - An object with options.\n *\n * @returns The new date with the day of the week set\n *\n * @example\n * // Set week day to Sunday, with the default weekStartsOn of Sunday:\n * const result = setDay(new Date(2014, 8, 1), 0)\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Set week day to Sunday, with a weekStartsOn of Monday:\n * const result = setDay(new Date(2014, 8, 1), 0, { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport function setDay(date, day, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const date_ = toDate(date, options?.in);\n  const currentDay = date_.getDay();\n\n  const remainder = day % 7;\n  const dayIndex = (remainder + 7) % 7;\n\n  const delta = 7 - weekStartsOn;\n  const diff =\n    day < 0 || day > 6\n      ? day - ((currentDay + delta) % 7)\n      : ((dayIndex + delta) % 7) - ((currentDay + delta) % 7);\n  return addDays(date_, diff, options);\n}\n\n// Fallback for modularized imports:\nexport default setDay;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISODay} function options.\n */\n\n/**\n * @name getISODay\n * @category Weekday Helpers\n * @summary Get the day of the ISO week of the given date.\n *\n * @description\n * Get the day of the ISO week of the given date,\n * which is 7 for Sunday, 1 for Monday etc.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The day of ISO week\n *\n * @example\n * // Which day of the ISO week is 26 February 2012?\n * const result = getISODay(new Date(2012, 1, 26))\n * //=> 7\n */\nexport function getISODay(date, options) {\n  const day = toDate(date, options?.in).getDay();\n  return day === 0 ? 7 : day;\n}\n\n// Fallback for modularized imports:\nexport default getISODay;\n", "import { addDays } from \"./addDays.js\";\nimport { getISODay } from \"./getISODay.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISODay} function options.\n */\n\n/**\n * @name setISODay\n * @category Weekday Helpers\n * @summary Set the day of the ISO week to the given date.\n *\n * @description\n * Set the day of the ISO week to the given date.\n * ISO week starts with Monday.\n * 7 is the index of Sunday, 1 is the index of Monday, etc.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param day - The day of the ISO week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the day of the ISO week set\n *\n * @example\n * // Set Sunday to 1 September 2014:\n * const result = setISODay(new Date(2014, 8, 1), 7)\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport function setISODay(date, day, options) {\n  const date_ = toDate(date, options?.in);\n  const currentDay = getISODay(date_, options);\n  const diff = day - currentDay;\n  return addDays(date_, diff, options);\n}\n\n// Fallback for modularized imports:\nexport default setISODay;\n", "import { constructFrom } from \"../../constructFrom.js\";\nimport { transpose } from \"../../transpose.js\";\n\nconst TIMEZONE_UNIT_PRIORITY = 10;\n\nexport class Setter {\n  subPriority = 0;\n\n  validate(_utcDate, _options) {\n    return true;\n  }\n}\n\nexport class ValueSetter extends Setter {\n  constructor(\n    value,\n\n    validateValue,\n\n    setValue,\n\n    priority,\n    subPriority,\n  ) {\n    super();\n    this.value = value;\n    this.validateValue = validateValue;\n    this.setValue = setValue;\n    this.priority = priority;\n    if (subPriority) {\n      this.subPriority = subPriority;\n    }\n  }\n\n  validate(date, options) {\n    return this.validateValue(date, this.value, options);\n  }\n\n  set(date, flags, options) {\n    return this.setValue(date, flags, this.value, options);\n  }\n}\n\nexport class DateTimezoneSetter extends Setter {\n  priority = TIMEZONE_UNIT_PRIORITY;\n  subPriority = -1;\n\n  constructor(context, reference) {\n    super();\n    this.context = context || ((date) => constructFrom(reference, date));\n  }\n\n  set(date, flags) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(date, transpose(date, this.context));\n  }\n}\n", "import { ValueSetter } from \"./Setter.js\";\n\nexport class Parser {\n  run(dateString, token, match, options) {\n    const result = this.parse(dateString, token, match, options);\n    if (!result) {\n      return null;\n    }\n\n    return {\n      setter: new ValueSetter(\n        result.value,\n        this.validate,\n        this.set,\n        this.priority,\n        this.subPriority,\n      ),\n      rest: result.rest,\n    };\n  }\n\n  validate(_utcDate, _value, _options) {\n    return true;\n  }\n}\n", "import { Parser } from \"../Parser.js\";\n\nexport class <PERSON><PERSON>ars<PERSON> extends Parser {\n  priority = 140;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return (\n          match.era(dateString, { width: \"abbreviated\" }) ||\n          match.era(dateString, { width: \"narrow\" })\n        );\n\n      // A, B\n      case \"GGGGG\":\n        return match.era(dateString, { width: \"narrow\" });\n      // <PERSON><PERSON>, Before Christ\n      case \"GGGG\":\n      default:\n        return (\n          match.era(dateString, { width: \"wide\" }) ||\n          match.era(dateString, { width: \"abbreviated\" }) ||\n          match.era(dateString, { width: \"narrow\" })\n        );\n    }\n  }\n\n  set(date, flags, value) {\n    flags.era = value;\n    date.setFullYear(value, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"R\", \"u\", \"t\", \"T\"];\n}\n", "export const numericPatterns = {\n  month: /^(1[0-2]|0?\\d)/, // 0 to 12\n  date: /^(3[0-1]|[0-2]?\\d)/, // 0 to 31\n  dayOfYear: /^(36[0-6]|3[0-5]\\d|[0-2]?\\d?\\d)/, // 0 to 366\n  week: /^(5[0-3]|[0-4]?\\d)/, // 0 to 53\n  hour23h: /^(2[0-3]|[0-1]?\\d)/, // 0 to 23\n  hour24h: /^(2[0-4]|[0-1]?\\d)/, // 0 to 24\n  hour11h: /^(1[0-1]|0?\\d)/, // 0 to 11\n  hour12h: /^(1[0-2]|0?\\d)/, // 0 to 12\n  minute: /^[0-5]?\\d/, // 0 to 59\n  second: /^[0-5]?\\d/, // 0 to 59\n\n  singleDigit: /^\\d/, // 0 to 9\n  twoDigits: /^\\d{1,2}/, // 0 to 99\n  threeDigits: /^\\d{1,3}/, // 0 to 999\n  fourDigits: /^\\d{1,4}/, // 0 to 9999\n\n  anyDigitsSigned: /^-?\\d+/,\n  singleDigitSigned: /^-?\\d/, // 0 to 9, -0 to -9\n  twoDigitsSigned: /^-?\\d{1,2}/, // 0 to 99, -0 to -99\n  threeDigitsSigned: /^-?\\d{1,3}/, // 0 to 999, -0 to -999\n  fourDigitsSigned: /^-?\\d{1,4}/, // 0 to 9999, -0 to -9999\n};\n\nexport const timezonePatterns = {\n  basicOptionalMinutes: /^([+-])(\\d{2})(\\d{2})?|Z/,\n  basic: /^([+-])(\\d{2})(\\d{2})|Z/,\n  basicOptionalSeconds: /^([+-])(\\d{2})(\\d{2})((\\d{2}))?|Z/,\n  extended: /^([+-])(\\d{2}):(\\d{2})|Z/,\n  extendedOptionalSeconds: /^([+-])(\\d{2}):(\\d{2})(:(\\d{2}))?|Z/,\n};\n", "import {\n  millisecondsInHour,\n  millisecondsInMinute,\n  millisecondsInSecond,\n} from \"../../constants.js\";\n\nimport { numericPatterns } from \"./constants.js\";\n\nexport function mapValue(parseFnResult, mapFn) {\n  if (!parseFnResult) {\n    return parseFnResult;\n  }\n\n  return {\n    value: mapFn(parseFnResult.value),\n    rest: parseFnResult.rest,\n  };\n}\n\nexport function parseNumericPattern(pattern, dateString) {\n  const matchResult = dateString.match(pattern);\n\n  if (!matchResult) {\n    return null;\n  }\n\n  return {\n    value: parseInt(matchResult[0], 10),\n    rest: dateString.slice(matchResult[0].length),\n  };\n}\n\nexport function parseTimezonePattern(pattern, dateString) {\n  const matchResult = dateString.match(pattern);\n\n  if (!matchResult) {\n    return null;\n  }\n\n  // Input is 'Z'\n  if (matchResult[0] === \"Z\") {\n    return {\n      value: 0,\n      rest: dateString.slice(1),\n    };\n  }\n\n  const sign = matchResult[1] === \"+\" ? 1 : -1;\n  const hours = matchResult[2] ? parseInt(matchResult[2], 10) : 0;\n  const minutes = matchResult[3] ? parseInt(matchResult[3], 10) : 0;\n  const seconds = matchResult[5] ? parseInt(matchResult[5], 10) : 0;\n\n  return {\n    value:\n      sign *\n      (hours * millisecondsInHour +\n        minutes * millisecondsInMinute +\n        seconds * millisecondsInSecond),\n    rest: dateString.slice(matchResult[0].length),\n  };\n}\n\nexport function parseAnyDigitsSigned(dateString) {\n  return parseNumericPattern(numericPatterns.anyDigitsSigned, dateString);\n}\n\nexport function parseNDigits(n, dateString) {\n  switch (n) {\n    case 1:\n      return parseNumericPattern(numericPatterns.singleDigit, dateString);\n    case 2:\n      return parseNumericPattern(numericPatterns.twoDigits, dateString);\n    case 3:\n      return parseNumericPattern(numericPatterns.threeDigits, dateString);\n    case 4:\n      return parseNumericPattern(numericPatterns.fourDigits, dateString);\n    default:\n      return parseNumericPattern(new RegExp(\"^\\\\d{1,\" + n + \"}\"), dateString);\n  }\n}\n\nexport function parseNDigitsSigned(n, dateString) {\n  switch (n) {\n    case 1:\n      return parseNumericPattern(numericPatterns.singleDigitSigned, dateString);\n    case 2:\n      return parseNumericPattern(numericPatterns.twoDigitsSigned, dateString);\n    case 3:\n      return parseNumericPattern(numericPatterns.threeDigitsSigned, dateString);\n    case 4:\n      return parseNumericPattern(numericPatterns.fourDigitsSigned, dateString);\n    default:\n      return parseNumericPattern(new RegExp(\"^-?\\\\d{1,\" + n + \"}\"), dateString);\n  }\n}\n\nexport function dayPeriodEnumToHours(dayPeriod) {\n  switch (dayPeriod) {\n    case \"morning\":\n      return 4;\n    case \"evening\":\n      return 17;\n    case \"pm\":\n    case \"noon\":\n    case \"afternoon\":\n      return 12;\n    case \"am\":\n    case \"midnight\":\n    case \"night\":\n    default:\n      return 0;\n  }\n}\n\nexport function normalizeTwoDigitYear(twoDigitYear, currentYear) {\n  const isCommonEra = currentYear > 0;\n  // Absolute number of the current year:\n  // 1 -> 1 AC\n  // 0 -> 1 BC\n  // -1 -> 2 BC\n  const absCurrentYear = isCommonEra ? currentYear : 1 - currentYear;\n\n  let result;\n  if (absCurrentYear <= 50) {\n    result = twoDigitYear || 100;\n  } else {\n    const rangeEnd = absCurrentYear + 50;\n    const rangeEndCentury = Math.trunc(rangeEnd / 100) * 100;\n    const isPreviousCentury = twoDigitYear >= rangeEnd % 100;\n    result = twoDigitYear + rangeEndCentury - (isPreviousCentury ? 100 : 0);\n  }\n\n  return isCommonEra ? result : 1 - result;\n}\n\nexport function isLeapYearIndex(year) {\n  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n\n// From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_Patterns\n// | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n// |----------|-------|----|-------|-------|-------|\n// | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n// | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n// | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n// | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n// | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\nexport class YearParser extends Parser {\n  priority = 130;\n  incompatibleTokens = [\"Y\", \"R\", \"u\", \"w\", \"I\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n\n  parse(dateString, token, match) {\n    const valueCallback = (year) => ({\n      year,\n      isTwoDigitYear: token === \"yy\",\n    });\n\n    switch (token) {\n      case \"y\":\n        return mapValue(parseNDigits(4, dateString), valueCallback);\n      case \"yo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"year\",\n          }),\n          valueCallback,\n        );\n      default:\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }\n\n  validate(_date, value) {\n    return value.isTwoDigitYear || value.year > 0;\n  }\n\n  set(date, flags, value) {\n    const currentYear = date.getFullYear();\n\n    if (value.isTwoDigitYear) {\n      const normalizedTwoDigitYear = normalizeTwoDigitYear(\n        value.year,\n        currentYear,\n      );\n      date.setFullYear(normalizedTwoDigitYear, 0, 1);\n      date.setHours(0, 0, 0, 0);\n      return date;\n    }\n\n    const year =\n      !(\"era\" in flags) || flags.era === 1 ? value.year : 1 - value.year;\n    date.setFullYear(year, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n}\n", "import { getWeekYear } from \"../../../getWeekYear.js\";\n\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n\n// Local week-numbering year\nexport class LocalWeekYearParser extends Parser {\n  priority = 130;\n\n  parse(dateString, token, match) {\n    const valueCallback = (year) => ({\n      year,\n      isTwoDigitYear: token === \"YY\",\n    });\n\n    switch (token) {\n      case \"Y\":\n        return mapValue(parseNDigits(4, dateString), valueCallback);\n      case \"Yo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"year\",\n          }),\n          valueCallback,\n        );\n      default:\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }\n\n  validate(_date, value) {\n    return value.isTwoDigitYear || value.year > 0;\n  }\n\n  set(date, flags, value, options) {\n    const currentYear = getWeekYear(date, options);\n\n    if (value.isTwoDigitYear) {\n      const normalizedTwoDigitYear = normalizeTwoDigitYear(\n        value.year,\n        currentYear,\n      );\n      date.setFullYear(\n        normalizedTwoDigitYear,\n        0,\n        options.firstWeekContainsDate,\n      );\n      date.setHours(0, 0, 0, 0);\n      return startOfWeek(date, options);\n    }\n\n    const year =\n      !(\"era\" in flags) || flags.era === 1 ? value.year : 1 - value.year;\n    date.setFullYear(year, 0, options.firstWeekContainsDate);\n    date.setHours(0, 0, 0, 0);\n    return startOfWeek(date, options);\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"Q\",\n    \"q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { startOfISOWeek } from \"../../../startOfISOWeek.js\";\nimport { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigitsSigned } from \"../utils.js\";\n\n// ISO week-numbering year\nexport class ISOWeekYearParser extends Parser {\n  priority = 130;\n\n  parse(dateString, token) {\n    if (token === \"R\") {\n      return parseNDigitsSigned(4, dateString);\n    }\n\n    return parseNDigitsSigned(token.length, dateString);\n  }\n\n  set(date, _flags, value) {\n    const firstWeekOfYear = constructFrom(date, 0);\n    firstWeekOfYear.setFullYear(value, 0, 4);\n    firstWeekOfYear.setHours(0, 0, 0, 0);\n    return startOfISOWeek(firstWeekOfYear);\n  }\n\n  incompatibleTokens = [\n    \"G\",\n    \"y\",\n    \"Y\",\n    \"u\",\n    \"Q\",\n    \"q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"d\",\n    \"D\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { parseNDigitsSigned } from \"../utils.js\";\n\nexport class ExtendedYearParser extends Parser {\n  priority = 130;\n\n  parse(dateString, token) {\n    if (token === \"u\") {\n      return parseNDigitsSigned(4, dateString);\n    }\n\n    return parseNDigitsSigned(token.length, dateString);\n  }\n\n  set(date, _flags, value) {\n    date.setFullYear(value, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"G\", \"y\", \"Y\", \"R\", \"w\", \"I\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { parseNDigits } from \"../utils.js\";\n\nexport class QuarterParser extends Parser {\n  priority = 120;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"Q\":\n      case \"QQ\": // 01, 02, 03, 04\n        return parseNDigits(token.length, dateString);\n      // 1st, 2nd, 3rd, 4th\n      case \"Qo\":\n        return match.ordinalNumber(dateString, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"QQQ\":\n        return (\n          match.quarter(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"QQQQQ\":\n        return match.quarter(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"QQQQ\":\n      default:\n        return (\n          match.quarter(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 4;\n  }\n\n  set(date, _flags, value) {\n    date.setMonth((value - 1) * 3, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { parseNDigits } from \"../utils.js\";\n\nexport class StandAloneQuarterParser extends Parser {\n  priority = 120;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"q\":\n      case \"qq\": // 01, 02, 03, 04\n        return parseNDigits(token.length, dateString);\n      // 1st, 2nd, 3rd, 4th\n      case \"qo\":\n        return match.ordinalNumber(dateString, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"qqq\":\n        return (\n          match.quarter(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"narrow\",\n            context: \"standalone\",\n          })\n        );\n\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"qqqqq\":\n        return match.quarter(dateString, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"qqqq\":\n      default:\n        return (\n          match.quarter(dateString, {\n            width: \"wide\",\n            context: \"standalone\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"narrow\",\n            context: \"standalone\",\n          })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 4;\n  }\n\n  set(date, _flags, value) {\n    date.setMonth((value - 1) * 3, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class MonthParser extends Parser {\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"L\",\n    \"w\",\n    \"I\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n\n  priority = 110;\n\n  parse(dateString, token, match) {\n    const valueCallback = (value) => value - 1;\n\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"M\":\n        return mapValue(\n          parseNumericPattern(numericPatterns.month, dateString),\n          valueCallback,\n        );\n      // 01, 02, ..., 12\n      case \"MM\":\n        return mapValue(parseNDigits(2, dateString), valueCallback);\n      // 1st, 2nd, ..., 12th\n      case \"Mo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"month\",\n          }),\n          valueCallback,\n        );\n      // Jan, Feb, ..., Dec\n      case \"MMM\":\n        return (\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // J, F, ..., D\n      case \"MMMMM\":\n        return match.month(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // January, February, ..., December\n      case \"MMMM\":\n      default:\n        return (\n          match.month(dateString, { width: \"wide\", context: \"formatting\" }) ||\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 11;\n  }\n\n  set(date, _flags, value) {\n    date.setMonth(value, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class StandAloneMonthParser extends Parser {\n  priority = 110;\n\n  parse(dateString, token, match) {\n    const valueCallback = (value) => value - 1;\n\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return mapValue(\n          parseNumericPattern(numericPatterns.month, dateString),\n          valueCallback,\n        );\n      // 01, 02, ..., 12\n      case \"LL\":\n        return mapValue(parseNDigits(2, dateString), valueCallback);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"month\",\n          }),\n          valueCallback,\n        );\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return (\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n\n      // J, F, ..., D\n      case \"LLLLL\":\n        return match.month(dateString, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return (\n          match.month(dateString, { width: \"wide\", context: \"standalone\" }) ||\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 11;\n  }\n\n  set(date, _flags, value) {\n    date.setMonth(value, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"w\",\n    \"I\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { setWeek } from \"../../../setWeek.js\";\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\n// Local week of year\nexport class LocalWeekParser extends Parser {\n  priority = 100;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"w\":\n        return parseNumericPattern(numericPatterns.week, dateString);\n      case \"wo\":\n        return match.ordinalNumber(dateString, { unit: \"week\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 53;\n  }\n\n  set(date, _flags, value, options) {\n    return startOfWeek(setWeek(date, value, options), options);\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { setISOWeek } from \"../../../setISOWeek.js\";\nimport { startOfISOWeek } from \"../../../startOfISOWeek.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\n// ISO week of year\nexport class ISOWeekParser extends Parser {\n  priority = 100;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"I\":\n        return parseNumericPattern(numericPatterns.week, dateString);\n      case \"Io\":\n        return match.ordinalNumber(dateString, { unit: \"week\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 53;\n  }\n\n  set(date, _flags, value) {\n    return startOfISOWeek(setISOWeek(date, value));\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"Y\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"d\",\n    \"D\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport {\n  isLeapYearIndex,\n  parseNDigits,\n  parseNumericPattern,\n} from \"../utils.js\";\n\nconst DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst DAYS_IN_MONTH_LEAP_YEAR = [\n  31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31,\n];\n\n// Day of the month\nexport class DateParser extends Parser {\n  priority = 90;\n  subPriority = 1;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"d\":\n        return parseNumericPattern(numericPatterns.date, dateString);\n      case \"do\":\n        return match.ordinalNumber(dateString, { unit: \"date\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(date, value) {\n    const year = date.getFullYear();\n    const isLeapYear = isLeapYearIndex(year);\n    const month = date.getMonth();\n    if (isLeapYear) {\n      return value >= 1 && value <= DAYS_IN_MONTH_LEAP_YEAR[month];\n    } else {\n      return value >= 1 && value <= DAYS_IN_MONTH[month];\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setDate(value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"w\",\n    \"I\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport {\n  isLeapYearIndex,\n  parseNDigits,\n  parseNumericPattern,\n} from \"../utils.js\";\n\nexport class DayOfYearParser extends Parser {\n  priority = 90;\n\n  subpriority = 1;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"D\":\n      case \"DD\":\n        return parseNumericPattern(numericPatterns.dayOfYear, dateString);\n      case \"Do\":\n        return match.ordinalNumber(dateString, { unit: \"date\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(date, value) {\n    const year = date.getFullYear();\n    const isLeapYear = isLeapYearIndex(year);\n    if (isLeapYear) {\n      return value >= 1 && value <= 366;\n    } else {\n      return value >= 1 && value <= 365;\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setMonth(0, value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"I\",\n    \"d\",\n    \"E\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { setDay } from \"../../../setDay.js\";\nimport { Parser } from \"../Parser.js\";\n\n// Day of week\nexport class Day<PERSON><PERSON><PERSON> extends Parser {\n  priority = 90;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      // Tue\n      case \"E\":\n      case \"EE\":\n      case \"EEE\":\n        return (\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // T\n      case \"EEEEE\":\n        return match.day(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"EEEEEE\":\n        return (\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // Tuesday\n      case \"EEEE\":\n      default:\n        return (\n          match.day(dateString, { width: \"wide\", context: \"formatting\" }) ||\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 6;\n  }\n\n  set(date, _flags, value, options) {\n    date = setDay(date, value, options);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"D\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}\n", "import { setDay } from \"../../../setDay.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// Local day of week\nexport class LocalDayParser extends Parser {\n  priority = 90;\n  parse(dateString, token, match, options) {\n    const valueCallback = (value) => {\n      // We want here floor instead of trunc, so we get -7 for value 0 instead of 0\n      const wholeWeekDays = Math.floor((value - 1) / 7) * 7;\n      return ((value + options.weekStartsOn + 6) % 7) + wholeWeekDays;\n    };\n\n    switch (token) {\n      // 3\n      case \"e\":\n      case \"ee\": // 03\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      // 3rd\n      case \"eo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"day\",\n          }),\n          valueCallback,\n        );\n      // Tue\n      case \"eee\":\n        return (\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // T\n      case \"eeeee\":\n        return match.day(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"eeeeee\":\n        return (\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // Tuesday\n      case \"eeee\":\n      default:\n        return (\n          match.day(dateString, { width: \"wide\", context: \"formatting\" }) ||\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 6;\n  }\n\n  set(date, _flags, value, options) {\n    date = setDay(date, value, options);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"E\",\n    \"i\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { setDay } from \"../../../setDay.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// Stand-alone local day of week\nexport class StandAloneLocalDayParser extends Parser {\n  priority = 90;\n\n  parse(dateString, token, match, options) {\n    const valueCallback = (value) => {\n      // We want here floor instead of trunc, so we get -7 for value 0 instead of 0\n      const wholeWeekDays = Math.floor((value - 1) / 7) * 7;\n      return ((value + options.weekStartsOn + 6) % 7) + wholeWeekDays;\n    };\n\n    switch (token) {\n      // 3\n      case \"c\":\n      case \"cc\": // 03\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      // 3rd\n      case \"co\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"day\",\n          }),\n          valueCallback,\n        );\n      // Tue\n      case \"ccc\":\n        return (\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"standalone\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n\n      // T\n      case \"ccccc\":\n        return match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // Tu\n      case \"cccccc\":\n        return (\n          match.day(dateString, { width: \"short\", context: \"standalone\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n\n      // Tuesday\n      case \"cccc\":\n      default:\n        return (\n          match.day(dateString, { width: \"wide\", context: \"standalone\" }) ||\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"standalone\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 6;\n  }\n\n  set(date, _flags, value, options) {\n    date = setDay(date, value, options);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"E\",\n    \"i\",\n    \"e\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { setISODay } from \"../../../setISODay.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// ISO day of week\nexport class ISODayParser extends Parser {\n  priority = 90;\n\n  parse(dateString, token, match) {\n    const valueCallback = (value) => {\n      if (value === 0) {\n        return 7;\n      }\n      return value;\n    };\n\n    switch (token) {\n      // 2\n      case \"i\":\n      case \"ii\": // 02\n        return parseNDigits(token.length, dateString);\n      // 2nd\n      case \"io\":\n        return match.ordinalNumber(dateString, { unit: \"day\" });\n      // Tue\n      case \"iii\":\n        return mapValue(\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n            match.day(dateString, {\n              width: \"short\",\n              context: \"formatting\",\n            }) ||\n            match.day(dateString, {\n              width: \"narrow\",\n              context: \"formatting\",\n            }),\n          valueCallback,\n        );\n      // T\n      case \"iiiii\":\n        return mapValue(\n          match.day(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          }),\n          valueCallback,\n        );\n      // Tu\n      case \"iiiiii\":\n        return mapValue(\n          match.day(dateString, {\n            width: \"short\",\n            context: \"formatting\",\n          }) ||\n            match.day(dateString, {\n              width: \"narrow\",\n              context: \"formatting\",\n            }),\n          valueCallback,\n        );\n      // Tuesday\n      case \"iiii\":\n      default:\n        return mapValue(\n          match.day(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n            match.day(dateString, {\n              width: \"abbreviated\",\n              context: \"formatting\",\n            }) ||\n            match.day(dateString, {\n              width: \"short\",\n              context: \"formatting\",\n            }) ||\n            match.day(dateString, {\n              width: \"narrow\",\n              context: \"formatting\",\n            }),\n          valueCallback,\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 7;\n  }\n\n  set(date, _flags, value) {\n    date = setISODay(date, value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"Y\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"d\",\n    \"D\",\n    \"E\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { dayPeriodEnumToHours } from \"../utils.js\";\n\nexport class AMPMParser extends Parser {\n  priority = 80;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n      case \"aaa\":\n        return (\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n\n      case \"aaaaa\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"aaaa\":\n      default:\n        return (\n          match.dayPeriod(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"b\", \"B\", \"H\", \"k\", \"t\", \"T\"];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { dayPeriodEnumToHours } from \"../utils.js\";\n\nexport class AMPMMidnightParser extends Parser {\n  priority = 80;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n      case \"bbb\":\n        return (\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n\n      case \"bbbbb\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"bbbb\":\n      default:\n        return (\n          match.dayPeriod(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"B\", \"H\", \"k\", \"t\", \"T\"];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { dayPeriodEnumToHours } from \"../utils.js\";\n\n// in the morning, in the afternoon, in the evening, at night\nexport class DayPeriodParser extends Parser {\n  priority = 80;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"B\":\n      case \"BB\":\n      case \"BBB\":\n        return (\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n\n      case \"BBBBB\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"BBBB\":\n      default:\n        return (\n          match.dayPeriod(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"b\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour1to12Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"h\":\n        return parseNumericPattern(numericPatterns.hour12h, dateString);\n      case \"ho\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 12;\n  }\n\n  set(date, _flags, value) {\n    const isPM = date.getHours() >= 12;\n    if (isPM && value < 12) {\n      date.setHours(value + 12, 0, 0, 0);\n    } else if (!isPM && value === 12) {\n      date.setHours(0, 0, 0, 0);\n    } else {\n      date.setHours(value, 0, 0, 0);\n    }\n    return date;\n  }\n\n  incompatibleTokens = [\"H\", \"K\", \"k\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour0to23Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"H\":\n        return parseNumericPattern(numericPatterns.hour23h, dateString);\n      case \"Ho\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 23;\n  }\n\n  set(date, _flags, value) {\n    date.setHours(value, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"b\", \"h\", \"K\", \"k\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour0To11Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"K\":\n        return parseNumericPattern(numericPatterns.hour11h, dateString);\n      case \"Ko\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 11;\n  }\n\n  set(date, _flags, value) {\n    const isPM = date.getHours() >= 12;\n    if (isPM && value < 12) {\n      date.setHours(value + 12, 0, 0, 0);\n    } else {\n      date.setHours(value, 0, 0, 0);\n    }\n    return date;\n  }\n\n  incompatibleTokens = [\"h\", \"H\", \"k\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour1To24Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"k\":\n        return parseNumericPattern(numericPatterns.hour24h, dateString);\n      case \"ko\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 24;\n  }\n\n  set(date, _flags, value) {\n    const hours = value <= 24 ? value % 24 : value;\n    date.setHours(hours, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"b\", \"h\", \"H\", \"K\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class MinuteParser extends Parser {\n  priority = 60;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"m\":\n        return parseNumericPattern(numericPatterns.minute, dateString);\n      case \"mo\":\n        return match.ordinalNumber(dateString, { unit: \"minute\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 59;\n  }\n\n  set(date, _flags, value) {\n    date.setMinutes(value, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class SecondParser extends Parser {\n  priority = 50;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"s\":\n        return parseNumericPattern(numericPatterns.second, dateString);\n      case \"so\":\n        return match.ordinalNumber(dateString, { unit: \"second\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 59;\n  }\n\n  set(date, _flags, value) {\n    date.setSeconds(value, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"t\", \"T\"];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\nexport class FractionOfSecondParser extends Parser {\n  priority = 30;\n\n  parse(dateString, token) {\n    const valueCallback = (value) =>\n      Math.trunc(value * Math.pow(10, -token.length + 3));\n    return mapValue(parseNDigits(token.length, dateString), valueCallback);\n  }\n\n  set(date, _flags, value) {\n    date.setMilliseconds(value);\n    return date;\n  }\n\n  incompatibleTokens = [\"t\", \"T\"];\n}\n", "import { constructFrom } from \"../../../constructFrom.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"../../../_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { timezonePatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseTimezonePattern } from \"../utils.js\";\n\n// Timezone (ISO-8601. +00:00 is `'Z'`)\nexport class ISOTimezoneWithZParser extends Parser {\n  priority = 10;\n\n  parse(dateString, token) {\n    switch (token) {\n      case \"X\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalMinutes,\n          dateString,\n        );\n      case \"XX\":\n        return parseTimezonePattern(timezonePatterns.basic, dateString);\n      case \"XXXX\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalSeconds,\n          dateString,\n        );\n      case \"XXXXX\":\n        return parseTimezonePattern(\n          timezonePatterns.extendedOptionalSeconds,\n          dateString,\n        );\n      case \"XXX\":\n      default:\n        return parseTimezonePattern(timezonePatterns.extended, dateString);\n    }\n  }\n\n  set(date, flags, value) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(\n      date,\n      date.getTime() - getTimezoneOffsetInMilliseconds(date) - value,\n    );\n  }\n\n  incompatibleTokens = [\"t\", \"T\", \"x\"];\n}\n", "import { constructFrom } from \"../../../constructFrom.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"../../../_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { timezonePatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseTimezonePattern } from \"../utils.js\";\n\n// Timezone (ISO-8601)\nexport class ISOTimezoneParser extends Parser {\n  priority = 10;\n\n  parse(dateString, token) {\n    switch (token) {\n      case \"x\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalMinutes,\n          dateString,\n        );\n      case \"xx\":\n        return parseTimezonePattern(timezonePatterns.basic, dateString);\n      case \"xxxx\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalSeconds,\n          dateString,\n        );\n      case \"xxxxx\":\n        return parseTimezonePattern(\n          timezonePatterns.extendedOptionalSeconds,\n          dateString,\n        );\n      case \"xxx\":\n      default:\n        return parseTimezonePattern(timezonePatterns.extended, dateString);\n    }\n  }\n\n  set(date, flags, value) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(\n      date,\n      date.getTime() - getTimezoneOffsetInMilliseconds(date) - value,\n    );\n  }\n\n  incompatibleTokens = [\"t\", \"T\", \"X\"];\n}\n", "import { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseAnyDigitsSigned } from \"../utils.js\";\n\nexport class TimestampSecondsParser extends Parser {\n  priority = 40;\n\n  parse(dateString) {\n    return parseAnyDigitsSigned(dateString);\n  }\n\n  set(date, _flags, value) {\n    return [constructFrom(date, value * 1000), { timestampIsSet: true }];\n  }\n\n  incompatibleTokens = \"*\";\n}\n", "import { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseAnyDigitsSigned } from \"../utils.js\";\n\nexport class TimestampMillisecondsParser extends Parser {\n  priority = 20;\n\n  parse(dateString) {\n    return parseAnyDigitsSigned(dateString);\n  }\n\n  set(date, _flags, value) {\n    return [constructFrom(date, value), { timestampIsSet: true }];\n  }\n\n  incompatibleTokens = \"*\";\n}\n", "import { EraParser } from \"./parsers/EraParser.js\";\nimport { YearParser } from \"./parsers/YearParser.js\";\nimport { LocalWeekYearParser } from \"./parsers/LocalWeekYearParser.js\";\nimport { ISOWeekYearParser } from \"./parsers/ISOWeekYearParser.js\";\nimport { ExtendedYearParser } from \"./parsers/ExtendedYearParser.js\";\nimport { QuarterParser } from \"./parsers/QuarterParser.js\";\nimport { StandAloneQuarterParser } from \"./parsers/StandAloneQuarterParser.js\";\nimport { MonthParser } from \"./parsers/MonthParser.js\";\nimport { StandAloneMonthParser } from \"./parsers/StandAloneMonthParser.js\";\nimport { LocalWeekParser } from \"./parsers/LocalWeekParser.js\";\nimport { ISOWeekParser } from \"./parsers/ISOWeekParser.js\";\nimport { DateParser } from \"./parsers/DateParser.js\";\nimport { DayOfYearParser } from \"./parsers/DayOfYearParser.js\";\nimport { DayParser } from \"./parsers/DayParser.js\";\nimport { LocalDayParser } from \"./parsers/LocalDayParser.js\";\nimport { StandAloneLocalDayParser } from \"./parsers/StandAloneLocalDayParser.js\";\nimport { ISODayParser } from \"./parsers/ISODayParser.js\";\nimport { AMPMParser } from \"./parsers/AMPMParser.js\";\nimport { AMPMMidnightParser } from \"./parsers/AMPMMidnightParser.js\";\nimport { DayPeriodParser } from \"./parsers/DayPeriodParser.js\";\nimport { Hour1to12Parser } from \"./parsers/Hour1to12Parser.js\";\nimport { Hour0to23Parser } from \"./parsers/Hour0to23Parser.js\";\nimport { Hour0To11Parser } from \"./parsers/Hour0To11Parser.js\";\nimport { Hour1To24Parser } from \"./parsers/Hour1To24Parser.js\";\nimport { MinuteParser } from \"./parsers/MinuteParser.js\";\nimport { SecondParser } from \"./parsers/SecondParser.js\";\nimport { FractionOfSecondParser } from \"./parsers/FractionOfSecondParser.js\";\nimport { ISOTimezoneWithZParser } from \"./parsers/ISOTimezoneWithZParser.js\";\nimport { ISOTimezoneParser } from \"./parsers/ISOTimezoneParser.js\";\nimport { TimestampSecondsParser } from \"./parsers/TimestampSecondsParser.js\";\nimport { TimestampMillisecondsParser } from \"./parsers/TimestampMillisecondsParser.js\";\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O* | Timezone (GMT)                 |\n * |  p  |                                |  P  |                                |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z* | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `parse` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n */\nexport const parsers = {\n  G: new EraParser(),\n  y: new YearParser(),\n  Y: new LocalWeekYearParser(),\n  R: new ISOWeekYearParser(),\n  u: new ExtendedYearParser(),\n  Q: new QuarterParser(),\n  q: new StandAloneQuarterParser(),\n  M: new MonthParser(),\n  L: new StandAloneMonthParser(),\n  w: new LocalWeekParser(),\n  I: new ISOWeekParser(),\n  d: new DateParser(),\n  D: new DayOfYearParser(),\n  E: new DayParser(),\n  e: new LocalDayParser(),\n  c: new StandAloneLocalDayParser(),\n  i: new ISODayParser(),\n  a: new AMPMParser(),\n  b: new AMPMMidnightParser(),\n  B: new DayPeriodParser(),\n  h: new Hour1to12Parser(),\n  H: new Hour0to23Parser(),\n  K: new Hour0To11Parser(),\n  k: new Hour1To24Parser(),\n  m: new MinuteParser(),\n  s: new SecondParser(),\n  S: new FractionOfSecondParser(),\n  X: new ISOTimezoneWithZParser(),\n  x: new ISOTimezoneParser(),\n  t: new TimestampSecondsParser(),\n  T: new TimestampMillisecondsParser(),\n};\n", "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { longFormatters } from \"./_lib/format/longFormatters.js\";\nimport {\n  isProtectedDayOfYearToken,\n  isProtectedWeekYearToken,\n  warnOrThrowProtectedError,\n} from \"./_lib/protectedTokens.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { getDefaultOptions } from \"./getDefaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\nimport { DateTimezoneSetter } from \"./parse/_lib/Setter.js\";\nimport { parsers } from \"./parse/_lib/parsers.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { longFormatters, parsers };\n\n/**\n * The {@link parse} function options.\n */\n\n// This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp =\n  /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nconst longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\n\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\n\nconst notWhitespaceRegExp = /\\S/;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @name parse\n * @category Common Helpers\n * @summary Parse the date.\n *\n * @description\n * Return the date parsed from string using the given format string.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters in the format string wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the format string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 5 below the table).\n *\n * Not all tokens are compatible. Combinations that don't make sense or could lead to bugs are prohibited\n * and will throw `RangeError`. For example usage of 24-hour format token with AM/PM token will throw an exception:\n *\n * ```javascript\n * parse('23 AM', 'HH a', new Date())\n * //=> RangeError: The format string mustn't contain `HH` and `a` at the same time\n * ```\n *\n * See the compatibility table: https://docs.google.com/spreadsheets/d/e/2PACX-1vQOPU3xUhplll6dyoMmVUXHKl_8CRDs6_ueLmex3SoqwhuolkuN3O05l4rqx5h1dKX8eb46Ul-CCSrq/pubhtml?gid=0&single=true\n *\n * Accepted format string patterns:\n * | Unit                            |Prior| Pattern | Result examples                   | Notes |\n * |---------------------------------|-----|---------|-----------------------------------|-------|\n * | Era                             | 140 | G..GGG  | AD, BC                            |       |\n * |                                 |     | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 |     | GGGGG   | A, B                              |       |\n * | Calendar year                   | 130 | y       | 44, 1, 1900, 2017, 9999           | 4     |\n * |                                 |     | yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | yy      | 44, 01, 00, 17                    | 4     |\n * |                                 |     | yyy     | 044, 001, 123, 999                | 4     |\n * |                                 |     | yyyy    | 0044, 0001, 1900, 2017            | 4     |\n * |                                 |     | yyyyy   | ...                               | 2,4   |\n * | Local week-numbering year       | 130 | Y       | 44, 1, 1900, 2017, 9000           | 4     |\n * |                                 |     | Yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | YY      | 44, 01, 00, 17                    | 4,6   |\n * |                                 |     | YYY     | 044, 001, 123, 999                | 4     |\n * |                                 |     | YYYY    | 0044, 0001, 1900, 2017            | 4,6   |\n * |                                 |     | YYYYY   | ...                               | 2,4   |\n * | ISO week-numbering year         | 130 | R       | -43, 1, 1900, 2017, 9999, -9999   | 4,5   |\n * |                                 |     | RR      | -43, 01, 00, 17                   | 4,5   |\n * |                                 |     | RRR     | -043, 001, 123, 999, -999         | 4,5   |\n * |                                 |     | RRRR    | -0043, 0001, 2017, 9999, -9999    | 4,5   |\n * |                                 |     | RRRRR   | ...                               | 2,4,5 |\n * | Extended year                   | 130 | u       | -43, 1, 1900, 2017, 9999, -999    | 4     |\n * |                                 |     | uu      | -43, 01, 99, -99                  | 4     |\n * |                                 |     | uuu     | -043, 001, 123, 999, -999         | 4     |\n * |                                 |     | uuuu    | -0043, 0001, 2017, 9999, -9999    | 4     |\n * |                                 |     | uuuuu   | ...                               | 2,4   |\n * | Quarter (formatting)            | 120 | Q       | 1, 2, 3, 4                        |       |\n * |                                 |     | Qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | QQ      | 01, 02, 03, 04                    |       |\n * |                                 |     | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | 120 | q       | 1, 2, 3, 4                        |       |\n * |                                 |     | qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | qq      | 01, 02, 03, 04                    |       |\n * |                                 |     | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | qqqqq   | 1, 2, 3, 4                        | 3     |\n * | Month (formatting)              | 110 | M       | 1, 2, ..., 12                     |       |\n * |                                 |     | Mo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | MM      | 01, 02, ..., 12                   |       |\n * |                                 |     | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | MMMM    | January, February, ..., December  | 2     |\n * |                                 |     | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | 110 | L       | 1, 2, ..., 12                     |       |\n * |                                 |     | Lo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | LL      | 01, 02, ..., 12                   |       |\n * |                                 |     | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | LLLL    | January, February, ..., December  | 2     |\n * |                                 |     | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | 100 | w       | 1, 2, ..., 53                     |       |\n * |                                 |     | wo      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | 100 | I       | 1, 2, ..., 53                     | 5     |\n * |                                 |     | Io      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | II      | 01, 02, ..., 53                   | 5     |\n * | Day of month                    |  90 | d       | 1, 2, ..., 31                     |       |\n * |                                 |     | do      | 1st, 2nd, ..., 31st               | 5     |\n * |                                 |     | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     |  90 | D       | 1, 2, ..., 365, 366               | 7     |\n * |                                 |     | Do      | 1st, 2nd, ..., 365th, 366th       | 5     |\n * |                                 |     | DD      | 01, 02, ..., 365, 366             | 7     |\n * |                                 |     | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 |     | DDDD    | ...                               | 2     |\n * | Day of week (formatting)        |  90 | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 |     | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    |  90 | i       | 1, 2, 3, ..., 7                   | 5     |\n * |                                 |     | io      | 1st, 2nd, ..., 7th                | 5     |\n * |                                 |     | ii      | 01, 02, ..., 07                   | 5     |\n * |                                 |     | iii     | Mon, Tue, Wed, ..., Sun           | 5     |\n * |                                 |     | iiii    | Monday, Tuesday, ..., Sunday      | 2,5   |\n * |                                 |     | iiiii   | M, T, W, T, F, S, S               | 5     |\n * |                                 |     | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 5     |\n * | Local day of week (formatting)  |  90 | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | eo      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | ee      | 02, 03, ..., 01                   |       |\n * |                                 |     | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 |     | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) |  90 | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | co      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | cc      | 02, 03, ..., 01                   |       |\n * |                                 |     | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 |     | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          |  80 | a..aaa  | AM, PM                            |       |\n * |                                 |     | aaaa    | a.m., p.m.                        | 2     |\n * |                                 |     | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          |  80 | b..bbb  | AM, PM, noon, midnight            |       |\n * |                                 |     | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 |     | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             |  80 | B..BBB  | at night, in the morning, ...     |       |\n * |                                 |     | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 |     | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     |  70 | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 |     | ho      | 1st, 2nd, ..., 11th, 12th         | 5     |\n * |                                 |     | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     |  70 | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 |     | Ho      | 0th, 1st, 2nd, ..., 23rd          | 5     |\n * |                                 |     | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     |  70 | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 |     | Ko      | 1st, 2nd, ..., 11th, 0th          | 5     |\n * |                                 |     | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     |  70 | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 |     | ko      | 24th, 1st, 2nd, ..., 23rd         | 5     |\n * |                                 |     | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          |  60 | m       | 0, 1, ..., 59                     |       |\n * |                                 |     | mo      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | mm      | 00, 01, ..., 59                   |       |\n * | Second                          |  50 | s       | 0, 1, ..., 59                     |       |\n * |                                 |     | so      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | ss      | 00, 01, ..., 59                   |       |\n * | Seconds timestamp               |  40 | t       | 512969520                         |       |\n * |                                 |     | tt      | ...                               | 2     |\n * | Fraction of second              |  30 | S       | 0, 1, ..., 9                      |       |\n * |                                 |     | SS      | 00, 01, ..., 99                   |       |\n * |                                 |     | SSS     | 000, 001, ..., 999                |       |\n * |                                 |     | SSSS    | ...                               | 2     |\n * | Milliseconds timestamp          |  20 | T       | 512969520900                      |       |\n * |                                 |     | TT      | ...                               | 2     |\n * | Timezone (ISO-8601 w/ Z)        |  10 | X       | -08, +0530, Z                     |       |\n * |                                 |     | XX      | -0800, +0530, Z                   |       |\n * |                                 |     | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 |     | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 |     | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       |  10 | x       | -08, +0530, +00                   |       |\n * |                                 |     | xx      | -0800, +0530, +0000               |       |\n * |                                 |     | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 |     | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 |     | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Long localized date             |  NA | P       | 05/29/1453                        | 5,8   |\n * |                                 |     | PP      | May 29, 1453                      |       |\n * |                                 |     | PPP     | May 29th, 1453                    |       |\n * |                                 |     | PPPP    | Sunday, May 29th, 1453            | 2,5,8 |\n * | Long localized time             |  NA | p       | 12:00 AM                          | 5,8   |\n * |                                 |     | pp      | 12:00:00 AM                       |       |\n * | Combination of date and time    |  NA | Pp      | 05/29/1453, 12:00 AM              |       |\n * |                                 |     | PPpp    | May 29, 1453, 12:00:00 AM         |       |\n * |                                 |     | PPPpp   | May 29th, 1453 at ...             |       |\n * |                                 |     | PPPPpp  | Sunday, May 29th, 1453 at ...     | 2,5,8 |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular.\n *    In `format` function, they will produce different result:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n *    `parse` will try to match both formatting and stand-alone units interchangeably.\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table:\n *    - for numerical units (`yyyyyyyy`) `parse` will try to match a number\n *      as wide as the sequence\n *    - for text units (`MMMMMMMM`) `parse` will try to match the widest variation of the unit.\n *      These variations are marked with \"2\" in the last column of the table.\n *\n * 3. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 4. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` will try to guess the century of two digit year by proximity with `referenceDate`:\n *\n *    `parse('50', 'yy', new Date(2018, 0, 1)) //=> Sat Jan 01 2050 00:00:00`\n *\n *    `parse('75', 'yy', new Date(2018, 0, 1)) //=> Wed Jan 01 1975 00:00:00`\n *\n *    while `uu` will just assign the year as is:\n *\n *    `parse('50', 'uu', new Date(2018, 0, 1)) //=> Sat Jan 01 0050 00:00:00`\n *\n *    `parse('75', 'uu', new Date(2018, 0, 1)) //=> Tue Jan 01 0075 00:00:00`\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [setISOWeekYear](https://date-fns.org/docs/setISOWeekYear)\n *    and [setWeekYear](https://date-fns.org/docs/setWeekYear)).\n *\n * 5. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 6. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 7. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 8. `P+` tokens do not have a defined priority since they are merely aliases to other tokens based\n *    on the given locale.\n *\n *    using `en-US` locale: `P` => `MM/dd/yyyy`\n *    using `en-US` locale: `p` => `hh:mm a`\n *    using `pt-BR` locale: `P` => `dd/MM/yyyy`\n *    using `pt-BR` locale: `p` => `HH:mm`\n *\n * Values will be assigned to the date in the descending order of its unit's priority.\n * Units of an equal priority overwrite each other in the order of appearance.\n *\n * If no values of higher priority are parsed (e.g. when parsing string 'January 1st' without a year),\n * the values will be taken from 3rd argument `referenceDate` which works as a context of parsing.\n *\n * `referenceDate` must be passed for correct work of the function.\n * If you're not sure which `referenceDate` to supply, create a new instance of Date:\n * `parse('02/11/2014', 'MM/dd/yyyy', new Date())`\n * In this case parsing will be done in the context of the current date.\n * If `referenceDate` is `Invalid Date` or a value not convertible to valid `Date`,\n * then `Invalid Date` will be returned.\n *\n * The result may vary by locale.\n *\n * If `formatString` matches with `dateString` but does not provides tokens, `referenceDate` will be returned.\n *\n * If parsing failed, `Invalid Date` will be returned.\n * Invalid Date is a Date, whose time value is NaN.\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dateStr - The string to parse\n * @param formatStr - The string of tokens\n * @param referenceDate - defines values missing from the parsed dateString\n * @param options - An object with options.\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * @returns The parsed date\n *\n * @throws `options.locale` must contain `match` property\n * @throws use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * // Parse 11 February 2014 from middle-endian format:\n * var result = parse('02/11/2014', 'MM/dd/yyyy', new Date())\n * //=> Tue Feb 11 2014 00:00:00\n *\n * @example\n * // Parse 28th of February in Esperanto locale in the context of 2010 year:\n * import eo from 'date-fns/locale/eo'\n * var result = parse('28-a de februaro', \"do 'de' MMMM\", new Date(2010, 0, 1), {\n *   locale: eo\n * })\n * //=> Sun Feb 28 2010 00:00:00\n */\nexport function parse(dateStr, formatStr, referenceDate, options) {\n  const invalidDate = () => constructFrom(options?.in || referenceDate, NaN);\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  if (!formatStr)\n    return dateStr ? invalidDate() : toDate(referenceDate, options?.in);\n\n  const subFnOptions = {\n    firstWeekContainsDate,\n    weekStartsOn,\n    locale,\n  };\n\n  // If timezone isn't specified, it will try to use the context or\n  // the reference date and fallback to the system time zone.\n  const setters = [new DateTimezoneSetter(options?.in, referenceDate)];\n\n  const tokens = formatStr\n    .match(longFormattingTokensRegExp)\n    .map((substring) => {\n      const firstCharacter = substring[0];\n      if (firstCharacter in longFormatters) {\n        const longFormatter = longFormatters[firstCharacter];\n        return longFormatter(substring, locale.formatLong);\n      }\n      return substring;\n    })\n    .join(\"\")\n    .match(formattingTokensRegExp);\n\n  const usedTokens = [];\n\n  for (let token of tokens) {\n    if (\n      !options?.useAdditionalWeekYearTokens &&\n      isProtectedWeekYearToken(token)\n    ) {\n      warnOrThrowProtectedError(token, formatStr, dateStr);\n    }\n    if (\n      !options?.useAdditionalDayOfYearTokens &&\n      isProtectedDayOfYearToken(token)\n    ) {\n      warnOrThrowProtectedError(token, formatStr, dateStr);\n    }\n\n    const firstCharacter = token[0];\n    const parser = parsers[firstCharacter];\n    if (parser) {\n      const { incompatibleTokens } = parser;\n      if (Array.isArray(incompatibleTokens)) {\n        const incompatibleToken = usedTokens.find(\n          (usedToken) =>\n            incompatibleTokens.includes(usedToken.token) ||\n            usedToken.token === firstCharacter,\n        );\n        if (incompatibleToken) {\n          throw new RangeError(\n            `The format string mustn't contain \\`${incompatibleToken.fullToken}\\` and \\`${token}\\` at the same time`,\n          );\n        }\n      } else if (parser.incompatibleTokens === \"*\" && usedTokens.length > 0) {\n        throw new RangeError(\n          `The format string mustn't contain \\`${token}\\` and any other token at the same time`,\n        );\n      }\n\n      usedTokens.push({ token: firstCharacter, fullToken: token });\n\n      const parseResult = parser.run(\n        dateStr,\n        token,\n        locale.match,\n        subFnOptions,\n      );\n\n      if (!parseResult) {\n        return invalidDate();\n      }\n\n      setters.push(parseResult.setter);\n\n      dateStr = parseResult.rest;\n    } else {\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\n          \"Format string contains an unescaped latin alphabet character `\" +\n            firstCharacter +\n            \"`\",\n        );\n      }\n\n      // Replace two single quote characters with one single quote character\n      if (token === \"''\") {\n        token = \"'\";\n      } else if (firstCharacter === \"'\") {\n        token = cleanEscapedString(token);\n      }\n\n      // Cut token from string, or, if string doesn't match the token, return Invalid Date\n      if (dateStr.indexOf(token) === 0) {\n        dateStr = dateStr.slice(token.length);\n      } else {\n        return invalidDate();\n      }\n    }\n  }\n\n  // Check if the remaining input contains something other than whitespace\n  if (dateStr.length > 0 && notWhitespaceRegExp.test(dateStr)) {\n    return invalidDate();\n  }\n\n  const uniquePrioritySetters = setters\n    .map((setter) => setter.priority)\n    .sort((a, b) => b - a)\n    .filter((priority, index, array) => array.indexOf(priority) === index)\n    .map((priority) =>\n      setters\n        .filter((setter) => setter.priority === priority)\n        .sort((a, b) => b.subPriority - a.subPriority),\n    )\n    .map((setterArray) => setterArray[0]);\n\n  let date = toDate(referenceDate, options?.in);\n\n  if (isNaN(+date)) return invalidDate();\n\n  const flags = {};\n  for (const setter of uniquePrioritySetters) {\n    if (!setter.validate(date, subFnOptions)) {\n      return invalidDate();\n    }\n\n    const result = setter.set(date, flags, subFnOptions);\n    // Result is tuple (date, flags)\n    if (Array.isArray(result)) {\n      date = result[0];\n      Object.assign(flags, result[1]);\n      // Result is date\n    } else {\n      date = result;\n    }\n  }\n\n  return date;\n}\n\nfunction cleanEscapedString(input) {\n  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default parse;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setDate} function options.\n */\n\n/**\n * @name setDate\n * @category Day Helpers\n * @summary Set the day of the month to the given date.\n *\n * @description\n * Set the day of the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param dayOfMonth - The day of the month of the new date\n * @param options - The options\n *\n * @returns The new date with the day of the month set\n *\n * @example\n * // Set the 30th day of the month to 1 September 2014:\n * const result = setDate(new Date(2014, 8, 1), 30)\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function setDate(date, dayOfMonth, options) {\n  const _date = toDate(date, options?.in);\n  _date.setDate(dayOfMonth);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setDate;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setHours} function options.\n */\n\n/**\n * @name setHours\n * @category Hour Helpers\n * @summary Set the hours to the given date.\n *\n * @description\n * Set the hours to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param hours - The hours of the new date\n * @param options - An object with options\n *\n * @returns The new date with the hours set\n *\n * @example\n * // Set 4 hours to 1 September 2014 11:30:00:\n * const result = setHours(new Date(2014, 8, 1, 11, 30), 4)\n * //=> Mon Sep 01 2014 04:30:00\n */\nexport function setHours(date, hours, options) {\n  const _date = toDate(date, options?.in);\n  _date.setHours(hours);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setHours;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setMinutes} function options.\n */\n\n/**\n * @name setMinutes\n * @category Minute Helpers\n * @summary Set the minutes to the given date.\n *\n * @description\n * Set the minutes to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, returned from the context function, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param minutes - The minutes of the new date\n * @param options - An object with options\n *\n * @returns The new date with the minutes set\n *\n * @example\n * // Set 45 minutes to 1 September 2014 11:30:40:\n * const result = setMinutes(new Date(2014, 8, 1, 11, 30, 40), 45)\n * //=> Mon Sep 01 2014 11:45:40\n */\nexport function setMinutes(date, minutes, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setMinutes(minutes);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default setMinutes;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { getDaysInMonth } from \"./getDaysInMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setMonth} function options.\n */\n\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param month - The month index to set (0-11)\n * @param options - The options\n *\n * @returns The new date with the month set\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\nexport function setMonth(date, month, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const day = _date.getDate();\n\n  const midMonth = constructFrom(options?.in || date, 0);\n  midMonth.setFullYear(year, month, 15);\n  midMonth.setHours(0, 0, 0, 0);\n  const daysInMonth = getDaysInMonth(midMonth);\n\n  // Set the earlier date, allows to wrap Jan 31 to Feb 28\n  _date.setMonth(month, Math.min(day, daysInMonth));\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMonth;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setSeconds} function options.\n */\n\n/**\n * @name setSeconds\n * @category Second Helpers\n * @summary Set the seconds to the given date, with context support.\n *\n * @description\n * Set the seconds to the given date, with an optional context for time zone specification.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param seconds - The seconds of the new date\n * @param options - An object with options\n *\n * @returns The new date with the seconds set\n *\n * @example\n * // Set 45 seconds to 1 September 2014 11:30:40:\n * const result = setSeconds(new Date(2014, 8, 1, 11, 30, 40), 45)\n * //=> Mon Sep 01 2014 11:30:45\n */\nexport function setSeconds(date, seconds, options) {\n  const _date = toDate(date, options?.in);\n  _date.setSeconds(seconds);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setSeconds;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setMilliseconds} function options.\n */\n\n/**\n * @name setMilliseconds\n * @category Millisecond Helpers\n * @summary Set the milliseconds to the given date.\n *\n * @description\n * Set the milliseconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param milliseconds - The milliseconds of the new date\n * @param options - The options\n *\n * @returns The new date with the milliseconds set\n *\n * @example\n * // Set 300 milliseconds to 1 September 2014 11:30:40.500:\n * const result = setMilliseconds(new Date(2014, 8, 1, 11, 30, 40, 500), 300)\n * //=> Mon Sep 01 2014 11:30:40.300\n */\nexport function setMilliseconds(date, milliseconds, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMilliseconds(milliseconds);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMilliseconds;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setYear} function options.\n */\n\n/**\n * @name setYear\n * @category Year Helpers\n * @summary Set the year to the given date.\n *\n * @description\n * Set the year to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param year - The year of the new date\n * @param options - An object with options.\n *\n * @returns The new date with the year set\n *\n * @example\n * // Set year 2013 to 1 September 2014:\n * const result = setYear(new Date(2014, 8, 1), 2013)\n * //=> Sun Sep 01 2013 00:00:00\n */\nexport function setYear(date, year, options) {\n  const date_ = toDate(date, options?.in);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(+date_)) return constructFrom(options?.in || date, NaN);\n\n  date_.setFullYear(year);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default setYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfMonth} function options.\n */\n\n/**\n * @name startOfMonth\n * @category Month Helpers\n * @summary Return the start of a month for the given date.\n *\n * @description\n * Return the start of a month for the given date. The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments.\n * Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed,\n * or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a month\n *\n * @example\n * // The start of a month for 2 September 2014 11:55:00:\n * const result = startOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setDate(1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfMonth;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfMonth} function options.\n */\n\n/**\n * @name endOfMonth\n * @category Month Helpers\n * @summary Return the end of a month for the given date.\n *\n * @description\n * Return the end of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a month\n *\n * @example\n * // The end of a month for 2 September 2014 11:55:00:\n * const result = endOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\nexport function endOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfMonth;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWithinInterval} function options.\n */\n\n/**\n * @name isWithinInterval\n * @category Interval Helpers\n * @summary Is the given date within the interval?\n *\n * @description\n * Is the given date within the interval? (Including start and end.)\n *\n * @param date - The date to check\n * @param interval - The interval to check\n * @param options - An object with options\n *\n * @returns The date is within the interval\n *\n * @example\n * // For the date within the interval:\n * isWithinInterval(new Date(2014, 0, 3), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * // => true\n *\n * @example\n * // For the date outside of the interval:\n * isWithinInterval(new Date(2014, 0, 10), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * // => false\n *\n * @example\n * // For date equal to the interval start:\n * isWithinInterval(date, { start, end: date })\n * // => true\n *\n * @example\n * // For date equal to the interval end:\n * isWithinInterval(date, { start: date, end })\n * // => true\n */\nexport function isWithinInterval(date, interval, options) {\n  const time = +toDate(date, options?.in);\n  const [startTime, endTime] = [\n    +toDate(interval.start, options?.in),\n    +toDate(interval.end, options?.in),\n  ].sort((a, b) => a - b);\n\n  return time >= startTime && time <= endTime;\n}\n\n// Fallback for modularized imports:\nexport default isWithinInterval;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AA6BO,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC7C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,MAAI,MAAM,MAAM,EAAG,QAAO,eAAc,mCAAS,OAAM,MAAM,GAAG;AAGhE,MAAI,CAAC,OAAQ,QAAO;AAEpB,QAAM,QAAQ,MAAM,QAAQ,IAAI,MAAM;AACtC,SAAO;AACT;;;ACTO,SAAS,gBAAgB,MAAM,QAAQ,SAAS;AACrD,SAAO,eAAc,mCAAS,OAAM,MAAM,CAAC,OAAO,IAAI,IAAI,MAAM;AAClE;;;ACHO,SAAS,WAAW,MAAM,QAAQ,SAAS;AAChD,SAAO,gBAAgB,MAAM,SAAS,KAAM,OAAO;AACrD;;;ACDO,SAAS,WAAW,MAAM,QAAQ,SAAS;AAChD,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,QAAQ,MAAM,QAAQ,IAAI,SAAS,oBAAoB;AAC7D,SAAO;AACT;;;ACJO,SAAS,SAAS,MAAM,QAAQ,SAAS;AAC9C,SAAO,gBAAgB,MAAM,SAAS,oBAAoB,OAAO;AACnE;;;ACHO,SAAS,SAAS,MAAM,QAAQ,SAAS;AAC9C,SAAO,QAAQ,MAAM,SAAS,GAAG,OAAO;AAC1C;;;ACGO,SAAS,UAAU,MAAM,QAAQ,SAAS;AAC/C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,MAAI,MAAM,MAAM,EAAG,QAAO,eAAc,mCAAS,OAAM,MAAM,GAAG;AAChE,MAAI,CAAC,QAAQ;AAEX,WAAO;AAAA,EACT;AACA,QAAM,aAAa,MAAM,QAAQ;AAUjC,QAAM,oBAAoB,eAAc,mCAAS,OAAM,MAAM,MAAM,QAAQ,CAAC;AAC5E,oBAAkB,SAAS,MAAM,SAAS,IAAI,SAAS,GAAG,CAAC;AAC3D,QAAM,cAAc,kBAAkB,QAAQ;AAC9C,MAAI,cAAc,aAAa;AAG7B,WAAO;AAAA,EACT,OAAO;AAQL,UAAM;AAAA,MACJ,kBAAkB,YAAY;AAAA,MAC9B,kBAAkB,SAAS;AAAA,MAC3B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;;;AC5CO,SAAS,SAAS,MAAM,QAAQ,SAAS;AAC9C,SAAO,UAAU,MAAM,SAAS,IAAI,OAAO;AAC7C;;;ACFO,SAAS,SAAS,MAAM,SAAS;AACtC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,SAAS,IAAI,IAAI,IAAI,GAAG;AAC9B,SAAO;AACT;;;ACEO,SAAS,UAAU,MAAM,SAAS;AAlCzC;AAmCE,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1B,eAAe,kBACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,MAAM,MAAM,OAAO;AACzB,QAAM,QAAQ,MAAM,eAAe,KAAK,KAAK,KAAK,MAAM;AAExD,QAAM,QAAQ,MAAM,QAAQ,IAAI,IAAI;AACpC,QAAM,SAAS,IAAI,IAAI,IAAI,GAAG;AAC9B,SAAO;AACT;;;ACtBO,SAAS,UAAU,MAAM,SAAS;AACvC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,YAAY,OAAO,GAAG,GAAG,CAAC;AAChC,QAAM,SAAS,IAAI,IAAI,IAAI,GAAG;AAC9B,SAAO;AACT;;;ACNO,SAAS,WAAW,MAAM,SAAS;AACxC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;;;ACnBO,SAAS,gCAAgC,MAAM;AACpD,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,UAAU,IAAI;AAAA,IAClB,KAAK;AAAA,MACH,MAAM,YAAY;AAAA,MAClB,MAAM,SAAS;AAAA,MACf,MAAM,QAAQ;AAAA,MACd,MAAM,SAAS;AAAA,MACf,MAAM,WAAW;AAAA,MACjB,MAAM,WAAW;AAAA,MACjB,MAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AACA,UAAQ,eAAe,MAAM,YAAY,CAAC;AAC1C,SAAO,CAAC,OAAO,CAAC;AAClB;;;ACYO,SAAS,yBAAyB,WAAW,aAAa,SAAS;AACxE,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,QAAM,kBAAkB,WAAW,UAAU;AAC7C,QAAM,oBAAoB,WAAW,YAAY;AAEjD,QAAM,iBACJ,CAAC,kBAAkB,gCAAgC,eAAe;AACpE,QAAM,mBACJ,CAAC,oBAAoB,gCAAgC,iBAAiB;AAKxE,SAAO,KAAK,OAAO,iBAAiB,oBAAoB,iBAAiB;AAC3E;;;AC/BO,SAAS,YAAY,MAAM,SAAS;AACzC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,YAAY,MAAM,YAAY,GAAG,GAAG,CAAC;AAC3C,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;;;ACPO,SAAS,aAAa,MAAM,SAAS;AAC1C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,yBAAyB,OAAO,YAAY,KAAK,CAAC;AAC/D,QAAM,YAAY,OAAO;AACzB,SAAO;AACT;;;ACDO,SAAS,eAAe,MAAM,SAAS;AAC5C,SAAO,YAAY,MAAM,EAAE,GAAG,SAAS,cAAc,EAAE,CAAC;AAC1D;;;ACJO,SAAS,eAAe,MAAM,SAAS;AAC5C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAE/B,QAAM,4BAA4B,cAAc,OAAO,CAAC;AACxD,4BAA0B,YAAY,OAAO,GAAG,GAAG,CAAC;AACpD,4BAA0B,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7C,QAAM,kBAAkB,eAAe,yBAAyB;AAEhE,QAAM,4BAA4B,cAAc,OAAO,CAAC;AACxD,4BAA0B,YAAY,MAAM,GAAG,CAAC;AAChD,4BAA0B,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7C,QAAM,kBAAkB,eAAe,yBAAyB;AAEhE,MAAI,MAAM,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AAChD,WAAO,OAAO;AAAA,EAChB,WAAW,MAAM,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AACvD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;;;AChBO,SAAS,mBAAmB,MAAM,SAAS;AAChD,QAAM,OAAO,eAAe,MAAM,OAAO;AACzC,QAAM,kBAAkB,eAAc,mCAAS,OAAM,MAAM,CAAC;AAC5D,kBAAgB,YAAY,MAAM,GAAG,CAAC;AACtC,kBAAgB,SAAS,GAAG,GAAG,GAAG,CAAC;AACnC,SAAO,eAAe,eAAe;AACvC;;;ACVO,SAAS,WAAW,MAAM,SAAS;AACxC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC,mBAAmB,KAAK;AAK/D,SAAO,KAAK,MAAM,OAAO,kBAAkB,IAAI;AACjD;;;ACMO,SAAS,YAAY,MAAM,SAAS;AA3C3C;AA4CE,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAE/B,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,yBACJ,mCAAS,4BACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,0BAC1B,eAAe,2BACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,0BAChC;AAEF,QAAM,sBAAsB,eAAc,mCAAS,OAAM,MAAM,CAAC;AAChE,sBAAoB,YAAY,OAAO,GAAG,GAAG,qBAAqB;AAClE,sBAAoB,SAAS,GAAG,GAAG,GAAG,CAAC;AACvC,QAAM,kBAAkB,YAAY,qBAAqB,OAAO;AAEhE,QAAM,sBAAsB,eAAc,mCAAS,OAAM,MAAM,CAAC;AAChE,sBAAoB,YAAY,MAAM,GAAG,qBAAqB;AAC9D,sBAAoB,SAAS,GAAG,GAAG,GAAG,CAAC;AACvC,QAAM,kBAAkB,YAAY,qBAAqB,OAAO;AAEhE,MAAI,CAAC,SAAS,CAAC,iBAAiB;AAC9B,WAAO,OAAO;AAAA,EAChB,WAAW,CAAC,SAAS,CAAC,iBAAiB;AACrC,WAAO;AAAA,EACT,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;;;AC1BO,SAAS,gBAAgB,MAAM,SAAS;AA9C/C;AA+CE,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,yBACJ,mCAAS,4BACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,0BAC1B,eAAe,2BACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,0BAChC;AAEF,QAAM,OAAO,YAAY,MAAM,OAAO;AACtC,QAAM,YAAY,eAAc,mCAAS,OAAM,MAAM,CAAC;AACtD,YAAU,YAAY,MAAM,GAAG,qBAAqB;AACpD,YAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7B,QAAM,QAAQ,YAAY,WAAW,OAAO;AAC5C,SAAO;AACT;;;AClBO,SAAS,QAAQ,MAAM,SAAS;AACrC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,CAAC,YAAY,OAAO,OAAO,IAAI,CAAC,gBAAgB,OAAO,OAAO;AAK3E,SAAO,KAAK,MAAM,OAAO,kBAAkB,IAAI;AACjD;;;ACnDO,SAAS,gBAAgB,QAAQ,cAAc;AACpD,QAAM,OAAO,SAAS,IAAI,MAAM;AAChC,QAAM,SAAS,KAAK,IAAI,MAAM,EAAE,SAAS,EAAE,SAAS,cAAc,GAAG;AACrE,SAAO,OAAO;AAChB;;;ACWO,IAAM,kBAAkB;AAAA;AAAA,EAE7B,EAAE,MAAM,OAAO;AAUb,UAAM,aAAa,KAAK,YAAY;AAEpC,UAAM,OAAO,aAAa,IAAI,aAAa,IAAI;AAC/C,WAAO,gBAAgB,UAAU,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM;AAAA,EACzE;AAAA;AAAA,EAGA,EAAE,MAAM,OAAO;AACb,UAAM,QAAQ,KAAK,SAAS;AAC5B,WAAO,UAAU,MAAM,OAAO,QAAQ,CAAC,IAAI,gBAAgB,QAAQ,GAAG,CAAC;AAAA,EACzE;AAAA;AAAA,EAGA,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,QAAQ,GAAG,MAAM,MAAM;AAAA,EACrD;AAAA;AAAA,EAGA,EAAE,MAAM,OAAO;AACb,UAAM,qBAAqB,KAAK,SAAS,IAAI,MAAM,IAAI,OAAO;AAE9D,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,mBAAmB,YAAY;AAAA,MACxC,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO,mBAAmB,CAAC;AAAA,MAC7B,KAAK;AAAA,MACL;AACE,eAAO,uBAAuB,OAAO,SAAS;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAGA,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,SAAS,IAAI,MAAM,IAAI,MAAM,MAAM;AAAA,EACjE;AAAA;AAAA,EAGA,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,SAAS,GAAG,MAAM,MAAM;AAAA,EACtD;AAAA;AAAA,EAGA,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,WAAW,GAAG,MAAM,MAAM;AAAA,EACxD;AAAA;AAAA,EAGA,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,WAAW,GAAG,MAAM,MAAM;AAAA,EACxD;AAAA;AAAA,EAGA,EAAE,MAAM,OAAO;AACb,UAAM,iBAAiB,MAAM;AAC7B,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,oBAAoB,KAAK;AAAA,MAC7B,eAAe,KAAK,IAAI,IAAI,iBAAiB,CAAC;AAAA,IAChD;AACA,WAAO,gBAAgB,mBAAmB,MAAM,MAAM;AAAA,EACxD;AACF;;;AClFA,IAAM,gBAAgB;AAAA,EACpB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,OAAO;AACT;AAgDO,IAAM,aAAa;AAAA;AAAA,EAExB,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,MAAM,KAAK,YAAY,IAAI,IAAI,IAAI;AACzC,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,IAAI,KAAK,EAAE,OAAO,cAAc,CAAC;AAAA;AAAA,MAEnD,KAAK;AACH,eAAO,SAAS,IAAI,KAAK,EAAE,OAAO,SAAS,CAAC;AAAA;AAAA,MAE9C,KAAK;AAAA,MACL;AACE,eAAO,SAAS,IAAI,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAElC,QAAI,UAAU,MAAM;AAClB,YAAM,aAAa,KAAK,YAAY;AAEpC,YAAM,OAAO,aAAa,IAAI,aAAa,IAAI;AAC/C,aAAO,SAAS,cAAc,MAAM,EAAE,MAAM,OAAO,CAAC;AAAA,IACtD;AAEA,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACtC;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU,SAAS;AAC3C,UAAM,iBAAiB,YAAY,MAAM,OAAO;AAEhD,UAAM,WAAW,iBAAiB,IAAI,iBAAiB,IAAI;AAG3D,QAAI,UAAU,MAAM;AAClB,YAAM,eAAe,WAAW;AAChC,aAAO,gBAAgB,cAAc,CAAC;AAAA,IACxC;AAGA,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,UAAU,EAAE,MAAM,OAAO,CAAC;AAAA,IAC1D;AAGA,WAAO,gBAAgB,UAAU,MAAM,MAAM;AAAA,EAC/C;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO;AACxB,UAAM,cAAc,eAAe,IAAI;AAGvC,WAAO,gBAAgB,aAAa,MAAM,MAAM;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,GAAG,SAAU,MAAM,OAAO;AACxB,UAAM,OAAO,KAAK,YAAY;AAC9B,WAAO,gBAAgB,MAAM,MAAM,MAAM;AAAA,EAC3C;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,UAAU,KAAK,MAAM,KAAK,SAAS,IAAI,KAAK,CAAC;AACnD,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,OAAO;AAAA;AAAA,MAEvB,KAAK;AACH,eAAO,gBAAgB,SAAS,CAAC;AAAA;AAAA,MAEnC,KAAK;AACH,eAAO,SAAS,cAAc,SAAS,EAAE,MAAM,UAAU,CAAC;AAAA;AAAA,MAE5D,KAAK;AACH,eAAO,SAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,UAAU,KAAK,MAAM,KAAK,SAAS,IAAI,KAAK,CAAC;AACnD,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,OAAO;AAAA;AAAA,MAEvB,KAAK;AACH,eAAO,gBAAgB,SAAS,CAAC;AAAA;AAAA,MAEnC,KAAK;AACH,eAAO,SAAS,cAAc,SAAS,EAAE,MAAM,UAAU,CAAC;AAAA;AAAA,MAE5D,KAAK;AACH,eAAO,SAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,QAAQ,KAAK,SAAS;AAC5B,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA;AAAA,MAEtC,KAAK;AACH,eAAO,SAAS,cAAc,QAAQ,GAAG,EAAE,MAAM,QAAQ,CAAC;AAAA;AAAA,MAE5D,KAAK;AACH,eAAO,SAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,MAAM,OAAO,EAAE,OAAO,QAAQ,SAAS,aAAa,CAAC;AAAA,IACzE;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,QAAQ,KAAK,SAAS;AAC5B,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,QAAQ,CAAC;AAAA;AAAA,MAEzB,KAAK;AACH,eAAO,gBAAgB,QAAQ,GAAG,CAAC;AAAA;AAAA,MAErC,KAAK;AACH,eAAO,SAAS,cAAc,QAAQ,GAAG,EAAE,MAAM,QAAQ,CAAC;AAAA;AAAA,MAE5D,KAAK;AACH,eAAO,SAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,MAAM,OAAO,EAAE,OAAO,QAAQ,SAAS,aAAa,CAAC;AAAA,IACzE;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU,SAAS;AAC3C,UAAM,OAAO,QAAQ,MAAM,OAAO;AAElC,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,MAAM,EAAE,MAAM,OAAO,CAAC;AAAA,IACtD;AAEA,WAAO,gBAAgB,MAAM,MAAM,MAAM;AAAA,EAC3C;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,UAAU,WAAW,IAAI;AAE/B,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,SAAS,EAAE,MAAM,OAAO,CAAC;AAAA,IACzD;AAEA,WAAO,gBAAgB,SAAS,MAAM,MAAM;AAAA,EAC9C;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,KAAK,QAAQ,GAAG,EAAE,MAAM,OAAO,CAAC;AAAA,IAChE;AAEA,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACtC;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,YAAY,aAAa,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,WAAW,EAAE,MAAM,YAAY,CAAC;AAAA,IAChE;AAEA,WAAO,gBAAgB,WAAW,MAAM,MAAM;AAAA,EAChD;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,YAAY,KAAK,OAAO;AAC9B,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU,SAAS;AAC3C,UAAM,YAAY,KAAK,OAAO;AAC9B,UAAM,kBAAkB,YAAY,QAAQ,eAAe,KAAK,KAAK;AACrE,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,cAAc;AAAA;AAAA,MAE9B,KAAK;AACH,eAAO,gBAAgB,gBAAgB,CAAC;AAAA;AAAA,MAE1C,KAAK;AACH,eAAO,SAAS,cAAc,gBAAgB,EAAE,MAAM,MAAM,CAAC;AAAA,MAC/D,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU,SAAS;AAC3C,UAAM,YAAY,KAAK,OAAO;AAC9B,UAAM,kBAAkB,YAAY,QAAQ,eAAe,KAAK,KAAK;AACrE,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,cAAc;AAAA;AAAA,MAE9B,KAAK;AACH,eAAO,gBAAgB,gBAAgB,MAAM,MAAM;AAAA;AAAA,MAErD,KAAK;AACH,eAAO,SAAS,cAAc,gBAAgB,EAAE,MAAM,MAAM,CAAC;AAAA,MAC/D,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,YAAY,KAAK,OAAO;AAC9B,UAAM,eAAe,cAAc,IAAI,IAAI;AAC3C,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,YAAY;AAAA;AAAA,MAE5B,KAAK;AACH,eAAO,gBAAgB,cAAc,MAAM,MAAM;AAAA;AAAA,MAEnD,KAAK;AACH,eAAO,SAAS,cAAc,cAAc,EAAE,MAAM,MAAM,CAAC;AAAA;AAAA,MAE7D,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,qBAAqB,QAAQ,MAAM,IAAI,OAAO;AAEpD,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AACH,eAAO,SACJ,UAAU,oBAAoB;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,EACA,YAAY;AAAA,MACjB,KAAK;AACH,eAAO,SAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI;AACJ,QAAI,UAAU,IAAI;AAChB,2BAAqB,cAAc;AAAA,IACrC,WAAW,UAAU,GAAG;AACtB,2BAAqB,cAAc;AAAA,IACrC,OAAO;AACL,2BAAqB,QAAQ,MAAM,IAAI,OAAO;AAAA,IAChD;AAEA,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AACH,eAAO,SACJ,UAAU,oBAAoB;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,EACA,YAAY;AAAA,MACjB,KAAK;AACH,eAAO,SAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI;AACJ,QAAI,SAAS,IAAI;AACf,2BAAqB,cAAc;AAAA,IACrC,WAAW,SAAS,IAAI;AACtB,2BAAqB,cAAc;AAAA,IACrC,WAAW,SAAS,GAAG;AACrB,2BAAqB,cAAc;AAAA,IACrC,OAAO;AACL,2BAAqB,cAAc;AAAA,IACrC;AAEA,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AACH,eAAO,SAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AAAA,MACL;AACE,eAAO,SAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,QAAI,UAAU,MAAM;AAClB,UAAI,QAAQ,KAAK,SAAS,IAAI;AAC9B,UAAI,UAAU,EAAG,SAAQ;AACzB,aAAO,SAAS,cAAc,OAAO,EAAE,MAAM,OAAO,CAAC;AAAA,IACvD;AAEA,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACtC;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,KAAK,SAAS,GAAG,EAAE,MAAM,OAAO,CAAC;AAAA,IACjE;AAEA,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACtC;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,UAAM,QAAQ,KAAK,SAAS,IAAI;AAEhC,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,OAAO,EAAE,MAAM,OAAO,CAAC;AAAA,IACvD;AAEA,WAAO,gBAAgB,OAAO,MAAM,MAAM;AAAA,EAC5C;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,QAAI,QAAQ,KAAK,SAAS;AAC1B,QAAI,UAAU,EAAG,SAAQ;AAEzB,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,OAAO,EAAE,MAAM,OAAO,CAAC;AAAA,IACvD;AAEA,WAAO,gBAAgB,OAAO,MAAM,MAAM;AAAA,EAC5C;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,KAAK,WAAW,GAAG,EAAE,MAAM,SAAS,CAAC;AAAA,IACrE;AAEA,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACtC;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,UAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAO,SAAS,cAAc,KAAK,WAAW,GAAG,EAAE,MAAM,SAAS,CAAC;AAAA,IACrE;AAEA,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACtC;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO;AACxB,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACtC;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,iBAAiB,KAAK,kBAAkB;AAE9C,QAAI,mBAAmB,GAAG;AACxB,aAAO;AAAA,IACT;AAEA,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO,kCAAkC,cAAc;AAAA;AAAA;AAAA;AAAA,MAKzD,KAAK;AAAA,MACL,KAAK;AACH,eAAO,eAAe,cAAc;AAAA;AAAA;AAAA;AAAA,MAKtC,KAAK;AAAA,MACL,KAAK;AAAA;AAAA,MACL;AACE,eAAO,eAAe,gBAAgB,GAAG;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,iBAAiB,KAAK,kBAAkB;AAE9C,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO,kCAAkC,cAAc;AAAA;AAAA;AAAA;AAAA,MAKzD,KAAK;AAAA,MACL,KAAK;AACH,eAAO,eAAe,cAAc;AAAA;AAAA;AAAA;AAAA,MAKtC,KAAK;AAAA,MACL,KAAK;AAAA;AAAA,MACL;AACE,eAAO,eAAe,gBAAgB,GAAG;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,iBAAiB,KAAK,kBAAkB;AAE9C,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,QAAQ,oBAAoB,gBAAgB,GAAG;AAAA;AAAA,MAExD,KAAK;AAAA,MACL;AACE,eAAO,QAAQ,eAAe,gBAAgB,GAAG;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,iBAAiB,KAAK,kBAAkB;AAE9C,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,QAAQ,oBAAoB,gBAAgB,GAAG;AAAA;AAAA,MAExD,KAAK;AAAA,MACL;AACE,eAAO,QAAQ,eAAe,gBAAgB,GAAG;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,YAAY,KAAK,MAAM,CAAC,OAAO,GAAI;AACzC,WAAO,gBAAgB,WAAW,MAAM,MAAM;AAAA,EAChD;AAAA;AAAA,EAGA,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,WAAO,gBAAgB,CAAC,MAAM,MAAM,MAAM;AAAA,EAC5C;AACF;AAEA,SAAS,oBAAoB,QAAQ,YAAY,IAAI;AACnD,QAAM,OAAO,SAAS,IAAI,MAAM;AAChC,QAAM,YAAY,KAAK,IAAI,MAAM;AACjC,QAAM,QAAQ,KAAK,MAAM,YAAY,EAAE;AACvC,QAAM,UAAU,YAAY;AAC5B,MAAI,YAAY,GAAG;AACjB,WAAO,OAAO,OAAO,KAAK;AAAA,EAC5B;AACA,SAAO,OAAO,OAAO,KAAK,IAAI,YAAY,gBAAgB,SAAS,CAAC;AACtE;AAEA,SAAS,kCAAkC,QAAQ,WAAW;AAC5D,MAAI,SAAS,OAAO,GAAG;AACrB,UAAM,OAAO,SAAS,IAAI,MAAM;AAChC,WAAO,OAAO,gBAAgB,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,EACxD;AACA,SAAO,eAAe,QAAQ,SAAS;AACzC;AAEA,SAAS,eAAe,QAAQ,YAAY,IAAI;AAC9C,QAAM,OAAO,SAAS,IAAI,MAAM;AAChC,QAAM,YAAY,KAAK,IAAI,MAAM;AACjC,QAAM,QAAQ,gBAAgB,KAAK,MAAM,YAAY,EAAE,GAAG,CAAC;AAC3D,QAAM,UAAU,gBAAgB,YAAY,IAAI,CAAC;AACjD,SAAO,OAAO,QAAQ,YAAY;AACpC;;;ACvwBA,IAAM,oBAAoB,CAAC,SAAS,eAAe;AACjD,UAAQ,SAAS;AAAA,IACf,KAAK;AACH,aAAO,WAAW,KAAK,EAAE,OAAO,QAAQ,CAAC;AAAA,IAC3C,KAAK;AACH,aAAO,WAAW,KAAK,EAAE,OAAO,SAAS,CAAC;AAAA,IAC5C,KAAK;AACH,aAAO,WAAW,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,IAC1C,KAAK;AAAA,IACL;AACE,aAAO,WAAW,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,EAC5C;AACF;AAEA,IAAM,oBAAoB,CAAC,SAAS,eAAe;AACjD,UAAQ,SAAS;AAAA,IACf,KAAK;AACH,aAAO,WAAW,KAAK,EAAE,OAAO,QAAQ,CAAC;AAAA,IAC3C,KAAK;AACH,aAAO,WAAW,KAAK,EAAE,OAAO,SAAS,CAAC;AAAA,IAC5C,KAAK;AACH,aAAO,WAAW,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,IAC1C,KAAK;AAAA,IACL;AACE,aAAO,WAAW,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,EAC5C;AACF;AAEA,IAAM,wBAAwB,CAAC,SAAS,eAAe;AACrD,QAAM,cAAc,QAAQ,MAAM,WAAW,KAAK,CAAC;AACnD,QAAM,cAAc,YAAY,CAAC;AACjC,QAAM,cAAc,YAAY,CAAC;AAEjC,MAAI,CAAC,aAAa;AAChB,WAAO,kBAAkB,SAAS,UAAU;AAAA,EAC9C;AAEA,MAAI;AAEJ,UAAQ,aAAa;AAAA,IACnB,KAAK;AACH,uBAAiB,WAAW,SAAS,EAAE,OAAO,QAAQ,CAAC;AACvD;AAAA,IACF,KAAK;AACH,uBAAiB,WAAW,SAAS,EAAE,OAAO,SAAS,CAAC;AACxD;AAAA,IACF,KAAK;AACH,uBAAiB,WAAW,SAAS,EAAE,OAAO,OAAO,CAAC;AACtD;AAAA,IACF,KAAK;AAAA,IACL;AACE,uBAAiB,WAAW,SAAS,EAAE,OAAO,OAAO,CAAC;AACtD;AAAA,EACJ;AAEA,SAAO,eACJ,QAAQ,YAAY,kBAAkB,aAAa,UAAU,CAAC,EAC9D,QAAQ,YAAY,kBAAkB,aAAa,UAAU,CAAC;AACnE;AAEO,IAAM,iBAAiB;AAAA,EAC5B,GAAG;AAAA,EACH,GAAG;AACL;;;AC/BO,SAAS,OAAO,OAAO;AAC5B,SACE,iBAAiB,QAChB,OAAO,UAAU,YAChB,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAEhD;;;ACJO,SAAS,QAAQ,MAAM;AAC5B,SAAO,EAAG,CAAC,OAAO,IAAI,KAAK,OAAO,SAAS,YAAa,MAAM,CAAC,OAAO,IAAI,CAAC;AAC7E;;;ACpCA,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AAExB,IAAM,cAAc,CAAC,KAAK,MAAM,MAAM,MAAM;AAErC,SAAS,0BAA0B,OAAO;AAC/C,SAAO,iBAAiB,KAAK,KAAK;AACpC;AAEO,SAAS,yBAAyB,OAAO;AAC9C,SAAO,gBAAgB,KAAK,KAAK;AACnC;AAEO,SAAS,0BAA0B,OAAOA,SAAQ,OAAO;AAC9D,QAAM,WAAW,QAAQ,OAAOA,SAAQ,KAAK;AAC7C,UAAQ,KAAK,QAAQ;AACrB,MAAI,YAAY,SAAS,KAAK,EAAG,OAAM,IAAI,WAAW,QAAQ;AAChE;AAEA,SAAS,QAAQ,OAAOA,SAAQ,OAAO;AACrC,QAAM,UAAU,MAAM,CAAC,MAAM,MAAM,UAAU;AAC7C,SAAO,SAAS,MAAM,YAAY,CAAC,mBAAmB,KAAK,YAAYA,OAAM,sBAAsB,OAAO,mBAAmB,KAAK;AACpI;;;ACKA,IAAM,yBACJ;AAIF,IAAM,6BAA6B;AAEnC,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAC1B,IAAM,gCAAgC;AAoS/B,SAAS,OAAO,MAAM,WAAW,SAAS;AAxUjD;AAyUE,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,UAAS,mCAAS,WAAU,eAAe,UAAU;AAE3D,QAAM,yBACJ,mCAAS,4BACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,0BAC1B,eAAe,2BACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,0BAChC;AAEF,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1B,eAAe,kBACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,QAAM,eAAe,OAAO,MAAM,mCAAS,EAAE;AAE7C,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAI,QAAQ,UACT,MAAM,0BAA0B,EAChC,IAAI,CAAC,cAAc;AAClB,UAAM,iBAAiB,UAAU,CAAC;AAClC,QAAI,mBAAmB,OAAO,mBAAmB,KAAK;AACpD,YAAM,gBAAgB,eAAe,cAAc;AACnD,aAAO,cAAc,WAAW,OAAO,UAAU;AAAA,IACnD;AACA,WAAO;AAAA,EACT,CAAC,EACA,KAAK,EAAE,EACP,MAAM,sBAAsB,EAC5B,IAAI,CAAC,cAAc;AAElB,QAAI,cAAc,MAAM;AACtB,aAAO,EAAE,SAAS,OAAO,OAAO,IAAI;AAAA,IACtC;AAEA,UAAM,iBAAiB,UAAU,CAAC;AAClC,QAAI,mBAAmB,KAAK;AAC1B,aAAO,EAAE,SAAS,OAAO,OAAO,mBAAmB,SAAS,EAAE;AAAA,IAChE;AAEA,QAAI,WAAW,cAAc,GAAG;AAC9B,aAAO,EAAE,SAAS,MAAM,OAAO,UAAU;AAAA,IAC3C;AAEA,QAAI,eAAe,MAAM,6BAA6B,GAAG;AACvD,YAAM,IAAI;AAAA,QACR,mEACE,iBACA;AAAA,MACJ;AAAA,IACF;AAEA,WAAO,EAAE,SAAS,OAAO,OAAO,UAAU;AAAA,EAC5C,CAAC;AAGH,MAAI,OAAO,SAAS,cAAc;AAChC,YAAQ,OAAO,SAAS,aAAa,cAAc,KAAK;AAAA,EAC1D;AAEA,QAAM,mBAAmB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO,MACJ,IAAI,CAAC,SAAS;AACb,QAAI,CAAC,KAAK,QAAS,QAAO,KAAK;AAE/B,UAAM,QAAQ,KAAK;AAEnB,QACG,EAAC,mCAAS,gCACT,yBAAyB,KAAK,KAC/B,EAAC,mCAAS,iCACT,0BAA0B,KAAK,GACjC;AACA,gCAA0B,OAAO,WAAW,OAAO,IAAI,CAAC;AAAA,IAC1D;AAEA,UAAM,YAAY,WAAW,MAAM,CAAC,CAAC;AACrC,WAAO,UAAU,cAAc,OAAO,OAAO,UAAU,gBAAgB;AAAA,EACzE,CAAC,EACA,KAAK,EAAE;AACZ;AAEA,SAAS,mBAAmB,OAAO;AACjC,QAAM,UAAU,MAAM,MAAM,mBAAmB;AAE/C,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,CAAC,EAAE,QAAQ,mBAAmB,GAAG;AAClD;;;ACtZO,SAAS,QAAQ,MAAM,SAAS;AACrC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,QAAQ;AAC3C;;;ACDO,SAAS,eAAe,MAAM,SAAS;AAC5C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,aAAa,MAAM,SAAS;AAClC,QAAM,iBAAiB,cAAc,OAAO,CAAC;AAC7C,iBAAe,YAAY,MAAM,aAAa,GAAG,CAAC;AAClD,iBAAe,SAAS,GAAG,GAAG,GAAG,CAAC;AAClC,SAAO,eAAe,QAAQ;AAChC;;;ACTO,SAAS,SAAS,MAAM,SAAS;AACtC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,SAAS;AAC5C;;;ACFO,SAAS,WAAW,MAAM,SAAS;AACxC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,WAAW;AAC9C;;;ACFO,SAAS,SAAS,MAAM,SAAS;AACtC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,SAAS;AAC5C;;;ACPO,SAAS,WAAW,MAAM;AAC/B,SAAO,OAAO,IAAI,EAAE,WAAW;AACjC;;;ACFO,SAAS,gBAAgB,MAAM;AACpC,SAAO,OAAO,IAAI,EAAE,gBAAgB;AACtC;;;ACGO,SAAS,QAAQ,MAAM,SAAS;AACrC,SAAO,OAAO,MAAM,mCAAS,EAAE,EAAE,YAAY;AAC/C;;;ACNO,SAAS,QAAQ,MAAM,eAAe;AAC3C,SAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,aAAa;AAC9C;;;ACFO,SAAS,SAAS,MAAM,eAAe;AAC5C,SAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,aAAa;AAC9C;;;ACCO,SAAS,QAAQ,UAAU,WAAW;AAC3C,SAAO,CAAC,OAAO,QAAQ,MAAM,CAAC,OAAO,SAAS;AAChD;;;ACWO,SAAS,UAAU,WAAW,aAAa,SAAS;AACzD,QAAM,CAAC,WAAW,UAAU,IAAI;AAAA,IAC9B,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,SAAO,CAAC,WAAW,SAAS,MAAM,CAAC,WAAW,UAAU;AAC1D;;;AClBO,SAAS,WAAW,WAAW,aAAa,SAAS;AAC1D,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,SAAO,WAAW,YAAY,MAAM,aAAa,YAAY;AAC/D;;;ACFO,SAAS,YAAY,WAAW,aAAa,SAAS;AAC3D,QAAM,CAAC,YAAY,YAAY,IAAI;AAAA,IACjC,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,SACE,WAAW,YAAY,MAAM,aAAa,YAAY,KACtD,WAAW,SAAS,MAAM,aAAa,SAAS;AAEpD;;;ACZO,SAAS,YAAY,MAAM,SAAS;AACzC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,WAAW,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACDO,SAAS,WAAW,UAAU,WAAW,SAAS;AACvD,QAAM,CAAC,WAAW,UAAU,IAAI;AAAA,IAC9B,mCAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,SAAO,CAAC,YAAY,SAAS,MAAM,CAAC,YAAY,UAAU;AAC5D;;;ACZO,SAASC,qBAAoB;AAClC,SAAO,OAAO,OAAO,CAAC,GAAG,kBAA0B,CAAC;AACtD;;;ACEO,SAAS,UAAU,MAAM,aAAa;AAC3C,QAAM,QAAQ,cAAc,WAAW,IACnC,IAAI,YAAY,CAAC,IACjB,cAAc,aAAa,CAAC;AAChC,QAAM,YAAY,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,KAAK,QAAQ,CAAC;AACrE,QAAM;AAAA,IACJ,KAAK,SAAS;AAAA,IACd,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,gBAAgB;AAAA,EACvB;AACA,SAAO;AACT;AAEA,SAAS,cAAc,aAAa;AA5CpC;AA6CE,SACE,OAAO,gBAAgB,gBACvB,iBAAY,cAAZ,mBAAuB,iBAAgB;AAE3C;;;ACJO,SAAS,QAAQ,MAAM,MAAM,SAAS;AAC3C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,QAAQ,OAAO,OAAO,IAAI;AACvC,QAAM,QAAQ,MAAM,QAAQ,IAAI,OAAO,CAAC;AACxC,SAAO,OAAO,OAAO,mCAAS,EAAE;AAClC;;;ACnBO,SAAS,WAAW,MAAM,MAAM,SAAS;AAC9C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,WAAW,OAAO,OAAO,IAAI;AAC1C,QAAM,QAAQ,MAAM,QAAQ,IAAI,OAAO,CAAC;AACxC,SAAO;AACT;;;ACDO,SAAS,OAAO,MAAM,KAAK,SAAS;AAnC3C;AAoCE,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1B,eAAe,kBACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,aAAa,MAAM,OAAO;AAEhC,QAAM,YAAY,MAAM;AACxB,QAAM,YAAY,YAAY,KAAK;AAEnC,QAAM,QAAQ,IAAI;AAClB,QAAM,OACJ,MAAM,KAAK,MAAM,IACb,OAAQ,aAAa,SAAS,KAC5B,WAAW,SAAS,KAAO,aAAa,SAAS;AACzD,SAAO,QAAQ,OAAO,MAAM,OAAO;AACrC;;;AC7BO,SAAS,UAAU,MAAM,SAAS;AACvC,QAAM,MAAM,OAAO,MAAM,mCAAS,EAAE,EAAE,OAAO;AAC7C,SAAO,QAAQ,IAAI,IAAI;AACzB;;;ACEO,SAAS,UAAU,MAAM,KAAK,SAAS;AAC5C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,aAAa,UAAU,OAAO,OAAO;AAC3C,QAAM,OAAO,MAAM;AACnB,SAAO,QAAQ,OAAO,MAAM,OAAO;AACrC;;;AClCA,IAAM,yBAAyB;AAExB,IAAM,SAAN,MAAa;AAAA,EAAb;AACL,uCAAc;AAAA;AAAA,EAEd,SAAS,UAAU,UAAU;AAC3B,WAAO;AAAA,EACT;AACF;AAEO,IAAM,cAAN,cAA0B,OAAO;AAAA,EACtC,YACE,OAEA,eAEA,UAEA,UACA,aACA;AACA,UAAM;AACN,SAAK,QAAQ;AACb,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,QAAI,aAAa;AACf,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,SAAS,MAAM,SAAS;AACtB,WAAO,KAAK,cAAc,MAAM,KAAK,OAAO,OAAO;AAAA,EACrD;AAAA,EAEA,IAAI,MAAM,OAAO,SAAS;AACxB,WAAO,KAAK,SAAS,MAAM,OAAO,KAAK,OAAO,OAAO;AAAA,EACvD;AACF;AAEO,IAAM,qBAAN,cAAiC,OAAO;AAAA,EAI7C,YAAY,SAAS,WAAW;AAC9B,UAAM;AAJR,oCAAW;AACX,uCAAc;AAIZ,SAAK,UAAU,YAAY,CAAC,SAAS,cAAc,WAAW,IAAI;AAAA,EACpE;AAAA,EAEA,IAAI,MAAM,OAAO;AACf,QAAI,MAAM,eAAgB,QAAO;AACjC,WAAO,cAAc,MAAM,UAAU,MAAM,KAAK,OAAO,CAAC;AAAA,EAC1D;AACF;;;ACtDO,IAAM,SAAN,MAAa;AAAA,EAClB,IAAI,YAAY,OAAO,OAAO,SAAS;AACrC,UAAM,SAAS,KAAK,MAAM,YAAY,OAAO,OAAO,OAAO;AAC3D,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL,QAAQ,IAAI;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,MAAM,OAAO;AAAA,IACf;AAAA,EACF;AAAA,EAEA,SAAS,UAAU,QAAQ,UAAU;AACnC,WAAO;AAAA,EACT;AACF;;;ACtBO,IAAM,YAAN,cAAwB,OAAO;AAAA,EAA/B;AAAA;AACL,oCAAW;AAkCX,8CAAqB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EAhCxC,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eACE,MAAM,IAAI,YAAY,EAAE,OAAO,cAAc,CAAC,KAC9C,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,CAAC;AAAA;AAAA,MAI7C,KAAK;AACH,eAAO,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,CAAC;AAAA;AAAA,MAElD,KAAK;AAAA,MACL;AACE,eACE,MAAM,IAAI,YAAY,EAAE,OAAO,OAAO,CAAC,KACvC,MAAM,IAAI,YAAY,EAAE,OAAO,cAAc,CAAC,KAC9C,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,CAAC;AAAA,IAE/C;AAAA,EACF;AAAA,EAEA,IAAI,MAAM,OAAO,OAAO;AACtB,UAAM,MAAM;AACZ,SAAK,YAAY,OAAO,GAAG,CAAC;AAC5B,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAGF;;;ACtCO,IAAM,kBAAkB;AAAA,EAC7B,OAAO;AAAA;AAAA,EACP,MAAM;AAAA;AAAA,EACN,WAAW;AAAA;AAAA,EACX,MAAM;AAAA;AAAA,EACN,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,QAAQ;AAAA;AAAA,EACR,QAAQ;AAAA;AAAA,EAER,aAAa;AAAA;AAAA,EACb,WAAW;AAAA;AAAA,EACX,aAAa;AAAA;AAAA,EACb,YAAY;AAAA;AAAA,EAEZ,iBAAiB;AAAA,EACjB,mBAAmB;AAAA;AAAA,EACnB,iBAAiB;AAAA;AAAA,EACjB,mBAAmB;AAAA;AAAA,EACnB,kBAAkB;AAAA;AACpB;AAEO,IAAM,mBAAmB;AAAA,EAC9B,sBAAsB;AAAA,EACtB,OAAO;AAAA,EACP,sBAAsB;AAAA,EACtB,UAAU;AAAA,EACV,yBAAyB;AAC3B;;;ACtBO,SAAS,SAAS,eAAe,OAAO;AAC7C,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,OAAO,MAAM,cAAc,KAAK;AAAA,IAChC,MAAM,cAAc;AAAA,EACtB;AACF;AAEO,SAAS,oBAAoB,SAAS,YAAY;AACvD,QAAM,cAAc,WAAW,MAAM,OAAO;AAE5C,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,OAAO,SAAS,YAAY,CAAC,GAAG,EAAE;AAAA,IAClC,MAAM,WAAW,MAAM,YAAY,CAAC,EAAE,MAAM;AAAA,EAC9C;AACF;AAEO,SAAS,qBAAqB,SAAS,YAAY;AACxD,QAAM,cAAc,WAAW,MAAM,OAAO;AAE5C,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAGA,MAAI,YAAY,CAAC,MAAM,KAAK;AAC1B,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM,WAAW,MAAM,CAAC;AAAA,IAC1B;AAAA,EACF;AAEA,QAAM,OAAO,YAAY,CAAC,MAAM,MAAM,IAAI;AAC1C,QAAM,QAAQ,YAAY,CAAC,IAAI,SAAS,YAAY,CAAC,GAAG,EAAE,IAAI;AAC9D,QAAM,UAAU,YAAY,CAAC,IAAI,SAAS,YAAY,CAAC,GAAG,EAAE,IAAI;AAChE,QAAM,UAAU,YAAY,CAAC,IAAI,SAAS,YAAY,CAAC,GAAG,EAAE,IAAI;AAEhE,SAAO;AAAA,IACL,OACE,QACC,QAAQ,qBACP,UAAU,uBACV,UAAU;AAAA,IACd,MAAM,WAAW,MAAM,YAAY,CAAC,EAAE,MAAM;AAAA,EAC9C;AACF;AAEO,SAAS,qBAAqB,YAAY;AAC/C,SAAO,oBAAoB,gBAAgB,iBAAiB,UAAU;AACxE;AAEO,SAAS,aAAa,GAAG,YAAY;AAC1C,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,oBAAoB,gBAAgB,aAAa,UAAU;AAAA,IACpE,KAAK;AACH,aAAO,oBAAoB,gBAAgB,WAAW,UAAU;AAAA,IAClE,KAAK;AACH,aAAO,oBAAoB,gBAAgB,aAAa,UAAU;AAAA,IACpE,KAAK;AACH,aAAO,oBAAoB,gBAAgB,YAAY,UAAU;AAAA,IACnE;AACE,aAAO,oBAAoB,IAAI,OAAO,YAAY,IAAI,GAAG,GAAG,UAAU;AAAA,EAC1E;AACF;AAEO,SAAS,mBAAmB,GAAG,YAAY;AAChD,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,oBAAoB,gBAAgB,mBAAmB,UAAU;AAAA,IAC1E,KAAK;AACH,aAAO,oBAAoB,gBAAgB,iBAAiB,UAAU;AAAA,IACxE,KAAK;AACH,aAAO,oBAAoB,gBAAgB,mBAAmB,UAAU;AAAA,IAC1E,KAAK;AACH,aAAO,oBAAoB,gBAAgB,kBAAkB,UAAU;AAAA,IACzE;AACE,aAAO,oBAAoB,IAAI,OAAO,cAAc,IAAI,GAAG,GAAG,UAAU;AAAA,EAC5E;AACF;AAEO,SAAS,qBAAqB,WAAW;AAC9C,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL;AACE,aAAO;AAAA,EACX;AACF;AAEO,SAAS,sBAAsB,cAAc,aAAa;AAC/D,QAAM,cAAc,cAAc;AAKlC,QAAM,iBAAiB,cAAc,cAAc,IAAI;AAEvD,MAAI;AACJ,MAAI,kBAAkB,IAAI;AACxB,aAAS,gBAAgB;AAAA,EAC3B,OAAO;AACL,UAAM,WAAW,iBAAiB;AAClC,UAAM,kBAAkB,KAAK,MAAM,WAAW,GAAG,IAAI;AACrD,UAAM,oBAAoB,gBAAgB,WAAW;AACrD,aAAS,eAAe,mBAAmB,oBAAoB,MAAM;AAAA,EACvE;AAEA,SAAO,cAAc,SAAS,IAAI;AACpC;AAEO,SAAS,gBAAgB,MAAM;AACpC,SAAO,OAAO,QAAQ,KAAM,OAAO,MAAM,KAAK,OAAO,QAAQ;AAC/D;;;AC7HO,IAAM,aAAN,cAAyB,OAAO;AAAA,EAAhC;AAAA;AACL,oCAAW;AACX,8CAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EAEtE,MAAM,YAAY,OAAO,OAAO;AAC9B,UAAM,gBAAgB,CAAC,UAAU;AAAA,MAC/B;AAAA,MACA,gBAAgB,UAAU;AAAA,IAC5B;AAEA,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,SAAS,aAAa,GAAG,UAAU,GAAG,aAAa;AAAA,MAC5D,KAAK;AACH,eAAO;AAAA,UACL,MAAM,cAAc,YAAY;AAAA,YAC9B,MAAM;AAAA,UACR,CAAC;AAAA,UACD;AAAA,QACF;AAAA,MACF;AACE,eAAO,SAAS,aAAa,MAAM,QAAQ,UAAU,GAAG,aAAa;AAAA,IACzE;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,MAAM,kBAAkB,MAAM,OAAO;AAAA,EAC9C;AAAA,EAEA,IAAI,MAAM,OAAO,OAAO;AACtB,UAAM,cAAc,KAAK,YAAY;AAErC,QAAI,MAAM,gBAAgB;AACxB,YAAM,yBAAyB;AAAA,QAC7B,MAAM;AAAA,QACN;AAAA,MACF;AACA,WAAK,YAAY,wBAAwB,GAAG,CAAC;AAC7C,WAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,aAAO;AAAA,IACT;AAEA,UAAM,OACJ,EAAE,SAAS,UAAU,MAAM,QAAQ,IAAI,MAAM,OAAO,IAAI,MAAM;AAChE,SAAK,YAAY,MAAM,GAAG,CAAC;AAC3B,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AACF;;;ACpDO,IAAM,sBAAN,cAAkC,OAAO;AAAA,EAAzC;AAAA;AACL,oCAAW;AAmDX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EA/DA,MAAM,YAAY,OAAO,OAAO;AAC9B,UAAM,gBAAgB,CAAC,UAAU;AAAA,MAC/B;AAAA,MACA,gBAAgB,UAAU;AAAA,IAC5B;AAEA,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,SAAS,aAAa,GAAG,UAAU,GAAG,aAAa;AAAA,MAC5D,KAAK;AACH,eAAO;AAAA,UACL,MAAM,cAAc,YAAY;AAAA,YAC9B,MAAM;AAAA,UACR,CAAC;AAAA,UACD;AAAA,QACF;AAAA,MACF;AACE,eAAO,SAAS,aAAa,MAAM,QAAQ,UAAU,GAAG,aAAa;AAAA,IACzE;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,MAAM,kBAAkB,MAAM,OAAO;AAAA,EAC9C;AAAA,EAEA,IAAI,MAAM,OAAO,OAAO,SAAS;AAC/B,UAAM,cAAc,YAAY,MAAM,OAAO;AAE7C,QAAI,MAAM,gBAAgB;AACxB,YAAM,yBAAyB;AAAA,QAC7B,MAAM;AAAA,QACN;AAAA,MACF;AACA,WAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MACV;AACA,WAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,aAAO,YAAY,MAAM,OAAO;AAAA,IAClC;AAEA,UAAM,OACJ,EAAE,SAAS,UAAU,MAAM,QAAQ,IAAI,MAAM,OAAO,IAAI,MAAM;AAChE,SAAK,YAAY,MAAM,GAAG,QAAQ,qBAAqB;AACvD,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO,YAAY,MAAM,OAAO;AAAA,EAClC;AAiBF;;;ACpEO,IAAM,oBAAN,cAAgC,OAAO;AAAA,EAAvC;AAAA;AACL,oCAAW;AAiBX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EA/BA,MAAM,YAAY,OAAO;AACvB,QAAI,UAAU,KAAK;AACjB,aAAO,mBAAmB,GAAG,UAAU;AAAA,IACzC;AAEA,WAAO,mBAAmB,MAAM,QAAQ,UAAU;AAAA,EACpD;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,UAAM,kBAAkB,cAAc,MAAM,CAAC;AAC7C,oBAAgB,YAAY,OAAO,GAAG,CAAC;AACvC,oBAAgB,SAAS,GAAG,GAAG,GAAG,CAAC;AACnC,WAAO,eAAe,eAAe;AAAA,EACvC;AAmBF;;;ACtCO,IAAM,qBAAN,cAAiC,OAAO;AAAA,EAAxC;AAAA;AACL,oCAAW;AAgBX,8CAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EAd3E,MAAM,YAAY,OAAO;AACvB,QAAI,UAAU,KAAK;AACjB,aAAO,mBAAmB,GAAG,UAAU;AAAA,IACzC;AAEA,WAAO,mBAAmB,MAAM,QAAQ,UAAU;AAAA,EACpD;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,YAAY,OAAO,GAAG,CAAC;AAC5B,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAGF;;;AClBO,IAAM,gBAAN,cAA4B,OAAO;AAAA,EAAnC;AAAA;AACL,oCAAW;AA4DX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EAzEA,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA;AAAA,MAE9C,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,UAAU,CAAC;AAAA;AAAA,MAE5D,KAAK;AACH,eACE,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAIL,KAAK;AACH,eAAO,MAAM,QAAQ,YAAY;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eACE,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IAEP;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,UAAU,QAAQ,KAAK,GAAG,CAAC;AAChC,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAkBF;;;AC7EO,IAAM,0BAAN,cAAsC,OAAO;AAAA,EAA7C;AAAA;AACL,oCAAW;AA4DX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EAzEA,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA;AAAA,MAE9C,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,UAAU,CAAC;AAAA;AAAA,MAE5D,KAAK;AACH,eACE,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAIL,KAAK;AACH,eAAO,MAAM,QAAQ,YAAY;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eACE,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,QAAQ,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IAEP;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,UAAU,QAAQ,KAAK,GAAG,CAAC;AAChC,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAkBF;;;AC5EO,IAAM,cAAN,cAA0B,OAAO;AAAA,EAAjC;AAAA;AACL,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,oCAAW;AAAA;AAAA,EAEX,MAAM,YAAY,OAAO,OAAO;AAC9B,UAAM,gBAAgB,CAAC,UAAU,QAAQ;AAEzC,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO;AAAA,UACL,oBAAoB,gBAAgB,OAAO,UAAU;AAAA,UACrD;AAAA,QACF;AAAA;AAAA,MAEF,KAAK;AACH,eAAO,SAAS,aAAa,GAAG,UAAU,GAAG,aAAa;AAAA;AAAA,MAE5D,KAAK;AACH,eAAO;AAAA,UACL,MAAM,cAAc,YAAY;AAAA,YAC9B,MAAM;AAAA,UACR,CAAC;AAAA,UACD;AAAA,QACF;AAAA;AAAA,MAEF,KAAK;AACH,eACE,MAAM,MAAM,YAAY;AAAA,UACtB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,MAAM,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA;AAAA,MAItE,KAAK;AACH,eAAO,MAAM,MAAM,YAAY;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eACE,MAAM,MAAM,YAAY,EAAE,OAAO,QAAQ,SAAS,aAAa,CAAC,KAChE,MAAM,MAAM,YAAY;AAAA,UACtB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,MAAM,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA,IAExE;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,SAAS,OAAO,CAAC;AACtB,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AACF;;;AC/EO,IAAM,wBAAN,cAAoC,OAAO;AAAA,EAA3C;AAAA;AACL,oCAAW;AA+DX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EA3EA,MAAM,YAAY,OAAO,OAAO;AAC9B,UAAM,gBAAgB,CAAC,UAAU,QAAQ;AAEzC,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AACH,eAAO;AAAA,UACL,oBAAoB,gBAAgB,OAAO,UAAU;AAAA,UACrD;AAAA,QACF;AAAA;AAAA,MAEF,KAAK;AACH,eAAO,SAAS,aAAa,GAAG,UAAU,GAAG,aAAa;AAAA;AAAA,MAE5D,KAAK;AACH,eAAO;AAAA,UACL,MAAM,cAAc,YAAY;AAAA,YAC9B,MAAM;AAAA,UACR,CAAC;AAAA,UACD;AAAA,QACF;AAAA;AAAA,MAEF,KAAK;AACH,eACE,MAAM,MAAM,YAAY;AAAA,UACtB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,MAAM,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA;AAAA,MAItE,KAAK;AACH,eAAO,MAAM,MAAM,YAAY;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eACE,MAAM,MAAM,YAAY,EAAE,OAAO,QAAQ,SAAS,aAAa,CAAC,KAChE,MAAM,MAAM,YAAY;AAAA,UACtB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,MAAM,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA,IAExE;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,SAAS,OAAO,CAAC;AACtB,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAiBF;;;AC5EO,IAAM,kBAAN,cAA8B,OAAO;AAAA,EAArC;AAAA;AACL,oCAAW;AAqBX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EAjCA,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,oBAAoB,gBAAgB,MAAM,UAAU;AAAA,MAC7D,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,OAAO,CAAC;AAAA,MACzD;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO,SAAS;AAChC,WAAO,YAAY,QAAQ,MAAM,OAAO,OAAO,GAAG,OAAO;AAAA,EAC3D;AAiBF;;;ACrCO,IAAM,gBAAN,cAA4B,OAAO;AAAA,EAAnC;AAAA;AACL,oCAAW;AAqBX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EAlCA,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,oBAAoB,gBAAgB,MAAM,UAAU;AAAA,MAC7D,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,OAAO,CAAC;AAAA,MACzD;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,WAAO,eAAe,WAAW,MAAM,KAAK,CAAC;AAAA,EAC/C;AAkBF;;;ACrCA,IAAM,gBAAgB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACrE,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAC9C;AAGO,IAAM,aAAN,cAAyB,OAAO;AAAA,EAAhC;AAAA;AACL,oCAAW;AACX,uCAAc;AA8Bd,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EAzCA,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,oBAAoB,gBAAgB,MAAM,UAAU;AAAA,MAC7D,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,OAAO,CAAC;AAAA,MACzD;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,MAAM,OAAO;AACpB,UAAM,OAAO,KAAK,YAAY;AAC9B,UAAM,aAAa,gBAAgB,IAAI;AACvC,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,YAAY;AACd,aAAO,SAAS,KAAK,SAAS,wBAAwB,KAAK;AAAA,IAC7D,OAAO;AACL,aAAO,SAAS,KAAK,SAAS,cAAc,KAAK;AAAA,IACnD;AAAA,EACF;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,QAAQ,KAAK;AAClB,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAgBF;;;ACpDO,IAAM,kBAAN,cAA8B,OAAO;AAAA,EAArC;AAAA;AACL,oCAAW;AAEX,uCAAc;AA8Bd,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EA5CA,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,oBAAoB,gBAAgB,WAAW,UAAU;AAAA,MAClE,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,OAAO,CAAC;AAAA,MACzD;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,MAAM,OAAO;AACpB,UAAM,OAAO,KAAK,YAAY;AAC9B,UAAM,aAAa,gBAAgB,IAAI;AACvC,QAAI,YAAY;AACd,aAAO,SAAS,KAAK,SAAS;AAAA,IAChC,OAAO;AACL,aAAO,SAAS,KAAK,SAAS;AAAA,IAChC;AAAA,EACF;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,SAAS,GAAG,KAAK;AACtB,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAmBF;;;ACvDO,IAAM,YAAN,cAAwB,OAAO;AAAA,EAA/B;AAAA;AACL,oCAAW;AAuDX,8CAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EArDlD,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eACE,MAAM,IAAI,YAAY;AAAA,UACpB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,SAAS,aAAa,CAAC,KAC/D,MAAM,IAAI,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA;AAAA,MAIpE,KAAK;AACH,eAAO,MAAM,IAAI,YAAY;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eACE,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,SAAS,aAAa,CAAC,KAC/D,MAAM,IAAI,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA;AAAA,MAIpE,KAAK;AAAA,MACL;AACE,eACE,MAAM,IAAI,YAAY,EAAE,OAAO,QAAQ,SAAS,aAAa,CAAC,KAC9D,MAAM,IAAI,YAAY;AAAA,UACpB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,SAAS,aAAa,CAAC,KAC/D,MAAM,IAAI,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA,IAEtE;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO,SAAS;AAChC,WAAO,OAAO,MAAM,OAAO,OAAO;AAClC,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAGF;;;ACvDO,IAAM,iBAAN,cAA6B,OAAO;AAAA,EAApC;AAAA;AACL,oCAAW;AAsEX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EArFA,MAAM,YAAY,OAAO,OAAO,SAAS;AACvC,UAAM,gBAAgB,CAAC,UAAU;AAE/B,YAAM,gBAAgB,KAAK,OAAO,QAAQ,KAAK,CAAC,IAAI;AACpD,cAAS,QAAQ,QAAQ,eAAe,KAAK,IAAK;AAAA,IACpD;AAEA,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,aAAa,MAAM,QAAQ,UAAU,GAAG,aAAa;AAAA;AAAA,MAEvE,KAAK;AACH,eAAO;AAAA,UACL,MAAM,cAAc,YAAY;AAAA,YAC9B,MAAM;AAAA,UACR,CAAC;AAAA,UACD;AAAA,QACF;AAAA;AAAA,MAEF,KAAK;AACH,eACE,MAAM,IAAI,YAAY;AAAA,UACpB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,SAAS,aAAa,CAAC,KAC/D,MAAM,IAAI,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA;AAAA,MAIpE,KAAK;AACH,eAAO,MAAM,IAAI,YAAY;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eACE,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,SAAS,aAAa,CAAC,KAC/D,MAAM,IAAI,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA;AAAA,MAIpE,KAAK;AAAA,MACL;AACE,eACE,MAAM,IAAI,YAAY,EAAE,OAAO,QAAQ,SAAS,aAAa,CAAC,KAC9D,MAAM,IAAI,YAAY;AAAA,UACpB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,SAAS,aAAa,CAAC,KAC/D,MAAM,IAAI,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA,IAEtE;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO,SAAS;AAChC,WAAO,OAAO,MAAM,OAAO,OAAO;AAClC,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAmBF;;;ACxFO,IAAM,2BAAN,cAAuC,OAAO;AAAA,EAA9C;AAAA;AACL,oCAAW;AAuEX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EArFA,MAAM,YAAY,OAAO,OAAO,SAAS;AACvC,UAAM,gBAAgB,CAAC,UAAU;AAE/B,YAAM,gBAAgB,KAAK,OAAO,QAAQ,KAAK,CAAC,IAAI;AACpD,cAAS,QAAQ,QAAQ,eAAe,KAAK,IAAK;AAAA,IACpD;AAEA,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,aAAa,MAAM,QAAQ,UAAU,GAAG,aAAa;AAAA;AAAA,MAEvE,KAAK;AACH,eAAO;AAAA,UACL,MAAM,cAAc,YAAY;AAAA,YAC9B,MAAM;AAAA,UACR,CAAC;AAAA,UACD;AAAA,QACF;AAAA;AAAA,MAEF,KAAK;AACH,eACE,MAAM,IAAI,YAAY;AAAA,UACpB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,SAAS,aAAa,CAAC,KAC/D,MAAM,IAAI,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA;AAAA,MAIpE,KAAK;AACH,eAAO,MAAM,IAAI,YAAY;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA;AAAA,MAEH,KAAK;AACH,eACE,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,SAAS,aAAa,CAAC,KAC/D,MAAM,IAAI,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA;AAAA,MAIpE,KAAK;AAAA,MACL;AACE,eACE,MAAM,IAAI,YAAY,EAAE,OAAO,QAAQ,SAAS,aAAa,CAAC,KAC9D,MAAM,IAAI,YAAY;AAAA,UACpB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,IAAI,YAAY,EAAE,OAAO,SAAS,SAAS,aAAa,CAAC,KAC/D,MAAM,IAAI,YAAY,EAAE,OAAO,UAAU,SAAS,aAAa,CAAC;AAAA,IAEtE;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO,SAAS;AAChC,WAAO,OAAO,MAAM,OAAO,OAAO;AAClC,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAmBF;;;ACzFO,IAAM,eAAN,cAA2B,OAAO;AAAA,EAAlC;AAAA;AACL,oCAAW;AA4FX,8CAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,EA1GA,MAAM,YAAY,OAAO,OAAO;AAC9B,UAAM,gBAAgB,CAAC,UAAU;AAC/B,UAAI,UAAU,GAAG;AACf,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,OAAO;AAAA;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA;AAAA,MAE9C,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,MAAM,CAAC;AAAA;AAAA,MAExD,KAAK;AACH,eAAO;AAAA,UACL,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC,KACC,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC,KACD,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC;AAAA,UACH;AAAA,QACF;AAAA;AAAA,MAEF,KAAK;AACH,eAAO;AAAA,UACL,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,QACF;AAAA;AAAA,MAEF,KAAK;AACH,eAAO;AAAA,UACL,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC,KACC,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC;AAAA,UACH;AAAA,QACF;AAAA;AAAA,MAEF,KAAK;AAAA,MACL;AACE,eAAO;AAAA,UACL,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC,KACC,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC,KACD,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC,KACD,MAAM,IAAI,YAAY;AAAA,YACpB,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC;AAAA,UACH;AAAA,QACF;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,WAAO,UAAU,MAAM,KAAK;AAC5B,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,WAAO;AAAA,EACT;AAmBF;;;AChHO,IAAM,aAAN,cAAyB,OAAO;AAAA,EAAhC;AAAA;AACL,oCAAW;AA+CX,8CAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EA7ClD,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eACE,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGL,KAAK;AACH,eAAO,MAAM,UAAU,YAAY;AAAA,UACjC,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AAAA,MACL;AACE,eACE,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IAEP;AAAA,EACF;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,SAAS,qBAAqB,KAAK,GAAG,GAAG,GAAG,CAAC;AAClD,WAAO;AAAA,EACT;AAGF;;;ACjDO,IAAM,qBAAN,cAAiC,OAAO;AAAA,EAAxC;AAAA;AACL,oCAAW;AA+CX,8CAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EA7ClD,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eACE,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGL,KAAK;AACH,eAAO,MAAM,UAAU,YAAY;AAAA,UACjC,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AAAA,MACL;AACE,eACE,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IAEP;AAAA,EACF;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,SAAS,qBAAqB,KAAK,GAAG,GAAG,GAAG,CAAC;AAClD,WAAO;AAAA,EACT;AAGF;;;AChDO,IAAM,kBAAN,cAA8B,OAAO;AAAA,EAArC;AAAA;AACL,oCAAW;AA+CX,8CAAqB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EA7CxC,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eACE,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGL,KAAK;AACH,eAAO,MAAM,UAAU,YAAY;AAAA,UACjC,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AAAA,MACL;AACE,eACE,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,KACD,MAAM,UAAU,YAAY;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IAEP;AAAA,EACF;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,SAAS,qBAAqB,KAAK,GAAG,GAAG,GAAG,CAAC;AAClD,WAAO;AAAA,EACT;AAGF;;;ACjDO,IAAM,kBAAN,cAA8B,OAAO;AAAA,EAArC;AAAA;AACL,oCAAW;AA6BX,8CAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EA3B7C,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,oBAAoB,gBAAgB,SAAS,UAAU;AAAA,MAChE,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,OAAO,CAAC;AAAA,MACzD;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,UAAM,OAAO,KAAK,SAAS,KAAK;AAChC,QAAI,QAAQ,QAAQ,IAAI;AACtB,WAAK,SAAS,QAAQ,IAAI,GAAG,GAAG,CAAC;AAAA,IACnC,WAAW,CAAC,QAAQ,UAAU,IAAI;AAChC,WAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,IAC1B,OAAO;AACL,WAAK,SAAS,OAAO,GAAG,GAAG,CAAC;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAGF;;;AC/BO,IAAM,kBAAN,cAA8B,OAAO;AAAA,EAArC;AAAA;AACL,oCAAW;AAsBX,8CAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EApBvD,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,oBAAoB,gBAAgB,SAAS,UAAU;AAAA,MAChE,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,OAAO,CAAC;AAAA,MACzD;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,SAAS,OAAO,GAAG,GAAG,CAAC;AAC5B,WAAO;AAAA,EACT;AAGF;;;ACxBO,IAAM,kBAAN,cAA8B,OAAO;AAAA,EAArC;AAAA;AACL,oCAAW;AA2BX,8CAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EAzB7C,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,oBAAoB,gBAAgB,SAAS,UAAU;AAAA,MAChE,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,OAAO,CAAC;AAAA,MACzD;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,UAAM,OAAO,KAAK,SAAS,KAAK;AAChC,QAAI,QAAQ,QAAQ,IAAI;AACtB,WAAK,SAAS,QAAQ,IAAI,GAAG,GAAG,CAAC;AAAA,IACnC,OAAO;AACL,WAAK,SAAS,OAAO,GAAG,GAAG,CAAC;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAGF;;;AC7BO,IAAM,kBAAN,cAA8B,OAAO;AAAA,EAArC;AAAA;AACL,oCAAW;AAuBX,8CAAqB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EArBvD,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,oBAAoB,gBAAgB,SAAS,UAAU;AAAA,MAChE,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,OAAO,CAAC;AAAA,MACzD;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,UAAM,QAAQ,SAAS,KAAK,QAAQ,KAAK;AACzC,SAAK,SAAS,OAAO,GAAG,GAAG,CAAC;AAC5B,WAAO;AAAA,EACT;AAGF;;;ACzBO,IAAM,eAAN,cAA2B,OAAO;AAAA,EAAlC;AAAA;AACL,oCAAW;AAsBX,8CAAqB,CAAC,KAAK,GAAG;AAAA;AAAA,EApB9B,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,oBAAoB,gBAAgB,QAAQ,UAAU;AAAA,MAC/D,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,SAAS,CAAC;AAAA,MAC3D;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,WAAW,OAAO,GAAG,CAAC;AAC3B,WAAO;AAAA,EACT;AAGF;;;ACxBO,IAAM,eAAN,cAA2B,OAAO;AAAA,EAAlC;AAAA;AACL,oCAAW;AAsBX,8CAAqB,CAAC,KAAK,GAAG;AAAA;AAAA,EApB9B,MAAM,YAAY,OAAO,OAAO;AAC9B,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,oBAAoB,gBAAgB,QAAQ,UAAU;AAAA,MAC/D,KAAK;AACH,eAAO,MAAM,cAAc,YAAY,EAAE,MAAM,SAAS,CAAC;AAAA,MAC3D;AACE,eAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,SAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,WAAW,OAAO,CAAC;AACxB,WAAO;AAAA,EACT;AAGF;;;ACzBO,IAAM,yBAAN,cAAqC,OAAO;AAAA,EAA5C;AAAA;AACL,oCAAW;AAaX,8CAAqB,CAAC,KAAK,GAAG;AAAA;AAAA,EAX9B,MAAM,YAAY,OAAO;AACvB,UAAM,gBAAgB,CAAC,UACrB,KAAK,MAAM,QAAQ,KAAK,IAAI,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC;AACpD,WAAO,SAAS,aAAa,MAAM,QAAQ,UAAU,GAAG,aAAa;AAAA,EACvE;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,SAAK,gBAAgB,KAAK;AAC1B,WAAO;AAAA,EACT;AAGF;;;ACXO,IAAM,yBAAN,cAAqC,OAAO;AAAA,EAA5C;AAAA;AACL,oCAAW;AAmCX,8CAAqB,CAAC,KAAK,KAAK,GAAG;AAAA;AAAA,EAjCnC,MAAM,YAAY,OAAO;AACvB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO;AAAA,UACL,iBAAiB;AAAA,UACjB;AAAA,QACF;AAAA,MACF,KAAK;AACH,eAAO,qBAAqB,iBAAiB,OAAO,UAAU;AAAA,MAChE,KAAK;AACH,eAAO;AAAA,UACL,iBAAiB;AAAA,UACjB;AAAA,QACF;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,iBAAiB;AAAA,UACjB;AAAA,QACF;AAAA,MACF,KAAK;AAAA,MACL;AACE,eAAO,qBAAqB,iBAAiB,UAAU,UAAU;AAAA,IACrE;AAAA,EACF;AAAA,EAEA,IAAI,MAAM,OAAO,OAAO;AACtB,QAAI,MAAM,eAAgB,QAAO;AACjC,WAAO;AAAA,MACL;AAAA,MACA,KAAK,QAAQ,IAAI,gCAAgC,IAAI,IAAI;AAAA,IAC3D;AAAA,EACF;AAGF;;;ACrCO,IAAM,oBAAN,cAAgC,OAAO;AAAA,EAAvC;AAAA;AACL,oCAAW;AAmCX,8CAAqB,CAAC,KAAK,KAAK,GAAG;AAAA;AAAA,EAjCnC,MAAM,YAAY,OAAO;AACvB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO;AAAA,UACL,iBAAiB;AAAA,UACjB;AAAA,QACF;AAAA,MACF,KAAK;AACH,eAAO,qBAAqB,iBAAiB,OAAO,UAAU;AAAA,MAChE,KAAK;AACH,eAAO;AAAA,UACL,iBAAiB;AAAA,UACjB;AAAA,QACF;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,iBAAiB;AAAA,UACjB;AAAA,QACF;AAAA,MACF,KAAK;AAAA,MACL;AACE,eAAO,qBAAqB,iBAAiB,UAAU,UAAU;AAAA,IACrE;AAAA,EACF;AAAA,EAEA,IAAI,MAAM,OAAO,OAAO;AACtB,QAAI,MAAM,eAAgB,QAAO;AACjC,WAAO;AAAA,MACL;AAAA,MACA,KAAK,QAAQ,IAAI,gCAAgC,IAAI,IAAI;AAAA,IAC3D;AAAA,EACF;AAGF;;;ACxCO,IAAM,yBAAN,cAAqC,OAAO;AAAA,EAA5C;AAAA;AACL,oCAAW;AAUX,8CAAqB;AAAA;AAAA,EARrB,MAAM,YAAY;AAChB,WAAO,qBAAqB,UAAU;AAAA,EACxC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,WAAO,CAAC,cAAc,MAAM,QAAQ,GAAI,GAAG,EAAE,gBAAgB,KAAK,CAAC;AAAA,EACrE;AAGF;;;ACZO,IAAM,8BAAN,cAA0C,OAAO;AAAA,EAAjD;AAAA;AACL,oCAAW;AAUX,8CAAqB;AAAA;AAAA,EARrB,MAAM,YAAY;AAChB,WAAO,qBAAqB,UAAU;AAAA,EACxC;AAAA,EAEA,IAAI,MAAM,QAAQ,OAAO;AACvB,WAAO,CAAC,cAAc,MAAM,KAAK,GAAG,EAAE,gBAAgB,KAAK,CAAC;AAAA,EAC9D;AAGF;;;AC0DO,IAAM,UAAU;AAAA,EACrB,GAAG,IAAI,UAAU;AAAA,EACjB,GAAG,IAAI,WAAW;AAAA,EAClB,GAAG,IAAI,oBAAoB;AAAA,EAC3B,GAAG,IAAI,kBAAkB;AAAA,EACzB,GAAG,IAAI,mBAAmB;AAAA,EAC1B,GAAG,IAAI,cAAc;AAAA,EACrB,GAAG,IAAI,wBAAwB;AAAA,EAC/B,GAAG,IAAI,YAAY;AAAA,EACnB,GAAG,IAAI,sBAAsB;AAAA,EAC7B,GAAG,IAAI,gBAAgB;AAAA,EACvB,GAAG,IAAI,cAAc;AAAA,EACrB,GAAG,IAAI,WAAW;AAAA,EAClB,GAAG,IAAI,gBAAgB;AAAA,EACvB,GAAG,IAAI,UAAU;AAAA,EACjB,GAAG,IAAI,eAAe;AAAA,EACtB,GAAG,IAAI,yBAAyB;AAAA,EAChC,GAAG,IAAI,aAAa;AAAA,EACpB,GAAG,IAAI,WAAW;AAAA,EAClB,GAAG,IAAI,mBAAmB;AAAA,EAC1B,GAAG,IAAI,gBAAgB;AAAA,EACvB,GAAG,IAAI,gBAAgB;AAAA,EACvB,GAAG,IAAI,gBAAgB;AAAA,EACvB,GAAG,IAAI,gBAAgB;AAAA,EACvB,GAAG,IAAI,gBAAgB;AAAA,EACvB,GAAG,IAAI,aAAa;AAAA,EACpB,GAAG,IAAI,aAAa;AAAA,EACpB,GAAG,IAAI,uBAAuB;AAAA,EAC9B,GAAG,IAAI,uBAAuB;AAAA,EAC9B,GAAG,IAAI,kBAAkB;AAAA,EACzB,GAAG,IAAI,uBAAuB;AAAA,EAC9B,GAAG,IAAI,4BAA4B;AACrC;;;AC1EA,IAAMC,0BACJ;AAIF,IAAMC,8BAA6B;AAEnC,IAAMC,uBAAsB;AAC5B,IAAMC,qBAAoB;AAE1B,IAAM,sBAAsB;AAC5B,IAAMC,iCAAgC;AA4S/B,SAAS,MAAM,SAAS,WAAW,eAAe,SAAS;AAxVlE;AAyVE,QAAM,cAAc,MAAM,eAAc,mCAAS,OAAM,eAAe,GAAG;AACzE,QAAM,iBAAiBC,mBAAkB;AACzC,QAAM,UAAS,mCAAS,WAAU,eAAe,UAAU;AAE3D,QAAM,yBACJ,mCAAS,4BACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,0BAC1B,eAAe,2BACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,0BAChC;AAEF,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1B,eAAe,kBACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,MAAI,CAAC;AACH,WAAO,UAAU,YAAY,IAAI,OAAO,eAAe,mCAAS,EAAE;AAEpE,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAIA,QAAM,UAAU,CAAC,IAAI,mBAAmB,mCAAS,IAAI,aAAa,CAAC;AAEnE,QAAM,SAAS,UACZ,MAAMJ,2BAA0B,EAChC,IAAI,CAAC,cAAc;AAClB,UAAM,iBAAiB,UAAU,CAAC;AAClC,QAAI,kBAAkB,gBAAgB;AACpC,YAAM,gBAAgB,eAAe,cAAc;AACnD,aAAO,cAAc,WAAW,OAAO,UAAU;AAAA,IACnD;AACA,WAAO;AAAA,EACT,CAAC,EACA,KAAK,EAAE,EACP,MAAMD,uBAAsB;AAE/B,QAAM,aAAa,CAAC;AAEpB,WAAS,SAAS,QAAQ;AACxB,QACE,EAAC,mCAAS,gCACV,yBAAyB,KAAK,GAC9B;AACA,gCAA0B,OAAO,WAAW,OAAO;AAAA,IACrD;AACA,QACE,EAAC,mCAAS,iCACV,0BAA0B,KAAK,GAC/B;AACA,gCAA0B,OAAO,WAAW,OAAO;AAAA,IACrD;AAEA,UAAM,iBAAiB,MAAM,CAAC;AAC9B,UAAM,SAAS,QAAQ,cAAc;AACrC,QAAI,QAAQ;AACV,YAAM,EAAE,mBAAmB,IAAI;AAC/B,UAAI,MAAM,QAAQ,kBAAkB,GAAG;AACrC,cAAM,oBAAoB,WAAW;AAAA,UACnC,CAAC,cACC,mBAAmB,SAAS,UAAU,KAAK,KAC3C,UAAU,UAAU;AAAA,QACxB;AACA,YAAI,mBAAmB;AACrB,gBAAM,IAAI;AAAA,YACR,uCAAuC,kBAAkB,SAAS,YAAY,KAAK;AAAA,UACrF;AAAA,QACF;AAAA,MACF,WAAW,OAAO,uBAAuB,OAAO,WAAW,SAAS,GAAG;AACrE,cAAM,IAAI;AAAA,UACR,uCAAuC,KAAK;AAAA,QAC9C;AAAA,MACF;AAEA,iBAAW,KAAK,EAAE,OAAO,gBAAgB,WAAW,MAAM,CAAC;AAE3D,YAAM,cAAc,OAAO;AAAA,QACzB;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP;AAAA,MACF;AAEA,UAAI,CAAC,aAAa;AAChB,eAAO,YAAY;AAAA,MACrB;AAEA,cAAQ,KAAK,YAAY,MAAM;AAE/B,gBAAU,YAAY;AAAA,IACxB,OAAO;AACL,UAAI,eAAe,MAAMI,8BAA6B,GAAG;AACvD,cAAM,IAAI;AAAA,UACR,mEACE,iBACA;AAAA,QACJ;AAAA,MACF;AAGA,UAAI,UAAU,MAAM;AAClB,gBAAQ;AAAA,MACV,WAAW,mBAAmB,KAAK;AACjC,gBAAQE,oBAAmB,KAAK;AAAA,MAClC;AAGA,UAAI,QAAQ,QAAQ,KAAK,MAAM,GAAG;AAChC,kBAAU,QAAQ,MAAM,MAAM,MAAM;AAAA,MACtC,OAAO;AACL,eAAO,YAAY;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAGA,MAAI,QAAQ,SAAS,KAAK,oBAAoB,KAAK,OAAO,GAAG;AAC3D,WAAO,YAAY;AAAA,EACrB;AAEA,QAAM,wBAAwB,QAC3B,IAAI,CAAC,WAAW,OAAO,QAAQ,EAC/B,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,EACpB,OAAO,CAAC,UAAU,OAAO,UAAU,MAAM,QAAQ,QAAQ,MAAM,KAAK,EACpE;AAAA,IAAI,CAAC,aACJ,QACG,OAAO,CAAC,WAAW,OAAO,aAAa,QAAQ,EAC/C,KAAK,CAAC,GAAG,MAAM,EAAE,cAAc,EAAE,WAAW;AAAA,EACjD,EACC,IAAI,CAAC,gBAAgB,YAAY,CAAC,CAAC;AAEtC,MAAI,OAAO,OAAO,eAAe,mCAAS,EAAE;AAE5C,MAAI,MAAM,CAAC,IAAI,EAAG,QAAO,YAAY;AAErC,QAAM,QAAQ,CAAC;AACf,aAAW,UAAU,uBAAuB;AAC1C,QAAI,CAAC,OAAO,SAAS,MAAM,YAAY,GAAG;AACxC,aAAO,YAAY;AAAA,IACrB;AAEA,UAAM,SAAS,OAAO,IAAI,MAAM,OAAO,YAAY;AAEnD,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAO,OAAO,CAAC;AACf,aAAO,OAAO,OAAO,OAAO,CAAC,CAAC;AAAA,IAEhC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAASA,oBAAmB,OAAO;AACjC,SAAO,MAAM,MAAMJ,oBAAmB,EAAE,CAAC,EAAE,QAAQC,oBAAmB,GAAG;AAC3E;;;ACjeO,SAAS,QAAQ,MAAM,YAAY,SAAS;AACjD,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,QAAQ,UAAU;AACxB,SAAO;AACT;;;ACJO,SAAS,SAAS,MAAM,OAAO,SAAS;AAC7C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,SAAS,KAAK;AACpB,SAAO;AACT;;;ACJO,SAAS,WAAW,MAAM,SAAS,SAAS;AACjD,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,WAAW,OAAO;AACxB,SAAO;AACT;;;ACFO,SAAS,SAAS,MAAM,OAAO,SAAS;AAC7C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,MAAM,MAAM,QAAQ;AAE1B,QAAM,WAAW,eAAc,mCAAS,OAAM,MAAM,CAAC;AACrD,WAAS,YAAY,MAAM,OAAO,EAAE;AACpC,WAAS,SAAS,GAAG,GAAG,GAAG,CAAC;AAC5B,QAAM,cAAc,eAAe,QAAQ;AAG3C,QAAM,SAAS,OAAO,KAAK,IAAI,KAAK,WAAW,CAAC;AAChD,SAAO;AACT;;;ACfO,SAAS,WAAW,MAAM,SAAS,SAAS;AACjD,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,WAAW,OAAO;AACxB,SAAO;AACT;;;ACJO,SAAS,gBAAgB,MAAM,cAAc,SAAS;AAC3D,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,gBAAgB,YAAY;AAClC,SAAO;AACT;;;ACHO,SAAS,QAAQ,MAAM,MAAM,SAAS;AAC3C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AAGtC,MAAI,MAAM,CAAC,KAAK,EAAG,QAAO,eAAc,mCAAS,OAAM,MAAM,GAAG;AAEhE,QAAM,YAAY,IAAI;AACtB,SAAO;AACT;;;ACRO,SAAS,aAAa,MAAM,SAAS;AAC1C,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,QAAQ,CAAC;AACf,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;;;ACNO,SAAS,WAAW,MAAM,SAAS;AACxC,QAAM,QAAQ,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,QAAQ,MAAM,SAAS;AAC7B,QAAM,YAAY,MAAM,YAAY,GAAG,QAAQ,GAAG,CAAC;AACnD,QAAM,SAAS,IAAI,IAAI,IAAI,GAAG;AAC9B,SAAO;AACT;;;ACYO,SAAS,iBAAiB,MAAM,UAAU,SAAS;AACxD,QAAM,OAAO,CAAC,OAAO,MAAM,mCAAS,EAAE;AACtC,QAAM,CAAC,WAAW,OAAO,IAAI;AAAA,IAC3B,CAAC,OAAO,SAAS,OAAO,mCAAS,EAAE;AAAA,IACnC,CAAC,OAAO,SAAS,KAAK,mCAAS,EAAE;AAAA,EACnC,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAEtB,SAAO,QAAQ,aAAa,QAAQ;AACtC;", "names": ["format", "getDefaultOptions", "formattingTokensRegExp", "longFormattingTokensRegExp", "escapedStringRegExp", "doubleQuoteRegExp", "unescapedLatinCharacterRegExp", "getDefaultOptions", "cleanEscapedString"]}