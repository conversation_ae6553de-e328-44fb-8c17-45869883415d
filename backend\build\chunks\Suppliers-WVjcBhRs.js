import{e,F as s,j as t,G as r,a as o,z as a,H as i,c as l}from"../entries/index-xsXxT3-W.js";import{d as n,r as c}from"./router-BtYqujaw.js";import{L as m}from"./Layout-DaeN7D4t.js";import{s as d}from"./suppliers-Dwe__EFi.js";import{S as p}from"./Search-BT-I8ZrW.js";import{s as u}from"./supplier-list-BiBQJaIV.js";import{g as j,d as S}from"./SupplierService-9DC5V5ZJ.js";import{P as h}from"./Pager-B4DUIA8f.js";import{C as x,a as I}from"./ArrowForwardIos-BCaVe-sv.js";import{T as E}from"./Backdrop-Czag2Ija.js";import{T as N}from"./Tooltip-CKMkVqOx.js";import{I as f}from"./IconButton-CxOCoGF3.js";import{D as _}from"./Delete-BfnPAJno.js";import{E as P}from"./Edit-Bc0UCPtn.js";import{V as D}from"./Visibility-D3efFHY1.js";import{D as T,a as g,b as C,d as y}from"./Grow-Cp8xsNYl.js";import{B as L}from"./Button-BeKLLPpp.js";import{I as b}from"./InfoBox-DNJEsGlP.js";import"./vendor-dblfw9z9.js";import"./zod-4O8Zwsja.js";import"./TextField-D_yQOTzE.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-DiTut-u0.js";import"./OutlinedInput-BX8yFQbF.js";import"./InputLabel-C8rcdOGQ.js";import"./isHostComponent-DR4iSCFs.js";import"./ownerWindow-ChLfdzZL.js";import"./Input-D1AdR9CM.js";import"./FormHelperText-DDZ4BMA4.js";import"./Menu-C_-X8cS7.js";import"./Paper-C-atefOs.js";import"./mergeSlotProps-DEridHif.js";import"./Clear-CDOl64hX.js";import"./Search-CKOds7xB.js";import"./Info-CNP9gYBt.js";const w=({user:l,keyword:m,onDelete:d,onLoad:p})=>{const b=n(),[w,A]=c.useState(m),[O,v]=c.useState(!0),[R,M]=c.useState(!1),[F,G]=c.useState(!1),[k,U]=c.useState([]),[$,H]=c.useState(0),[B,z]=c.useState(0),[W,Z]=c.useState(1),[V,Y]=c.useState(!1),[q,J]=c.useState(""),[K,Q]=c.useState(-1),X=async(s,t)=>{try{M(!0);const r=await j(t||"",s,e.PAGE_SIZE),o=r&&r.length>0?r[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!o)return void a();const l=Array.isArray(o.pageInfo)&&o.pageInfo.length>0?o.pageInfo[0].totalRecords:0;let n=[];n=e.PAGINATION_MODE===i.PAGINATION_MODE.INFINITE_SCROLL||e.isMobile?1===s?o.resultData:[...k,...o.resultData]:o.resultData,U(n),H((s-1)*e.PAGE_SIZE+n.length),z(l),G(o.resultData.length>0),((e.PAGINATION_MODE===i.PAGINATION_MODE.INFINITE_SCROLL||e.isMobile)&&1===s||e.PAGINATION_MODE===i.PAGINATION_MODE.CLASSIC&&!e.isMobile)&&window.scrollTo(0,0),p&&p({rows:o.resultData,rowCount:l})}catch(r){a(r)}finally{M(!1),v(!1)}};c.useEffect((()=>{m!==w&&X(1,m),A(m||"")}),[m,w]),c.useEffect((()=>{X(W,w)}),[W]),c.useEffect((()=>{if(e.isMobile){const s=document.querySelector("body");s&&(s.onscroll=()=>{F&&!R&&window.scrollY>0&&window.scrollY+window.innerHeight+e.INFINITE_SCROLL_OFFSET>=document.body.scrollHeight&&(M(!0),Z(W+1))})}}),[F,R,W,w]);const ee=e=>{const s=e.currentTarget.getAttribute("data-id"),t=Number(e.currentTarget.getAttribute("data-index"));Y(!0),J(s),Q(t)},se=s(l);return t.jsxs(t.Fragment,{children:[t.jsxs("section",{className:"supplier-list",children:[0===k.length?!O&&!R&&t.jsx(x,{variant:"outlined",className:"empty-list",children:t.jsx(I,{children:t.jsx(E,{color:"textSecondary",children:u.EMPTY_LIST})})}):k.map(((s,a)=>{const i=se||l&&l._id===s._id,n=se;return t.jsxs("article",{children:[t.jsxs("div",{className:"supplier-item",children:[t.jsx("div",{className:"supplier-item-avatar",children:t.jsx("img",{src:r(e.CDN_USERS,s.avatar),alt:s.fullName})}),t.jsx("span",{className:"supplier-item-title",children:s.fullName}),null!=s.dressCount?t.jsx("span",{className:"supplier-item-subtitle",children:`${s.dressCount} ${s.dressCount>1?o.DRESSES:o.DRESS}`}):null]}),t.jsxs("div",{className:"supplier-actions",children:[n&&t.jsx(N,{title:o.DELETE,children:t.jsx(f,{"data-id":s._id,"data-index":a,onClick:ee,children:t.jsx(_,{})})}),i&&t.jsx(N,{title:o.UPDATE,children:t.jsx(f,{onClick:()=>b(`/update-supplier?c=${s._id}`),children:t.jsx(P,{})})}),t.jsx(N,{title:u.VIEW_SUPPLIER,children:t.jsx(f,{onClick:()=>b(`/supplier?c=${s._id}`),children:t.jsx(D,{})})})]})]},s._id)})),t.jsxs(T,{disableEscapeKeyDown:!0,maxWidth:"xs",open:V,children:[t.jsx(g,{className:"dialog-header",children:o.CONFIRM_TITLE}),t.jsx(C,{children:u.DELETE_SUPPLIER}),t.jsxs(y,{className:"dialog-actions",children:[t.jsx(L,{onClick:()=>{Y(!1),J(""),Q(-1)},variant:"contained",className:"btn-secondary",children:o.CANCEL}),t.jsx(L,{onClick:async()=>{try{if(""!==q&&K>-1)if(M(!1),Y(!1),200===await S(q)){const e=$-1;k.splice(K,1),U(k),H(e),z(B-1),J(""),Q(-1),M(!1),d&&d(e)}else a(),J(""),Q(-1),M(!1);else a(),Y(!1),J(""),Q(-1),M(!1)}catch(e){a(e)}},variant:"contained",color:"error",children:o.DELETE})]})]})]}),!e.isMobile&&t.jsx(h,{page:W,pageSize:e.PAGE_SIZE,rowCount:$,totalRecords:B,onNext:()=>Z(W+1),onPrevious:()=>Z(W-1)})]})},A=()=>{const e=l.c(14),r=n(),[o,a]=c.useState(),[i,u]=c.useState(""),[j,S]=c.useState(-1);let h;e[0]===Symbol.for("react.memo_cache_sentinel")?(h=e=>{u(e)},e[0]=h):h=e[0];const x=h;let I;e[1]===Symbol.for("react.memo_cache_sentinel")?(I=e=>{e&&S(e.rowCount)},e[1]=I):I=e[1];const E=I;let N;e[2]===Symbol.for("react.memo_cache_sentinel")?(N=e=>{S(e)},e[2]=N):N=e[2];const f=N;let _;e[3]===Symbol.for("react.memo_cache_sentinel")?(_=e=>{a(e)},e[3]=_):_=e[3];const P=_;let D;e[4]!==o?(D=s(o),e[4]=o,e[5]=D):D=e[5];const T=D;let g,C;return e[6]!==T||e[7]!==i||e[8]!==r||e[9]!==j||e[10]!==o?(g=o&&t.jsxs("div",{className:"suppliers",children:[t.jsx("div",{className:"col-1",children:t.jsxs("div",{className:"col-1-container",children:[t.jsx(p,{className:"search",onSubmit:x}),j>-1&&T&&t.jsx(L,{type:"submit",variant:"contained",className:"btn-primary new-supplier",size:"small",onClick:()=>r("/create-supplier"),children:d.NEW_SUPPLIER}),j>0&&t.jsx(b,{value:`${j} ${j>1?d.SUPPLIERS:d.SUPPLIER}`,className:"supplier-count"})]})}),t.jsx("div",{className:"col-2",children:t.jsx(w,{user:o,keyword:i,onLoad:E,onDelete:f})})]}),e[6]=T,e[7]=i,e[8]=r,e[9]=j,e[10]=o,e[11]=g):g=e[11],e[12]!==g?(C=t.jsx(m,{onLoad:P,strict:!0,children:g}),e[12]=g,e[13]=C):C=e[13],C};export{A as default};
