import{b as e,s,a,aN as t,j as r,aO as o,e as i,z as n,aP as l,B as m,c}from"../entries/index-xsXxT3-W.js";import{d,r as E}from"./router-BtYqujaw.js";import{L as u}from"./Layout-DaeN7D4t.js";import{z as j,u as S,s as C}from"./zod-4O8Zwsja.js";import{P as N}from"./Paper-C-atefOs.js";import{F as p,I as b}from"./InputLabel-C8rcdOGQ.js";import{O as f}from"./OutlinedInput-BX8yFQbF.js";import{F as A}from"./FormHelperText-DDZ4BMA4.js";import{B as x,C as h}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-DiTut-u0.js";const g=new e({fr:{CONTACT_HEADING:"Contact",SUBJECT:"Objet",MESSAGE:"Message",SEND:"Envoyer",MESSAGE_SENT:"Message envoyé"},en:{CONTACT_HEADING:"Contact",SUBJECT:"Subject",MESSAGE:"Message",SEND:"Send",MESSAGE_SENT:"Message sent"},es:{CONTACT_HEADING:"Contacto",SUBJECT:"Asunto",MESSAGE:"Mensaje",SEND:"Enviar",MESSAGE_SENT:"Mensaje enviado"}});s(g);const T=j.object({email:j.string().email({message:a.EMAIL_NOT_VALID}),subject:j.string(),message:j.string()}),M=({user:e,className:s})=>{const c=d(),{reCaptchaLoaded:u,generateReCaptchaToken:j}=t(),[M,_]=E.useState(!1),{register:G,setValue:I,handleSubmit:y,reset:L,formState:{errors:v,isSubmitting:D},clearErrors:O}=S({resolver:C(T),mode:"onSubmit"}),B=E.useCallback((e=>{e&&(_(!0),I("email",e.email))}),[I]);return E.useEffect((()=>{B(e)}),[B,e]),r.jsxs(N,{className:(s?`${s} `:"")+"contact-form",elevation:10,children:[r.jsx("h1",{className:"contact-form-title",children:g.CONTACT_HEADING}),r.jsxs("form",{onSubmit:y((async s=>{try{console.log("boo");let a="";u&&(a=await j(),await o(a)||(a="")),i.RECAPTCHA_ENABLED;const t={from:s.email,to:i.CONTACT_EMAIL,subject:s.subject,message:s.message,isContactForm:!0};200===await l(t)?(L(),B(e),m(g.MESSAGE_SENT)):n()}catch(a){n(a)}})),children:[!M&&r.jsxs(p,{fullWidth:!0,margin:"dense",error:!!v.email,children:[r.jsx(b,{className:"required",children:a.EMAIL}),r.jsx(f,{type:"text",...G("email"),label:a.EMAIL,required:!0,autoComplete:"off",onChange:()=>O()}),r.jsx(A,{error:!!v.email,children:v.email?.message||""})]}),r.jsxs(p,{fullWidth:!0,margin:"dense",children:[r.jsx(b,{className:"required",children:g.SUBJECT}),r.jsx(f,{type:"text",...G("subject"),label:g.SUBJECT,required:!0,autoComplete:"off"})]}),r.jsxs(p,{fullWidth:!0,margin:"dense",children:[r.jsx(b,{className:"required",children:g.MESSAGE}),r.jsx(f,{type:"text",label:g.MESSAGE,...G("message"),autoComplete:"off",required:!0,multiline:!0,minRows:7,maxRows:7})]}),r.jsxs("div",{className:"buttons",children:[r.jsx(x,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom btn","aria-label":"Send",disabled:D,children:D?r.jsx(h,{color:"inherit",size:24}):g.SEND}),r.jsx(x,{variant:"outlined",color:"primary",className:"btn-margin-bottom btn","aria-label":"Cancel",onClick:()=>{c("/")},children:a.CANCEL})]})]})]})},_=()=>{const e=c.c(3),[s,a]=E.useState();let t;e[0]===Symbol.for("react.memo_cache_sentinel")?(t=e=>{a(e)},e[0]=t):t=e[0];const o=t;let i;return e[1]!==s?(i=r.jsx(u,{onLoad:o,strict:!0,children:r.jsx("div",{className:"contact",children:r.jsx(M,{user:s,className:"form"})})}),e[1]=s,e[2]=i):i=e[2],i};export{_ as default};
