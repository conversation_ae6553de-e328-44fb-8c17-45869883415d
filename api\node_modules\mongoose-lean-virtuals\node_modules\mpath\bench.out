*b566c26: Fri Mar 15 2013 14:14:04 GMT-0700 (PDT)
mpath.get("first", obj) x 3,827,405 ops/sec ±0.91% (90 runs sampled)
mpath.get("first.second", obj) x 4,930,222 ops/sec ±1.92% (91 runs sampled)
mpath.get("first.second.third.1.name", obj) x 3,070,837 ops/sec ±1.45% (97 runs sampled)
mpath.get("comments", obj) x 3,649,771 ops/sec ±1.71% (93 runs sampled)
mpath.get("comments.1", obj) x 3,846,728 ops/sec ±0.86% (94 runs sampled)
mpath.get("comments.2.name", obj) x 3,527,680 ops/sec ±0.95% (96 runs sampled)
mpath.get("comments.2.comments.1.comments.0.val", obj) x 2,046,982 ops/sec ±0.80% (96 runs sampled)
mpath.get("comments.name", obj) x 625,546 ops/sec ±2.02% (82 runs sampled)
*e42bdb1: Fri Mar 15 2013 14:19:28 GMT-0700 (PDT)
mpath.get("first", obj) x 3,700,783 ops/sec ±1.30% (95 runs sampled)
mpath.get("first.second", obj) x 4,621,795 ops/sec ±0.86% (95 runs sampled)
mpath.get("first.second.third.1.name", obj) x 3,012,671 ops/sec ±1.21% (100 runs sampled)
mpath.get("comments", obj) x 3,677,694 ops/sec ±0.80% (96 runs sampled)
mpath.get("comments.1", obj) x 3,798,862 ops/sec ±0.81% (91 runs sampled)
mpath.get("comments.2.name", obj) x 3,489,356 ops/sec ±0.66% (98 runs sampled)
mpath.get("comments.2.comments.1.comments.0.val", obj) x 2,004,076 ops/sec ±0.85% (99 runs sampled)
mpath.get("comments.name", obj) x 613,270 ops/sec ±1.33% (83 runs sampled)
*0521aac: Fri Mar 15 2013 16:37:16 GMT-0700 (PDT)
mpath.get("first", obj) x 3,834,755 ops/sec ±0.70% (100 runs sampled)
mpath.get("first.second", obj) x 4,999,965 ops/sec ±1.01% (98 runs sampled)
mpath.get("first.second.third.1.name", obj) x 3,125,953 ops/sec ±0.97% (100 runs sampled)
mpath.get("comments", obj) x 3,759,233 ops/sec ±0.81% (97 runs sampled)
mpath.get("comments.1", obj) x 3,894,893 ops/sec ±0.76% (96 runs sampled)
mpath.get("comments.2.name", obj) x 3,576,929 ops/sec ±0.68% (98 runs sampled)
mpath.get("comments.2.comments.1.comments.0.val", obj) x 2,149,610 ops/sec ±0.67% (97 runs sampled)
mpath.get("comments.name", obj) x 629,259 ops/sec ±1.30% (87 runs sampled)
mpath.set("first", obj, val) x 2,869,477 ops/sec ±0.63% (97 runs sampled)
mpath.set("first.second", obj, val) x 2,418,751 ops/sec ±0.62% (98 runs sampled)
mpath.set("first.second.third.1.name", obj, val) x 2,313,099 ops/sec ±0.69% (94 runs sampled)
mpath.set("comments", obj, val) x 2,680,882 ops/sec ±0.76% (99 runs sampled)
mpath.set("comments.1", obj, val) x 2,401,829 ops/sec ±0.68% (98 runs sampled)
mpath.set("comments.2.name", obj, val) x 2,335,081 ops/sec ±1.07% (96 runs sampled)
mpath.set("comments.2.comments.1.comments.0.val", obj, val) x 2,245,436 ops/sec ±0.76% (92 runs sampled)
mpath.set("comments.name", obj, val) x 2,356,278 ops/sec ±1.15% (100 runs sampled)
*97e85d3: Fri Mar 15 2013 16:39:21 GMT-0700 (PDT)
mpath.get("first", obj) x 3,837,614 ops/sec ±0.74% (99 runs sampled)
mpath.get("first.second", obj) x 4,991,779 ops/sec ±1.01% (94 runs sampled)
mpath.get("first.second.third.1.name", obj) x 3,078,455 ops/sec ±1.17% (96 runs sampled)
mpath.get("comments", obj) x 3,770,961 ops/sec ±0.45% (101 runs sampled)
mpath.get("comments.1", obj) x 3,832,814 ops/sec ±0.67% (92 runs sampled)
mpath.get("comments.2.name", obj) x 3,536,436 ops/sec ±0.49% (100 runs sampled)
mpath.get("comments.2.comments.1.comments.0.val", obj) x 2,141,947 ops/sec ±0.72% (98 runs sampled)
mpath.get("comments.name", obj) x 667,898 ops/sec ±1.62% (85 runs sampled)
mpath.set("first", obj, val) x 2,642,517 ops/sec ±0.72% (98 runs sampled)
mpath.set("first.second", obj, val) x 2,502,124 ops/sec ±1.28% (99 runs sampled)
mpath.set("first.second.third.1.name", obj, val) x 2,426,804 ops/sec ±0.55% (99 runs sampled)
mpath.set("comments", obj, val) x 2,699,478 ops/sec ±0.85% (98 runs sampled)
mpath.set("comments.1", obj, val) x 2,494,454 ops/sec ±1.05% (96 runs sampled)
mpath.set("comments.2.name", obj, val) x 2,463,894 ops/sec ±0.86% (98 runs sampled)
mpath.set("comments.2.comments.1.comments.0.val", obj, val) x 2,320,398 ops/sec ±0.82% (95 runs sampled)
mpath.set("comments.name", obj, val) x 2,512,408 ops/sec ±0.77% (95 runs sampled)
