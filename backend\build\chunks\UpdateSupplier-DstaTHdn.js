import{F as e,j as s,R as i,a as r,U as a,z as t,J as n,A as o,e as l,B as m}from"../entries/index-xsXxT3-W.js";import{d as c,r as d}from"./router-BtYqujaw.js";import{u as p,a as u,s as h}from"./zod-4O8Zwsja.js";import{C as j,s as f}from"./ContractList-hpnQO1ah.js";import{L as x}from"./Layout-DaeN7D4t.js";import{s as N}from"./create-supplier-BpB8o_Zh.js";import{a as g,v as y,u as R}from"./SupplierService-9DC5V5ZJ.js";import{E as C}from"./Error-DRzAdbbx.js";import{S as D}from"./SimpleBackdrop-CqsJhYJ4.js";import E from"./NoMatch-DMPclUW6.js";import{A as L}from"./Avatar-CvDHTACZ.js";import{P as S}from"./Paper-C-atefOs.js";import{I as b}from"./Info-CNP9gYBt.js";import{F as A,I}from"./InputLabel-C8rcdOGQ.js";import{I as _}from"./Input-D1AdR9CM.js";import{F as v}from"./FormHelperText-DDZ4BMA4.js";import{F as O,S as k}from"./Switch-C5asfh_w.js";import{B as w}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./IconButton-CxOCoGF3.js";import"./Delete-BfnPAJno.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-DiTut-u0.js";import"./Backdrop-Czag2Ija.js";import"./DressService-DkS6e_O5.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./SwitchBase-DrUkTXjH.js";const T=()=>{const T=c(),[P,W]=d.useState(),[B,M]=d.useState(),[q,F]=d.useState(!1),[U,z]=d.useState(!1),[V,G]=d.useState(!1),[H,Y]=d.useState(!1),[K,Q]=d.useState(""),[J,X]=d.useState(!1),{control:Z,register:$,handleSubmit:ee,setValue:se,setError:ie,clearErrors:re,setFocus:ae,formState:{errors:te,isSubmitting:ne},trigger:oe}=p({resolver:h(f),mode:"onBlur",defaultValues:{fullName:"",email:"",phone:"",location:"",bio:"",blacklisted:!1,payLater:!1,licenseRequired:!1,minimumRentalDays:"",priceChangeRate:"",supplierDressLimit:"",notifyAdminOnNewDress:!1}}),{payLater:le,licenseRequired:me,notifyAdminOnNewDress:ce,blacklisted:de}=u({control:Z}),pe=e(P);return s.jsxs(x,{onLoad:async e=>{if(e&&e.verified){G(!0),W(e);const i=new URLSearchParams(window.location.search);if(i.has("c")){const r=i.get("c");if(r&&""!==r)try{const s=await g(r);if(s){if(e.type!==a.Admin&&e._id!==s._id)return G(!1),void Y(!0);M(s),se("email",s.email||""),Q(s.avatar||""),se("fullName",s.fullName||""),se("phone",s.phone||""),se("location",s.location||""),se("bio",s.bio||""),se("payLater",!!s.payLater),se("licenseRequired",!!s.licenseRequired),se("minimumRentalDays",s.minimumRentalDays?.toString()||""),se("priceChangeRate",s.priceChangeRate?.toString()||""),se("supplierDressLimit",s.supplierDressLimit?.toString()||""),se("notifyAdminOnNewDress",!!s.notifyAdminOnNewDress),se("blacklisted",!!s.blacklisted),z(!0),G(!1)}else G(!1),Y(!0)}catch(s){t(s),G(!1),F(!0),z(!1)}else G(!1),Y(!0)}else G(!1),Y(!0)}},strict:!0,children:[U&&s.jsx("div",{className:"update-supplier",children:s.jsx(S,{className:"supplier-form-update",elevation:10,children:s.jsxs("form",{onSubmit:ee((async e=>{try{if(!B)return void t();if(B.fullName!==e.fullName&&200!==await y({fullName:e.fullName}))return ie("fullName",{message:N.INVALID_SUPPLIER_NAME}),void ae("fullName");if(!K)return X(!0),void F(!1);const s={_id:B._id,fullName:e.fullName,phone:e.phone,location:e.location,bio:e.bio,payLater:!!e.payLater,licenseRequired:!!e.licenseRequired,minimumRentalDays:e.minimumRentalDays?Number(e.minimumRentalDays):void 0,priceChangeRate:e.priceChangeRate?Number(e.priceChangeRate):void 0,supplierDressLimit:e.supplierDressLimit?Number(e.supplierDressLimit):void 0,notifyAdminOnNewDress:e.notifyAdminOnNewDress,blacklisted:!!e.blacklisted},i=await R(s);200===i.status?(M(i.data),m(r.UPDATED)):t()}catch(s){t(s)}}),(()=>{const e=Object.keys(te)[0];e&&ae(e)})),children:[s.jsx(L,{type:i.Supplier,mode:"update",record:B,size:"large",readonly:!1,hideDelete:!0,onBeforeUpload:()=>{G(!0)},onChange:e=>{if(B&&P){const s=n(B);if(s.avatar=e,P._id===B._id){const s=n(P);s.avatar=e,W(s)}G(!1),M(s),e&&X(!1)}else t()},color:"disabled",className:"avatar-ctn"}),s.jsxs("div",{className:"info",children:[s.jsx(b,{}),s.jsx("span",{children:N.RECOMMENDED_IMAGE_SIZE})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:r.FULL_NAME}),s.jsx(_,{...$("fullName"),type:"text",error:!!te.fullName,required:!0,autoComplete:"off"}),s.jsx(v,{error:!!te.fullName,children:te.fullName?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:r.EMAIL}),s.jsx(_,{...$("email"),type:"text",autoComplete:"off",disabled:!0})]}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(O,{control:s.jsx(k,{...$("blacklisted"),checked:de,onChange:e=>{se("blacklisted",e.target.checked)},color:"primary"}),label:r.BLACKLISTED,title:r.BLACKLISTED_TOOLTIP})}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(O,{control:s.jsx(k,{...$("payLater"),checked:le,onChange:e=>{se("payLater",e.target.checked)},color:"primary"}),label:r.PAY_LATER})}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(O,{control:s.jsx(k,{...$("licenseRequired"),checked:me,onChange:e=>{se("licenseRequired",e.target.checked)},color:"primary"}),label:r.LICENSE_REQUIRED})}),s.jsxs("div",{className:"info",children:[s.jsx(b,{}),s.jsx("span",{children:r.OPTIONAL})]}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(O,{control:s.jsx(k,{...$("notifyAdminOnNewDress"),checked:ce,disabled:P?.type===a.Supplier,onChange:e=>{se("notifyAdminOnNewDress",e.target.checked)},color:"primary"}),label:r.NOTIFY_ADMIN_ON_NEW_DRESS})}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.SUPPLIER_DRESS_LIMIT}),s.jsx(_,{...$("supplierDressLimit"),type:"text",autoComplete:"off",error:!!te.supplierDressLimit,onChange:()=>re("supplierDressLimit")}),s.jsx(v,{error:!!te.supplierDressLimit,children:te.supplierDressLimit?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.MIN_RENTAL_DAYS}),s.jsx(_,{...$("minimumRentalDays"),type:"text",autoComplete:"off",error:!!te.minimumRentalDays,onChange:()=>re("minimumRentalDays")}),s.jsx(v,{error:!!te.minimumRentalDays,children:te.minimumRentalDays?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.PRICE_CHANGE_RATE}),s.jsx(_,{...$("priceChangeRate"),type:"text",autoComplete:"off",error:!!te.priceChangeRate,onChange:()=>re("priceChangeRate")}),s.jsx(v,{error:!!te.priceChangeRate,children:te.priceChangeRate?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.PHONE}),s.jsx(_,{...$("phone",{onBlur:()=>oe("phone")}),type:"text",autoComplete:"off",error:!!te.phone,onChange:()=>re("phone")}),s.jsx(v,{error:!!te.phone,children:te.phone?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.LOCATION}),s.jsx(_,{...$("location"),type:"text",autoComplete:"off"})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.BIO}),s.jsx(_,{...$("bio"),type:"text",autoComplete:"off"})]}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(j,{supplier:B})}),pe&&s.jsx(A,{fullWidth:!0,margin:"dense",className:"resend-activation-link",children:s.jsx(w,{variant:"outlined",onClick:async()=>{if(B)try{200===await o(B.email,!1,l.APP_TYPE)?m(r.ACTIVATION_EMAIL_SENT):t()}catch(e){t(e)}},children:r.RESEND_ACTIVATION_LINK})}),s.jsxs("div",{className:"buttons",children:[s.jsx(w,{type:"submit",variant:"contained",className:"btn-primary btn-margin btn-margin-bottom",size:"small",onClick:()=>T(`/change-password?u=${B&&B._id}`),children:r.RESET_PASSWORD}),s.jsx(w,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:ne,children:r.SAVE}),s.jsx(w,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>T("/suppliers"),children:r.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[q&&s.jsx(C,{message:r.GENERIC_ERROR}),J&&s.jsx(C,{message:r.IMAGE_REQUIRED})]})]})})}),V&&s.jsx(D,{text:r.PLEASE_WAIT}),H&&s.jsx(E,{hideHeader:!0})]})};export{T as default};
