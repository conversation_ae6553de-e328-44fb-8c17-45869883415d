import{aF as t}from"../entries/index-xsXxT3-W.js";const e=e=>t.post("/api/validate-supplier",e,{withCredentials:!0}).then((t=>t.status)),a=e=>t.put("/api/update-supplier",e,{withCredentials:!0}).then((t=>({status:t.status,data:t.data}))),s=e=>t.delete(`/api/delete-supplier/${encodeURIComponent(e)}`,{withCredentials:!0}).then((t=>t.status)),n=e=>t.get(`/api/supplier/${encodeURIComponent(e)}`,{withCredentials:!0}).then((t=>t.data)),p=(e,a,s)=>t.get(`/api/suppliers/${a}/${s}/?s=${encodeURIComponent(e)}`,{withCredentials:!0}).then((t=>t.data)),i=()=>t.get("/api/all-suppliers",{withCredentials:!0}).then((t=>t.data)),d=(e,a)=>{const s=new FormData;return s.append("file",a),t.post(`/api/create-contract/${e}`,s,{withCredentials:!0,headers:{"Content-Type":"multipart/form-data"}}).then((t=>t.data))},r=(e,a,s)=>{const n=new FormData;return n.append("file",s),t.post(`/api/update-contract/${e}/${a}`,n,{withCredentials:!0,headers:{"Content-Type":"multipart/form-data"}}).then((t=>({status:t.status,data:t.data})))},o=(e,a)=>t.post(`/api/delete-contract/${e}/${a}`,null,{withCredentials:!0}).then((t=>t.status)),l=e=>t.post(`/api/delete-temp-contract/${encodeURIComponent(e)}`,null,{withCredentials:!0}).then((t=>t.status));export{n as a,l as b,o as c,s as d,r as e,d as f,p as g,i as h,a as u,e as v};
