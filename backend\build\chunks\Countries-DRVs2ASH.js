import{b as e,s,u as t,e as o,j as r,F as a,a as i,z as n,H as c,c as l}from"../entries/index-xsXxT3-W.js";import{d as m,r as d}from"./router-BtYqujaw.js";import{L as u}from"./Layout-DaeN7D4t.js";import{S as p}from"./Search-BT-I8ZrW.js";import{g as j,c as N,d as E}from"./CountryService-CPWL_VJK.js";import{P as T}from"./Pager-B4DUIA8f.js";import{P as h}from"./Progress-C5Vwt7m-.js";import{C as x,a as I}from"./ArrowForwardIos-BCaVe-sv.js";import{T as y}from"./Backdrop-Czag2Ija.js";import{L as C}from"./Menu-C_-X8cS7.js";import{L as _}from"./ListItem-Bmdw8GrH.js";import{L as f}from"./ListItemAvatar-CtDTZqea.js";import{A as S}from"./Avatar-Dvwllg8p.js";import{C as O}from"./Flag-CMGasDVj.js";import{L}from"./ListItemText-DUhWzkV9.js";import{T as A}from"./Tooltip-CKMkVqOx.js";import{I as D}from"./IconButton-CxOCoGF3.js";import{E as g}from"./Edit-Bc0UCPtn.js";import{D as w}from"./Delete-BfnPAJno.js";import{D as R,a as b,b as P,d as U}from"./Grow-Cp8xsNYl.js";import{B as Y}from"./Button-BeKLLPpp.js";import{I as v}from"./InfoBox-DNJEsGlP.js";import"./vendor-dblfw9z9.js";import"./zod-4O8Zwsja.js";import"./TextField-D_yQOTzE.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-DiTut-u0.js";import"./OutlinedInput-BX8yFQbF.js";import"./InputLabel-C8rcdOGQ.js";import"./isHostComponent-DR4iSCFs.js";import"./ownerWindow-ChLfdzZL.js";import"./Input-D1AdR9CM.js";import"./FormHelperText-DDZ4BMA4.js";import"./Clear-CDOl64hX.js";import"./Search-CKOds7xB.js";import"./Paper-C-atefOs.js";import"./mergeSlotProps-DEridHif.js";import"./listItemTextClasses-BcbgzvlE.js";import"./Info-CNP9gYBt.js";const M=new e({fr:{NEW_COUNTRY:"Nouveau pays",DELETE_COUNTRY:"Êtes-vous sûr de vouloir supprimer ce pays ?",CANNOT_DELETE_COUNTRY:"Ce pays ne peut pas être supprimé car il est lié à des lieux.",EMPTY_LIST:"Pas de pays.",COUNTRY:"pays",COUNTRIES:"pays"},en:{NEW_COUNTRY:"New country",DELETE_COUNTRY:"Are you sure you want to delete this country?",CANNOT_DELETE_COUNTRY:"This country cannot be deleted because it is related to locations.",EMPTY_LIST:"No countries.",COUNTRY:"country",COUNTRIES:"countries"},es:{NEW_COUNTRY:"Nuevo país",DELETE_COUNTRY:"¿Estás seguro de que quieres eliminar este país?",CANNOT_DELETE_COUNTRY:"Este país no puede ser eliminado porque está relacionado con ubicaciones.",EMPTY_LIST:"No hay países.",COUNTRY:"país",COUNTRIES:"países"}});s(M);const F=({keyword:e,onLoad:s,onDelete:l})=>{const u=m(),{user:p}=t(),[v,F]=d.useState(e),[G,k]=d.useState(!0),[W,B]=d.useState(!1),[H,q]=d.useState(!1),[z,Z]=d.useState([]),[$,K]=d.useState(0),[J,Q]=d.useState(0),[X,V]=d.useState(1),[ee,se]=d.useState(!1),[te,oe]=d.useState(!1),[re,ae]=d.useState(""),[ie,ne]=d.useState(-1),ce=async(e,t)=>{try{B(!0);const r=await j(t||"",e,o.PAGE_SIZE),a=r&&r.length>0?r[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!a)return void n();const i=Array.isArray(a.pageInfo)&&a.pageInfo.length>0?a.pageInfo[0].totalRecords:0;let l=[];l=o.PAGINATION_MODE===c.PAGINATION_MODE.INFINITE_SCROLL||o.isMobile?1===e?a.resultData:[...z,...a.resultData]:a.resultData,Z(l),K((e-1)*o.PAGE_SIZE+l.length),Q(i),q(a.resultData.length>0),((o.PAGINATION_MODE===c.PAGINATION_MODE.INFINITE_SCROLL||o.isMobile)&&1===e||o.PAGINATION_MODE===c.PAGINATION_MODE.CLASSIC&&!o.isMobile)&&window.scrollTo(0,0),s&&s({rows:a.resultData,rowCount:i})}catch(r){n(r)}finally{B(!1),k(!1)}};d.useEffect((()=>{e!==v&&ce(1,e),F(e||"")}),[e,v]),d.useEffect((()=>{ce(X,v)}),[X]),d.useEffect((()=>{if(o.isMobile){const e=document.querySelector("body");e&&(e.onscroll=()=>{H&&!W&&window.scrollY>0&&window.scrollY+window.innerHeight+o.INFINITE_SCROLL_OFFSET>=document.body.scrollHeight&&(B(!0),V(X+1))})}}),[H,W,X,v]);const le=async e=>{try{const s=e.currentTarget.getAttribute("data-id"),t=Number(e.currentTarget.getAttribute("data-index")),o=await N(s);204===o?(se(!0),ae(s),ne(t)):200===o?oe(!0):n()}catch(s){n(s)}};return p&&r.jsxs(r.Fragment,{children:[r.jsxs("section",{className:"country-list",children:[0===z.length?!G&&!W&&r.jsx(x,{variant:"outlined",className:"empty-list",children:r.jsx(I,{children:r.jsx(y,{color:"textSecondary",children:M.EMPTY_LIST})})}):r.jsx(C,{className:"country-list-items",children:z.map(((e,s)=>r.jsxs(_,{className:"country-list-item",secondaryAction:(a(p)||e.supplier?._id===p._id)&&r.jsxs("div",{children:[r.jsx(A,{title:i.UPDATE,children:r.jsx(D,{edge:"end",onClick:()=>u(`/update-country?loc=${e._id}`),children:r.jsx(g,{})})}),r.jsx(A,{title:i.DELETE,children:r.jsx(D,{edge:"end","data-id":e._id,"data-index":s,onClick:le,children:r.jsx(w,{})})})]}),children:[r.jsx(f,{children:r.jsx(S,{children:r.jsx(O,{})})}),r.jsx(L,{primary:r.jsx(y,{className:"country-title",children:e.name})})]},e._id)))}),r.jsxs(R,{disableEscapeKeyDown:!0,maxWidth:"xs",open:te,children:[r.jsx(b,{className:"dialog-header",children:i.INFO}),r.jsx(P,{children:M.CANNOT_DELETE_COUNTRY}),r.jsx(U,{className:"dialog-actions",children:r.jsx(Y,{onClick:()=>{oe(!1)},variant:"contained",className:"btn-secondary",children:i.CLOSE})})]}),r.jsxs(R,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ee,children:[r.jsx(b,{className:"dialog-header",children:i.CONFIRM_TITLE}),r.jsx(P,{children:M.DELETE_COUNTRY}),r.jsxs(U,{className:"dialog-actions",children:[r.jsx(Y,{onClick:()=>{se(!1),ae(""),ne(-1)},variant:"contained",className:"btn-secondary",children:i.CANCEL}),r.jsx(Y,{onClick:async()=>{try{if(""!==re&&ie>-1)if(B(!0),se(!1),200===await E(re)){const e=$-1;z.splice(ie,1),Z(z),K(e),Q(J-1),ae(""),ne(-1),B(!1),l&&l(e)}else n(),ae(""),ne(-1),B(!1);else n(),se(!1),ae(""),ne(-1)}catch(e){n(e)}},variant:"contained",color:"error",children:i.DELETE})]})]}),W&&r.jsx(h,{})]}),!o.isMobile&&r.jsx(T,{page:X,pageSize:o.PAGE_SIZE,rowCount:$,totalRecords:J,onNext:()=>V(X+1),onPrevious:()=>V(X-1)})]})},G=()=>{const e=l.c(17),s=m(),[t,o]=d.useState(""),[a,i]=d.useState(-1);let n;e[0]===Symbol.for("react.memo_cache_sentinel")?(n=e=>{o(e)},e[0]=n):n=e[0];const c=n;let j;e[1]===Symbol.for("react.memo_cache_sentinel")?(j=e=>{e&&i(e.rowCount)},e[1]=j):j=e[1];const N=j;let E;e[2]===Symbol.for("react.memo_cache_sentinel")?(E=e=>{i(e)},e[2]=E):E=e[2];const T=E,h=k;let x,I,y,C,_,f;return e[3]===Symbol.for("react.memo_cache_sentinel")?(x=r.jsx(p,{className:"search",onSubmit:c}),e[3]=x):x=e[3],e[4]!==s||e[5]!==a?(I=a>-1&&r.jsx(Y,{variant:"contained",className:"btn-primary new-country",size:"small",onClick:()=>s("/create-country"),children:M.NEW_COUNTRY}),e[4]=s,e[5]=a,e[6]=I):I=e[6],e[7]!==a?(y=a>0&&r.jsx(v,{value:`${a} ${a>1?M.COUNTRIES:M.COUNTRY}`,className:"country-count"}),e[7]=a,e[8]=y):y=e[8],e[9]!==I||e[10]!==y?(C=r.jsx("div",{className:"col-1",children:r.jsxs("div",{className:"col-1-container",children:[x,I,y]})}),e[9]=I,e[10]=y,e[11]=C):C=e[11],e[12]!==t?(_=r.jsx("div",{className:"col-2",children:r.jsx(F,{keyword:t,onLoad:N,onDelete:T})}),e[12]=t,e[13]=_):_=e[13],e[14]!==C||e[15]!==_?(f=r.jsx(u,{onLoad:h,strict:!0,children:r.jsxs("div",{className:"countries",children:[C,_]})}),e[14]=C,e[15]=_,e[16]=f):f=e[16],f};function k(){}export{G as default};
