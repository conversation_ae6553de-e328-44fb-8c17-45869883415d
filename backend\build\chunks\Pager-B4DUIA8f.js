import{c as s,j as a,a as e}from"../entries/index-xsXxT3-W.js";import{I as o}from"./IconButton-CxOCoGF3.js";import{P as r,N as i}from"./ArrowForwardIos-BCaVe-sv.js";const c=c=>{const n=s.c(7),{page:t,pageSize:l,totalRecords:d,rowCount:j,onNext:m,onPrevious:x}=c;let p;return n[0]!==m||n[1]!==x||n[2]!==t||n[3]!==l||n[4]!==j||n[5]!==d?(p=(t>1||j<d)&&a.jsx("div",{className:"pager-container",children:a.jsxs("div",{className:"pager",children:[a.jsx("div",{className:"row-count",children:`${(t-1)*l+1}-${j} ${e.OF} ${d}`}),a.jsxs("div",{className:"actions",children:[a.jsx(o,{onClick:x,disabled:1===t,children:a.jsx(r,{className:"icon"})}),a.jsx(o,{onClick:m,disabled:j>=d,children:a.jsx(i,{className:"icon"})})]})]})})||a.jsx(a.Fragment,{}),n[0]=m,n[1]=x,n[2]=t,n[3]=l,n[4]=j,n[5]=d,n[6]=p):p=n[6],p};export{c as P};
