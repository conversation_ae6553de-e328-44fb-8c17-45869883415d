import{c as s,j as e,a as t}from"../entries/index-CEzJO5Xy.js";import{B as r}from"./Button-DGZYUY3P.js";import{d as a}from"./router-BtYqujaw.js";const n=n=>{const o=s.c(8),{style:c}=n,i=a();let l,m,j,d;return o[0]!==c?(l=c||{},o[0]=c,o[1]=l):l=o[1],o[2]===Symbol.for("react.memo_cache_sentinel")?(m=e.jsx("h2",{children:t.GENERIC_ERROR}),o[2]=m):m=o[2],o[3]!==i?(j=e.jsx(r,{variant:"text",onClick:()=>i("/"),className:"btn-lnk",children:t.GO_TO_HOME}),o[3]=i,o[4]=j):j=o[4],o[5]!==l||o[6]!==j?(d=e.jsxs("div",{className:"msg",style:l,children:[m,j]}),o[5]=l,o[6]=j,o[7]=d):d=o[7],d};export{n as E};
