import{b as e,s,c as a,j as t,af as i,a as l,a9 as r,e as n,J as o,ag as c,ah as d,ai as u,G as E,S as m,aj as S,ak as h,Z as N,z as T,F as x,al as f}from"../entries/index-CEzJO5Xy.js";import{r as p,d as j}from"./router-BtYqujaw.js";import{a as g,b as v,d as C}from"./BookingService-BJ4R0IJT.js";import{I as O}from"./InputLabel-BbcIE26O.js";import{S as _}from"./TextField-BAse--ht.js";import{M as I}from"./MenuItem-suKfXYI2.js";import{B as b}from"./BookingStatus-Bg6x_fQB.js";import{L as D}from"./Link-B-UCzRRJ.js";import{f as A,e as L}from"./format-4arn0GRM.js";import{V as P}from"./Check-D745pofy.js";import{B as U}from"./Button-DGZYUY3P.js";import{D as k}from"./DataGrid-DM8uCtAG.js";import{D as y,a as R,b as G}from"./Grow-CjOKj0i1.js";import{D as M}from"./DialogTitle-BZXwroUN.js";import{T as w}from"./Tooltip-BkJF6Mu0.js";import{I as B}from"./IconButton-CnBvmeAK.js";import{E as K}from"./Edit-DIF9Bumd.js";import{D as $}from"./Delete-CnqjtpsJ.js";import{f as F}from"./fr-CaQg1DLH.js";const Y=new e({fr:{CAR:"Voiture",SUPPLIER:"Fournisseur",CUSTOMER:"Client",PRICE:"Prix",STATUS:"Statut",UPDATE_SELECTION:"Modifier la sélection",DELETE_SELECTION:"Supprimer la sélection",UPDATE_STATUS:"Modification du statut",NEW_STATUS:"Nouveau statut",DELETE_BOOKING:"Êtes-vous sûr de vouloir supprimer cette réservation ?",DELETE_BOOKINGS:"Êtes-vous sûr de vouloir supprimer les réservations sélectionnées ?",EMPTY_LIST:"Pas de réservations.",DAYS:"Jours",COST:"Total"},en:{CAR:"Car",SUPPLIER:"Supplier",CUSTOMER:"Customer",PRICE:"Price",STATUS:"Status",UPDATE_SELECTION:"Edit selection",DELETE_SELECTION:"Delete selection",UPDATE_STATUS:"Status modification",NEW_STATUS:"New status",DELETE_BOOKING:"Are you sure you want to delete this booking?",DELETE_BOOKINGS:"Are you sure you want to delete the selected bookings?",EMPTY_LIST:"No bookings.",DAYS:"Days",COST:"COST"},es:{CAR:"Coche",SUPPLIER:"Proveedor",CUSTOMER:"Cliente",PRICE:"Precio",STATUS:"Estado",UPDATE_SELECTION:"Modificar selección",DELETE_SELECTION:"Eliminar selección",UPDATE_STATUS:"Modificación del estado",NEW_STATUS:"Nuevo estado",DELETE_BOOKING:"¿Estás seguro de que quieres eliminar esta reserva?",DELETE_BOOKINGS:"¿Estás seguro de que quieres eliminar las reservas seleccionadas?",EMPTY_LIST:"Sin reservas.",DAYS:"Días",COST:"Coste"},ar:{CAR:"فستان",SUPPLIER:"المورد",CUSTOMER:"العميل",PRICE:"السعر",STATUS:"الحالة",UPDATE_SELECTION:"تحديث التحديد",DELETE_SELECTION:"حذف التحديد",UPDATE_STATUS:"تعديل الحالة",NEW_STATUS:"حالة جديدة",DELETE_BOOKING:"هل أنت متأكد من أنك تريد حذف هذا الحجز؟",DELETE_BOOKINGS:"هل أنت متأكد من أنك تريد حذف الحجوزات المحددة؟",EMPTY_LIST:"لا توجد حجوزات.",DAYS:"أيام",COST:"التكلفة"}});s(Y);const q=e=>{const s=a.c(18),{value:n,label:o,required:c,variant:d,disabled:u,style:E,onChange:m}=e,[S,h]=p.useState("");let N,T,x;s[0]!==n||s[1]!==S?(N=()=>{n&&n!==S&&h(n)},T=[n,S],s[0]=n,s[1]=S,s[2]=N,s[3]=T):(N=s[2],T=s[3]),p.useEffect(N,T),s[4]!==m?(x=e=>{h(e.target.value),m&&m(e.target.value)},s[4]=m,s[5]=x):x=s[5];const f=x;let j,g,v;return s[6]!==E?(j=E||{},s[6]=E,s[7]=j):j=s[7],s[8]!==u||s[9]!==f||s[10]!==o||s[11]!==c||s[12]!==S||s[13]!==d?(g=u?t.jsx("span",{className:`bs-s-sv bs-s-${S}`,style:{marginTop:5},children:i(S)}):t.jsxs(t.Fragment,{children:[t.jsx(O,{className:c?"required":"",children:o}),t.jsxs(_,{label:o,value:S,onChange:f,variant:d||"standard",required:c,fullWidth:!0,renderValue:W,children:[t.jsx(I,{value:r.Void,className:"bs-s bs-s-void",children:l.BOOKING_STATUS_VOID}),t.jsx(I,{value:r.Pending,className:"bs-s bs-s-pending",children:l.BOOKING_STATUS_PENDING}),t.jsx(I,{value:r.Deposit,className:"bs-s bs-s-deposit",children:l.BOOKING_STATUS_DEPOSIT}),t.jsx(I,{value:r.Paid,className:"bs-s bs-s-paid",children:l.BOOKING_STATUS_PAID}),t.jsx(I,{value:r.Reserved,className:"bs-s bs-s-reserved",children:l.BOOKING_STATUS_RESERVED}),t.jsx(I,{value:r.Cancelled,className:"bs-s bs-s-cancelled",children:l.BOOKING_STATUS_CANCELLED})]})]}),s[8]=u,s[9]=f,s[10]=o,s[11]=c,s[12]=S,s[13]=d,s[14]=g):g=s[14],s[15]!==j||s[16]!==g?(v=t.jsx("div",{style:j,children:g}),s[15]=j,s[16]=g,s[17]=v):v=s[17],v};function W(e){return t.jsx("span",{className:`bs-s-sv bs-s-${e}`,children:i(e)})}const z=({suppliers:e,statuses:s,filter:a,car:r,dress:O,offset:_,user:I,loggedUser:W,containerClassName:z,hideDates:Z,hideDressColumn:V,hideSupplierColumn:H,language:J,checkboxSelection:Q,onLoad:X})=>{const ee=j(),[se,ae]=p.useState(),[te,ie]=p.useState(),[le,re]=p.useState(0),[ne,oe]=p.useState(n.isMobile?n.BOOKINGS_MOBILE_PAGE_SIZE:n.BOOKINGS_PAGE_SIZE),[ce,de]=p.useState([]),[ue,Ee]=p.useState([]),[me,Se]=p.useState(0),[he,Ne]=p.useState(!1),[Te,xe]=p.useState(""),[fe,pe]=p.useState([]),[je,ge]=p.useState(-1),[ve,Ce]=p.useState(e),[Oe,_e]=p.useState(s),[Ie,be]=p.useState(),[De,Ae]=p.useState(a),[Le,Pe]=p.useState(r||""),[Ue,ke]=p.useState(O||""),[ye,Re]=p.useState(!1),[Ge,Me]=p.useState(!1),[we,Be]=p.useState(0),[Ke,$e]=p.useState({pageSize:n.BOOKINGS_PAGE_SIZE,page:0}),[Fe,Ye]=p.useState(!0);p.useEffect((()=>{n.isMobile||(re(Ke.page),oe(Ke.pageSize))}),[Ke]);const qe=async(e,s,a,t)=>{try{const a=n.isMobile?n.BOOKINGS_MOBILE_PAGE_SIZE:ne;if(ve&&Oe){Ye(!0);const i={suppliers:ve,statuses:Oe,filter:De||void 0,dress:t||Ue,user:s&&s._id||void 0},l=await g(i,e+1,a),r=l&&l.length>0?l[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!r)return void T();const o=Array.isArray(r.pageInfo)&&r.pageInfo.length>0?r.pageInfo[0].totalRecords:0;if(n.isMobile){const s=0===e?r.resultData:[...ue,...r.resultData];Ee(s),Se(o),Ne(r.resultData.length>0),X&&X({rows:r.resultData,rowCount:o})}else Ee(r.resultData),Se(o),X&&X({rows:r.resultData,rowCount:o})}else Ee([]),Se(0),X&&X({rows:[],rowCount:0})}catch(i){T(i)}finally{Ye(!1)}};p.useEffect((()=>{Ce(e)}),[e]),p.useEffect((()=>{_e(s)}),[s]),p.useEffect((()=>{Ae(a)}),[a]),p.useEffect((()=>{Pe(r||""),r&&qe(le,te)}),[r]),p.useEffect((()=>{ke(O||""),O&&qe(le,te,0,O)}),[O]),p.useEffect((()=>{Be(_||0)}),[_]),p.useEffect((()=>{ie(I),I&&qe(le,I)}),[I]),p.useEffect((()=>{ve&&Oe&&se&&qe(le,te)}),[le]),p.useEffect((()=>{if(ve&&Oe&&se)if(0===le)qe(0,te);else{const e=o(Ke);e.page=0,$e(e)}}),[ne]);const We=e=>{if(e){const s=new Date(e);return`${f(s.getDate())}-${f(s.getMonth()+1)}-${s.getFullYear()}`}throw new Error("Invalid date")},ze=()=>{const e=[{field:"customer",headerName:Y.CUSTOMER,flex:1,renderCell:({row:e,value:s})=>t.jsx(D,{href:`/user?u=${e.customer._id}`,children:s}),valueGetter:e=>e?.fullName},{field:"from",headerName:l.FROM,flex:1,valueGetter:e=>We(e)},{field:"to",headerName:l.TO,flex:1,valueGetter:e=>We(e)},{field:"price",headerName:Y.PRICE,flex:1,renderCell:({value:e})=>t.jsx("span",{className:"bp",children:e}),valueGetter:e=>N(e,l.CURRENCY,J)},{field:"status",headerName:Y.STATUS,flex:1,renderCell:({value:e})=>t.jsx(b,{value:e,showIcon:!0}),valueGetter:e=>e},{field:"action",headerName:"",sortable:!1,disableColumnMenu:!0,renderCell:({row:e})=>t.jsxs("div",{children:[t.jsx(w,{title:l.UPDATE,children:t.jsx(B,{onClick:()=>ee(`/update-booking?b=${e._id}`),children:t.jsx(K,{})})}),t.jsx(w,{title:l.DELETE,children:t.jsx(B,{onClick:s=>{s.stopPropagation(),xe(e._id||""),Me(!0)},children:t.jsx($,{})})})]}),renderHeader:()=>fe.length>0?t.jsxs("div",{children:[t.jsx(w,{title:Y.UPDATE_SELECTION,children:t.jsx(B,{onClick:()=>{Re(!0)},children:t.jsx(K,{})})}),t.jsx(w,{title:Y.DELETE_SELECTION,children:t.jsx(B,{onClick:()=>{Me(!0)},children:t.jsx($,{})})})]}):t.jsx(t.Fragment,{})}];return Z&&e.splice(1,2),V||e.unshift({field:"dress",headerName:Y.DRESS,flex:1,renderCell:({row:e,value:s})=>t.jsx(D,{href:`/dress?dr=${e.dress?._id}`,children:s}),valueGetter:e=>e?.name}),x(se)&&!H&&e.unshift({field:"supplier",headerName:l.SUPPLIER,flex:1,renderCell:({row:e,value:s})=>t.jsx(D,{href:`/supplier?c=${e.supplier._id}`,className:"cell-supplier",children:t.jsx("img",{src:E(n.CDN_USERS,e.supplier.avatar),alt:s})}),valueGetter:e=>e?.fullName}),e};p.useEffect((()=>{if(ve&&Oe&&se){const e=ze();if(de(e),0===le)qe(0,te);else{const e=o(Ke);e.page=0,$e(e)}}}),[ve,Oe,De]),p.useEffect((()=>{const e=ze();de(e)}),[fe]),p.useEffect((()=>{ae(W||void 0)}),[W]),p.useEffect((()=>{if(n.isMobile){const e=z?document.querySelector(`.${z}`):document.querySelector("div.bookings");e&&(e.onscroll=e=>{if(he&&!Fe){const s=e.target;s.scrollTop>0&&s.offsetHeight+s.scrollTop+n.INFINITE_SCROLL_OFFSET>=s.scrollHeight&&(Ye(!0),re(le+1))}})}}),[z,le,he,Fe,we]);const Ze=e=>{const s=e.currentTarget.getAttribute("data-id"),a=Number(e.currentTarget.getAttribute("data-index"));xe(s),ge(a),Me(!0),xe(s),ge(a)},Ve="fr"===J,He=Ve?F:L,Je=Ve?"eee d LLL yyyy kk:mm":"eee, d LLL yyyy, p",Qe=n.SUPPLIER_IMAGE_HEIGHT+10;return t.jsxs("div",{className:"bs-list",children:[se&&(n.isMobile?t.jsx(t.Fragment,{children:ue.map(((e,s)=>{const a=new Date(e.from),r=new Date(e.to);return c(a,r),t.jsxs("div",{className:"booking-details",children:[t.jsx("div",{className:`bs bs-${e.status}`,children:t.jsx("span",{children:i(e.status)})}),e.dress&&t.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[t.jsx("span",{className:"booking-detail-title",children:Y.DRESS}),t.jsx("div",{className:"booking-detail-value",children:t.jsx(D,{href:`dress/?dr=${e.dress._id}`,children:e.dress.name})})]}),t.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[t.jsx("span",{className:"booking-detail-title",children:Y.CUSTOMER}),t.jsx("div",{className:"booking-detail-value",children:t.jsx(D,{href:`user/?u=${e.customer._id}`,children:e.customer.fullName})})]}),t.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[t.jsx("span",{className:"booking-detail-title",children:Y.DAYS}),t.jsx("div",{className:"booking-detail-value",children:`${d(c(a,r))} (${u(A(a,Je,{locale:He}))} - ${u(A(r,Je,{locale:He}))})`})]}),t.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[t.jsx("span",{className:"booking-detail-title",children:l.PICK_UP_LOCATION}),t.jsx("div",{className:"booking-detail-value",children:e.pickupLocation.name})]}),t.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[t.jsx("span",{className:"booking-detail-title",children:l.DROP_OFF_LOCATION}),t.jsx("div",{className:"booking-detail-value",children:e.dropOffLocation.name})]}),t.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[t.jsx("span",{className:"booking-detail-title",children:l.SUPPLIER}),t.jsx("div",{className:"booking-detail-value",children:t.jsxs("div",{className:"car-supplier",children:[t.jsx("img",{src:E(n.CDN_USERS,e.supplier.avatar),alt:e.supplier.fullName}),t.jsx("span",{className:"car-supplier-name",children:e.supplier.fullName})]})})]}),(e.cancellation||e.amendments)&&t.jsx(t.Fragment,{children:t.jsxs("div",{className:"extras",children:[t.jsx("span",{className:"extras-title",children:l.OPTIONS}),e.cancellation&&t.jsxs("div",{className:"extra",children:[t.jsx(P,{className:"extra-icon"}),t.jsx("span",{className:"extra-title",children:m.CANCELLATION}),t.jsx("span",{className:"extra-text",children:S(e.dress?.cancellation,J)})]}),e.amendments&&t.jsxs("div",{className:"extra",children:[t.jsx(P,{className:"extra-icon"}),t.jsx("span",{className:"extra-title",children:m.AMENDMENTS}),t.jsx("span",{className:"extra-text",children:h(e.dress?.amendments,J)})]})]})}),t.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[t.jsx("span",{className:"booking-detail-title",children:Y.COST}),t.jsx("div",{className:"booking-detail-value booking-price",children:N(e.price,l.CURRENCY,J)})]}),t.jsxs("div",{className:"bs-buttons",children:[t.jsx(U,{variant:"contained",className:"btn-primary",size:"small",onClick:()=>ee(`/update-booking?b=${e._id}`),children:l.UPDATE}),t.jsx(U,{variant:"contained",className:"btn-secondary",size:"small","data-id":e._id,"data-index":s,onClick:Ze,children:l.DELETE})]})]},e._id)}))}):t.jsx(k,{checkboxSelection:Q,getRowId:e=>e._id,columns:ce,rows:ue,rowCount:me,loading:Fe,initialState:{pagination:{paginationModel:{pageSize:n.BOOKINGS_PAGE_SIZE}}},pageSizeOptions:[n.BOOKINGS_PAGE_SIZE,50,100],pagination:!0,paginationMode:"server",paginationModel:Ke,onPaginationModelChange:$e,onRowSelectionModelChange:e=>{pe(Array.from(new Set(e.ids)).map((e=>e.toString())))},disableRowSelectionOnClick:!0,className:"booking-grid"})),t.jsxs(y,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ye,children:[t.jsx(M,{className:"dialog-header",children:Y.UPDATE_STATUS}),t.jsx(R,{className:"bs-update-status",children:t.jsx(q,{label:Y.NEW_STATUS,onChange:e=>{be(e)}})}),t.jsxs(G,{className:"dialog-actions",children:[t.jsx(U,{onClick:()=>{Re(!1)},variant:"contained",className:"btn-secondary",children:l.CANCEL}),t.jsx(U,{onClick:async()=>{try{if(!Ie)return void T();const e={ids:fe,status:Ie};200===await v(e)?(ue.forEach((e=>{e._id&&fe.includes(e._id)&&(e.status=Ie)})),Ee(o(ue))):T(),Re(!1)}catch(e){T(e)}},variant:"contained",className:"btn-primary",children:l.UPDATE})]})]}),t.jsxs(y,{disableEscapeKeyDown:!0,maxWidth:"xs",open:Ge,children:[t.jsx(M,{className:"dialog-header",children:l.CONFIRM_TITLE}),t.jsx(R,{className:"dialog-content",children:0===fe.length?Y.DELETE_BOOKING:Y.DELETE_BOOKINGS}),t.jsxs(G,{className:"dialog-actions",children:[t.jsx(U,{onClick:()=>{Me(!1),xe("")},variant:"contained",className:"btn-secondary",children:l.CANCEL}),t.jsx(U,{onClick:async()=>{try{if(n.isMobile){const e=[Te];200===await C(e)?(ue.splice(je,1),Ee(ue),xe(""),ge(-1)):T(),Me(!1)}else{const e=fe.length>0?fe:[Te];200===await C(e)?fe.length>0?Ee(ue.filter((e=>e._id&&!fe.includes(e._id)))):Ee(ue.filter((e=>e._id!==Te))):T(),Me(!1)}}catch(e){T(e)}},variant:"contained",color:"error",children:l.DELETE})]})]})]})};export{z as B};
