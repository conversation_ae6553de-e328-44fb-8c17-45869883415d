import{c as e,j as a}from"../entries/index-xsXxT3-W.js";import{r as t}from"./router-BtYqujaw.js";import{d as r,D as s,L as o,A as n}from"./DatePicker-BDzBD9XN.js";import{a as l,e as c}from"./fr-DJt_zj3p.js";const i=i=>{const m=e.c(29),{value:d,label:u,minDate:f,maxDate:p,required:D,language:j,variant:x,readOnly:h,onChange:v,onError:y}=i,[b,g]=t.useState(null),[E,S]=t.useState();let _,L,q,w;m[0]!==d?(_=()=>{g(d||null)},L=[d],m[0]=d,m[1]=_,m[2]=L):(_=m[1],L=m[2]),t.useEffect(_,L),m[3]!==f?(q=()=>{if(f){const e=new Date(f);e.setHours(10,0,0,0),S(e)}else S(void 0)},w=[f],m[3]=f,m[4]=q,m[5]=w):(q=m[4],w=m[5]),t.useEffect(q,w);const A="fr"===j?l:"es"===j?r:c;let C,H;m[6]===Symbol.for("react.memo_cache_sentinel")?(C=["year","month","day"],m[6]=C):C=m[6],m[7]!==E||m[8]!==v||m[9]!==y?(H=e=>{e&&e.setHours(10,0,0,0),g(e),v&&v(e),e&&E&&e<E&&y&&y("minDate",e)},m[7]=E,m[8]=v,m[9]=y,m[10]=H):H=m[10];const O=x||"standard";let P,k,B,F,U;return m[11]!==D||m[12]!==O?(P={variant:O,required:D},m[11]=D,m[12]=O,m[13]=P):P=m[13],m[14]===Symbol.for("react.memo_cache_sentinel")?(k={actions:["accept","cancel","clear"]},m[14]=k):k=m[14],m[15]!==P?(B={textField:P,actionBar:k},m[15]=P,m[16]=B):B=m[16],m[17]!==u||m[18]!==p||m[19]!==E||m[20]!==y||m[21]!==h||m[22]!==B||m[23]!==H||m[24]!==b?(F=a.jsx(s,{label:u,views:C,value:b,readOnly:h,onChange:H,onError:y,minDate:E,maxDate:p,slotProps:B}),m[17]=u,m[18]=p,m[19]=E,m[20]=y,m[21]=h,m[22]=B,m[23]=H,m[24]=b,m[25]=F):F=m[25],m[26]!==F||m[27]!==A?(U=a.jsx(o,{adapterLocale:A,dateAdapter:n,children:F}),m[26]=F,m[27]=A,m[28]=U):U=m[28],U};export{i as D};
