div.booking {
  display: flex;
  flex-direction: row;
  transform: translate3d(0, 0, 0);
}

div.booking div.col-2 div.col-2-header {
  text-align: right;
}

div.booking div.col-1 div.booking-buttons {
  float: right;
}

div.booking div.col-2 div.col-2-header div.price {
  width: fit-content;
  float: right;
}

div.booking div.col-2 div.col-2-header span.price-main {
  color: #383838;
  font-size: 2em;
  font-weight: 700;
  line-height: 1em;
  white-space: nowrap;
  display: table-row;
  text-align: right;
}

div.booking div.col-2 div.col-2-header span.price-days,
div.booking div.col-2 div.col-2-header span.price-day {
  font-size: 13px;
  color: #a1a1a1;
  display: table-row;
  text-align: right;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.booking div.col-1 {
    padding: 20px;
    background: #fefefe;
    border-bottom: #eee solid 1px;
  }

  div.booking div.col-2 {
    display: none;
  }
}

@media only screen and (width <=1270px) {
  div.booking div.col-2 .dress {
    display: none;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.booking div.col-1 {
    background: #fefefe;
    border-right: #eee solid 1px;
    padding: 25px;
    flex: 0 1 30%;
  }

  div.booking div.col-2 {
    flex: 1 1;
    padding-top: 10px;
  }

  div.booking div.col-2 div.col-2-header {
    padding-right: 25px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }
}
