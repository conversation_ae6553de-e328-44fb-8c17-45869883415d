import{r as t}from"./router-BtYqujaw.js";import{i as e,j as r,k as i,ao as o}from"../entries/index-xsXxT3-W.js";import{s as a,c as l,m as n}from"./Button-BeKLLPpp.js";import{a as s}from"./MenuItem-P0BnGnrT.js";import{c as p}from"./Grow-Cp8xsNYl.js";const d=a("div",{name:"<PERSON>i<PERSON>iv<PERSON>",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:r}=t;return[e.root,r.absolute&&e.absolute,e[r.variant],r.light&&e.light,"vertical"===r.orientation&&e.vertical,r.flexItem&&e.flexItem,r.children&&e.withChildren,r.children&&"vertical"===r.orientation&&e.withChildrenVertical,"right"===r.textAlign&&"vertical"!==r.orientation&&e.textAlignRight,"left"===r.textAlign&&"vertical"!==r.orientation&&e.textAlignLeft]}})(n((({theme:t})=>({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(t.vars||t).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:t.vars?`rgba(${t.vars.palette.dividerChannel} / 0.08)`:o(t.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:t.spacing(2),marginRight:t.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:t.spacing(1),marginBottom:t.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:({ownerState:t})=>!!t.children,style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:({ownerState:t})=>t.children&&"vertical"!==t.orientation,style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(t.vars||t).palette.divider}`,borderTopStyle:"inherit"}}},{props:({ownerState:t})=>"vertical"===t.orientation&&t.children,style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(t.vars||t).palette.divider}`,borderLeftStyle:"inherit"}}},{props:({ownerState:t})=>"right"===t.textAlign&&"vertical"!==t.orientation,style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:({ownerState:t})=>"left"===t.textAlign&&"vertical"!==t.orientation,style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]})))),c=a("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(t,e)=>{const{ownerState:r}=t;return[e.wrapper,"vertical"===r.orientation&&e.wrapperVertical]}})(n((({theme:t})=>({display:"inline-block",paddingLeft:`calc(${t.spacing(1)} * 1.2)`,paddingRight:`calc(${t.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${t.spacing(1)} * 1.2)`,paddingBottom:`calc(${t.spacing(1)} * 1.2)`}}]})))),h=t.forwardRef((function(t,o){const a=e({props:t,name:"MuiDivider"}),{absolute:n=!1,children:p,className:h,orientation:g="horizontal",component:v=(p||"vertical"===g?"div":"hr"),flexItem:f=!1,light:m=!1,role:b=("hr"!==v?"separator":void 0),textAlign:w="center",variant:x="fullWidth",...u}=a,y={...a,absolute:n,component:v,flexItem:f,light:m,orientation:g,role:b,textAlign:w,variant:x},S=(t=>{const{absolute:e,children:r,classes:i,flexItem:o,light:a,orientation:n,textAlign:p,variant:d}=t;return l({root:["root",e&&"absolute",d,a&&"light","vertical"===n&&"vertical",o&&"flexItem",r&&"withChildren",r&&"vertical"===n&&"withChildrenVertical","right"===p&&"vertical"!==n&&"textAlignRight","left"===p&&"vertical"!==n&&"textAlignLeft"],wrapper:["wrapper","vertical"===n&&"wrapperVertical"]},s,i)})(y);return r.jsx(d,{as:v,className:i(S.root,h),role:b,ref:o,ownerState:y,"aria-orientation":"separator"!==b||"hr"===v&&"vertical"!==g?void 0:g,...u,children:p?r.jsx(c,{className:S.wrapper,ownerState:y,children:p}):null})}));h&&(h.muiSkipListHighlight=!0);const g=p(r.jsx("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"})),v=p(r.jsx("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}));export{h as D,v as K,g as a};
