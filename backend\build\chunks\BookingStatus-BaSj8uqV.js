import{j as s,aF as a,p as e,c as t,ac as o,ad as i}from"../entries/index-xsXxT3-W.js";import{c as n}from"./Grow-Cp8xsNYl.js";const c=n(s.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z"})),d=n(s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"})),l=n(s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-1 14H9V8h2zm4 0h-2V8h2z"})),p=n(s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m5 11H7v-2h10z"})),r=s=>a.post("/api/update-booking-status",s,{withCredentials:!0}).then((s=>s.status)),h=s=>a.post("/api/delete-bookings",s,{withCredentials:!0}).then((s=>s.status)),m=(s,t,o)=>a.post(`/api/bookings/${t}/${o}/${e()}`,s,{withCredentials:!0}).then((s=>s.data)),b=a=>{const e=t.c(14),{showIcon:n,onClick:r,value:h}=a;let m,b;e[0]!==r?(m=s=>{r&&r(s)},e[0]=r,e[1]=m):m=e[1],e[2]!==n||e[3]!==h?(b=n&&(a=>[i.Deposit,i.Reserved,i.Paid].includes(a)?s.jsx(d,{className:`bs-icon bs-icon-${a?.toLowerCase()}`}):a===i.Void?s.jsx(p,{className:"bs-icon bs-icon-void"}):a===i.Pending?s.jsx(l,{className:"bs-icon bs-icon-pending"}):s.jsx(c,{className:"bs-icon bs-icon-cancelled"}))(h),e[2]=n,e[3]=h,e[4]=b):b=e[4];const j=`bs bs-${h?.toLowerCase()}`;let x,C,u;return e[5]!==h?(x=o(h),e[5]=h,e[6]=x):x=e[6],e[7]!==j||e[8]!==x?(C=s.jsx("span",{className:j,children:x}),e[7]=j,e[8]=x,e[9]=C):C=e[9],e[10]!==m||e[11]!==b||e[12]!==C?(u=s.jsxs("div",{className:"booking-status",onClick:m,role:"presentation",children:[b,C]}),e[10]=m,e[11]=b,e[12]=C,e[13]=u):u=e[13],u};export{b as B,h as d,m as g,r as u};
