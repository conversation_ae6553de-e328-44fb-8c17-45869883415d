import{c as e,e as s,R as a,j as r,G as o,I as i,a as t,J as l,z as c}from"../entries/index-xsXxT3-W.js";import{d as n,r as m}from"./router-BtYqujaw.js";import{s as p}from"./supplier-list-BiBQJaIV.js";import{d,a as j}from"./SupplierService-9DC5V5ZJ.js";import{L as h}from"./Layout-DaeN7D4t.js";import{S as u}from"./SimpleBackdrop-CqsJhYJ4.js";import{A as f}from"./Avatar-CvDHTACZ.js";import{D as S}from"./DressList-qxSdI7YO.js";import{I as x}from"./InfoBox-DNJEsGlP.js";import{E as _}from"./Error-FiYP5RHa.js";import N from"./NoMatch-DMPclUW6.js";import{T as E}from"./Backdrop-Czag2Ija.js";import{L as v}from"./Link-sHEcszvT.js";import{T as b}from"./Tooltip-CKMkVqOx.js";import{I as y}from"./IconButton-CxOCoGF3.js";import{E as L}from"./Edit-Bc0UCPtn.js";import{D}from"./Delete-BfnPAJno.js";import{a as g,b as w,d as I,D as A}from"./Grow-Cp8xsNYl.js";import{B as C}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./DressService-DkS6e_O5.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./useSlot-DiTut-u0.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./Pager-B4DUIA8f.js";import"./ArrowForwardIos-BCaVe-sv.js";import"./Paper-C-atefOs.js";import"./SupplierBadge-ehv63WPF.js";import"./Straighten-Isz6BfHc.js";import"./Clear-CDOl64hX.js";import"./Visibility-D3efFHY1.js";import"./Info-CNP9gYBt.js";import"./ownerWindow-ChLfdzZL.js";const k=()=>{const k=e.c(43),T=n(),[B,P]=m.useState(),[R,U]=m.useState();let G;k[0]===Symbol.for("react.memo_cache_sentinel")?(G=[],k[0]=G):G=k[0];const[F,M]=m.useState(G),[W,O]=m.useState(!1),[$,z]=m.useState(!1),[H,J]=m.useState(!0),[V,K]=m.useState(!1),[Q,X]=m.useState(!1),[q,Y]=m.useState(-1),[Z,ee]=m.useState(s.DEFAULT_LANGUAGE);let se;k[1]===Symbol.for("react.memo_cache_sentinel")?(se=()=>{J(!0)},k[1]=se):se=k[1];const ae=se;let re;k[2]!==R||k[3]!==B?(re=e=>{if(B&&R&&B._id===R._id){const s=l(B);s.avatar=e,P(s)}J(!1)},k[2]=R,k[3]=B,k[4]=re):re=k[4];const oe=re;let ie;k[5]===Symbol.for("react.memo_cache_sentinel")?(ie=()=>{X(!0)},k[5]=ie):ie=k[5];const te=ie;let le;k[6]!==T||k[7]!==R?(le=async()=>{if(R)try{X(!1),200===await d(R._id)?T("/suppliers"):c()}catch(e){c(e)}else c()},k[6]=T,k[7]=R,k[8]=le):le=k[8];const ce=le;let ne;k[9]===Symbol.for("react.memo_cache_sentinel")?(ne=()=>{X(!1)},k[9]=ne):ne=k[9];const me=ne;let pe;k[10]===Symbol.for("react.memo_cache_sentinel")?(pe=e=>{e&&Y(e.rowCount)},k[10]=pe):pe=k[10];const de=pe;let je;k[11]===Symbol.for("react.memo_cache_sentinel")?(je=e=>{Y(e)},k[11]=je):je=k[11];const he=je;let ue;k[12]===Symbol.for("react.memo_cache_sentinel")?(ue=async e=>{if(P(e),ee(e?.language),e&&e.verified){const e=new URLSearchParams(window.location.search);if(e.has("c")){const s=e.get("c");if(s&&""!==s)try{const e=await j(s);e?(U(e),M([e._id]),z(!0),J(!1)):(J(!1),K(!0))}catch{J(!1),O(!0),z(!1)}else J(!1),K(!0)}else J(!1),K(!0)}},k[12]=ue):ue=k[12];const fe=ue,Se=B&&R&&(B.type===a.Admin||B._id===R._id);let xe,_e,Ne,Ee,ve,be,ye,Le,De,ge;return k[13]!==Se||k[14]!==Z||k[15]!==T||k[16]!==oe||k[17]!==q||k[18]!==R||k[19]!==F||k[20]!==B||k[21]!==$?(xe=$&&R&&F&&r.jsxs("div",{className:"supplier",children:[r.jsxs("div",{className:"col-1",children:[r.jsx("section",{className:"supplier-avatar-sec",children:Se?r.jsx(f,{record:R,type:a.Supplier,mode:"update",size:"large",hideDelete:!0,onBeforeUpload:ae,onChange:oe,readonly:!Se,color:"disabled",className:"supplier-avatar"}):r.jsxs("div",{className:"car-supplier",children:[r.jsx("span",{className:"car-supplier-logo",children:r.jsx("img",{src:o(s.CDN_USERS,R.avatar),alt:R.fullName,style:{width:s.SUPPLIER_IMAGE_WIDTH}})}),r.jsx("span",{className:"car-supplier-info",children:R.fullName})]})}),Se&&r.jsx(E,{variant:"h4",className:"supplier-name",children:R.fullName}),R.bio&&(i(R.bio)?r.jsx(v,{href:R.bio,target:"_blank",rel:"noreferrer",className:"supplier-bio-link",children:R.bio}):r.jsx(E,{variant:"h6",className:"supplier-info",children:R.bio})),R.location&&""!==R.location&&r.jsx(E,{variant:"h6",className:"supplier-info",children:R.location}),R.phone&&""!==R.phone&&r.jsx(E,{variant:"h6",className:"supplier-info",children:R.phone}),r.jsxs("div",{className:"supplier-actions",children:[Se&&r.jsx(b,{title:t.UPDATE,children:r.jsx(y,{onClick:()=>T(`/update-supplier?c=${R._id}`),children:r.jsx(L,{})})}),Se&&r.jsx(b,{title:t.DELETE,children:r.jsx(y,{"data-id":R._id,onClick:te,children:r.jsx(D,{})})})]}),q>0&&r.jsx(x,{value:`${q} ${q>1?t.DRESSES:t.DRESS}`,className:"dress-count"})]}),r.jsx("div",{className:"col-2",children:r.jsx(S,{user:B,suppliers:F,keyword:"",reload:!1,language:Z,onLoad:de,onDelete:he,hideSupplier:!0})})]}),k[13]=Se,k[14]=Z,k[15]=T,k[16]=oe,k[17]=q,k[18]=R,k[19]=F,k[20]=B,k[21]=$,k[22]=xe):xe=k[22],k[23]===Symbol.for("react.memo_cache_sentinel")?(_e=r.jsx(g,{className:"dialog-header",children:t.CONFIRM_TITLE}),Ne=r.jsx(w,{children:p.DELETE_SUPPLIER}),k[23]=_e,k[24]=Ne):(_e=k[23],Ne=k[24]),k[25]===Symbol.for("react.memo_cache_sentinel")?(Ee=r.jsx(C,{onClick:me,variant:"contained",className:"btn-secondary",children:t.CANCEL}),k[25]=Ee):Ee=k[25],k[26]!==ce?(ve=r.jsxs(I,{className:"dialog-actions",children:[Ee,r.jsx(C,{onClick:ce,variant:"contained",color:"error",children:t.DELETE})]}),k[26]=ce,k[27]=ve):ve=k[27],k[28]!==Q||k[29]!==ve?(be=r.jsxs(A,{disableEscapeKeyDown:!0,maxWidth:"xs",open:Q,children:[_e,Ne,ve]}),k[28]=Q,k[29]=ve,k[30]=be):be=k[30],k[31]!==H?(ye=H&&r.jsx(u,{text:t.LOADING}),k[31]=H,k[32]=ye):ye=k[32],k[33]!==W?(Le=W&&r.jsx(_,{}),k[33]=W,k[34]=Le):Le=k[34],k[35]!==V?(De=V&&r.jsx(N,{hideHeader:!0}),k[35]=V,k[36]=De):De=k[36],k[37]!==be||k[38]!==ye||k[39]!==Le||k[40]!==De||k[41]!==xe?(ge=r.jsxs(h,{onLoad:fe,strict:!0,children:[xe,be,ye,Le,De]}),k[37]=be,k[38]=ye,k[39]=Le,k[40]=De,k[41]=xe,k[42]=ge):ge=k[42],ge};export{k as default};
