import{r as e}from"./router-BtYqujaw.js";import{j as o,i as t,k as r,l as a,ao as s}from"../entries/index-xsXxT3-W.js";import{S as i}from"./SwitchBase-DrUkTXjH.js";import{c as n}from"./Grow-Cp8xsNYl.js";import{a as l,g as c,s as p,c as d,r as m,m as h,b as v}from"./Button-BeKLLPpp.js";import{u}from"./useSlot-DiTut-u0.js";import{m as b}from"./mergeSlotProps-DEridHif.js";const f=n(o.jsx("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"})),z=n(o.jsx("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})),j=n(o.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}));function x(e){return c("MuiCheckbox",e)}const y=l("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]),k=p(i,{shouldForwardProp:e=>m(e)||"classes"===e,name:"MuiCheckbox",slot:"Root",overridesResolver:(e,o)=>{const{ownerState:t}=e;return[o.root,t.indeterminate&&o.indeterminate,o[`size${a(t.size)}`],"default"!==t.color&&o[`color${a(t.color)}`]]}})(h((({theme:e})=>({color:(e.vars||e).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:s(e.palette.action.active,e.palette.action.hoverOpacity)}}},...Object.entries(e.palette).filter(v()).map((([o])=>({props:{color:o,disableRipple:!1},style:{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[o].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:s(e.palette[o].main,e.palette.action.hoverOpacity)}}}))),...Object.entries(e.palette).filter(v()).map((([o])=>({props:{color:o},style:{[`&.${y.checked}, &.${y.indeterminate}`]:{color:(e.vars||e).palette[o].main},[`&.${y.disabled}`]:{color:(e.vars||e).palette.action.disabled}}}))),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]})))),S=o.jsx(z,{}),$=o.jsx(f,{}),C=o.jsx(j,{}),P=e.forwardRef((function(s,i){const n=t({props:s,name:"MuiCheckbox"}),{checkedIcon:l=S,color:c="primary",icon:p=$,indeterminate:m=!1,indeterminateIcon:h=C,inputProps:v,size:f="medium",disableRipple:z=!1,className:j,slots:y={},slotProps:P={},...R}=n,w=m?h:p,M=m?h:l,g={...n,disableRipple:z,color:c,indeterminate:m,size:f},O=(e=>{const{classes:o,indeterminate:t,color:r,size:s}=e,i={root:["root",t&&"indeterminate",`color${a(r)}`,`size${a(s)}`]},n=d(i,x,o);return{...o,...n}})(g),H=P.input??v,[V,F]=u("root",{ref:i,elementType:k,className:r(O.root,j),shouldForwardComponentProp:!0,externalForwardedProps:{slots:y,slotProps:P,...R},ownerState:g,additionalProps:{type:"checkbox",icon:e.cloneElement(w,{fontSize:w.props.fontSize??f}),checkedIcon:e.cloneElement(M,{fontSize:M.props.fontSize??f}),disableRipple:z,slots:y,slotProps:{input:b("function"==typeof H?H(g):H,{"data-indeterminate":m})}}});return o.jsx(V,{...F,classes:O})}));export{P as C};
