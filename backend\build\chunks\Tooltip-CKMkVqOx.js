import{r as e}from"./router-BtYqujaw.js";import{i as t,m as o,j as r,k as p,l as a,ao as n}from"../entries/index-xsXxT3-W.js";import{u as s}from"./Paper-C-atefOs.js";import{a as i,g as l,i as c,f as m,e as d,u,s as g,T as h,c as f,m as w,k as y}from"./Button-BeKLLPpp.js";import{u as b}from"./useSlot-DiTut-u0.js";import{u as v,G as x,P as T}from"./Grow-Cp8xsNYl.js";import{g as S}from"./Backdrop-Czag2Ija.js";function R(e){return l("MuiTooltip",e)}const P=i("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]),$=g(T,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.popper,!o.disableInteractive&&t.popperInteractive,o.arrow&&t.popperArrow,!o.open&&t.popperClose]}})(w((({theme:e})=>({zIndex:(e.vars||e).zIndex.tooltip,pointerEvents:"none",variants:[{props:({ownerState:e})=>!e.disableInteractive,style:{pointerEvents:"auto"}},{props:({open:e})=>!e,style:{pointerEvents:"none"}},{props:({ownerState:e})=>e.arrow,style:{[`&[data-popper-placement*="bottom"] .${P.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${P.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${P.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${P.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:({ownerState:e})=>e.arrow&&!e.isRtl,style:{[`&[data-popper-placement*="right"] .${P.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:({ownerState:e})=>e.arrow&&!!e.isRtl,style:{[`&[data-popper-placement*="right"] .${P.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:e})=>e.arrow&&!e.isRtl,style:{[`&[data-popper-placement*="left"] .${P.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:e})=>e.arrow&&!!e.isRtl,style:{[`&[data-popper-placement*="left"] .${P.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]})))),M=g("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.tooltip,o.touch&&t.touch,o.arrow&&t.tooltipArrow,t[`tooltipPlacement${a(o.placement.split("-")[0])}`]]}})(w((({theme:e})=>{return{backgroundColor:e.vars?e.vars.palette.Tooltip.bg:n(e.palette.grey[700],.92),borderRadius:(e.vars||e).shape.borderRadius,color:(e.vars||e).palette.common.white,fontFamily:e.typography.fontFamily,padding:"4px 8px",fontSize:e.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:e.typography.fontWeightMedium,[`.${P.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${P.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${P.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${P.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:({ownerState:e})=>e.arrow,style:{position:"relative",margin:0}},{props:({ownerState:e})=>e.touch,style:{padding:"8px 16px",fontSize:e.typography.pxToRem(14),lineHeight:(t=16/14,Math.round(1e5*t)/1e5+"em"),fontWeight:e.typography.fontWeightRegular}},{props:({ownerState:e})=>!e.isRtl,style:{[`.${P.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${P.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:({ownerState:e})=>!e.isRtl&&e.touch,style:{[`.${P.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${P.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:({ownerState:e})=>!!e.isRtl,style:{[`.${P.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${P.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:({ownerState:e})=>!!e.isRtl&&e.touch,style:{[`.${P.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${P.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:({ownerState:e})=>e.touch,style:{[`.${P.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:({ownerState:e})=>e.touch,style:{[`.${P.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]};var t}))),O=g("span",{name:"MuiTooltip",slot:"Arrow"})(w((({theme:e})=>({overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:e.vars?e.vars.palette.Tooltip.bg:n(e.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}}))));let L=!1;const j=new h;let k={x:0,y:0};function E(e,t){return(o,...r)=>{t&&t(o,...r),e(o,...r)}}const C=e.forwardRef((function(n,i){const l=t({props:n,name:"MuiTooltip"}),{arrow:g=!1,children:h,classes:w,components:P={},componentsProps:C={},describeChild:F=!1,disableFocusListener:B=!1,disableHoverListener:I=!1,disableInteractive:W=!1,disableTouchListener:A=!1,enterDelay:N=100,enterNextDelay:z=0,enterTouchDelay:D=700,followCursor:U=!1,id:G,leaveDelay:H=0,leaveTouchDelay:J=1500,onClose:Q,onOpen:V,open:X,placement:Y="bottom",PopperComponent:q,PopperProps:K={},slotProps:Z={},slots:_={},title:ee,TransitionComponent:te,TransitionProps:oe,...re}=l,pe=e.isValidElement(h)?h:r.jsx("span",{children:h}),ae=s(),ne=o(),[se,ie]=e.useState(),[le,ce]=e.useState(null),me=e.useRef(!1),de=W||U,ue=c(),ge=c(),he=c(),fe=c(),[we,ye]=v({controlled:X,default:!1,name:"Tooltip",state:"open"});let be=we;const ve=m(G),xe=e.useRef(),Te=d((()=>{void 0!==xe.current&&(document.body.style.WebkitUserSelect=xe.current,xe.current=void 0),fe.clear()}));e.useEffect((()=>Te),[Te]);const Se=e=>{j.clear(),L=!0,ye(!0),V&&!be&&V(e)},Re=d((e=>{j.start(800+H,(()=>{L=!1})),ye(!1),Q&&be&&Q(e),ue.start(ae.transitions.duration.shortest,(()=>{me.current=!1}))})),Pe=e=>{me.current&&"touchstart"!==e.type||(se&&se.removeAttribute("title"),ge.clear(),he.clear(),N||L&&z?ge.start(L?z:N,(()=>{Se(e)})):Se(e))},$e=e=>{ge.clear(),he.start(H,(()=>{Re(e)}))},[,Me]=e.useState(!1),Oe=e=>{y(e.target)||(Me(!1),$e(e))},Le=e=>{se||ie(e.currentTarget),y(e.target)&&(Me(!0),Pe(e))},je=e=>{me.current=!0;const t=pe.props;t.onTouchStart&&t.onTouchStart(e)};e.useEffect((()=>{if(be)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){"Escape"===e.key&&Re(e)}}),[Re,be]);const ke=u(S(pe),ie,i);ee||0===ee||(be=!1);const Ee=e.useRef(),Ce={},Fe="string"==typeof ee;F?(Ce.title=be||!Fe||I?null:ee,Ce["aria-describedby"]=be?ve:null):(Ce["aria-label"]=Fe?ee:null,Ce["aria-labelledby"]=be&&!Fe?ve:null);const Be={...Ce,...re,...pe.props,className:p(re.className,pe.props.className),onTouchStart:je,ref:ke,...U?{onMouseMove:e=>{const t=pe.props;t.onMouseMove&&t.onMouseMove(e),k={x:e.clientX,y:e.clientY},Ee.current&&Ee.current.update()}}:{}},Ie={};A||(Be.onTouchStart=e=>{je(e),he.clear(),ue.clear(),Te(),xe.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",fe.start(D,(()=>{document.body.style.WebkitUserSelect=xe.current,Pe(e)}))},Be.onTouchEnd=e=>{pe.props.onTouchEnd&&pe.props.onTouchEnd(e),Te(),he.start(J,(()=>{Re(e)}))}),I||(Be.onMouseOver=E(Pe,Be.onMouseOver),Be.onMouseLeave=E($e,Be.onMouseLeave),de||(Ie.onMouseOver=Pe,Ie.onMouseLeave=$e)),B||(Be.onFocus=E(Le,Be.onFocus),Be.onBlur=E(Oe,Be.onBlur),de||(Ie.onFocus=Le,Ie.onBlur=Oe));const We={...l,isRtl:ne,arrow:g,disableInteractive:de,placement:Y,PopperComponentProp:q,touch:me.current},Ae="function"==typeof Z.popper?Z.popper(We):Z.popper,Ne=e.useMemo((()=>{let e=[{name:"arrow",enabled:Boolean(le),options:{element:le,padding:4}}];return K.popperOptions?.modifiers&&(e=e.concat(K.popperOptions.modifiers)),Ae?.popperOptions?.modifiers&&(e=e.concat(Ae.popperOptions.modifiers)),{...K.popperOptions,...Ae?.popperOptions,modifiers:e}}),[le,K.popperOptions,Ae?.popperOptions]),ze=(e=>{const{classes:t,disableInteractive:o,arrow:r,touch:p,placement:n}=e,s={popper:["popper",!o&&"popperInteractive",r&&"popperArrow"],tooltip:["tooltip",r&&"tooltipArrow",p&&"touch",`tooltipPlacement${a(n.split("-")[0])}`],arrow:["arrow"]};return f(s,R,t)})(We),De="function"==typeof Z.transition?Z.transition(We):Z.transition,Ue={slots:{popper:P.Popper,transition:P.Transition??te,tooltip:P.Tooltip,arrow:P.Arrow,..._},slotProps:{arrow:Z.arrow??C.arrow,popper:{...K,...Ae??C.popper},tooltip:Z.tooltip??C.tooltip,transition:{...oe,...De??C.transition}}},[Ge,He]=b("popper",{elementType:$,externalForwardedProps:Ue,ownerState:We,className:p(ze.popper,K?.className)}),[Je,Qe]=b("transition",{elementType:x,externalForwardedProps:Ue,ownerState:We}),[Ve,Xe]=b("tooltip",{elementType:M,className:ze.tooltip,externalForwardedProps:Ue,ownerState:We}),[Ye,qe]=b("arrow",{elementType:O,className:ze.arrow,externalForwardedProps:Ue,ownerState:We,ref:ce});return r.jsxs(e.Fragment,{children:[e.cloneElement(pe,Be),r.jsx(Ge,{as:q??T,placement:Y,anchorEl:U?{getBoundingClientRect:()=>({top:k.y,left:k.x,right:k.x,bottom:k.y,width:0,height:0})}:se,popperRef:Ee,open:!!se&&be,id:ve,transition:!0,...Ie,...He,popperOptions:Ne,children:({TransitionProps:e})=>r.jsx(Je,{timeout:ae.transitions.duration.shorter,...e,...Qe,children:r.jsxs(Ve,{...Xe,children:[ee,g?r.jsx(Ye,{...qe}):null]})})})]})}));export{C as T};
