import{b as e,s,u as o,e as t,j as a,F as r,R as i,a as n,z as l,H as c,c as m}from"../entries/index-CEzJO5Xy.js";import{d,r as u}from"./router-BtYqujaw.js";import{L as N}from"./Layout-BQBjg4Lf.js";import{S as j}from"./Search-CuqoyoO6.js";import{g as p,c as T,d as E}from"./LocationService-BtQFgoWL.js";import{P as I}from"./Pager-C0zDCFUN.js";import{A as O}from"./Avatar-BtfxKR-8.js";import{P as L}from"./Progress-CNEDa8ss.js";import{C as A,a as h}from"./ArrowForwardIos-BMce9t8T.js";import{T as x}from"./Backdrop-Bzn12VyM.js";import{L as C}from"./Menu-ZU0DMgjT.js";import{L as _}from"./ListItem-D1VHRhQp.js";import{L as S}from"./ListItemAvatar-Bv6onK36.js";import{L as f}from"./ListItemText-DBn_RuMq.js";import{T as g}from"./Tooltip-BkJF6Mu0.js";import{I as y}from"./IconButton-CnBvmeAK.js";import{E as D}from"./Edit-DIF9Bumd.js";import{D as w}from"./Delete-CnqjtpsJ.js";import{D as b,a as v,b as P}from"./Grow-CjOKj0i1.js";import{D as M}from"./DialogTitle-BZXwroUN.js";import{B as F}from"./Button-DGZYUY3P.js";import{I as k}from"./InfoBox-Csm94Gbd.js";import"./vendor-dblfw9z9.js";import"./zod-4O8Zwsja.js";import"./TextField-BAse--ht.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-CtA82Ni6.js";import"./OutlinedInput-g8mR4MM3.js";import"./InputLabel-BbcIE26O.js";import"./isHostComponent-DR4iSCFs.js";import"./ownerWindow-ChLfdzZL.js";import"./Input-BQdee9z7.js";import"./FormHelperText-DFSsjBsL.js";import"./Clear-BpXDeTL8.js";import"./Search-BNrZEqND.js";import"./DressService-J0XavNJj.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./Paper-CcwAvfvc.js";import"./mergeSlotProps-Cay5TZBz.js";import"./listItemTextClasses-DFwCkkgK.js";import"./Info-C_WcR51V.js";const G=new e({fr:{NEW_LOCATION:"Nouveau lieu",DELETE_LOCATION:"Êtes-vous sûr de vouloir supprimer ce lieu ?",CANNOT_DELETE_LOCATION:"Ce lieu ne peut pas être supprimé car il est lié à des voitures.",EMPTY_LIST:"Pas de lieux.",LOCATION:"lieu",LOCATIONS:"lieux"},en:{NEW_LOCATION:"New location",DELETE_LOCATION:"Are you sure you want to delete this location?",CANNOT_DELETE_LOCATION:"This location cannot be deleted because it is related to cars.",EMPTY_LIST:"No locations.",LOCATION:"location",LOCATIONS:"locations"},es:{NEW_LOCATION:"Nuevo lugar",DELETE_LOCATION:"¿Estás seguro de que quieres eliminar este lugar?",CANNOT_DELETE_LOCATION:"Este lugar no puede ser eliminado porque está relacionado con coches.",EMPTY_LIST:"No hay lugares.",LOCATION:"lugar",LOCATIONS:"lugares"},ar:{NEW_LOCATION:"موقع جديد",DELETE_LOCATION:"هل أنت متأكد من أنك تريد حذف هذا الموقع؟",CANNOT_DELETE_LOCATION:"لا يمكن حذف هذا الموقع لأنه مرتبط بالفساتين.",EMPTY_LIST:"لا توجد مواقع.",LOCATION:"موقع",LOCATIONS:"مواقع"}});s(G);const R=({keyword:e,onLoad:s,onDelete:m})=>{const N=d(),{user:j}=o(),[k,R]=u.useState(e),[W,Y]=u.useState(!0),[B,H]=u.useState(!1),[q,z]=u.useState(!1),[Z,$]=u.useState([]),[K,U]=u.useState(0),[J,Q]=u.useState(0),[V,X]=u.useState(1),[ee,se]=u.useState(!1),[oe,te]=u.useState(!1),[ae,re]=u.useState(""),[ie,ne]=u.useState(-1),le=async(e,o)=>{try{H(!0);const a=await p(o||"",e,t.PAGE_SIZE),r=a&&a.length>0?a[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!r)return void l();const i=Array.isArray(r.pageInfo)&&r.pageInfo.length>0?r.pageInfo[0].totalRecords:0;let n=[];n=t.PAGINATION_MODE===c.PAGINATION_MODE.INFINITE_SCROLL||t.isMobile?1===e?r.resultData:[...Z,...r.resultData]:r.resultData,$(n),U((e-1)*t.PAGE_SIZE+n.length),Q(i),z(r.resultData.length>0),((t.PAGINATION_MODE===c.PAGINATION_MODE.INFINITE_SCROLL||t.isMobile)&&1===e||t.PAGINATION_MODE===c.PAGINATION_MODE.CLASSIC&&!t.isMobile)&&window.scrollTo(0,0),s&&s({rows:r.resultData,rowCount:i})}catch(a){l(a)}finally{H(!1),Y(!1)}};u.useEffect((()=>{e!==k&&le(1,e),R(e||"")}),[e,k]),u.useEffect((()=>{le(V,k)}),[V]),u.useEffect((()=>{if(t.isMobile){const e=document.querySelector("body");e&&(e.onscroll=()=>{q&&!B&&window.scrollY>0&&window.scrollY+window.innerHeight+t.INFINITE_SCROLL_OFFSET>=document.body.scrollHeight&&(H(!0),X(V+1))})}}),[q,B,V,k]);const ce=async e=>{try{const s=e.currentTarget.getAttribute("data-id"),o=Number(e.currentTarget.getAttribute("data-index")),t=await T(s);204===t?(se(!0),re(s),ne(o)):200===t?te(!0):l()}catch(s){l(s)}};return j&&a.jsxs(a.Fragment,{children:[a.jsxs("section",{className:"location-list",children:[0===Z.length?!W&&!B&&a.jsx(A,{variant:"outlined",className:"empty-list",children:a.jsx(h,{children:a.jsx(x,{color:"textSecondary",children:G.EMPTY_LIST})})}):a.jsx(C,{className:"location-list-items",children:Z.map(((e,s)=>a.jsxs(_,{className:"location-list-item",secondaryAction:(r(j)||e.supplier?._id===j._id)&&a.jsxs("div",{children:[a.jsx(g,{title:n.UPDATE,children:a.jsx(y,{edge:"end",onClick:()=>N(`/update-location?loc=${e._id}`),children:a.jsx(D,{})})}),a.jsx(g,{title:n.DELETE,children:a.jsx(y,{edge:"end","data-id":e._id,"data-index":s,onClick:ce,children:a.jsx(w,{})})})]}),children:[a.jsx(S,{children:a.jsx(O,{type:i.Location,mode:"update",record:e,size:"medium",readonly:!0,color:"disabled",className:"location-image"})}),a.jsx(f,{primary:a.jsx(x,{className:"location-title",children:e.name}),secondary:e.country?.name&&e.country.name})]},e._id)))}),a.jsxs(b,{disableEscapeKeyDown:!0,maxWidth:"xs",open:oe,children:[a.jsx(M,{className:"dialog-header",children:n.INFO}),a.jsx(v,{children:G.CANNOT_DELETE_LOCATION}),a.jsx(P,{className:"dialog-actions",children:a.jsx(F,{onClick:()=>{te(!1)},variant:"contained",className:"btn-secondary",children:n.CLOSE})})]}),a.jsxs(b,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ee,children:[a.jsx(M,{className:"dialog-header",children:n.CONFIRM_TITLE}),a.jsx(v,{children:G.DELETE_LOCATION}),a.jsxs(P,{className:"dialog-actions",children:[a.jsx(F,{onClick:()=>{se(!1),re(""),ne(-1)},variant:"contained",className:"btn-secondary",children:n.CANCEL}),a.jsx(F,{onClick:async()=>{try{if(""!==ae&&ie>-1)if(H(!0),se(!1),200===await E(ae)){const e=K-1;Z.splice(ie,1),$(Z),U(e),Q(J-1),re(""),ne(-1),H(!1),m&&m(e)}else l(),re(""),ne(-1),H(!1);else l(),se(!1),re(""),ne(-1)}catch(e){l(e)}},variant:"contained",color:"error",children:n.DELETE})]})]}),B&&a.jsx(L,{})]}),!t.isMobile&&a.jsx(I,{page:V,pageSize:t.PAGE_SIZE,rowCount:K,totalRecords:J,onNext:()=>X(V+1),onPrevious:()=>X(V-1)})]})},W=()=>{const e=m.c(17),s=d(),[o,t]=u.useState(""),[r,i]=u.useState(-1);let n;e[0]===Symbol.for("react.memo_cache_sentinel")?(n=e=>{t(e)},e[0]=n):n=e[0];const l=n;let c;e[1]===Symbol.for("react.memo_cache_sentinel")?(c=e=>{e&&i(e.rowCount)},e[1]=c):c=e[1];const p=c;let T;e[2]===Symbol.for("react.memo_cache_sentinel")?(T=e=>{i(e)},e[2]=T):T=e[2];const E=T,I=Y;let O,L,A,h,x,C;return e[3]===Symbol.for("react.memo_cache_sentinel")?(O=a.jsx(j,{className:"search",onSubmit:l}),e[3]=O):O=e[3],e[4]!==s||e[5]!==r?(L=r>-1&&a.jsx(F,{variant:"contained",className:"btn-primary new-location",size:"small",onClick:()=>s("/create-location"),children:G.NEW_LOCATION}),e[4]=s,e[5]=r,e[6]=L):L=e[6],e[7]!==r?(A=r>0&&a.jsx(k,{value:`${r} ${r>1?G.LOCATIONS:G.LOCATION}`,className:"location-count"}),e[7]=r,e[8]=A):A=e[8],e[9]!==L||e[10]!==A?(h=a.jsx("div",{className:"col-1",children:a.jsxs("div",{className:"col-1-container",children:[O,L,A]})}),e[9]=L,e[10]=A,e[11]=h):h=e[11],e[12]!==o?(x=a.jsx("div",{className:"col-2",children:a.jsx(R,{keyword:o,onLoad:p,onDelete:E})}),e[12]=o,e[13]=x):x=e[13],e[14]!==h||e[15]!==x?(C=a.jsx(N,{onLoad:I,strict:!0,children:a.jsxs("div",{className:"locations",children:[h,x]})}),e[14]=h,e[15]=x,e[16]=C):C=e[16],C};function Y(){}export{W as default};
