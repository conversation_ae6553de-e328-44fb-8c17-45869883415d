import { Schema, model } from 'mongoose';
const dateBasedPriceSchema = new Schema({
  startDate: {
    type: Date,
    required: [true, "can't be blank"]
  },
  endDate: {
    type: Date,
    required: [true, "can't be blank"]
  },
  dailyPrice: {
    type: Number,
    required: [true, "can't be blank"]
  }
}, {
  timestamps: true,
  strict: true,
  collection: 'DateBasedPrice'
});
const DateBasedPrice = model('DateBasedPrice', dateBasedPriceSchema);
export default DateBasedPrice;