import{r as e,g as t,a as r}from"./vendor-dblfw9z9.js";function n(e,t){for(var r=0;r<t.length;r++){const n=t[r];if("string"!=typeof n&&!Array.isArray(n))for(const t in n)if("default"!==t&&!(t in e)){const r=Object.getOwnPropertyDescriptor(n,t);r&&Object.defineProperty(e,t,r.get?r:{enumerable:!0,get:()=>n[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var a=e();const o=t(a),i=n({__proto__:null,default:o},[a]);var l,s={};!function(){if(l)return s;l=1,Object.defineProperty(s,"__esModule",{value:!0}),s.parse=function(e,t){const r=new o,n=e.length;if(n<2)return r;const a=t?.decode||c;let l=0;do{const t=e.indexOf("=",l);if(-1===t)break;const o=e.indexOf(";",l),s=-1===o?n:o;if(t>s){l=e.lastIndexOf(";",t-1)+1;continue}const c=i(e,l,t),d=u(e,t,c),h=e.slice(c,d);if(void 0===r[h]){let n=i(e,t+1,s),o=u(e,s,n);const l=a(e.slice(n,o));r[h]=l}l=s+1}while(l<n);return r},s.serialize=function(o,i,l){const s=l?.encode||encodeURIComponent;if(!e.test(o))throw new TypeError(`argument name is invalid: ${o}`);const u=s(i);if(!t.test(u))throw new TypeError(`argument val is invalid: ${i}`);let c=o+"="+u;if(!l)return c;if(void 0!==l.maxAge){if(!Number.isInteger(l.maxAge))throw new TypeError(`option maxAge is invalid: ${l.maxAge}`);c+="; Max-Age="+l.maxAge}if(l.domain){if(!r.test(l.domain))throw new TypeError(`option domain is invalid: ${l.domain}`);c+="; Domain="+l.domain}if(l.path){if(!n.test(l.path))throw new TypeError(`option path is invalid: ${l.path}`);c+="; Path="+l.path}if(l.expires){if(!function(e){return"[object Date]"===a.call(e)}(l.expires)||!Number.isFinite(l.expires.valueOf()))throw new TypeError(`option expires is invalid: ${l.expires}`);c+="; Expires="+l.expires.toUTCString()}if(l.httpOnly&&(c+="; HttpOnly"),l.secure&&(c+="; Secure"),l.partitioned&&(c+="; Partitioned"),l.priority)switch("string"==typeof l.priority?l.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${l.priority}`)}if(l.sameSite)switch("string"==typeof l.sameSite?l.sameSite.toLowerCase():l.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${l.sameSite}`)}return c};const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,o=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function i(e,t,r){do{const r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function u(e,t,r){for(;t>r;){const r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}();var u,c=e=>{throw TypeError(e)},d=(e,t,r)=>(((e,t)=>{t.has(e)||c("Cannot read from private field")})(e,t),r?r.call(e):t.get(e)),h="popstate";function f(e={}){return function(e,t,r,n={}){let{window:a=document.defaultView,v5Compat:o=!1}=n,i=a.history,l="POP",s=null,u=c();function c(){return(i.state||{idx:null}).idx}function d(){l="POP";let e=c(),t=null==e?null:e-u;u=e,s&&s({action:l,location:p.location,delta:t})}function f(e){return b(e)}null==u&&(u=0,i.replaceState({...i.state,idx:u},""));let p={get action(){return l},get location(){return e(a,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(h,d),s=e,()=>{a.removeEventListener(h,d),s=null}},createHref:e=>t(a,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){l="PUSH";let r=v(p.location,e,t);u=c()+1;let n=y(r,u),d=p.createHref(r);try{i.pushState(n,"",d)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(d)}o&&s&&s({action:l,location:p.location,delta:1})},replace:function(e,t){l="REPLACE";let r=v(p.location,e,t);u=c();let n=y(r,u),a=p.createHref(r);i.replaceState(n,"",a),o&&s&&s({action:l,location:p.location,delta:0})},go:e=>i.go(e)};return p}((function(e,t){let{pathname:r,search:n,hash:a}=e.location;return v("",{pathname:r,search:n,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:g(t)}),0,e)}function p(e,t){if(!1===e||null==e)throw new Error(t)}function m(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(r){}}}function y(e,t){return{usr:e.state,key:e.key,idx:t}}function v(e,t,r=null,n){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?w(t):t,state:r,key:t&&t.key||n||Math.random().toString(36).substring(2,10)}}function g({pathname:e="/",search:t="",hash:r=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),r&&"#"!==r&&(e+="#"===r.charAt(0)?r:"#"+r),e}function w(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function b(e,t=!1){let r="http://localhost";"undefined"!=typeof window&&(r="null"!==window.location.origin?window.location.origin:window.location.href),p(r,"No window.location.(origin|href) available to create URL");let n="string"==typeof e?e:g(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var E=class{constructor(e){var t,r,n;if(t=this,r=u,n=new Map,r.has(t)?c("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(t):r.set(t,n),e)for(let[a,o]of e)this.set(a,o)}get(e){if(d(this,u).has(e))return d(this,u).get(e);if(void 0!==e.defaultValue)return e.defaultValue;throw new Error("No value found for context")}set(e,t){d(this,u).set(e,t)}};u=new WeakMap;var R=new Set(["lazy","caseSensitive","path","id","index","children"]),x=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function S(e,t,r=[],n={}){return e.map(((e,a)=>{let o=[...r,String(a)],i="string"==typeof e.id?e.id:o.join("-");if(p(!0!==e.index||!e.children,"Cannot specify children on an index route"),p(!n[i],`Found a route id collision on id "${i}".  Route id's must be globally unique within Data Router usages`),function(e){return!0===e.index}(e)){let r={...e,...t(e),id:i};return n[i]=r,r}{let r={...e,...t(e),id:i,children:void 0};return n[i]=r,e.children&&(r.children=S(e.children,t,o,n)),r}}))}function C(e,t,r="/"){return P(e,t,r,!1)}function P(e,t,r,n){let a=F(("string"==typeof t?w(t):t).pathname||"/",r);if(null==a)return null;let o=L(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){return e.length===t.length&&e.slice(0,-1).every(((e,r)=>e===t[r]))?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let i=null;for(let l=0;null==i&&l<o.length;++l){let e=N(a);i=_(o[l],e,n)}return i}function L(e,t=[],r=[],n=""){let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(p(i.relativePath.startsWith(n),`Absolute route path "${i.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(n.length));let l=q([n,i.relativePath]),s=r.concat(i);e.children&&e.children.length>0&&(p(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${l}".`),L(e.children,t,s,l)),(null!=e.path||e.index)&&t.push({path:l,score:z(l,e.index),routesMeta:s})};return e.forEach(((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let r of k(e.path))a(e,t,r);else a(e,t)})),t}function k(e){let t=e.split("/");if(0===t.length)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(0===n.length)return a?[o,""]:[o];let i=k(n.join("/")),l=[];return l.push(...i.map((e=>""===e?o:[o,e].join("/")))),a&&l.push(...i),l.map((t=>e.startsWith("/")&&""===t?"/":t))}var D=/^:[\w-]+$/,T=3,M=2,$=1,A=10,O=-2,j=e=>"*"===e;function z(e,t){let r=e.split("/"),n=r.length;return r.some(j)&&(n+=O),t&&(n+=M),r.filter((e=>!j(e))).reduce(((e,t)=>e+(D.test(t)?T:""===t?$:A)),n)}function _(e,t,r=!1){let{routesMeta:n}=e,a={},o="/",i=[];for(let l=0;l<n.length;++l){let e=n[l],s=l===n.length-1,u="/"===o?t:t.slice(o.length)||"/",c=U({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&r&&!n[n.length-1].route.index&&(c=U({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:q([o,c.pathname]),pathnameBase:Y(q([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=q([o,c.pathnameBase]))}return i}function U(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=function(e,t=!1,r=!0){m("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,r)=>(n.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")));return e.endsWith("*")?(n.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:n.reduce(((e,{paramName:t,isOptional:r},n)=>{if("*"===t){let e=l[n]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const a=l[n];return e[t]=r&&!a?void 0:(a||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:i,pattern:e}}function N(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return m(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function F(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}function H(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function I(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function B(e){let t=I(e);return t.map(((e,r)=>r===t.length-1?e.pathname:e.pathnameBase))}function W(e,t,r,n=!1){let a;"string"==typeof e?a=w(e):(a={...e},p(!a.pathname||!a.pathname.includes("?"),H("?","pathname","search",a)),p(!a.pathname||!a.pathname.includes("#"),H("#","pathname","hash",a)),p(!a.search||!a.search.includes("#"),H("#","search","hash",a)));let o,i=""===e||""===a.pathname,l=i?"/":a.pathname;if(null==l)o=r;else{let e=t.length-1;if(!n&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t="/"){let{pathname:r,search:n="",hash:a=""}="string"==typeof e?w(e):e,o=r?r.startsWith("/")?r:function(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?r.length>1&&r.pop():"."!==e&&r.push(e)})),r.length>1?r.join("/"):"/"}(r,t):t;return{pathname:o,search:V(n),hash:J(a)}}(a,o),u=l&&"/"!==l&&l.endsWith("/"),c=(i||"."===l)&&r.endsWith("/");return s.pathname.endsWith("/")||!u&&!c||(s.pathname+="/"),s}var q=e=>e.join("/").replace(/\/\/+/g,"/"),Y=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),V=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",J=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"",K=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function X(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}var G=["POST","PUT","PATCH","DELETE"],Q=new Set(G),Z=["GET",...G],ee=new Set(Z),te=new Set([301,302,303,307,308]),re=new Set([307,308]),ne={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ae={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},oe={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},ie=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,le=e=>({hasErrorBoundary:Boolean(e.hasErrorBoundary)}),se="remix-router-transitions",ue=Symbol("ResetLoaderData");function ce(e,t,r,n,a,o){let i,l;if(a){i=[];for(let e of t)if(i.push(e),e.route.id===a){l=e;break}}else i=t,l=t[t.length-1];let s=W(n||".",B(i),F(e.pathname,r)||e.pathname,"path"===o);if(null==n&&(s.search=e.search,s.hash=e.hash),(null==n||""===n||"."===n)&&l){let e=Ve(s.search);if(l.route.index&&!e)s.search=s.search?s.search.replace(/^\?/,"?index&"):"?index";else if(!l.route.index&&e){let e=new URLSearchParams(s.search),t=e.getAll("index");e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let r=e.toString();s.search=r?`?${r}`:""}}return"/"!==r&&(s.pathname="/"===s.pathname?r:q([r,s.pathname])),g(s)}function de(e,t,r){if(!r||!function(e){return null!=e&&("formData"in e&&null!=e.formData||"body"in e&&void 0!==e.body)}(r))return{path:t};if(r.formMethod&&(n=r.formMethod,!ee.has(n.toUpperCase())))return{path:t,error:Ue(405,{method:r.formMethod})};var n;let a,o,i=()=>({path:t,error:Ue(400,{type:"invalid-body"})}),l=(r.formMethod||"get").toUpperCase(),s=Fe(t);if(void 0!==r.body){if("text/plain"===r.formEncType){if(!Ye(l))return i();let e="string"==typeof r.body?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce(((e,[t,r])=>`${e}${t}=${r}\n`),""):String(r.body);return{path:t,submission:{formMethod:l,formAction:s,formEncType:r.formEncType,formData:void 0,json:void 0,text:e}}}if("application/json"===r.formEncType){if(!Ye(l))return i();try{let e="string"==typeof r.body?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:l,formAction:s,formEncType:r.formEncType,formData:void 0,json:e,text:void 0}}}catch(d){return i()}}}if(p("function"==typeof FormData,"FormData is not available in this environment"),r.formData)a=Me(r.formData),o=r.formData;else if(r.body instanceof FormData)a=Me(r.body),o=r.body;else if(r.body instanceof URLSearchParams)a=r.body,o=$e(a);else if(null==r.body)a=new URLSearchParams,o=new FormData;else try{a=new URLSearchParams(r.body),o=$e(a)}catch(d){return i()}let u={formMethod:l,formAction:s,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:o,json:void 0,text:void 0};if(Ye(u.formMethod))return{path:t,submission:u};let c=w(t);return e&&c.search&&Ve(c.search)&&a.append("index",""),c.search=`?${a}`,{path:g(c),submission:u}}function he(e,t,r,n,a,o,i,l,s,u,c,d,h,f,p,m,y,v,g){let w,b=g?Ie(g[1])?g[1].error:g[1].data:void 0,E=a.createURL(o.location),R=a.createURL(s);if(c&&o.errors){let e=Object.keys(o.errors)[0];w=i.findIndex((t=>t.route.id===e))}else if(g&&Ie(g[1])){let e=g[0];w=i.findIndex((t=>t.route.id===e))-1}let x=g?g[1].statusCode:void 0,S=x&&x>=400,P={currentUrl:E,currentParams:o.matches[0]?.params||{},nextUrl:R,nextParams:i[0].params,...l,actionResult:b,actionStatus:x},L=i.map(((a,i)=>{let{route:l}=a,s=null;if(null!=w&&i>w?s=!1:l.lazy?s=!0:null==l.loader?s=!1:c?s=fe(l,o.loaderData,o.errors):function(e,t,r){let n=!t||r.route.id!==t.route.id,a=!e.hasOwnProperty(r.route.id);return n||a}(o.loaderData,o.matches[i],a)&&(s=!0),null!==s)return Ce(r,n,e,a,u,t,s);let h=!S&&(d||E.pathname+E.search===R.pathname+R.search||E.search!==R.search||function(e,t){let r=e.route.path;return e.pathname!==t.pathname||null!=r&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}(o.matches[i],a)),f={...P,defaultShouldRevalidate:h},p=pe(a,f);return Ce(r,n,e,a,u,t,p,f)})),k=[];return p.forEach(((e,l)=>{if(c||!i.some((t=>t.route.id===e.routeId))||f.has(l))return;let s=C(y,e.path,v);if(!s)return void k.push({key:l,routeId:e.routeId,path:e.path,matches:null,match:null,request:null,controller:null});if(m.has(l))return;let p=o.fetchers.get(l),g=Je(s,e.path),w=new AbortController,b=Te(a,e.path,w.signal),E=null;if(h.has(l))h.delete(l),E=Pe(r,n,b,s,g,u,t);else if(p&&"idle"!==p.state&&void 0===p.data)d&&(E=Pe(r,n,b,s,g,u,t));else{let e={...P,defaultShouldRevalidate:!S&&d};pe(g,e)&&(E=Pe(r,n,b,s,g,u,t,e))}E&&k.push({key:l,routeId:e.routeId,path:e.path,matches:E,match:g,request:b,controller:w})})),{dsMatches:L,revalidatingFetchers:k}}function fe(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=null!=t&&e.id in t,a=null!=r&&void 0!==r[e.id];return!(!n&&a)&&("function"==typeof e.loader&&!0===e.loader.hydrate||!n&&!a)}function pe(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if("boolean"==typeof r)return r}return t.defaultShouldRevalidate}function me(e,t,r,n,a){let o;if(e){let t=n[e];p(t,`No route found to patch children into: routeId = ${e}`),t.children||(t.children=[]),o=t.children}else o=r;let i=S(t.filter((e=>!o.some((t=>ye(e,t))))),a,[e||"_","patch",String(o?.length||"0")],n);o.push(...i)}function ye(e,t){return"id"in e&&"id"in t&&e.id===t.id||e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive&&(!(e.children&&0!==e.children.length||t.children&&0!==t.children.length)||e.children.every(((e,r)=>t.children?.some((t=>ye(e,t))))))}var ve=new WeakMap,ge=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let a=r[t.id];if(p(a,"No route found in manifest"),!a.lazy||"object"!=typeof a.lazy)return;let o=a.lazy[e];if(!o)return;let i=ve.get(a);i||(i={},ve.set(a,i));let l=i[e];if(l)return l;let s=(async()=>{let t=function(e){return R.has(e)}(e),r=void 0!==a[e]&&"hasErrorBoundary"!==e;if(t)m(!t,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),i[e]=Promise.resolve();else if(r)m(!1,`Route "${a.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let t=await o();null!=t&&(Object.assign(a,{[e]:t}),Object.assign(a,n(a)))}"object"==typeof a.lazy&&(a.lazy[e]=void 0,Object.values(a.lazy).every((e=>void 0===e))&&(a.lazy=void 0))})();return i[e]=s,s},we=new WeakMap;async function be(e){let t=e.matches.filter((e=>e.shouldLoad)),r={};return(await Promise.all(t.map((e=>e.resolve())))).forEach(((e,n)=>{r[t[n].route.id]=e})),r}async function Ee(e){return e.matches.some((e=>e.route.unstable_middleware))?Re(e,!1,(()=>be(e)),((e,t)=>({[t]:{type:"error",result:e}}))):be(e)}async function Re(e,t,r,n){let{matches:a,request:o,params:i,context:l}=e,s={handlerResult:void 0};try{let e=a.flatMap((e=>e.route.unstable_middleware?e.route.unstable_middleware.map((t=>[e.route.id,t])):[])),n=await xe({request:o,params:i,context:l},e,t,s,r);return t?n:s.handlerResult}catch(u){if(!s.middlewareError)throw u;let e=await n(s.middlewareError.error,s.middlewareError.routeId);return s.handlerResult?Object.assign(s.handlerResult,e):e}}async function xe(e,t,r,n,a,o=0){let{request:i}=e;if(i.signal.aborted){if(i.signal.reason)throw i.signal.reason;throw new Error(`Request aborted without an \`AbortSignal.reason\`: ${i.method} ${i.url}`)}let l=t[o];if(!l)return n.handlerResult=await a(),n.handlerResult;let[s,u]=l,c=!1,d=async()=>{if(c)throw new Error("You may only call `next()` once per middleware");c=!0,await xe(e,t,r,n,a,o+1)};try{let t=await u({request:e.request,params:e.params,context:e.context},d);return c?void 0===t?void 0:t:d()}catch(h){throw n.middlewareError?n.middlewareError.error!==h&&(n.middlewareError={routeId:s,error:h}):n.middlewareError={routeId:s,error:h},h}}function Se(e,t,r,n,a){let o=ge({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),i=function(e,t,r,n,a){let o=r[e.id];if(p(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if("function"==typeof e.lazy){let t=we.get(o);if(t)return{lazyRoutePromise:t,lazyHandlerPromise:t};let r=(async()=>{p("function"==typeof e.lazy,"No lazy route function found");let t=await e.lazy(),r={};for(let e in t){let n=t[e];if(void 0===n)continue;let i=(a=e,x.has(a)),l=void 0!==o[e]&&"hasErrorBoundary"!==e;i?m(!i,"Route property "+e+" is not a supported property to be returned from a lazy route function. This property will be ignored."):l?m(!l,`Route "${o.id}" has a static property "${e}" defined but its lazy function is also returning a value for this property. The lazy route property "${e}" will be ignored.`):r[e]=n}var a;Object.assign(o,r),Object.assign(o,{...n(o),lazy:void 0})})();return we.set(o,r),r.catch((()=>{})),{lazyRoutePromise:r,lazyHandlerPromise:r}}let i,l=Object.keys(e.lazy),s=[];for(let c of l){if(a&&a.includes(c))continue;let o=ge({key:c,route:e,manifest:r,mapRouteProperties:n});o&&(s.push(o),c===t&&(i=o))}let u=s.length>0?Promise.all(s).then((()=>{})):void 0;return u?.catch((()=>{})),i?.catch((()=>{})),{lazyRoutePromise:u,lazyHandlerPromise:i}}(n.route,Ye(r.method)?"action":"loader",t,e,a);return{middleware:o,route:i.lazyRoutePromise,handler:i.lazyHandlerPromise}}function Ce(e,t,r,n,a,o,i,l=null){let s=!1,u=Se(e,t,r,n,a);return{...n,_lazyPromises:u,shouldLoad:i,unstable_shouldRevalidateArgs:l,unstable_shouldCallHandler:e=>(s=!0,l?pe(n,"boolean"==typeof e?{...l,defaultShouldRevalidate:e}:l):i),resolve:e=>s||i||e&&"GET"===r.method&&(n.route.lazy||n.route.loader)?async function({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:a,scopedContext:o}){let i,l,s=Ye(e.method),u=s?"action":"loader",c=r=>{let n,i=new Promise(((e,t)=>n=t));l=()=>n(),e.signal.addEventListener("abort",l);let s=n=>"function"!=typeof r?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${u}" [routeId: ${t.route.id}]`)):r({request:e,params:t.params,context:o},...void 0!==n?[n]:[]),c=(async()=>{try{return{type:"data",result:await(a?a((e=>s(e))):s())}}catch(e){return{type:"error",result:e}}})();return Promise.race([c,i])};try{let a=s?t.route.action:t.route.loader;if(r||n)if(a){let e,[t]=await Promise.all([c(a).catch((t=>{e=t})),r,n]);if(void 0!==e)throw e;i=t}else{await r;let a=s?t.route.action:t.route.loader;if(!a){if("action"===u){let r=new URL(e.url),n=r.pathname+r.search;throw Ue(405,{method:e.method,pathname:n,routeId:t.route.id})}return{type:"data",result:void 0}}[i]=await Promise.all([c(a),n])}else{if(!a){let t=new URL(e.url);throw Ue(404,{pathname:t.pathname+t.search})}i=await c(a)}}catch(d){return{type:"error",result:d}}finally{l&&e.signal.removeEventListener("abort",l)}return i}({request:r,match:n,lazyHandlerPromise:u?.handler,lazyRoutePromise:u?.route,handlerOverride:e,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}function Pe(e,t,r,n,a,o,i,l=null){return n.map((n=>n.route.id!==a.route.id?{...n,shouldLoad:!1,unstable_shouldRevalidateArgs:l,unstable_shouldCallHandler:()=>!1,_lazyPromises:Se(e,t,r,n,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Ce(e,t,r,n,o,i,!0,l)))}async function Le(e){let{result:t,type:r}=e;if(qe(t)){let e;try{let r=t.headers.get("Content-Type");e=r&&/\bapplication\/json\b/.test(r)?null==t.body?null:await t.json():await t.text()}catch(n){return{type:"error",error:n}}return"error"===r?{type:"error",error:new K(t.status,t.statusText,e),statusCode:t.status,headers:t.headers}:{type:"data",data:e,statusCode:t.status,headers:t.headers}}return"error"===r?We(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new K(t.init?.status||500,void 0,t.data),statusCode:X(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:X(t)?t.status:void 0}:We(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function ke(e,t,r,n,a){let o=e.headers.get("Location");if(p(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!ie.test(o)){let i=n.slice(0,n.findIndex((e=>e.route.id===r))+1);o=ce(new URL(t.url),i,a,o),e.headers.set("Location",o)}return e}function De(e,t,r){if(ie.test(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),o=null!=F(a.pathname,r);if(a.origin===t.origin&&o)return a.pathname+a.search+a.hash}return e}function Te(e,t,r,n){let a=e.createURL(Fe(t)).toString(),o={signal:r};if(n&&Ye(n.formMethod)){let{formMethod:e,formEncType:t}=n;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(n.json)):"text/plain"===t?o.body=n.text:"application/x-www-form-urlencoded"===t&&n.formData?o.body=Me(n.formData):o.body=n.formData}return new Request(a,o)}function Me(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,"string"==typeof n?n:n.name);return t}function $e(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function Ae(e,t,r,n,a,o){let{loaderData:i,errors:l}=function(e,t,r,n=!1,a=!1){let o,i={},l=null,s=!1,u={},c=r&&Ie(r[1])?r[1].error:void 0;return e.forEach((r=>{if(!(r.route.id in t))return;let d=r.route.id,h=t[d];if(p(!Be(h),"Cannot handle redirect results in processLoaderData"),Ie(h)){let t=h.error;if(void 0!==c&&(t=c,c=void 0),l=l||{},a)l[d]=t;else{let r=ze(e,d);null==l[r.route.id]&&(l[r.route.id]=t)}n||(i[d]=ue),s||(s=!0,o=X(h.error)?h.error.status:500),h.headers&&(u[d]=h.headers)}else i[d]=h.data,h.statusCode&&200!==h.statusCode&&!s&&(o=h.statusCode),h.headers&&(u[d]=h.headers)})),void 0!==c&&r&&(l={[r[0]]:c},r[2]&&(i[r[2]]=void 0)),{loaderData:i,errors:l,statusCode:o||200,loaderHeaders:u}}(t,r,n);return a.filter((e=>!e.matches||e.matches.some((e=>e.shouldLoad)))).forEach((t=>{let{key:r,match:n,controller:a}=t,i=o[r];if(p(i,"Did not find corresponding fetcher result"),!a||!a.signal.aborted)if(Ie(i)){let t=ze(e.matches,n?.route.id);l&&l[t.route.id]||(l={...l,[t.route.id]:i.error}),e.fetchers.delete(r)}else if(Be(i))p(!1,"Unhandled fetcher revalidation redirect");else{let t=Qe(i.data);e.fetchers.set(r,t)}})),{loaderData:i,errors:l}}function Oe(e,t,r,n){let a=Object.entries(t).filter((([,e])=>e!==ue)).reduce(((e,[t,r])=>(e[t]=r,e)),{});for(let o of r){let r=o.route.id;if(!t.hasOwnProperty(r)&&e.hasOwnProperty(r)&&o.route.loader&&(a[r]=e[r]),n&&n.hasOwnProperty(r))break}return a}function je(e){return e?Ie(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function ze(e,t){return(t?e.slice(0,e.findIndex((e=>e.route.id===t))+1):[...e]).reverse().find((e=>!0===e.route.hasErrorBoundary))||e[0]}function _e(e){let t=1===e.length?e[0]:e.find((e=>e.index||!e.path||"/"===e.path))||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Ue(e,{pathname:t,routeId:r,method:n,type:a,message:o}={}){let i="Unknown Server Error",l="Unknown @remix-run/router error";return 400===e?(i="Bad Request",n&&t&&r?l=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:"invalid-body"===a&&(l="Unable to encode submission body")):403===e?(i="Forbidden",l=`Route "${r}" does not match URL "${t}"`):404===e?(i="Not Found",l=`No route matches URL "${t}"`):405===e&&(i="Method Not Allowed",n&&t&&r?l=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(l=`Invalid request method "${n.toUpperCase()}"`)),new K(e||500,i,new Error(l),!0)}function Ne(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[e,n]=t[r];if(Be(n))return{key:e,result:n}}}function Fe(e){return g({..."string"==typeof e?w(e):e,hash:""})}function He(e){return qe(e.result)&&te.has(e.result.status)}function Ie(e){return"error"===e.type}function Be(e){return"redirect"===(e&&e.type)}function We(e){return"object"==typeof e&&null!=e&&"type"in e&&"data"in e&&"init"in e&&"DataWithResponseInit"===e.type}function qe(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"object"==typeof e.headers&&void 0!==e.body}function Ye(e){return Q.has(e.toUpperCase())}function Ve(e){return new URLSearchParams(e).getAll("index").some((e=>""===e))}function Je(e,t){let r="string"==typeof t?w(t).search:t.search;if(e[e.length-1].route.index&&Ve(r||""))return e[e.length-1];let n=I(e);return n[n.length-1]}function Ke(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:o,json:i}=e;if(t&&r&&n)return null!=a?{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a}:null!=o?{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0}:void 0!==i?{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:i,text:void 0}:void 0}function Xe(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Ge(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Qe(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}var Ze=a.createContext(null);Ze.displayName="DataRouter";var et=a.createContext(null);et.displayName="DataRouterState";var tt=a.createContext({isTransitioning:!1});tt.displayName="ViewTransition";var rt=a.createContext(new Map);rt.displayName="Fetchers",a.createContext(null).displayName="Await";var nt=a.createContext(null);nt.displayName="Navigation";var at=a.createContext(null);at.displayName="Location";var ot=a.createContext({outlet:null,matches:[],isDataRoute:!1});ot.displayName="Route";var it=a.createContext(null);function lt(){return null!=a.useContext(at)}function st(){return p(lt(),"useLocation() may be used only in the context of a <Router> component."),a.useContext(at).location}it.displayName="RouteError";var ut="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ct(e){a.useContext(nt).static||a.useLayoutEffect(e)}function dt(){let{isDataRoute:e}=a.useContext(ot);return e?function(){let{router:e}=function(){let e=a.useContext(Ze);return p(e,gt("useNavigate")),e}(),t=wt("useNavigate"),r=a.useRef(!1);return ct((()=>{r.current=!0})),a.useCallback((async(n,a={})=>{m(r.current,ut),r.current&&("number"==typeof n?e.navigate(n):await e.navigate(n,{fromRouteId:t,...a}))}),[e,t])}():function(){p(lt(),"useNavigate() may be used only in the context of a <Router> component.");let e=a.useContext(Ze),{basename:t,navigator:r}=a.useContext(nt),{matches:n}=a.useContext(ot),{pathname:o}=st(),i=JSON.stringify(B(n)),l=a.useRef(!1);return ct((()=>{l.current=!0})),a.useCallback(((n,a={})=>{if(m(l.current,ut),!l.current)return;if("number"==typeof n)return void r.go(n);let s=W(n,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:q([t,s.pathname])),(a.replace?r.replace:r.push)(s,a.state,a)}),[t,r,i,o,e])}()}var ht=a.createContext(null);function ft(e,{relative:t}={}){let{matches:r}=a.useContext(ot),{pathname:n}=st(),o=JSON.stringify(B(r));return a.useMemo((()=>W(e,JSON.parse(o),n,"path"===t)),[e,o,n,t])}function pt(){let e=function(){let e=a.useContext(it),t=function(){let e=a.useContext(et);return p(e,gt("useRouteError")),e}(),r=wt("useRouteError");return void 0!==e?e:t.errors?.[r]}(),t=X(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:n},i={padding:"2px 4px",backgroundColor:n},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=a.createElement(a.Fragment,null,a.createElement("p",null,"💿 Hey developer 👋"),a.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",a.createElement("code",{style:i},"ErrorBoundary")," or"," ",a.createElement("code",{style:i},"errorElement")," prop on your route.")),a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),r?a.createElement("pre",{style:o},r):null,l)}var mt=a.createElement(pt,null),yt=class extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(ot.Provider,{value:this.props.routeContext},a.createElement(it.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function vt({routeContext:e,match:t,children:r}){let n=a.useContext(Ze);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),a.createElement(ot.Provider,{value:e},r)}function gt(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function wt(e){let t=function(e){let t=a.useContext(ot);return p(t,gt(e)),t}(e),r=t.matches[t.matches.length-1];return p(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}var bt={};function Et(e,t,r){t||bt[e]||(bt[e]=!0,m(!1,r))}var Rt={};function xt(e,t){e||Rt[t]||(Rt[t]=!0,console.warn(t))}function St(e){let t={hasErrorBoundary:e.hasErrorBoundary||null!=e.ErrorBoundary||null!=e.errorElement};return e.Component&&(e.element&&m(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:a.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&m(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:a.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&m(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:a.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var Ct=["HydrateFallback","hydrateFallbackElement"],Pt=class{constructor(){this.status="pending",this.promise=new Promise(((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}}))}};function Lt({router:e,flushSync:t}){let[r,n]=a.useState(e.state),[o,i]=a.useState(),[l,s]=a.useState({isTransitioning:!1}),[u,c]=a.useState(),[d,h]=a.useState(),[f,p]=a.useState(),m=a.useRef(new Map),y=a.useCallback(((r,{deletedFetchers:o,flushSync:l,viewTransitionOpts:f})=>{r.fetchers.forEach(((e,t)=>{void 0!==e.data&&m.current.set(t,e.data)})),o.forEach((e=>m.current.delete(e))),xt(!1===l||null!=t,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let y=null!=e.window&&null!=e.window.document&&"function"==typeof e.window.document.startViewTransition;if(xt(null==f||y,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),f&&y){if(t&&l){t((()=>{d&&(u&&u.resolve(),d.skipTransition()),s({isTransitioning:!0,flushSync:!0,currentLocation:f.currentLocation,nextLocation:f.nextLocation})}));let a=e.window.document.startViewTransition((()=>{t((()=>n(r)))}));return a.finished.finally((()=>{t((()=>{c(void 0),h(void 0),i(void 0),s({isTransitioning:!1})}))})),void t((()=>h(a)))}d?(u&&u.resolve(),d.skipTransition(),p({state:r,currentLocation:f.currentLocation,nextLocation:f.nextLocation})):(i(r),s({isTransitioning:!0,flushSync:!1,currentLocation:f.currentLocation,nextLocation:f.nextLocation}))}else t&&l?t((()=>n(r))):a.startTransition((()=>n(r)))}),[e.window,t,d,u]);a.useLayoutEffect((()=>e.subscribe(y)),[e,y]),a.useEffect((()=>{l.isTransitioning&&!l.flushSync&&c(new Pt)}),[l]),a.useEffect((()=>{if(u&&o&&e.window){let t=o,r=u.promise,l=e.window.document.startViewTransition((async()=>{a.startTransition((()=>n(t))),await r}));l.finished.finally((()=>{c(void 0),h(void 0),i(void 0),s({isTransitioning:!1})})),h(l)}}),[o,u,e.window]),a.useEffect((()=>{u&&o&&r.location.key===o.location.key&&u.resolve()}),[u,d,r.location,o]),a.useEffect((()=>{!l.isTransitioning&&f&&(i(f.state),s({isTransitioning:!0,flushSync:!1,currentLocation:f.currentLocation,nextLocation:f.nextLocation}),p(void 0))}),[l.isTransitioning,f]);let v=a.useMemo((()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:t=>e.navigate(t),push:(t,r,n)=>e.navigate(t,{state:r,preventScrollReset:n?.preventScrollReset}),replace:(t,r,n)=>e.navigate(t,{replace:!0,state:r,preventScrollReset:n?.preventScrollReset})})),[e]),g=e.basename||"/",w=a.useMemo((()=>({router:e,navigator:v,static:!1,basename:g})),[e,v,g]);return a.createElement(a.Fragment,null,a.createElement(Ze.Provider,{value:w},a.createElement(et.Provider,{value:r},a.createElement(rt.Provider,{value:m.current},a.createElement(tt.Provider,{value:l},a.createElement(Tt,{basename:g,location:r.location,navigationType:r.historyAction,navigator:v},a.createElement(kt,{routes:e.routes,future:e.future,state:r})))))),null)}var kt=a.memo((function({routes:e,future:t,state:r}){return function(e,t,r){p(lt(),"useRoutes() may be used only in the context of a <Router> component.");let n,{navigator:o,static:i}=a.useContext(nt),{matches:l}=a.useContext(ot),s=l[l.length-1],u=s?s.params:{},c=s?s.pathname:"/",d=s?s.pathnameBase:"/",h=s&&s.route;{let e=h&&h.path||"";Et(c,!h||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${c}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}n=st();let f=n.pathname||"/",y=f;if("/"!==d){let e=d.replace(/^\//,"").split("/");y="/"+f.replace(/^\//,"").split("/").slice(e.length).join("/")}let v=!i&&r&&r.matches&&r.matches.length>0?r.matches:C(e,{pathname:y});return m(h||null!=v,`No routes matched location "${n.pathname}${n.search}${n.hash}" `),m(null==v||void 0!==v[v.length-1].route.element||void 0!==v[v.length-1].route.Component||void 0!==v[v.length-1].route.lazy,`Matched leaf route at location "${n.pathname}${n.search}${n.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),function(e,t=[],r=null){if(null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(0!==t.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let n=e,o=r?.errors;if(null!=o){let e=n.findIndex((e=>e.route.id&&void 0!==o?.[e.route.id]));p(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),n=n.slice(0,Math.min(n.length,e+1))}let i=!1,l=-1;if(r)for(let a=0;a<n.length;a++){let e=n[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(l=a),e.route.id){let{loaderData:t,errors:a}=r,o=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!a||void 0===a[e.route.id]);if(e.route.lazy||o){i=!0,n=l>=0?n.slice(0,l+1):[n[0]];break}}}return n.reduceRight(((e,s,u)=>{let c,d=!1,h=null,f=null;r&&(c=o&&s.route.id?o[s.route.id]:void 0,h=s.route.errorElement||mt,i&&(l<0&&0===u?(Et("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,f=null):l===u&&(d=!0,f=s.route.hydrateFallbackElement||null)));let p=t.concat(n.slice(0,u+1)),m=()=>{let t;return t=c?h:d?f:s.route.Component?a.createElement(s.route.Component,null):s.route.element?s.route.element:e,a.createElement(vt,{match:s,routeContext:{outlet:e,matches:p,isDataRoute:null!=r},children:t})};return r&&(s.route.ErrorBoundary||s.route.errorElement||0===u)?a.createElement(yt,{location:r.location,revalidation:r.revalidation,component:h,error:c,children:m(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):m()}),null)}(v&&v.map((e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:q([d,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:q([d,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,r)}(e,0,r)}));function Dt(e){return function(e){let t=a.useContext(ot).outlet;return t?a.createElement(ht.Provider,{value:e},t):t}(e.context)}function Tt({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:o,static:i=!1}){p(!lt(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),s=a.useMemo((()=>({basename:l,navigator:o,static:i,future:{}})),[l,o,i]);"string"==typeof r&&(r=w(r));let{pathname:u="/",search:c="",hash:d="",state:h=null,key:f="default"}=r,y=a.useMemo((()=>{let e=F(u,l);return null==e?null:{location:{pathname:e,search:c,hash:d,state:h,key:f},navigationType:n}}),[l,u,c,d,h,f,n]);return m(null!=y,`<Router basename="${l}"> is not able to match the URL "${u}${c}${d}" because it does not start with the basename, so the <Router> won't render anything.`),null==y?null:a.createElement(nt.Provider,{value:s},a.createElement(at.Provider,{children:t,value:y}))}var Mt="get",$t="application/x-www-form-urlencoded";function At(e){return null!=e&&"string"==typeof e.tagName}var Ot=null,jt=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function zt(e){return null==e||jt.has(e)?e:(m(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${$t}"`),null)}function _t(e,t){if(!1===e||null==e)throw new Error(t)}function Ut(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}function Nt(e,t,r,n,a,o){let i=(e,t)=>!r[t]||e.route.id!==r[t].route.id,l=(e,t)=>r[t].pathname!==e.pathname||r[t].route.path?.endsWith("*")&&r[t].params["*"]!==e.params["*"];return"assets"===o?t.filter(((e,t)=>i(e,t)||l(e,t))):"data"===o?t.filter(((t,o)=>{let s=n.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,o)||l(t,o))return!0;if(t.route.shouldRevalidate){let n=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof n)return n}return!0})):[]}function Ft(){let e=a.useContext(Ze);return _t(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Ht=a.createContext(void 0);function It(){let e=a.useContext(Ht);return _t(e,"You must render this element inside a <HydratedRouter> element"),e}function Bt(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function Wt({page:e,...t}){let{router:r}=Ft(),n=a.useMemo((()=>C(r.routes,e,r.basename)),[r.routes,e,r.basename]);return n?a.createElement(qt,{page:e,matches:n,...t}):null}function qt({page:e,matches:t,...r}){let n=st(),{manifest:o,routeModules:i}=It(),{basename:l}=Ft(),{loaderData:s,matches:u}=function(){let e=a.useContext(et);return _t(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}(),c=a.useMemo((()=>Nt(e,t,u,o,n,"data")),[e,t,u,o,n]),d=a.useMemo((()=>Nt(e,t,u,o,n,"assets")),[e,t,u,o,n]),h=a.useMemo((()=>{if(e===n.pathname+n.search+n.hash)return[];let r=new Set,a=!1;if(t.forEach((e=>{let t=o.routes[e.route.id];t&&t.hasLoader&&(!c.some((t=>t.route.id===e.route.id))&&e.route.id in s&&i[e.route.id]?.shouldRevalidate||t.hasClientLoader?a=!0:r.add(e.route.id))})),0===r.size)return[];let u=function(e,t){let r="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===r.pathname?r.pathname="_root.data":t&&"/"===F(r.pathname,t)?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}(e,l);return a&&r.size>0&&u.searchParams.set("_routes",t.filter((e=>r.has(e.route.id))).map((e=>e.route.id)).join(",")),[u.pathname+u.search]}),[l,s,n,o,c,t,e,i]),f=a.useMemo((()=>function(e,t,{includeHydrateFallback:r}={}){return n=e.map((e=>{let n=t.routes[e.route.id];if(!n)return[];let a=[n.module];return n.clientActionModule&&(a=a.concat(n.clientActionModule)),n.clientLoaderModule&&(a=a.concat(n.clientLoaderModule)),r&&n.hydrateFallbackModule&&(a=a.concat(n.hydrateFallbackModule)),n.imports&&(a=a.concat(n.imports)),a})).flat(1),[...new Set(n)];var n}(d,o)),[d,o]),p=function(e){let{manifest:t,routeModules:r}=It(),[n,o]=a.useState([]);return a.useEffect((()=>{let n=!1;return async function(e,t,r){return function(e){let t=new Set;return new Set(void 0),e.reduce(((e,r)=>{let n=JSON.stringify(function(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}(r));return t.has(n)||(t.add(n),e.push({key:n,link:r})),e}),[])}((await Promise.all(e.map((async e=>{let n=t.routes[e.route.id];if(n){let e=await async function(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}(n,r);return e.links?e.links():[]}return[]})))).flat(1).filter(Ut).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}(e,t,r).then((e=>{n||o(e)})),()=>{n=!0}}),[e,t,r]),n}(d);return a.createElement(a.Fragment,null,h.map((e=>a.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...r}))),f.map((e=>a.createElement("link",{key:e,rel:"modulepreload",href:e,...r}))),p.map((({key:e,link:t})=>a.createElement("link",{key:e,...t}))))}function Yt(...e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}Ht.displayName="FrameworkContext";var Vt="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{Vt&&(window.__reactRouterVersion="7.6.0")}catch(or){}function Jt(e,t){return function(e){const t=e.window?e.window:"undefined"!=typeof window?window:void 0,r=void 0!==t&&void 0!==t.document&&void 0!==t.document.createElement;p(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n,a,o,i=e.hydrationRouteProperties||[],l=e.mapRouteProperties||le,s={},u=S(e.routes,l,void 0,s),c=e.basename||"/",d=e.dataStrategy||Ee,h={unstable_middleware:!1,...e.future},f=null,y=new Set,g=null,w=null,R=null,x=null!=e.hydrationData,L=C(u,e.history.location,c),k=!1,D=null;if(null!=L||e.patchRoutesOnNavigation)if(L&&!e.hydrationData&&rt(L,u,e.history.location.pathname).active&&(L=null),L)if(L.some((e=>e.route.lazy)))a=!1;else if(L.some((e=>e.route.loader))){let t=e.hydrationData?e.hydrationData.loaderData:null,r=e.hydrationData?e.hydrationData.errors:null;if(r){let e=L.findIndex((e=>void 0!==r[e.route.id]));a=L.slice(0,e+1).every((e=>!fe(e.route,t,r)))}else a=L.every((e=>!fe(e.route,t,r)))}else a=!0;else{a=!1,L=[];let t=rt(null,u,e.history.location.pathname);t.active&&t.matches&&(k=!0,L=t.matches)}else{let t=Ue(404,{pathname:e.history.location.pathname}),{matches:r,route:n}=_e(u);a=!0,L=r,D={[n.id]:t}}let T,M,$={historyAction:e.history.action,location:e.history.location,matches:L,initialized:a,navigation:ne,restoreScrollPosition:null==e.hydrationData&&null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||D,fetchers:new Map,blockers:new Map},A="POP",O=!1,j=!1,z=new Map,_=null,U=!1,N=!1,H=new Set,I=new Map,B=0,W=-1,q=new Map,Y=new Set,V=new Map,J=new Map,K=new Set,G=new Map,Q=null;function Z(e,t={}){$={...$,...e};let r=[],n=[];$.fetchers.forEach(((e,t)=>{"idle"===e.state&&(K.has(t)?r.push(t):n.push(t))})),K.forEach((e=>{$.fetchers.has(e)||I.has(e)||r.push(e)})),[...y].forEach((e=>e($,{deletedFetchers:r,viewTransitionOpts:t.viewTransitionOpts,flushSync:!0===t.flushSync}))),r.forEach((e=>Se(e))),n.forEach((e=>$.fetchers.delete(e)))}function ee(t,r,{flushSync:a}={}){let o,i=null!=$.actionData&&null!=$.navigation.formMethod&&Ye($.navigation.formMethod)&&"loading"===$.navigation.state&&!0!==t.state?._isRedirect;o=r.actionData?Object.keys(r.actionData).length>0?r.actionData:null:i?$.actionData:null;let l=r.loaderData?Oe($.loaderData,r.loaderData,r.matches||[],r.errors):$.loaderData,s=$.blockers;s.size>0&&(s=new Map(s),s.forEach(((e,t)=>s.set(t,oe))));let c,d=!0===O||null!=$.navigation.formMethod&&Ye($.navigation.formMethod)&&!0!==t.state?._isRedirect;if(n&&(u=n,n=void 0),U||"POP"===A||("PUSH"===A?e.history.push(t,t.state):"REPLACE"===A&&e.history.replace(t,t.state)),"POP"===A){let e=z.get($.location.pathname);e&&e.has(t.pathname)?c={currentLocation:$.location,nextLocation:t}:z.has(t.pathname)&&(c={currentLocation:t,nextLocation:$.location})}else if(j){let e=z.get($.location.pathname);e?e.add(t.pathname):(e=new Set([t.pathname]),z.set($.location.pathname,e)),c={currentLocation:$.location,nextLocation:t}}Z({...r,actionData:o,loaderData:l,historyAction:A,location:t,initialized:!0,navigation:ne,revalidation:"idle",restoreScrollPosition:tt(t,r.matches||$.matches),preventScrollReset:d,blockers:s},{viewTransitionOpts:c,flushSync:!0===a}),A="POP",O=!1,j=!1,U=!1,N=!1,Q?.resolve(),Q=null}async function te(t,r,a){T&&T.abort(),T=null,A=t,U=!0===(a&&a.startUninterruptedRevalidation),function(e,t){if(g&&R){let r=et(e,t);g[r]=R()}}($.location,$.matches),O=!0===(a&&a.preventScrollReset),j=!0===(a&&a.enableViewTransition);let o=n||u,d=a&&a.overrideNavigation,h=a?.initialHydration&&$.matches&&$.matches.length>0&&!k?$.matches:C(o,r,c),f=!0===(a&&a.flushSync);if(h&&$.initialized&&!N&&(m=r,(p=$.location).pathname===m.pathname&&p.search===m.search&&(""===p.hash?""!==m.hash:p.hash===m.hash||""!==m.hash))&&!(a&&a.submission&&Ye(a.submission.formMethod)))return void ee(r,{matches:h},{flushSync:f});var p,m;let y=rt(h,o,r.pathname);if(y.active&&y.matches&&(h=y.matches),!h){let{error:e,notFoundMatches:t,route:n}=Ze(r.pathname);return void ee(r,{matches:t,loaderData:{},errors:{[n.id]:e}},{flushSync:f})}T=new AbortController;let v,w=Te(e.history,r,T.signal,a&&a.submission),b=new E(e.unstable_getContext?await e.unstable_getContext():void 0);if(a&&a.pendingError)v=[ze(h).route.id,{type:"error",error:a.pendingError}];else if(a&&a.submission&&Ye(a.submission.formMethod)){let t=await async function(e,t,r,n,a,o,u,d={}){ge();let h,f=function(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}(t,r);if(Z({navigation:f},{flushSync:!0===d.flushSync}),o){let r=await nt(n,t.pathname,e.signal);if("aborted"===r.type)return{shortCircuited:!0};if("error"===r.type){let e=ze(r.partialMatches).route.id;return{matches:r.partialMatches,pendingActionResult:[e,{type:"error",error:r.error}]}}if(!r.matches){let{notFoundMatches:e,error:r,route:n}=Ze(t.pathname);return{matches:e,pendingActionResult:[n.id,{type:"error",error:r}]}}n=r.matches}let p=Je(n,t);if(p.route.action||p.route.lazy){let t=Pe(l,s,e,n,p,u?[]:i,a),r=await ye(e,t,a,null);if(h=r[p.route.id],!h)for(let e of n)if(r[e.route.id]){h=r[e.route.id];break}if(e.signal.aborted)return{shortCircuited:!0}}else h={type:"error",error:Ue(405,{method:e.method,pathname:t.pathname,routeId:p.route.id})};if(Be(h)){let t;return t=d&&null!=d.replace?d.replace:De(h.response.headers.get("Location"),new URL(e.url),c)===$.location.pathname+$.location.search,await pe(e,h,!0,{submission:r,replace:t}),{shortCircuited:!0}}if(Ie(h)){let e=ze(n,p.route.id);return!0!==(d&&d.replace)&&(A="PUSH"),{matches:n,pendingActionResult:[e.route.id,h,p.route.id]}}return{matches:n,pendingActionResult:[p.route.id,h]}}(w,r,a.submission,h,b,y.active,a&&!0===a.initialHydration,{replace:a.replace,flushSync:f});if(t.shortCircuited)return;if(t.pendingActionResult){let[e,n]=t.pendingActionResult;if(Ie(n)&&X(n.error)&&404===n.error.status)return T=null,void ee(r,{matches:t.matches,loaderData:{},errors:{[e]:n.error}})}h=t.matches||h,v=t.pendingActionResult,d=Xe(r,a.submission),f=!1,y.active=!1,w=Te(e.history,w.url,w.signal)}let{shortCircuited:x,matches:S,loaderData:P,errors:L}=await async function(t,r,a,o,d,h,f,p,m,y,v,g){let w=h||Xe(r,f),b=f||p||Ke(w),E=!U&&!y;if(d){if(E){let e=ue(g);Z({navigation:w,...void 0!==e?{actionData:e}:{}},{flushSync:v})}let e=await nt(a,r.pathname,t.signal);if("aborted"===e.type)return{shortCircuited:!0};if("error"===e.type){let t=ze(e.partialMatches).route.id;return{matches:e.partialMatches,loaderData:{},errors:{[t]:e.error}}}if(!e.matches){let{error:e,notFoundMatches:t,route:n}=Ze(r.pathname);return{matches:t,loaderData:{},errors:{[n.id]:e}}}a=e.matches}let R=n||u,{dsMatches:x,revalidatingFetchers:S}=he(t,o,l,s,e.history,$,a,b,r,y?[]:i,!0===y,N,H,K,V,Y,R,c,g);if(W=++B,!e.dataStrategy&&!x.some((e=>e.shouldLoad))&&0===S.length){let e=$e();return ee(r,{matches:a,loaderData:{},errors:g&&Ie(g[1])?{[g[0]]:g[1].error}:null,...je(g),...e?{fetchers:new Map($.fetchers)}:{}},{flushSync:v}),{shortCircuited:!0}}if(E){let e={};if(!d){e.navigation=w;let t=ue(g);void 0!==t&&(e.actionData=t)}S.length>0&&(e.fetchers=function(e){return e.forEach((e=>{let t=$.fetchers.get(e.key),r=Ge(void 0,t?t.data:void 0);$.fetchers.set(e.key,r)})),new Map($.fetchers)}(S)),Z(e,{flushSync:v})}S.forEach((e=>{Ce(e.key),e.controller&&I.set(e.key,e.controller)}));let C=()=>S.forEach((e=>Ce(e.key)));T&&T.signal.addEventListener("abort",C);let{loaderResults:P,fetcherResults:L}=await ve(x,S,t,o);if(t.signal.aborted)return{shortCircuited:!0};T&&T.signal.removeEventListener("abort",C),S.forEach((e=>I.delete(e.key)));let k=Ne(P);if(k)return await pe(t,k.result,!0,{replace:m}),{shortCircuited:!0};if(k=Ne(L),k)return Y.add(k.key),await pe(t,k.result,!0,{replace:m}),{shortCircuited:!0};let{loaderData:D,errors:M}=Ae($,a,P,g,S,L);y&&$.errors&&(M={...$.errors,...M});let A=$e(),O=Fe(W);return{matches:a,loaderData:D,errors:M,...A||O||S.length>0?{fetchers:new Map($.fetchers)}:{}}}(w,r,h,b,y.active,d,a&&a.submission,a&&a.fetcherSubmission,a&&a.replace,a&&!0===a.initialHydration,f,v);x||(T=null,ee(r,{matches:S||h,...je(v),loaderData:P,errors:L}))}function ue(e){return e&&!Ie(e[1])?{[e[0]]:e[1].data}:$.actionData?0===Object.keys($.actionData).length?null:$.actionData:void 0}async function pe(e,n,a,{submission:o,fetcherSubmission:i,preventScrollReset:l,replace:s}={}){n.response.headers.has("X-Remix-Revalidate")&&(N=!0);let u=n.response.headers.get("Location");p(u,"Expected a Location header on the redirect Response"),u=De(u,new URL(e.url),c);let d=v($.location,u,{_isRedirect:!0});if(r){let e=!1;if(n.response.headers.has("X-Remix-Reload-Document"))e=!0;else if(ie.test(u)){const r=b(u,!0);e=r.origin!==t.location.origin||null==F(r.pathname,c)}if(e)return void(s?t.location.replace(u):t.location.assign(u))}T=null;let h=!0===s||n.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:f,formAction:m,formEncType:y}=$.navigation;!o&&!i&&f&&m&&y&&(o=Ke($.navigation));let g=o||i;if(re.has(n.response.status)&&g&&Ye(g.formMethod))await te(h,d,{submission:{...g,formAction:u},preventScrollReset:l||O,enableViewTransition:a?j:void 0});else{let e=Xe(d,o);await te(h,d,{overrideNavigation:e,fetcherSubmission:i,preventScrollReset:l||O,enableViewTransition:a?j:void 0})}}async function ye(e,t,r,n){let a,o={};try{a=await async function(e,t,r,n,a){r.some((e=>e._lazyPromises?.middleware))&&await Promise.all(r.map((e=>e._lazyPromises?.middleware)));let o={request:t,params:r[0].params,context:a,matches:r},i=await e({...o,fetcherKey:n,unstable_runClientMiddleware:e=>{let t=o;return Re(t,!1,(()=>e({...t,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}})),((e,t)=>({[t]:{type:"error",result:e}})))}});try{await Promise.all(r.flatMap((e=>[e._lazyPromises?.handler,e._lazyPromises?.route])))}catch(or){}return i}(d,e,t,n,r)}catch(or){return t.filter((e=>e.shouldLoad)).forEach((t=>{o[t.route.id]={type:"error",error:or}})),o}if(e.signal.aborted)return o;for(let[i,l]of Object.entries(a))if(He(l)){let r=l.result;o[i]={type:"redirect",response:ke(r,e,i,t,c)}}else o[i]=await Le(l);return o}async function ve(e,t,r,n){let a=ye(r,e,n,null),o=Promise.all(t.map((async e=>{if(e.matches&&e.match&&e.request&&e.controller){let t=(await ye(e.request,e.matches,n,e.key))[e.match.route.id];return{[e.key]:t}}return Promise.resolve({[e.key]:{type:"error",error:Ue(404,{pathname:e.path})}})})));return{loaderResults:await a,fetcherResults:(await o).reduce(((e,t)=>Object.assign(e,t)),{})}}function ge(){N=!0,V.forEach(((e,t)=>{I.has(t)&&H.add(t),Ce(t)}))}function we(e,t,r={}){$.fetchers.set(e,t),Z({fetchers:new Map($.fetchers)},{flushSync:!0===(r&&r.flushSync)})}function be(e,t,r,n={}){let a=ze($.matches,t);Se(e),Z({errors:{[a.route.id]:r},fetchers:new Map($.fetchers)},{flushSync:!0===(n&&n.flushSync)})}function xe(e){return J.set(e,(J.get(e)||0)+1),K.has(e)&&K.delete(e),$.fetchers.get(e)||ae}function Se(e){let t=$.fetchers.get(e);!I.has(e)||t&&"loading"===t.state&&q.has(e)||Ce(e),V.delete(e),q.delete(e),Y.delete(e),K.delete(e),H.delete(e),$.fetchers.delete(e)}function Ce(e){let t=I.get(e);t&&(t.abort(),I.delete(e))}function Me(e){for(let t of e){let e=Qe(xe(t).data);$.fetchers.set(t,e)}}function $e(){let e=[],t=!1;for(let r of Y){let n=$.fetchers.get(r);p(n,`Expected fetcher: ${r}`),"loading"===n.state&&(Y.delete(r),e.push(r),t=!0)}return Me(e),t}function Fe(e){let t=[];for(let[r,n]of q)if(n<e){let e=$.fetchers.get(r);p(e,`Expected fetcher: ${r}`),"loading"===e.state&&(Ce(r),q.delete(r),t.push(r))}return Me(t),t.length>0}function We(e){$.blockers.delete(e),G.delete(e)}function qe(e,t){let r=$.blockers.get(e)||oe;p("unblocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"proceeding"===t.state||"blocked"===r.state&&"unblocked"===t.state||"proceeding"===r.state&&"unblocked"===t.state,`Invalid blocker state transition: ${r.state} -> ${t.state}`);let n=new Map($.blockers);n.set(e,t),Z({blockers:n})}function Ve({currentLocation:e,nextLocation:t,historyAction:r}){if(0===G.size)return;G.size>1&&m(!1,"A router only supports one blocker at a time");let n=Array.from(G.entries()),[a,o]=n[n.length-1],i=$.blockers.get(a);return i&&"proceeding"===i.state?void 0:o({currentLocation:e,nextLocation:t,historyAction:r})?a:void 0}function Ze(e){let t=Ue(404,{pathname:e}),r=n||u,{matches:a,route:o}=_e(r);return{notFoundMatches:a,route:o,error:t}}function et(e,t){return w&&w(e,t.map((e=>function(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}(e,$.loaderData))))||e.key}function tt(e,t){if(g){let r=et(e,t),n=g[r];if("number"==typeof n)return n}return null}function rt(t,r,n){if(e.patchRoutesOnNavigation){if(!t)return{active:!0,matches:P(r,n,c,!0)||[]};if(Object.keys(t[0].params).length>0)return{active:!0,matches:P(r,n,c,!0)}}return{active:!1,matches:null}}async function nt(t,r,a,o){if(!e.patchRoutesOnNavigation)return{type:"success",matches:t};let i=t;for(;;){let t=null==n,d=n||u,h=s;try{await e.patchRoutesOnNavigation({signal:a,path:r,matches:i,fetcherKey:o,patch:(e,t)=>{a.aborted||me(e,t,d,h,l)}})}catch(or){return{type:"error",error:or,partialMatches:i}}finally{t&&!a.aborted&&(u=[...u])}if(a.aborted)return{type:"aborted"};let f=C(d,r,c);if(f)return{type:"success",matches:f};let p=P(d,r,c,!0);if(!p||i.length===p.length&&i.every(((e,t)=>e.route.id===p[t].route.id)))return{type:"success",matches:null};i=p}}return o={get basename(){return c},get future(){return h},get state(){return $},get routes(){return u},get window(){return t},initialize:function(){if(f=e.history.listen((({action:t,location:r,delta:n})=>{if(M)return M(),void(M=void 0);m(0===G.size||null!=n,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let a=Ve({currentLocation:$.location,nextLocation:r,historyAction:t});if(a&&null!=n){let t=new Promise((e=>{M=e}));return e.history.go(-1*n),void qe(a,{state:"blocked",location:r,proceed(){qe(a,{state:"proceeding",proceed:void 0,reset:void 0,location:r}),t.then((()=>e.history.go(n)))},reset(){let e=new Map($.blockers);e.set(a,oe),Z({blockers:e})}})}return te(t,r)})),r){!function(e,t){try{let r=e.sessionStorage.getItem(se);if(r){let e=JSON.parse(r);for(let[r,n]of Object.entries(e||{}))n&&Array.isArray(n)&&t.set(r,new Set(n||[]))}}catch(or){}}(t,z);let e=()=>function(e,t){if(t.size>0){let n={};for(let[e,r]of t)n[e]=[...r];try{e.sessionStorage.setItem(se,JSON.stringify(n))}catch(r){m(!1,`Failed to save applied view transitions in sessionStorage (${r}).`)}}}(t,z);t.addEventListener("pagehide",e),_=()=>t.removeEventListener("pagehide",e)}return $.initialized||te("POP",$.location,{initialHydration:!0}),o},subscribe:function(e){return y.add(e),()=>y.delete(e)},enableScrollRestoration:function(e,t,r){if(g=e,R=t,w=r||null,!x&&$.navigation===ne){x=!0;let e=tt($.location,$.matches);null!=e&&Z({restoreScrollPosition:e})}return()=>{g=null,R=null,w=null}},navigate:async function t(r,n){if("number"==typeof r)return void e.history.go(r);let a=ce($.location,$.matches,c,r,n?.fromRouteId,n?.relative),{path:o,submission:i,error:l}=de(!1,a,n),s=$.location,u=v($.location,o,n&&n.state);u={...u,...e.history.encodeLocation(u)};let d=n&&null!=n.replace?n.replace:void 0,h="PUSH";!0===d?h="REPLACE":!1===d||null!=i&&Ye(i.formMethod)&&i.formAction===$.location.pathname+$.location.search&&(h="REPLACE");let f=n&&"preventScrollReset"in n?!0===n.preventScrollReset:void 0,p=!0===(n&&n.flushSync),m=Ve({currentLocation:s,nextLocation:u,historyAction:h});m?qe(m,{state:"blocked",location:u,proceed(){qe(m,{state:"proceeding",proceed:void 0,reset:void 0,location:u}),t(r,n)},reset(){let e=new Map($.blockers);e.set(m,oe),Z({blockers:e})}}):await te(h,u,{submission:i,pendingError:l,preventScrollReset:f,replace:n&&n.replace,enableViewTransition:n&&n.viewTransition,flushSync:p})},fetch:async function(t,r,a,o){Ce(t);let d=!0===(o&&o.flushSync),h=n||u,f=ce($.location,$.matches,c,a,r,o?.relative),m=C(h,f,c),y=rt(m,h,f);if(y.active&&y.matches&&(m=y.matches),!m)return void be(t,r,Ue(404,{pathname:f}),{flushSync:d});let{path:v,submission:g,error:w}=de(!0,f,o);if(w)return void be(t,r,w,{flushSync:d});let b=Je(m,v),R=new E(e.unstable_getContext?await e.unstable_getContext():void 0),x=!0===(o&&o.preventScrollReset);g&&Ye(g.formMethod)?await async function(t,r,a,o,d,h,f,m,y,v){function g(e){if(!e.route.action&&!e.route.lazy){let e=Ue(405,{method:v.formMethod,pathname:a,routeId:r});return be(t,r,e,{flushSync:m}),!0}return!1}if(ge(),V.delete(t),!f&&g(o))return;let w=$.fetchers.get(t);we(t,function(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}(v,w),{flushSync:m});let b=new AbortController,E=Te(e.history,a,b.signal,v);if(f){let e=await nt(d,a,E.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void be(t,r,e.error,{flushSync:m});if(!e.matches)return void be(t,r,Ue(404,{pathname:a}),{flushSync:m});if(g(o=Je(d=e.matches,a)))return}I.set(t,b);let R=B,x=Pe(l,s,E,d,o,i,h),S=(await ye(E,x,h,t))[o.route.id];if(E.signal.aborted)return void(I.get(t)===b&&I.delete(t));if(K.has(t)){if(Be(S)||Ie(S))return void we(t,Qe(void 0))}else{if(Be(S))return I.delete(t),W>R?void we(t,Qe(void 0)):(Y.add(t),we(t,Ge(v)),pe(E,S,!1,{fetcherSubmission:v,preventScrollReset:y}));if(Ie(S))return void be(t,r,S.error)}let P=$.navigation.location||$.location,L=Te(e.history,P,b.signal),k=n||u,D="idle"!==$.navigation.state?C(k,$.navigation.location,c):$.matches;p(D,"Didn't find any matches after fetcher action");let M=++B;q.set(t,M);let O=Ge(v,S.data);$.fetchers.set(t,O);let{dsMatches:j,revalidatingFetchers:z}=he(L,h,l,s,e.history,$,D,v,P,i,!1,N,H,K,V,Y,k,c,[o.route.id,S]);z.filter((e=>e.key!==t)).forEach((e=>{let t=e.key,r=$.fetchers.get(t),n=Ge(void 0,r?r.data:void 0);$.fetchers.set(t,n),Ce(t),e.controller&&I.set(t,e.controller)})),Z({fetchers:new Map($.fetchers)});let _=()=>z.forEach((e=>Ce(e.key)));b.signal.addEventListener("abort",_);let{loaderResults:U,fetcherResults:F}=await ve(j,z,L,h);if(b.signal.aborted)return;if(b.signal.removeEventListener("abort",_),q.delete(t),I.delete(t),z.forEach((e=>I.delete(e.key))),$.fetchers.has(t)){let e=Qe(S.data);$.fetchers.set(t,e)}let J=Ne(U);if(J)return pe(L,J.result,!1,{preventScrollReset:y});if(J=Ne(F),J)return Y.add(J.key),pe(L,J.result,!1,{preventScrollReset:y});let{loaderData:X,errors:G}=Ae($,D,U,void 0,z,F);Fe(M),"loading"===$.navigation.state&&M>W?(p(A,"Expected pending action"),T&&T.abort(),ee($.navigation.location,{matches:D,loaderData:X,errors:G,fetchers:new Map($.fetchers)})):(Z({errors:G,loaderData:Oe($.loaderData,X,D,G),fetchers:new Map($.fetchers)}),N=!1)}(t,r,v,b,m,R,y.active,d,x,g):(V.set(t,{routeId:r,path:v}),await async function(t,r,n,a,o,u,c,d,h,f){let p=$.fetchers.get(t);we(t,Ge(f,p?p.data:void 0),{flushSync:d});let m=new AbortController,y=Te(e.history,n,m.signal);if(c){let e=await nt(o,n,y.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void be(t,r,e.error,{flushSync:d});if(!e.matches)return void be(t,r,Ue(404,{pathname:n}),{flushSync:d});a=Je(o=e.matches,n)}I.set(t,m);let v=B,g=Pe(l,s,y,o,a,i,u),w=(await ye(y,g,u,t))[a.route.id];if(I.get(t)===m&&I.delete(t),!y.signal.aborted){if(!K.has(t))return Be(w)?W>v?void we(t,Qe(void 0)):(Y.add(t),void(await pe(y,w,!1,{preventScrollReset:h}))):void(Ie(w)?be(t,r,w.error):we(t,Qe(w.data)));we(t,Qe(void 0))}}(t,r,v,b,m,R,y.active,d,x,g))},revalidate:function(){Q||(Q=function(){let e,t,r=new Promise(((n,a)=>{e=async e=>{n(e);try{await r}catch(or){}},t=async e=>{a(e);try{await r}catch(or){}}}));return{promise:r,resolve:e,reject:t}}()),ge(),Z({revalidation:"loading"});let e=Q.promise;return"submitting"===$.navigation.state?e:"idle"===$.navigation.state?(te($.historyAction,$.location,{startUninterruptedRevalidation:!0}),e):(te(A||$.historyAction,$.navigation.location,{overrideNavigation:$.navigation,enableViewTransition:!0===j}),e)},createHref:t=>e.history.createHref(t),encodeLocation:t=>e.history.encodeLocation(t),getFetcher:xe,deleteFetcher:function(e){let t=(J.get(e)||0)-1;t<=0?(J.delete(e),K.add(e)):J.set(e,t),Z({fetchers:new Map($.fetchers)})},dispose:function(){f&&f(),_&&_(),y.clear(),T&&T.abort(),$.fetchers.forEach(((e,t)=>Se(t))),$.blockers.forEach(((e,t)=>We(t)))},getBlocker:function(e,t){let r=$.blockers.get(e)||oe;return G.get(e)!==t&&G.set(e,t),r},deleteBlocker:We,patchRoutes:function(e,t){let r=null==n;me(e,t,n||u,s,l),r&&(u=[...u],Z({}))},_internalFetchControllers:I,_internalSetRoutes:function(e){s={},n=S(e,l,void 0,s)}},o}({basename:t?.basename,unstable_getContext:t?.unstable_getContext,future:t?.future,history:f({window:t?.window}),hydrationData:Kt(),routes:e,mapRouteProperties:St,hydrationRouteProperties:Ct,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation,window:t?.window}).initialize()}function Kt(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:Xt(e.errors)}),e}function Xt(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,a]of t)if(a&&"RouteErrorResponse"===a.__type)r[n]=new K(a.status,a.statusText,a.data,!0===a.internal);else if(a&&"Error"===a.__type){if(a.__subType){let e=window[a.__subType];if("function"==typeof e)try{let t=new e(a.message);t.stack="",r[n]=t}catch(or){}}if(null==r[n]){let e=new Error(a.message);e.stack="",r[n]=e}}else r[n]=a;return r}var Gt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Qt=a.forwardRef((function({onClick:e,discover:t="render",prefetch:r="none",relative:n,reloadDocument:o,replace:i,state:l,target:s,to:u,preventScrollReset:c,viewTransition:d,...h},f){let y,{basename:v}=a.useContext(nt),w="string"==typeof u&&Gt.test(u),b=!1;if("string"==typeof u&&w&&(y=u,Vt))try{let e=new URL(window.location.href),t=u.startsWith("//")?new URL(e.protocol+u):new URL(u),r=F(t.pathname,v);t.origin===e.origin&&null!=r?u=r+t.search+t.hash:b=!0}catch(or){m(!1,`<Link to="${u}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let E=function(e,{relative:t}={}){p(lt(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=a.useContext(nt),{hash:o,pathname:i,search:l}=ft(e,{relative:t}),s=i;return"/"!==r&&(s="/"===i?r:q([r,i])),n.createHref({pathname:s,search:l,hash:o})}(u,{relative:n}),[R,x,S]=function(e,t){let r=a.useContext(Ht),[n,o]=a.useState(!1),[i,l]=a.useState(!1),{onFocus:s,onBlur:u,onMouseEnter:c,onMouseLeave:d,onTouchStart:h}=t,f=a.useRef(null);a.useEffect((()=>{if("render"===e&&l(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{l(e.isIntersecting)}))}),{threshold:.5});return f.current&&e.observe(f.current),()=>{e.disconnect()}}}),[e]),a.useEffect((()=>{if(n){let e=setTimeout((()=>{l(!0)}),100);return()=>{clearTimeout(e)}}}),[n]);let p=()=>{o(!0)},m=()=>{o(!1),l(!1)};return r?"intent"!==e?[i,f,{}]:[i,f,{onFocus:Bt(s,p),onBlur:Bt(u,m),onMouseEnter:Bt(c,p),onMouseLeave:Bt(d,m),onTouchStart:Bt(h,p)}]:[!1,f,{}]}(r,h),C=function(e,{target:t,replace:r,state:n,preventScrollReset:o,relative:i,viewTransition:l}={}){let s=dt(),u=st(),c=ft(e,{relative:i});return a.useCallback((a=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(a,t)){a.preventDefault();let t=void 0!==r?r:g(u)===g(c);s(e,{replace:t,state:n,preventScrollReset:o,relative:i,viewTransition:l})}}),[u,s,c,r,n,t,e,o,i,l])}(u,{replace:i,state:l,target:s,preventScrollReset:c,relative:n,viewTransition:d}),P=a.createElement("a",{...h,...S,href:y||E,onClick:b||o?e:function(t){e&&e(t),t.defaultPrevented||C(t)},ref:Yt(f,x),target:s,"data-discover":w||"render"!==t?void 0:"true"});return R&&!w?a.createElement(a.Fragment,null,P,a.createElement(Wt,{page:E})):P}));function Zt(e){let t=a.useContext(Ze);return p(t,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(e)),t}Qt.displayName="Link",a.forwardRef((function({"aria-current":e="page",caseSensitive:t=!1,className:r="",end:n=!1,style:o,to:i,viewTransition:l,children:s,...u},c){let d=ft(i,{relative:u.relative}),h=st(),f=a.useContext(et),{navigator:m,basename:y}=a.useContext(nt),v=null!=f&&function(e,t={}){let r=a.useContext(tt);p(null!=r,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=Zt("useViewTransitionState"),o=ft(e,{relative:t.relative});if(!r.isTransitioning)return!1;let i=F(r.currentLocation.pathname,n)||r.currentLocation.pathname,l=F(r.nextLocation.pathname,n)||r.nextLocation.pathname;return null!=U(o.pathname,l)||null!=U(o.pathname,i)}(d)&&!0===l,g=m.encodeLocation?m.encodeLocation(d).pathname:d.pathname,w=h.pathname,b=f&&f.navigation&&f.navigation.location?f.navigation.location.pathname:null;t||(w=w.toLowerCase(),b=b?b.toLowerCase():null,g=g.toLowerCase()),b&&y&&(b=F(b,y)||b);const E="/"!==g&&g.endsWith("/")?g.length-1:g.length;let R,x=w===g||!n&&w.startsWith(g)&&"/"===w.charAt(E),S=null!=b&&(b===g||!n&&b.startsWith(g)&&"/"===b.charAt(g.length)),C={isActive:x,isPending:S,isTransitioning:v},P=x?e:void 0;R="function"==typeof r?r(C):[r,x?"active":null,S?"pending":null,v?"transitioning":null].filter(Boolean).join(" ");let L="function"==typeof o?o(C):o;return a.createElement(Qt,{...u,"aria-current":P,className:R,ref:c,style:L,to:i,viewTransition:l},"function"==typeof s?s(C):s)})).displayName="NavLink",a.forwardRef((({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:o,state:i,method:l=Mt,action:s,onSubmit:u,relative:c,preventScrollReset:d,viewTransition:h,...f},m)=>{let y=function(){let{router:e}=Zt("useSubmit"),{basename:t}=a.useContext(nt),r=wt("useRouteId");return a.useCallback((async(n,a={})=>{let{action:o,method:i,encType:l,formData:s,body:u}=function(e,t){let r,n,a,o,i;if(At(l=e)&&"form"===l.tagName.toLowerCase()){let i=e.getAttribute("action");n=i?F(i,t):null,r=e.getAttribute("method")||Mt,a=zt(e.getAttribute("enctype"))||$t,o=new FormData(e)}else if(function(e){return At(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return At(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(n=l?F(l,t):null,r=e.getAttribute("formmethod")||i.getAttribute("method")||Mt,a=zt(e.getAttribute("formenctype"))||zt(i.getAttribute("enctype"))||$t,o=new FormData(i,e),!function(){if(null===Ot)try{new FormData(document.createElement("form"),0),Ot=!1}catch(or){Ot=!0}return Ot}()){let{name:t,type:r,value:n}=e;if("image"===r){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,n)}}else{if(At(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Mt,n=null,a=$t,i=e}var l;return o&&"text/plain"===a&&(i=o,o=void 0),{action:n,method:r.toLowerCase(),encType:a,formData:o,body:i}}(n,t);if(!1===a.navigate){let t=a.fetcherKey||tr();await e.fetch(t,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||i,formEncType:a.encType||l,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||i,formEncType:a.encType||l,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})}),[e,t,r])}(),v=function(e,{relative:t}={}){let{basename:r}=a.useContext(nt),n=a.useContext(ot);p(n,"useFormAction must be used inside a RouteContext");let[o]=n.matches.slice(-1),i={...ft(e||".",{relative:t})},l=st();if(null==e){i.search=l.search;let e=new URLSearchParams(i.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let r=e.toString();i.search=r?`?${r}`:""}}return e&&"."!==e||!o.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),"/"!==r&&(i.pathname="/"===i.pathname?r:q([r,i.pathname])),g(i)}(s,{relative:c}),w="get"===l.toLowerCase()?"get":"post",b="string"==typeof s&&Gt.test(s);return a.createElement("form",{ref:m,method:w,action:v,onSubmit:n?u:e=>{if(u&&u(e),e.defaultPrevented)return;e.preventDefault();let n=e.nativeEvent.submitter,a=n?.getAttribute("formmethod")||l;y(n||e.currentTarget,{fetcherKey:t,method:a,navigate:r,replace:o,state:i,relative:c,preventScrollReset:d,viewTransition:h})},...f,"data-discover":b||"render"!==e?void 0:"true"})})).displayName="Form";var er=0,tr=()=>`__${String(++er)}__`,rr=r();const nr=t(rr);function ar(e){return a.createElement(Lt,{flushSync:rr.flushSync,...e})}export{Dt as O,i as R,o as a,ar as b,Jt as c,dt as d,rr as e,nr as f,a as r,st as u};
