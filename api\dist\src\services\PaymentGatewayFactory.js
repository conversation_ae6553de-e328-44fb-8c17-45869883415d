import * as env from "../config/env.config.js";
import { PaymentGateway } from "../../../../packages/bookcars-types/index.js";
import Stripe from 'stripe';
import axios from 'axios';
// Stripe Gateway Implementation
class StripeGateway {
  stripe;
  constructor() {
    this.stripe = new Stripe(env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-04-30.basil'
    });
  }
  async processPayment(amount, currency, description) {
    const paymentIntent = await this.stripe.paymentIntents.create({
      amount: Math.round(amount * 100),
      // Stripe requires amount in cents
      currency,
      description
    });
    return paymentIntent;
  }
  async createCheckoutSession(bookingId, customerId, amount) {
    const session = await this.stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: 'usd',
          product_data: {
            name: `Booking #${bookingId}`
          },
          unit_amount: Math.round(amount * 100)
        },
        quantity: 1
      }],
      mode: 'payment',
      success_url: `${env.FRONTEND_HOST}/booking-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${env.FRONTEND_HOST}/booking-cancel`,
      client_reference_id: bookingId,
      customer: customerId
    });
    return session;
  }
  async refundPayment(paymentId, amount) {
    const refund = await this.stripe.refunds.create({
      payment_intent: paymentId,
      amount: Math.round(amount * 100)
    });
    return refund;
  }
}
// PayPal Gateway Implementation
class PayPalGateway {
  async processPayment(_amount, _currency, _description) {
    // PayPal implementation
    // This would use the PayPal SDK to create a payment
    return {
      success: true,
      id: 'mock-paypal-payment-id'
    };
  }
  async createCheckoutSession(_bookingId, _customerId, _amount) {
    // PayPal checkout session creation
    return {
      success: true,
      id: 'mock-paypal-session-id',
      url: 'https://paypal.com/checkout'
    };
  }
  async refundPayment(_paymentId, _amount) {
    // PayPal refund implementation
    return {
      success: true,
      id: 'mock-paypal-refund-id'
    };
  }
}
// Visa Gateway Implementation
class VisaGateway {
  async processPayment(amount, currency, description) {
    // This is a mock implementation - you would need to integrate with Visa's actual API
    const response = await axios.post('https://mock-visa-endpoint.com',
    // env.PAYMENT_GATEWAYS.VISA_ENDPOINT,
    {
      amount,
      currency,
      description
      // apiKey: env.PAYMENT_GATEWAYS.VISA_API_KEY,
    }, {
      headers: {
        // 'Authorization': `Bearer ${env.PAYMENT_GATEWAYS.VISA_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }
  async createCheckoutSession(_bookingId, _customerId, _amount) {
    // Visa checkout session creation
    return {
      success: true,
      id: 'mock-visa-session-id',
      url: 'https://visa.com/checkout'
    };
  }
  async refundPayment(_paymentId, _amount) {
    // Visa refund implementation
    return {
      success: true,
      id: 'mock-visa-refund-id'
    };
  }
}
// Factory class
export class PaymentGatewayFactory {
  static createGateway(type) {
    switch (type) {
      case PaymentGateway.Stripe:
        return new StripeGateway();
      // env.PAYMENT_GATEWAYS.STRIPE_ENABLED ? new StripeGateway() : null
      case PaymentGateway.PayPal:
        return new PayPalGateway();
      // env.PAYMENT_GATEWAYS.PAYPAL_ENABLED ? new PayPalGateway() : null
      case PaymentGateway.Visa:
        return new VisaGateway();
      // env.PAYMENT_GATEWAYS.VISA_ENABLED ? new VisaGateway() : null
      default:
        return null;
    }
  }
}