import{b as e,s,u as t,e as o,j as a,F as r,R as i,a as n,z as l,H as c,c as m}from"../entries/index-xsXxT3-W.js";import{d,r as u}from"./router-BtYqujaw.js";import{L as j}from"./Layout-DaeN7D4t.js";import{S as p}from"./Search-BT-I8ZrW.js";import{g as N,c as I,d as E}from"./LocationService-6NvQT9iL.js";import{P as T}from"./Pager-B4DUIA8f.js";import{A as O}from"./Avatar-CvDHTACZ.js";import{P as L}from"./Progress-C5Vwt7m-.js";import{C as h,a as x}from"./ArrowForwardIos-BCaVe-sv.js";import{T as A}from"./Backdrop-Czag2Ija.js";import{L as C}from"./Menu-C_-X8cS7.js";import{L as S}from"./ListItem-Bmdw8GrH.js";import{L as _}from"./ListItemAvatar-CtDTZqea.js";import{L as f}from"./ListItemText-DUhWzkV9.js";import{T as g}from"./Tooltip-CKMkVqOx.js";import{I as y}from"./IconButton-CxOCoGF3.js";import{E as D}from"./Edit-Bc0UCPtn.js";import{D as w}from"./Delete-BfnPAJno.js";import{D as b,a as v,b as P,d as M}from"./Grow-Cp8xsNYl.js";import{B as F}from"./Button-BeKLLPpp.js";import{I as k}from"./InfoBox-DNJEsGlP.js";import"./vendor-dblfw9z9.js";import"./zod-4O8Zwsja.js";import"./TextField-D_yQOTzE.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-DiTut-u0.js";import"./OutlinedInput-BX8yFQbF.js";import"./InputLabel-C8rcdOGQ.js";import"./isHostComponent-DR4iSCFs.js";import"./ownerWindow-ChLfdzZL.js";import"./Input-D1AdR9CM.js";import"./FormHelperText-DDZ4BMA4.js";import"./Clear-CDOl64hX.js";import"./Search-CKOds7xB.js";import"./DressService-DkS6e_O5.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./Paper-C-atefOs.js";import"./mergeSlotProps-DEridHif.js";import"./listItemTextClasses-BcbgzvlE.js";import"./Info-CNP9gYBt.js";const G=new e({fr:{NEW_LOCATION:"Nouveau lieu",DELETE_LOCATION:"Êtes-vous sûr de vouloir supprimer ce lieu ?",CANNOT_DELETE_LOCATION:"Ce lieu ne peut pas être supprimé car il est lié à des voitures.",EMPTY_LIST:"Pas de lieux.",LOCATION:"lieu",LOCATIONS:"lieux"},en:{NEW_LOCATION:"New location",DELETE_LOCATION:"Are you sure you want to delete this location?",CANNOT_DELETE_LOCATION:"This location cannot be deleted because it is related to cars.",EMPTY_LIST:"No locations.",LOCATION:"location",LOCATIONS:"locations"},es:{NEW_LOCATION:"Nuevo lugar",DELETE_LOCATION:"¿Estás seguro de que quieres eliminar este lugar?",CANNOT_DELETE_LOCATION:"Este lugar no puede ser eliminado porque está relacionado con coches.",EMPTY_LIST:"No hay lugares.",LOCATION:"lugar",LOCATIONS:"lugares"}});s(G);const R=({keyword:e,onLoad:s,onDelete:m})=>{const j=d(),{user:p}=t(),[k,R]=u.useState(e),[B,W]=u.useState(!0),[H,Y]=u.useState(!1),[q,z]=u.useState(!1),[Z,$]=u.useState([]),[K,U]=u.useState(0),[J,Q]=u.useState(0),[V,X]=u.useState(1),[ee,se]=u.useState(!1),[te,oe]=u.useState(!1),[ae,re]=u.useState(""),[ie,ne]=u.useState(-1),le=async(e,t)=>{try{Y(!0);const a=await N(t||"",e,o.PAGE_SIZE),r=a&&a.length>0?a[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!r)return void l();const i=Array.isArray(r.pageInfo)&&r.pageInfo.length>0?r.pageInfo[0].totalRecords:0;let n=[];n=o.PAGINATION_MODE===c.PAGINATION_MODE.INFINITE_SCROLL||o.isMobile?1===e?r.resultData:[...Z,...r.resultData]:r.resultData,$(n),U((e-1)*o.PAGE_SIZE+n.length),Q(i),z(r.resultData.length>0),((o.PAGINATION_MODE===c.PAGINATION_MODE.INFINITE_SCROLL||o.isMobile)&&1===e||o.PAGINATION_MODE===c.PAGINATION_MODE.CLASSIC&&!o.isMobile)&&window.scrollTo(0,0),s&&s({rows:r.resultData,rowCount:i})}catch(a){l(a)}finally{Y(!1),W(!1)}};u.useEffect((()=>{e!==k&&le(1,e),R(e||"")}),[e,k]),u.useEffect((()=>{le(V,k)}),[V]),u.useEffect((()=>{if(o.isMobile){const e=document.querySelector("body");e&&(e.onscroll=()=>{q&&!H&&window.scrollY>0&&window.scrollY+window.innerHeight+o.INFINITE_SCROLL_OFFSET>=document.body.scrollHeight&&(Y(!0),X(V+1))})}}),[q,H,V,k]);const ce=async e=>{try{const s=e.currentTarget.getAttribute("data-id"),t=Number(e.currentTarget.getAttribute("data-index")),o=await I(s);204===o?(se(!0),re(s),ne(t)):200===o?oe(!0):l()}catch(s){l(s)}};return p&&a.jsxs(a.Fragment,{children:[a.jsxs("section",{className:"location-list",children:[0===Z.length?!B&&!H&&a.jsx(h,{variant:"outlined",className:"empty-list",children:a.jsx(x,{children:a.jsx(A,{color:"textSecondary",children:G.EMPTY_LIST})})}):a.jsx(C,{className:"location-list-items",children:Z.map(((e,s)=>a.jsxs(S,{className:"location-list-item",secondaryAction:(r(p)||e.supplier?._id===p._id)&&a.jsxs("div",{children:[a.jsx(g,{title:n.UPDATE,children:a.jsx(y,{edge:"end",onClick:()=>j(`/update-location?loc=${e._id}`),children:a.jsx(D,{})})}),a.jsx(g,{title:n.DELETE,children:a.jsx(y,{edge:"end","data-id":e._id,"data-index":s,onClick:ce,children:a.jsx(w,{})})})]}),children:[a.jsx(_,{children:a.jsx(O,{type:i.Location,mode:"update",record:e,size:"medium",readonly:!0,color:"disabled",className:"location-image"})}),a.jsx(f,{primary:a.jsx(A,{className:"location-title",children:e.name}),secondary:e.country?.name&&e.country.name})]},e._id)))}),a.jsxs(b,{disableEscapeKeyDown:!0,maxWidth:"xs",open:te,children:[a.jsx(v,{className:"dialog-header",children:n.INFO}),a.jsx(P,{children:G.CANNOT_DELETE_LOCATION}),a.jsx(M,{className:"dialog-actions",children:a.jsx(F,{onClick:()=>{oe(!1)},variant:"contained",className:"btn-secondary",children:n.CLOSE})})]}),a.jsxs(b,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ee,children:[a.jsx(v,{className:"dialog-header",children:n.CONFIRM_TITLE}),a.jsx(P,{children:G.DELETE_LOCATION}),a.jsxs(M,{className:"dialog-actions",children:[a.jsx(F,{onClick:()=>{se(!1),re(""),ne(-1)},variant:"contained",className:"btn-secondary",children:n.CANCEL}),a.jsx(F,{onClick:async()=>{try{if(""!==ae&&ie>-1)if(Y(!0),se(!1),200===await E(ae)){const e=K-1;Z.splice(ie,1),$(Z),U(e),Q(J-1),re(""),ne(-1),Y(!1),m&&m(e)}else l(),re(""),ne(-1),Y(!1);else l(),se(!1),re(""),ne(-1)}catch(e){l(e)}},variant:"contained",color:"error",children:n.DELETE})]})]}),H&&a.jsx(L,{})]}),!o.isMobile&&a.jsx(T,{page:V,pageSize:o.PAGE_SIZE,rowCount:K,totalRecords:J,onNext:()=>X(V+1),onPrevious:()=>X(V-1)})]})},B=()=>{const e=m.c(17),s=d(),[t,o]=u.useState(""),[r,i]=u.useState(-1);let n;e[0]===Symbol.for("react.memo_cache_sentinel")?(n=e=>{o(e)},e[0]=n):n=e[0];const l=n;let c;e[1]===Symbol.for("react.memo_cache_sentinel")?(c=e=>{e&&i(e.rowCount)},e[1]=c):c=e[1];const N=c;let I;e[2]===Symbol.for("react.memo_cache_sentinel")?(I=e=>{i(e)},e[2]=I):I=e[2];const E=I,T=W;let O,L,h,x,A,C;return e[3]===Symbol.for("react.memo_cache_sentinel")?(O=a.jsx(p,{className:"search",onSubmit:l}),e[3]=O):O=e[3],e[4]!==s||e[5]!==r?(L=r>-1&&a.jsx(F,{variant:"contained",className:"btn-primary new-location",size:"small",onClick:()=>s("/create-location"),children:G.NEW_LOCATION}),e[4]=s,e[5]=r,e[6]=L):L=e[6],e[7]!==r?(h=r>0&&a.jsx(k,{value:`${r} ${r>1?G.LOCATIONS:G.LOCATION}`,className:"location-count"}),e[7]=r,e[8]=h):h=e[8],e[9]!==L||e[10]!==h?(x=a.jsx("div",{className:"col-1",children:a.jsxs("div",{className:"col-1-container",children:[O,L,h]})}),e[9]=L,e[10]=h,e[11]=x):x=e[11],e[12]!==t?(A=a.jsx("div",{className:"col-2",children:a.jsx(R,{keyword:t,onLoad:N,onDelete:E})}),e[12]=t,e[13]=A):A=e[13],e[14]!==x||e[15]!==A?(C=a.jsx(j,{onLoad:T,strict:!0,children:a.jsxs("div",{className:"locations",children:[x,A]})}),e[14]=x,e[15]=A,e[16]=C):C=e[16],C};function W(){}export{B as default};
