import{b as e,s,c as t,a8 as a,a as r,J as o,j as i,a9 as l,e as n,aa as c,z as m,R as d,G as u,ab as p,F as h,U as j}from"../entries/index-xsXxT3-W.js";import{r as f,d as x}from"./router-BtYqujaw.js";import{L as S}from"./Layout-DaeN7D4t.js";import{S as E}from"./Search-BT-I8ZrW.js";import{s as v}from"./user-list-CNjMkwmb.js";import{D as g,a as N,b,d as C}from"./Grow-Cp8xsNYl.js";import{B as y}from"./Button-BeKLLPpp.js";import{B as _}from"./Badge-zckTAo43.js";import{T as k}from"./Tooltip-CKMkVqOx.js";import{B as I}from"./Box-Dm2ZtwWL.js";import{V as w}from"./Check-BO6X9Q-4.js";import{L as R}from"./Link-sHEcszvT.js";import{I as T}from"./IconButton-CxOCoGF3.js";import{D}from"./Delete-BfnPAJno.js";import{E as L}from"./Edit-Bc0UCPtn.js";import{A}from"./Avatar-Dvwllg8p.js";import{A as P}from"./AccountCircle-DdIeIbov.js";import{D as U}from"./DataGrid-DJUEcXft.js";import"./vendor-dblfw9z9.js";import"./zod-4O8Zwsja.js";import"./TextField-D_yQOTzE.js";import"./useFormControl-B7jXtRD7.js";import"./Backdrop-Czag2Ija.js";import"./useSlot-DiTut-u0.js";import"./Paper-C-atefOs.js";import"./OutlinedInput-BX8yFQbF.js";import"./InputLabel-C8rcdOGQ.js";import"./isHostComponent-DR4iSCFs.js";import"./ownerWindow-ChLfdzZL.js";import"./Input-D1AdR9CM.js";import"./FormHelperText-DDZ4BMA4.js";import"./Menu-C_-X8cS7.js";import"./mergeSlotProps-DEridHif.js";import"./Clear-CDOl64hX.js";import"./Search-CKOds7xB.js";import"./getThemeProps-DSP27jpP.js";import"./Switch-C5asfh_w.js";import"./SwitchBase-DrUkTXjH.js";import"./MenuItem-P0BnGnrT.js";import"./listItemTextClasses-BcbgzvlE.js";import"./Toolbar-BTa0QYME.js";import"./KeyboardArrowRight-LSgfnPVa.js";import"./Chip-MGF1mKZa.js";import"./Autocomplete-CWN5GAd4.js";import"./ListItemText-DUhWzkV9.js";import"./Checkbox-F0AjCtoF.js";const G=new e({fr:{NEW_USER:"Nouvel utilisateur"},en:{NEW_USER:"New user"},es:{NEW_USER:"Nuevo usuario"}});s(G);const M=e=>{const s=t.c(17),{className:n,onChange:c}=e,m=a(),[d,u]=f.useState(m.map(F)),[p,h]=f.useState(!0);let j;s[0]===Symbol.for("react.memo_cache_sentinel")?(j=[],s[0]=j):j=s[0];const x=f.useRef(j);let S,E;s[1]===Symbol.for("react.memo_cache_sentinel")?(S=()=>{x.current.forEach(z)},E=[],s[1]=S,s[2]=E):(S=s[1],E=s[2]),f.useEffect(S,E);const v=e=>{const s=e.currentTarget.getAttribute("data-value");if(e.currentTarget.checked)d.push(s),d.length===m.length&&h(!0);else{const e=d.findIndex((e=>e===s));d.splice(e,1),0===d.length&&h(!1)}u(d),c&&c(o(d))};let g;s[3]!==v?(g=e=>{const s=e.currentTarget.previousSibling;s.checked=!s.checked;const t=e;t.currentTarget=s,v(t)},s[3]=v,s[4]=g):g=s[4];const N=g,b=()=>{if(p)x.current.forEach(O),h(!1),u([]);else{x.current.forEach(B);const e=m.map(W);h(!0),u(e),c&&c(o(e))}},C=(n?`${n} `:"")+"user-type-filter";let y;s[5]!==v||s[6]!==N?(y=(e,s)=>i.jsxs("li",{children:[i.jsx("input",{ref:e=>{x.current[s]=e},type:"checkbox","data-value":e.value,className:"user-type-checkbox",onChange:v}),i.jsx("span",{onClick:N,className:`bs bs-${e.value}`,role:"button",tabIndex:0,children:l(e.value)})]},e.value),s[5]=v,s[6]=N,s[7]=y):y=s[7];const _=m.map(y);let k;s[8]!==_?(k=i.jsx("ul",{className:"user-type-list",children:_}),s[8]=_,s[9]=k):k=s[9];const I=p?r.UNCHECK_ALL:r.CHECK_ALL;let w,R;return s[10]!==b||s[11]!==I?(w=i.jsx("div",{className:"filter-actions",children:i.jsx("span",{onClick:b,className:"uncheckall",role:"button",tabIndex:0,children:I})}),s[10]=b,s[11]=I,s[12]=w):w=s[12],s[13]!==w||s[14]!==C||s[15]!==k?(R=i.jsxs("div",{className:C,children:[k,w]}),s[13]=w,s[14]=C,s[15]=k,s[16]=R):R=s[16],R};function F(e){return e.value}function z(e){e.checked=!0}function O(e){e.checked=!1}function B(e){e.checked=!0}function W(e){return e.value}const H=({types:e,keyword:s,user:t,hideDesktopColumns:a,checkboxSelection:h,onLoad:j})=>{const S=x(),[E,G]=f.useState(),[M,F]=f.useState(0),[z,O]=f.useState(n.PAGE_SIZE),[B,W]=f.useState([]),[H,$]=f.useState([]),[V,Z]=f.useState(0),[K,J]=f.useState(!0),[Y,q]=f.useState(""),[Q,X]=f.useState([]),[ee,se]=f.useState(!1),[te,ae]=f.useState(),[re,oe]=f.useState(s),[ie,le]=f.useState(!1),[ne,ce]=f.useState({pageSize:n.PAGE_SIZE,page:0});f.useEffect((()=>{F(ne.page),O(ne.pageSize)}),[ne]);const me=async(e,s)=>{try{if(s&&te){J(!0);const t={user:s&&s._id||"",types:te},a=await c(t,re||"",e+1,z),r=a&&a.length>0?a[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!r)return void m();const o=Array.isArray(r.pageInfo)&&r.pageInfo.length>0?r.pageInfo[0].totalRecords:0,i=r.resultData;$(i),Z(o),j&&j({rows:r.resultData,rowCount:o})}}catch(t){m(t)}finally{J(!1)}};f.useEffect((()=>{ae(e)}),[e]),f.useEffect((()=>{oe(s||"")}),[s]),f.useEffect((()=>{te&&me(M,E)}),[M]),f.useEffect((()=>{if(te)if(0===M)me(0,E);else{const e=o(ne);e.page=0,ce(e)}}),[z]);const de=e=>{const s=[{field:"fullName",headerName:r.USER,flex:1,renderCell:({row:e,value:s})=>{const t=e;let a;if(t.avatar)if(t.type===d.Supplier)a=i.jsx("img",{src:u(n.CDN_USERS,e.avatar),alt:e.fullName});else{const e=t.avatar?t.avatar.startsWith("http")?t.avatar:u(n.CDN_USERS,t.avatar):"",s=i.jsx(A,{src:e,className:"avatar-small"});a=t.verified?i.jsx(_,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:i.jsx(k,{title:r.VERIFIED,children:i.jsx(I,{borderRadius:"50%",className:"user-avatar-verified-small",children:i.jsx(w,{className:"user-avatar-verified-icon-small"})})}),children:s}):i.jsx(_,{overlap:"circular",children:s})}else{const e=i.jsx(P,{className:"avatar-small",color:"disabled"});a=t.verified?i.jsx(_,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:i.jsx(k,{title:r.VERIFIED,children:i.jsx(I,{borderRadius:"50%",className:"user-avatar-verified-small",children:i.jsx(w,{className:"user-avatar-verified-icon-small"})})}),children:e}):i.jsx(_,{overlap:"circular",children:e})}return i.jsxs(R,{href:`/user?u=${e._id}`,className:"us-user",children:[i.jsx("span",{className:"us-avatar",children:a}),i.jsx("span",{children:s})]})},valueGetter:e=>e},{field:"email",headerName:r.EMAIL,flex:1,valueGetter:e=>e},{field:"phone",headerName:r.PHONE,flex:1,valueGetter:e=>e},{field:"type",headerName:r.TYPE,flex:1,renderCell:({value:e})=>i.jsx("span",{className:`bs us-${e?.toLowerCase()}`,children:l(e)}),valueGetter:e=>e},{field:"action",headerName:"",sortable:!1,disableColumnMenu:!0,renderCell:({row:s})=>{const t=s;return e.type===d.Admin||t.supplier===e._id?i.jsxs("div",{children:[i.jsx(k,{title:r.UPDATE,children:i.jsx(T,{onClick:()=>S(`/update-user?u=${s._id}`),children:i.jsx(L,{})})}),i.jsx(k,{title:r.DELETE,children:i.jsx(T,{onClick:e=>{e.stopPropagation(),q(s._id||""),se(!0)},children:i.jsx(D,{})})})]}):i.jsx(i.Fragment,{})},renderHeader:()=>Q.length>0?i.jsxs("div",{children:[i.jsx("div",{style:{width:40,display:"inline-block"}}),i.jsx(k,{title:v.DELETE_SELECTION,children:i.jsx(T,{onClick:()=>{se(!0)},children:i.jsx(D,{})})})]}):i.jsx(i.Fragment,{})}];return a&&s.splice(1,3),s};return f.useEffect((()=>{if(t&&te){G(t);const e=de(t);if(W(e),0===M)me(0,t);else{const e=o(ne);e.page=0,ce(e)}}}),[t,te,re]),f.useEffect((()=>{if(E&&ie){const e=de(E);W(e),le(!1)}}),[E,Q,ie]),i.jsxs("div",{className:"us-list",children:[E&&B.length>0&&i.jsx(U,{checkboxSelection:h,getRowId:e=>e._id,columns:B,rows:H,rowCount:V,loading:K,initialState:{pagination:{paginationModel:{pageSize:n.PAGE_SIZE}}},pageSizeOptions:[n.PAGE_SIZE,50,100],pagination:!0,paginationMode:"server",paginationModel:ne,onPaginationModelChange:ce,onRowSelectionModelChange:e=>{X(Array.from(new Set(e.ids)).map((e=>e.toString()))),le(!0)},getRowClassName:e=>e.row.blacklisted?"us-blacklisted":"",disableRowSelectionOnClick:!0}),i.jsxs(g,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ee,children:[i.jsx(N,{className:"dialog-header",children:r.CONFIRM_TITLE}),i.jsx(b,{className:"dialog-content",children:0===Q.length?v.DELETE_USER:v.DELETE_USERS}),i.jsxs(C,{className:"dialog-actions",children:[i.jsx(y,{onClick:()=>{se(!1),q("")},variant:"contained",className:"btn-secondary",children:r.CANCEL}),i.jsx(y,{onClick:async()=>{try{const e=Q.length>0?Q:[Y];se(!1),200===await p(e)?Q.length>0?$(H.filter((e=>!Q.includes(e._id)))):$(H.filter((e=>e._id!==Y))):m()}catch(e){m(e)}finally{J(!1)}},variant:"contained",color:"error",children:r.DELETE})]})]})]})},$=()=>{const e=t.c(11),s=x(),[r,o]=f.useState(),[l,c]=f.useState(!1),[m,d]=f.useState(),[u,p]=f.useState("");let v;e[0]===Symbol.for("react.memo_cache_sentinel")?(v=e=>{d(e)},e[0]=v):v=e[0];const g=v;let N;e[1]===Symbol.for("react.memo_cache_sentinel")?(N=e=>{p(e)},e[1]=N):N=e[1];const b=N;let C;e[2]===Symbol.for("react.memo_cache_sentinel")?(C=e=>{const s=h(e),t=s?a().map(V):[j.Supplier,j.User];o(e),c(s),d(t)},e[2]=C):C=e[2];const _=C;let k,I;return e[3]!==l||e[4]!==u||e[5]!==s||e[6]!==m||e[7]!==r?(k=r&&i.jsxs("div",{className:"users",children:[i.jsx("div",{className:"col-1",children:i.jsxs("div",{className:"div.col-1-container",children:[i.jsx(E,{onSubmit:b,className:"search"}),l&&i.jsx(M,{className:"user-type-filter",onChange:g}),i.jsx(y,{variant:"contained",className:"btn-primary new-user",size:"small",onClick:()=>s("/create-user"),children:G.NEW_USER})]})}),i.jsx("div",{className:"col-2",children:i.jsx(H,{user:r,types:m,keyword:u,checkboxSelection:!n.isMobile&&l,hideDesktopColumns:n.isMobile})})]}),e[3]=l,e[4]=u,e[5]=s,e[6]=m,e[7]=r,e[8]=k):k=e[8],e[9]!==k?(I=i.jsx(S,{onLoad:_,strict:!0,children:k}),e[9]=k,e[10]=I):I=e[10],I};function V(e){return e.value}export{$ as default};
