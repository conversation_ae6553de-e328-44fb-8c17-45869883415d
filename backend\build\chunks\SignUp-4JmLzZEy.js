import{b as e,s,c as r,u as o,j as a,a as t,e as i,D as n,p as m,E as l,d as c,g as d}from"../entries/index-xsXxT3-W.js";import{d as p,r as u}from"./router-BtYqujaw.js";import{u as f,z as _,s as h}from"./zod-4O8Zwsja.js";import{L as S}from"./Layout-DaeN7D4t.js";import{E as j}from"./Error-DRzAdbbx.js";import{S as g}from"./SimpleBackdrop-CqsJhYJ4.js";import{I as N,F as R}from"./InputLabel-C8rcdOGQ.js";import{I as w}from"./Input-D1AdR9CM.js";import{B as P}from"./Button-BeKLLPpp.js";import{F as I}from"./FormHelperText-DDZ4BMA4.js";import{P as x}from"./Paper-C-atefOs.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Czag2Ija.js";import"./useSlot-DiTut-u0.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const E=new e({fr:{SIGN_UP_HEADING:"Inscription",TOS_SIGN_UP:"J'ai lu et j'accepte les conditions générales d'utilisation.",SIGN_UP:"S'inscrire",RECAPTCHA_ERROR:"Veuillez remplir le captcha pour continuer.",SIGN_UP_ERROR:"Une erreur s'est produite lors de l'inscription."},en:{SIGN_UP_HEADING:"Sign up",TOS_SIGN_UP:"I read and agree with the Terms of Use.",SIGN_UP:"Sign up",RECAPTCHA_ERROR:"Fill out the captcha to continue.",SIGN_UP_ERROR:"An error occurred during sign up."},es:{SIGN_UP_HEADING:"Registrarse",TOS_SIGN_UP:"He leído y acepto los Términos de uso.",SIGN_UP:"Registrarse",RECAPTCHA_ERROR:"Complete el captcha para continuar.",SIGN_UP_ERROR:"Se produjo un error durante el registro."}});s(E);const A=_.object({fullName:_.string().min(1),email:_.string().email({message:t.EMAIL_NOT_VALID}),password:_.string().min(i.PASSWORD_MIN_LENGTH,{message:t.PASSWORD_ERROR}),confirmPassword:_.string()}).refine((e=>e.password===e.confirmPassword),{path:["confirmPassword"],message:t.PASSWORDS_DONT_MATCH}),C=()=>{const e=r.c(99),s=p(),{setUser:i,setUserLoaded:_}=o(),[C,b]=u.useState(!1);let G;e[0]===Symbol.for("react.memo_cache_sentinel")?(G={resolver:h(A),mode:"onSubmit"},e[0]=G):G=e[0];const{register:y,handleSubmit:O,formState:U,setError:D,clearErrors:L,setValue:T}=f(G),{errors:v,isSubmitting:H}=U;let F;e[1]!==s||e[2]!==D||e[3]!==i||e[4]!==_?(F=async e=>{try{if(200!==await n({email:e.email}))return void D("email",{message:t.EMAIL_ALREADY_REGISTERED});const r={email:e.email,password:e.password,fullName:e.fullName,language:m()};if(200===await l(r)){const r=await c({email:e.email,password:e.password});if(200===r.status){const e=await d(r.data._id);i(e),_(!0),s(`/${window.location.search}`)}}}catch(r){const e=r;console.error(e),D("root",{message:E.SIGN_UP_ERROR})}},e[1]=s,e[2]=D,e[3]=i,e[4]=_,e[5]=F):F=e[5];const W=F;let M;e[6]!==s?(M=e=>{e?s("/"):b(!0)},e[6]=s,e[7]=M):M=e[7];const q=M;let z,B,k,V,J,Q,Y,$,K,X,Z;e[8]!==C?(z=C?{}:{display:"none"},e[8]=C,e[9]=z):z=e[9],e[10]===Symbol.for("react.memo_cache_sentinel")?(B=a.jsx("h1",{className:"signup-form-title",children:E.SIGN_UP_HEADING}),e[10]=B):B=e[10],e[11]!==O||e[12]!==W?(k=O(W),e[11]=O,e[12]=W,e[13]=k):k=e[13],e[14]===Symbol.for("react.memo_cache_sentinel")?(V=a.jsx(N,{htmlFor:"full-name",children:t.FULL_NAME}),e[14]=V):V=e[14],e[15]!==y?(J=y("fullName"),e[15]=y,e[16]=J):J=e[16],e[17]!==T?(Q=e=>{T("fullName",e.target.value)},e[17]=T,e[18]=Q):Q=e[18],e[19]!==J||e[20]!==Q?(Y=a.jsxs(R,{fullWidth:!0,margin:"dense",children:[V,a.jsx(w,{type:"text",...J,autoComplete:"off",onChange:Q,required:!0})]}),e[19]=J,e[20]=Q,e[21]=Y):Y=e[21],e[22]===Symbol.for("react.memo_cache_sentinel")?($=a.jsx(N,{htmlFor:"email",children:t.EMAIL}),e[22]=$):$=e[22],e[23]!==y?(K=y("email"),e[23]=y,e[24]=K):K=e[24],e[25]!==L||e[26]!==T?(X=e=>{L("email"),T("email",e.target.value)},e[25]=L,e[26]=T,e[27]=X):X=e[27],e[28]!==K||e[29]!==X?(Z=a.jsx(w,{type:"text",...K,autoComplete:"off",onChange:X,required:!0}),e[28]=K,e[29]=X,e[30]=Z):Z=e[30];const ee=!!v.email,se=v.email?.message||"";let re,oe,ae,te,ie,ne,me;e[31]!==ee||e[32]!==se?(re=a.jsx(I,{error:ee,children:se}),e[31]=ee,e[32]=se,e[33]=re):re=e[33],e[34]!==Z||e[35]!==re?(oe=a.jsxs(R,{fullWidth:!0,margin:"dense",children:[$,Z,re]}),e[34]=Z,e[35]=re,e[36]=oe):oe=e[36],e[37]===Symbol.for("react.memo_cache_sentinel")?(ae=a.jsx(N,{htmlFor:"password",children:t.PASSWORD}),e[37]=ae):ae=e[37],e[38]!==y?(te=y("password"),e[38]=y,e[39]=te):te=e[39],e[40]===Symbol.for("react.memo_cache_sentinel")?(ie={autoComplete:"new-password",form:{autoComplete:"off"}},e[40]=ie):ie=e[40],e[41]!==L||e[42]!==T?(ne=e=>{L("password"),T("password",e.target.value)},e[41]=L,e[42]=T,e[43]=ne):ne=e[43],e[44]!==te||e[45]!==ne?(me=a.jsx(w,{...te,type:"password",inputProps:ie,onChange:ne,required:!0}),e[44]=te,e[45]=ne,e[46]=me):me=e[46];const le=!!v.password,ce=v.password?.message||"";let de,pe,ue,fe,_e,he,Se;e[47]!==le||e[48]!==ce?(de=a.jsx(I,{error:le,children:ce}),e[47]=le,e[48]=ce,e[49]=de):de=e[49],e[50]!==me||e[51]!==de?(pe=a.jsxs(R,{fullWidth:!0,margin:"dense",children:[ae,me,de]}),e[50]=me,e[51]=de,e[52]=pe):pe=e[52],e[53]===Symbol.for("react.memo_cache_sentinel")?(ue=a.jsx(N,{htmlFor:"confirm-password",children:t.CONFIRM_PASSWORD}),e[53]=ue):ue=e[53],e[54]!==y?(fe=y("confirmPassword"),e[54]=y,e[55]=fe):fe=e[55],e[56]===Symbol.for("react.memo_cache_sentinel")?(_e={autoComplete:"new-password",form:{autoComplete:"off"}},e[56]=_e):_e=e[56],e[57]!==L||e[58]!==T?(he=e=>{L("confirmPassword"),T("confirmPassword",e.target.value)},e[57]=L,e[58]=T,e[59]=he):he=e[59],e[60]!==fe||e[61]!==he?(Se=a.jsx(w,{...fe,type:"password",inputProps:_e,onChange:he,required:!0}),e[60]=fe,e[61]=he,e[62]=Se):Se=e[62];const je=!!v.confirmPassword,ge=v.confirmPassword?.message||"";let Ne,Re,we,Pe,Ie,xe,Ee,Ae,Ce,be,Ge,ye;return e[63]!==je||e[64]!==ge?(Ne=a.jsx(I,{error:je,children:ge}),e[63]=je,e[64]=ge,e[65]=Ne):Ne=e[65],e[66]!==Se||e[67]!==Ne?(Re=a.jsxs(R,{fullWidth:!0,margin:"dense",children:[ue,Se,Ne]}),e[66]=Se,e[67]=Ne,e[68]=Re):Re=e[68],e[69]!==H?(we=a.jsx(P,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:H,children:E.SIGN_UP}),e[69]=H,e[70]=we):we=e[70],e[71]!==s?(Pe=a.jsx(P,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>s("/"),children:t.CANCEL}),e[71]=s,e[72]=Pe):Pe=e[72],e[73]!==we||e[74]!==Pe?(Ie=a.jsxs("div",{className:"buttons",children:[we,Pe]}),e[73]=we,e[74]=Pe,e[75]=Ie):Ie=e[75],e[76]!==Y||e[77]!==oe||e[78]!==pe||e[79]!==Re||e[80]!==Ie?(xe=a.jsxs("div",{children:[Y,oe,pe,Re,Ie]}),e[76]=Y,e[77]=oe,e[78]=pe,e[79]=Re,e[80]=Ie,e[81]=xe):xe=e[81],e[82]!==v.root?(Ee=v.root&&a.jsx(j,{message:v.root.message}),e[82]=v.root,e[83]=Ee):Ee=e[83],e[84]!==Ee?(Ae=a.jsx("div",{className:"form-error",children:Ee}),e[84]=Ee,e[85]=Ae):Ae=e[85],e[86]!==xe||e[87]!==Ae||e[88]!==k?(Ce=a.jsxs("form",{onSubmit:k,children:[xe,Ae]}),e[86]=xe,e[87]=Ae,e[88]=k,e[89]=Ce):Ce=e[89],e[90]!==z||e[91]!==Ce?(be=a.jsx("div",{className:"signup",children:a.jsxs(x,{className:"signup-form",elevation:10,style:z,children:[B,Ce]})}),e[90]=z,e[91]=Ce,e[92]=be):be=e[92],e[93]!==H?(Ge=H&&a.jsx(g,{text:t.PLEASE_WAIT}),e[93]=H,e[94]=Ge):Ge=e[94],e[95]!==q||e[96]!==be||e[97]!==Ge?(ye=a.jsxs(S,{strict:!1,onLoad:q,children:[be,Ge]}),e[95]=q,e[96]=be,e[97]=Ge,e[98]=ye):ye=e[98],ye};export{C as default};
