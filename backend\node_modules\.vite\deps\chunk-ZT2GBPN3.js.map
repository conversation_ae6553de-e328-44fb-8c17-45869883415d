{"version": 3, "sources": ["../../@mui/x-data-grid/esm/constants/localeTextConstants.js"], "sourcesContent": ["export const GRID_DEFAULT_LOCALE_TEXT = {\n  // Root\n  noRowsLabel: 'No rows',\n  noResultsOverlayLabel: 'No results found.',\n  noColumnsOverlayLabel: 'No columns',\n  noColumnsOverlayManageColumns: 'Manage columns',\n  emptyPivotOverlayLabel: 'Add fields to rows, columns, and values to create a pivot table',\n  // Density selector toolbar button text\n  toolbarDensity: 'Density',\n  toolbarDensityLabel: 'Density',\n  toolbarDensityCompact: 'Compact',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Comfortable',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Columns',\n  toolbarColumnsLabel: 'Select columns',\n  // Filters toolbar button text\n  toolbarFilters: 'Filters',\n  toolbarFiltersLabel: 'Show filters',\n  toolbarFiltersTooltipHide: 'Hide filters',\n  toolbarFiltersTooltipShow: 'Show filters',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} active filters` : `${count} active filter`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Search…',\n  toolbarQuickFilterLabel: 'Search',\n  toolbarQuickFilterDeleteIconLabel: 'Clear',\n  // Export selector toolbar button text\n  toolbarExport: 'Export',\n  toolbarExportLabel: 'Export',\n  toolbarExportCSV: 'Download as CSV',\n  toolbarExportPrint: 'Print',\n  toolbarExportExcel: 'Download as Excel',\n  // Toolbar pivot button\n  toolbarPivot: 'Pivot',\n  // Toolbar AI Assistant button\n  toolbarAssistant: 'AI Assistant',\n  // Columns management text\n  columnsManagementSearchTitle: 'Search',\n  columnsManagementNoColumns: 'No columns',\n  columnsManagementShowHideAllText: 'Show/Hide All',\n  columnsManagementReset: 'Reset',\n  columnsManagementDeleteIconLabel: 'Clear',\n  // Filter panel text\n  filterPanelAddFilter: 'Add filter',\n  filterPanelRemoveAll: 'Remove all',\n  filterPanelDeleteIconLabel: 'Delete',\n  filterPanelLogicOperator: 'Logic operator',\n  filterPanelOperator: 'Operator',\n  filterPanelOperatorAnd: 'And',\n  filterPanelOperatorOr: 'Or',\n  filterPanelColumns: 'Columns',\n  filterPanelInputLabel: 'Value',\n  filterPanelInputPlaceholder: 'Filter value',\n  // Filter operators text\n  filterOperatorContains: 'contains',\n  filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'equals',\n  filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'starts with',\n  filterOperatorEndsWith: 'ends with',\n  filterOperatorIs: 'is',\n  filterOperatorNot: 'is not',\n  filterOperatorAfter: 'is after',\n  filterOperatorOnOrAfter: 'is on or after',\n  filterOperatorBefore: 'is before',\n  filterOperatorOnOrBefore: 'is on or before',\n  filterOperatorIsEmpty: 'is empty',\n  filterOperatorIsNotEmpty: 'is not empty',\n  filterOperatorIsAnyOf: 'is any of',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Contains',\n  headerFilterOperatorDoesNotContain: 'Does not contain',\n  headerFilterOperatorEquals: 'Equals',\n  headerFilterOperatorDoesNotEqual: 'Does not equal',\n  headerFilterOperatorStartsWith: 'Starts with',\n  headerFilterOperatorEndsWith: 'Ends with',\n  headerFilterOperatorIs: 'Is',\n  headerFilterOperatorNot: 'Is not',\n  headerFilterOperatorAfter: 'Is after',\n  headerFilterOperatorOnOrAfter: 'Is on or after',\n  headerFilterOperatorBefore: 'Is before',\n  headerFilterOperatorOnOrBefore: 'Is on or before',\n  headerFilterOperatorIsEmpty: 'Is empty',\n  headerFilterOperatorIsNotEmpty: 'Is not empty',\n  headerFilterOperatorIsAnyOf: 'Is any of',\n  'headerFilterOperator=': 'Equals',\n  'headerFilterOperator!=': 'Not equals',\n  'headerFilterOperator>': 'Greater than',\n  'headerFilterOperator>=': 'Greater than or equal to',\n  'headerFilterOperator<': 'Less than',\n  'headerFilterOperator<=': 'Less than or equal to',\n  headerFilterClear: 'Clear filter',\n  // Filter values text\n  filterValueAny: 'any',\n  filterValueTrue: 'true',\n  filterValueFalse: 'false',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  columnMenuAriaLabel: columnName => `${columnName} column menu`,\n  columnMenuShowColumns: 'Show columns',\n  columnMenuManageColumns: 'Manage columns',\n  columnMenuFilter: 'Filter',\n  columnMenuHideColumn: 'Hide column',\n  columnMenuUnsort: 'Unsort',\n  columnMenuSortAsc: 'Sort by ASC',\n  columnMenuSortDesc: 'Sort by DESC',\n  columnMenuManagePivot: 'Manage pivot',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} active filters` : `${count} active filter`,\n  columnHeaderFiltersLabel: 'Show filters',\n  columnHeaderSortIconLabel: 'Sort',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} rows selected` : `${count.toLocaleString()} row selected`,\n  // Total row amount footer text\n  footerTotalRows: 'Total Rows:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} of ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Checkbox selection',\n  checkboxSelectionSelectAllRows: 'Select all rows',\n  checkboxSelectionUnselectAllRows: 'Unselect all rows',\n  checkboxSelectionSelectRow: 'Select row',\n  checkboxSelectionUnselectRow: 'Unselect row',\n  // Boolean cell text\n  booleanCellTrueLabel: 'yes',\n  booleanCellFalseLabel: 'no',\n  // Actions cell more text\n  actionsCellMore: 'more',\n  // Column pinning text\n  pinToLeft: 'Pin to left',\n  pinToRight: 'Pin to right',\n  unpin: 'Unpin',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Group',\n  treeDataExpand: 'see children',\n  treeDataCollapse: 'hide children',\n  // Grouping columns\n  groupingColumnHeaderName: 'Group',\n  groupColumn: name => `Group by ${name}`,\n  unGroupColumn: name => `Stop grouping by ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Detail panel toggle',\n  expandDetailPanel: 'Expand',\n  collapseDetailPanel: 'Collapse',\n  // Pagination\n  paginationRowsPerPage: 'Rows per page:',\n  paginationDisplayedRows: ({\n    from,\n    to,\n    count,\n    estimated\n  }) => {\n    if (!estimated) {\n      return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n    }\n    const estimatedLabel = estimated && estimated > to ? `around ${estimated}` : `more than ${to}`;\n    return `${from}–${to} of ${count !== -1 ? count : estimatedLabel}`;\n  },\n  paginationItemAriaLabel: type => {\n    if (type === 'first') {\n      return 'Go to first page';\n    }\n    if (type === 'last') {\n      return 'Go to last page';\n    }\n    if (type === 'next') {\n      return 'Go to next page';\n    }\n    // if (type === 'previous') {\n    return 'Go to previous page';\n  },\n  // Row reordering text\n  rowReorderingHeaderName: 'Row reordering',\n  // Aggregation\n  aggregationMenuItemHeader: 'Aggregation',\n  aggregationFunctionLabelSum: 'sum',\n  aggregationFunctionLabelAvg: 'avg',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'size',\n  // Pivot panel\n  pivotToggleLabel: 'Pivot',\n  pivotRows: 'Rows',\n  pivotColumns: 'Columns',\n  pivotValues: 'Values',\n  pivotCloseButton: 'Close pivot settings',\n  pivotSearchButton: 'Search fields',\n  pivotSearchControlPlaceholder: 'Search fields',\n  pivotSearchControlLabel: 'Search fields',\n  pivotSearchControlClear: 'Clear search',\n  pivotNoFields: 'No fields',\n  pivotMenuMoveUp: 'Move up',\n  pivotMenuMoveDown: 'Move down',\n  pivotMenuMoveToTop: 'Move to top',\n  pivotMenuMoveToBottom: 'Move to bottom',\n  pivotMenuRows: 'Rows',\n  pivotMenuColumns: 'Columns',\n  pivotMenuValues: 'Values',\n  pivotMenuOptions: 'Field options',\n  pivotMenuAddToRows: 'Add to Rows',\n  pivotMenuAddToColumns: 'Add to Columns',\n  pivotMenuAddToValues: 'Add to Values',\n  pivotMenuRemove: 'Remove',\n  pivotDragToRows: 'Drag here to create rows',\n  pivotDragToColumns: 'Drag here to create columns',\n  pivotDragToValues: 'Drag here to create values',\n  pivotYearColumnHeaderName: '(Year)',\n  pivotQuarterColumnHeaderName: '(Quarter)',\n  // AI Assistant panel\n  aiAssistantPanelTitle: 'AI Assistant',\n  aiAssistantPanelClose: 'Close AI Assistant',\n  aiAssistantPanelNewConversation: 'New conversation',\n  aiAssistantPanelConversationHistory: 'Conversation history',\n  aiAssistantPanelEmptyConversation: 'No prompt history',\n  aiAssistantSuggestions: 'Suggestions',\n  // Prompt field\n  promptFieldLabel: 'Prompt',\n  promptFieldPlaceholder: 'Type a prompt…',\n  promptFieldPlaceholderWithRecording: 'Type or record a prompt…',\n  promptFieldPlaceholderListening: 'Listening for prompt…',\n  promptFieldSpeechRecognitionNotSupported: 'Speech recognition is not supported in this browser',\n  promptFieldSend: 'Send',\n  promptFieldRecord: 'Record',\n  promptFieldStopRecording: 'Stop recording',\n  // Prompt\n  promptRerun: 'Run again',\n  promptProcessing: 'Processing…',\n  promptAppliedChanges: 'Applied changes',\n  // Prompt changes\n  promptChangeGroupDescription: column => `Group by ${column}`,\n  promptChangeAggregationLabel: (column, aggregation) => `${column} (${aggregation})`,\n  promptChangeAggregationDescription: (column, aggregation) => `Aggregate ${column} (${aggregation})`,\n  promptChangeFilterLabel: (column, operator, value) => {\n    if (operator === 'is any of') {\n      return `${column} is any of: ${value}`;\n    }\n    return `${column} ${operator} ${value}`;\n  },\n  promptChangeFilterDescription: (column, operator, value) => {\n    if (operator === 'is any of') {\n      return `Filter where ${column} is any of: ${value}`;\n    }\n    return `Filter where ${column} ${operator} ${value}`;\n  },\n  promptChangeSortDescription: (column, direction) => `Sort by ${column} (${direction})`,\n  promptChangePivotEnableLabel: 'Pivot',\n  promptChangePivotEnableDescription: 'Enable pivot',\n  promptChangePivotColumnsLabel: count => `Columns (${count})`,\n  promptChangePivotColumnsDescription: (column, direction) => `${column}${direction ? ` (${direction})` : ''}`,\n  promptChangePivotRowsLabel: count => `Rows (${count})`,\n  promptChangePivotValuesLabel: count => `Values (${count})`,\n  promptChangePivotValuesDescription: (column, aggregation) => `${column} (${aggregation})`\n};"], "mappings": ";AAAO,IAAM,2BAA2B;AAAA;AAAA,EAEtC,aAAa;AAAA,EACb,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,wBAAwB;AAAA;AAAA,EAExB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,oBAAoB,GAAG,KAAK;AAAA;AAAA,EAExF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,cAAc;AAAA;AAAA,EAEd,kBAAkB;AAAA;AAAA,EAElB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA;AAAA,EAEnB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA,EACjB,qBAAqB,gBAAc,GAAG,UAAU;AAAA,EAChD,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA;AAAA,EAEvB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,oBAAoB,GAAG,KAAK;AAAA,EAC7F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,mBAAmB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAE/G,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,YAAY,IAAI;AAAA,EACrC,eAAe,UAAQ,oBAAoB,IAAI;AAAA;AAAA,EAE/C,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,uBAAuB;AAAA,EACvB,yBAAyB,CAAC;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,CAAC,WAAW;AACd,aAAO,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,IACrE;AACA,UAAM,iBAAiB,aAAa,YAAY,KAAK,UAAU,SAAS,KAAK,aAAa,EAAE;AAC5F,WAAO,GAAG,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,QAAQ,cAAc;AAAA,EAClE;AAAA,EACA,yBAAyB,UAAQ;AAC/B,QAAI,SAAS,SAAS;AACpB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAAA;AAAA,EAE9B,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,cAAc;AAAA,EACd,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,8BAA8B;AAAA;AAAA,EAE9B,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,wBAAwB;AAAA;AAAA,EAExB,kBAAkB;AAAA,EAClB,wBAAwB;AAAA,EACxB,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA;AAAA,EAE1B,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,sBAAsB;AAAA;AAAA,EAEtB,8BAA8B,YAAU,YAAY,MAAM;AAAA,EAC1D,8BAA8B,CAAC,QAAQ,gBAAgB,GAAG,MAAM,KAAK,WAAW;AAAA,EAChF,oCAAoC,CAAC,QAAQ,gBAAgB,aAAa,MAAM,KAAK,WAAW;AAAA,EAChG,yBAAyB,CAAC,QAAQ,UAAU,UAAU;AACpD,QAAI,aAAa,aAAa;AAC5B,aAAO,GAAG,MAAM,eAAe,KAAK;AAAA,IACtC;AACA,WAAO,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK;AAAA,EACvC;AAAA,EACA,+BAA+B,CAAC,QAAQ,UAAU,UAAU;AAC1D,QAAI,aAAa,aAAa;AAC5B,aAAO,gBAAgB,MAAM,eAAe,KAAK;AAAA,IACnD;AACA,WAAO,gBAAgB,MAAM,IAAI,QAAQ,IAAI,KAAK;AAAA,EACpD;AAAA,EACA,6BAA6B,CAAC,QAAQ,cAAc,WAAW,MAAM,KAAK,SAAS;AAAA,EACnF,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,+BAA+B,WAAS,YAAY,KAAK;AAAA,EACzD,qCAAqC,CAAC,QAAQ,cAAc,GAAG,MAAM,GAAG,YAAY,KAAK,SAAS,MAAM,EAAE;AAAA,EAC1G,4BAA4B,WAAS,SAAS,KAAK;AAAA,EACnD,8BAA8B,WAAS,WAAW,KAAK;AAAA,EACvD,oCAAoC,CAAC,QAAQ,gBAAgB,GAAG,MAAM,KAAK,WAAW;AACxF;", "names": []}