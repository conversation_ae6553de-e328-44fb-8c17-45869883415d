import { Request, Response } from 'express';
/**
 * Validate Supplier by fullname.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const validate: (req: Request, res: Response) => Promise<void>;
/**
 * Update Supplier.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const update: (req: Request, res: Response) => Promise<void>;
/**
 * Delete Supplier by ID.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const deleteSupplier: (req: Request, res: Response) => Promise<void>;
/**
 * Get Supplier by ID.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const getSupplier: (req: Request, res: Response) => Promise<void>;
/**
 * Get Suppliers.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const getSuppliers: (req: Request, res: Response) => Promise<void>;
/**
 * Get all Suppliers.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const getAllSuppliers: (req: Request, res: Response) => Promise<void>;
/**
 * Get Frontend Suppliers.
 *
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const getFrontendSuppliers: (req: Request, res: Response) => Promise<void>;
/**
 * Get Backend Suppliers.
 *
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const getBackendSuppliers: (req: Request, res: Response) => Promise<void>;
/**
 * Upload a contract to temp folder.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const createContract: (req: Request, res: Response) => Promise<void>;
/**
 * Update a contract.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const updateContract: (req: Request, res: Response) => Promise<void>;
/**
 * Delete a contract.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const deleteContract: (req: Request, res: Response) => Promise<void>;
/**
 * Delete a temp contract.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {*}
 */
export declare const deleteTempContract: (req: Request, res: Response) => Promise<void>;
