import{r as t}from"./router-BtYqujaw.js";import{i as s,j as r,k as e}from"../entries/index-xsXxT3-W.js";import{b as a}from"./Menu-C_-X8cS7.js";import{g as o,a as n,s as i,c as m}from"./Button-BeKLLPpp.js";function l(t){return o("MuiListItemAvatar",t)}n("MuiListItemAvatar",["root","alignItemsFlexStart"]);const u=i("div",{name:"MuiListItemAvatar",slot:"Root",overridesResolver:(t,s)=>{const{ownerState:r}=t;return[s.root,"flex-start"===r.alignItems&&s.alignItemsFlexStart]}})({minWidth:56,flexShrink:0,variants:[{props:{alignItems:"flex-start"},style:{marginTop:8}}]}),f=t.forwardRef((function(o,n){const i=s({props:o,name:"MuiListItemAvatar"}),{className:f,...I}=i,x=t.useContext(a),c={...i,alignItems:x.alignItems},g=(t=>{const{alignItems:s,classes:r}=t;return m({root:["root","flex-start"===s&&"alignItemsFlexStart"]},l,r)})(c);return r.jsx(u,{className:e(g.root,f),ownerState:c,ref:n,...I})}));export{f as L};
