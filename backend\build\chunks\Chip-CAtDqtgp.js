import{r as e}from"./router-BtYqujaw.js";import{j as a,i as t,k as o,l,aq as r}from"../entries/index-CEzJO5Xy.js";import{c as i}from"./Grow-CjOKj0i1.js";import{a as n,g as c,u as s,n as p,s as d,c as m,m as v,b}from"./Button-DGZYUY3P.js";const g=i(a.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}));function u(e){return c("MuiChip",e)}const $=n("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),y=d("div",{name:"MuiChip",slot:"Root",overridesResolver:(e,a)=>{const{ownerState:t}=e,{color:o,iconColor:r,clickable:i,onDelete:n,size:c,variant:s}=t;return[{[`& .${$.avatar}`]:a.avatar},{[`& .${$.avatar}`]:a[`avatar${l(c)}`]},{[`& .${$.avatar}`]:a[`avatarColor${l(o)}`]},{[`& .${$.icon}`]:a.icon},{[`& .${$.icon}`]:a[`icon${l(c)}`]},{[`& .${$.icon}`]:a[`iconColor${l(r)}`]},{[`& .${$.deleteIcon}`]:a.deleteIcon},{[`& .${$.deleteIcon}`]:a[`deleteIcon${l(c)}`]},{[`& .${$.deleteIcon}`]:a[`deleteIconColor${l(o)}`]},{[`& .${$.deleteIcon}`]:a[`deleteIcon${l(s)}Color${l(o)}`]},a.root,a[`size${l(c)}`],a[`color${l(o)}`],i&&a.clickable,i&&"default"!==o&&a[`clickableColor${l(o)})`],n&&a.deletable,n&&"default"!==o&&a[`deletableColor${l(o)}`],a[s],a[`${s}${l(o)}`]]}})(v((({theme:e})=>{const a="light"===e.palette.mode?e.palette.grey[700]:e.palette.grey[300];return{maxWidth:"100%",fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(e.vars||e).palette.text.primary,backgroundColor:(e.vars||e).palette.action.selected,borderRadius:16,whiteSpace:"nowrap",transition:e.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${$.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${$.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:e.vars?e.vars.palette.Chip.defaultAvatarColor:a,fontSize:e.typography.pxToRem(12)},[`& .${$.avatarColorPrimary}`]:{color:(e.vars||e).palette.primary.contrastText,backgroundColor:(e.vars||e).palette.primary.dark},[`& .${$.avatarColorSecondary}`]:{color:(e.vars||e).palette.secondary.contrastText,backgroundColor:(e.vars||e).palette.secondary.dark},[`& .${$.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:e.typography.pxToRem(10)},[`& .${$.icon}`]:{marginLeft:5,marginRight:-6},[`& .${$.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.26)`:r(e.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.4)`:r(e.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${$.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${$.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(e.palette).filter(b(["contrastText"])).map((([a])=>({props:{color:a},style:{backgroundColor:(e.vars||e).palette[a].main,color:(e.vars||e).palette[a].contrastText,[`& .${$.deleteIcon}`]:{color:e.vars?`rgba(${e.vars.palette[a].contrastTextChannel} / 0.7)`:r(e.palette[a].contrastText,.7),"&:hover, &:active":{color:(e.vars||e).palette[a].contrastText}}}}))),{props:e=>e.iconColor===e.color,style:{[`& .${$.icon}`]:{color:e.vars?e.vars.palette.Chip.defaultIconColor:a}}},{props:e=>e.iconColor===e.color&&"default"!==e.color,style:{[`& .${$.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${$.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:r(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}}},...Object.entries(e.palette).filter(b(["dark"])).map((([a])=>({props:{color:a,onDelete:!0},style:{[`&.${$.focusVisible}`]:{background:(e.vars||e).palette[a].dark}}}))),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:r(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity)},[`&.${$.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:r(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)},"&:active":{boxShadow:(e.vars||e).shadows[1]}}},...Object.entries(e.palette).filter(b(["dark"])).map((([a])=>({props:{color:a,clickable:!0},style:{[`&:hover, &.${$.focusVisible}`]:{backgroundColor:(e.vars||e).palette[a].dark}}}))),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:e.vars?`1px solid ${e.vars.palette.Chip.defaultBorder}`:`1px solid ${"light"===e.palette.mode?e.palette.grey[400]:e.palette.grey[700]}`,[`&.${$.clickable}:hover`]:{backgroundColor:(e.vars||e).palette.action.hover},[`&.${$.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`& .${$.avatar}`]:{marginLeft:4},[`& .${$.avatarSmall}`]:{marginLeft:2},[`& .${$.icon}`]:{marginLeft:4},[`& .${$.iconSmall}`]:{marginLeft:2},[`& .${$.deleteIcon}`]:{marginRight:5},[`& .${$.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(e.palette).filter(b()).map((([a])=>({props:{variant:"outlined",color:a},style:{color:(e.vars||e).palette[a].main,border:`1px solid ${e.vars?`rgba(${e.vars.palette[a].mainChannel} / 0.7)`:r(e.palette[a].main,.7)}`,[`&.${$.clickable}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette[a].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:r(e.palette[a].main,e.palette.action.hoverOpacity)},[`&.${$.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette[a].mainChannel} / ${e.vars.palette.action.focusOpacity})`:r(e.palette[a].main,e.palette.action.focusOpacity)},[`& .${$.deleteIcon}`]:{color:e.vars?`rgba(${e.vars.palette[a].mainChannel} / 0.7)`:r(e.palette[a].main,.7),"&:hover, &:active":{color:(e.vars||e).palette[a].main}}}})))]}}))),C=d("span",{name:"MuiChip",slot:"Label",overridesResolver:(e,a)=>{const{ownerState:t}=e,{size:o}=t;return[a.label,a[`label${l(o)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function h(e){return"Backspace"===e.key||"Delete"===e.key}const f=e.forwardRef((function(r,i){const n=t({props:r,name:"MuiChip"}),{avatar:c,className:d,clickable:v,color:b="default",component:$,deleteIcon:f,disabled:k=!1,icon:S,label:x,onClick:I,onDelete:O,onKeyDown:z,onKeyUp:R,size:w="medium",variant:L="filled",tabIndex:j,skipFocusWhenDisabled:T=!1,...D}=n,V=e.useRef(null),P=s(V,i),M=e=>{e.stopPropagation(),O&&O(e)},N=!(!1===v||!I)||v,E=N||O?p:$||"div",F={...n,component:E,disabled:k,size:w,color:b,iconColor:e.isValidElement(S)&&S.props.color||b,onDelete:!!O,clickable:N,variant:L},W=(e=>{const{classes:a,disabled:t,size:o,color:r,iconColor:i,onDelete:n,clickable:c,variant:s}=e,p={root:["root",s,t&&"disabled",`size${l(o)}`,`color${l(r)}`,c&&"clickable",c&&`clickableColor${l(r)}`,n&&"deletable",n&&`deletableColor${l(r)}`,`${s}${l(r)}`],label:["label",`label${l(o)}`],avatar:["avatar",`avatar${l(o)}`,`avatarColor${l(r)}`],icon:["icon",`icon${l(o)}`,`iconColor${l(i)}`],deleteIcon:["deleteIcon",`deleteIcon${l(o)}`,`deleteIconColor${l(r)}`,`deleteIcon${l(s)}Color${l(r)}`]};return m(p,u,a)})(F),K=E===p?{component:$||"div",focusVisibleClassName:W.focusVisible,...O&&{disableRipple:!0}}:{};let B=null;O&&(B=f&&e.isValidElement(f)?e.cloneElement(f,{className:o(f.props.className,W.deleteIcon),onClick:M}):a.jsx(g,{className:W.deleteIcon,onClick:M}));let A=null;c&&e.isValidElement(c)&&(A=e.cloneElement(c,{className:o(W.avatar,c.props.className)}));let H=null;return S&&e.isValidElement(S)&&(H=e.cloneElement(S,{className:o(W.icon,S.props.className)})),a.jsxs(y,{as:E,className:o(W.root,d),disabled:!(!N||!k)||void 0,onClick:I,onKeyDown:e=>{e.currentTarget===e.target&&h(e)&&e.preventDefault(),z&&z(e)},onKeyUp:e=>{e.currentTarget===e.target&&O&&h(e)&&O(e),R&&R(e)},ref:P,tabIndex:T&&k?-1:j,ownerState:F,...K,...D,children:[A||H,a.jsx(C,{className:W.label,ownerState:F,children:x}),B]})}));export{f as C};
