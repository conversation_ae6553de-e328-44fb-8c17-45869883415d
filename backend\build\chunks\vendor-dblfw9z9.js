var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var n,r,o={exports:{}},i={};function u(){if(n)return i;n=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),f=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),a=Symbol.for("react.suspense"),l=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),y=Symbol.iterator,d={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_=Object.assign,g={};function h(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||d}function v(){}function m(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||d}h.prototype.isReactComponent={},h.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},h.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=h.prototype;var S=m.prototype=new v;S.constructor=m,_(S,h.prototype),S.isPureReactComponent=!0;var b=Array.isArray,E={H:null,A:null,T:null,S:null,V:null},O=Object.prototype.hasOwnProperty;function R(t,n,r,o,i,u){return r=u.ref,{$$typeof:e,type:t,key:n,ref:void 0!==r?r:null,props:u}}function T(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}var w=/\/+/g;function C(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function H(){}function j(n,r,o,i,u){var s=typeof n;"undefined"!==s&&"boolean"!==s||(n=null);var f,c,a=!1;if(null===n)a=!0;else switch(s){case"bigint":case"string":case"number":a=!0;break;case"object":switch(n.$$typeof){case e:case t:a=!0;break;case p:return j((a=n._init)(n._payload),r,o,i,u)}}if(a)return u=u(n),a=""===i?"."+C(n,0):i,b(u)?(o="",null!=a&&(o=a.replace(w,"$&/")+"/"),j(u,r,o,"",(function(e){return e}))):null!=u&&(T(u)&&(f=u,c=o+(null==u.key||n&&n.key===u.key?"":(""+u.key).replace(w,"$&/")+"/")+a,u=R(f.type,c,void 0,0,0,f.props)),r.push(u)),1;a=0;var l,d=""===i?".":i+":";if(b(n))for(var _=0;_<n.length;_++)a+=j(i=n[_],r,o,s=d+C(i,_),u);else if("function"==typeof(_=null===(l=n)||"object"!=typeof l?null:"function"==typeof(l=y&&l[y]||l["@@iterator"])?l:null))for(n=_.call(n),_=0;!(i=n.next()).done;)a+=j(i=i.value,r,o,s=d+C(i,_++),u);else if("object"===s){if("function"==typeof n.then)return j(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(H,H):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(n),r,o,i,u);throw r=String(n),Error("Objects are not valid as a React child (found: "+("[object Object]"===r?"object with keys {"+Object.keys(n).join(", ")+"}":r)+"). If you meant to render a collection of children, use an array instead.")}return a}function A(e,t,n){if(null==e)return e;var r=[],o=0;return j(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function k(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var P="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function N(){}return i.Children={map:A,forEach:function(e,t,n){A(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return A(e,(function(){t++})),t},toArray:function(e){return A(e,(function(e){return e}))||[]},only:function(e){if(!T(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},i.Component=h,i.Fragment=r,i.Profiler=u,i.PureComponent=m,i.StrictMode=o,i.Suspense=a,i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=E,i.__COMPILER_RUNTIME={__proto__:null,c:function(e){return E.H.useMemoCache(e)}},i.cache=function(e){return function(){return e.apply(null,arguments)}},i.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=_({},e.props),o=e.key;if(null!=t)for(i in t.ref,void 0!==t.key&&(o=""+t.key),t)!O.call(t,i)||"key"===i||"__self"===i||"__source"===i||"ref"===i&&void 0===t.ref||(r[i]=t[i]);var i=arguments.length-2;if(1===i)r.children=n;else if(1<i){for(var u=Array(i),s=0;s<i;s++)u[s]=arguments[s+2];r.children=u}return R(e.type,o,void 0,0,0,r)},i.createContext=function(e){return(e={$$typeof:f,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},i.createElement=function(e,t,n){var r,o={},i=null;if(null!=t)for(r in void 0!==t.key&&(i=""+t.key),t)O.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var u=arguments.length-2;if(1===u)o.children=n;else if(1<u){for(var s=Array(u),f=0;f<u;f++)s[f]=arguments[f+2];o.children=s}if(e&&e.defaultProps)for(r in u=e.defaultProps)void 0===o[r]&&(o[r]=u[r]);return R(e,i,void 0,0,0,o)},i.createRef=function(){return{current:null}},i.forwardRef=function(e){return{$$typeof:c,render:e}},i.isValidElement=T,i.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:k}},i.memo=function(e,t){return{$$typeof:l,type:e,compare:void 0===t?null:t}},i.startTransition=function(e){var t=E.T,n={};E.T=n;try{var r=e(),o=E.S;null!==o&&o(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(N,P)}catch(i){P(i)}finally{E.T=t}},i.unstable_useCacheRefresh=function(){return E.H.useCacheRefresh()},i.use=function(e){return E.H.use(e)},i.useActionState=function(e,t,n){return E.H.useActionState(e,t,n)},i.useCallback=function(e,t){return E.H.useCallback(e,t)},i.useContext=function(e){return E.H.useContext(e)},i.useDebugValue=function(){},i.useDeferredValue=function(e,t){return E.H.useDeferredValue(e,t)},i.useEffect=function(e,t,n){var r=E.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},i.useId=function(){return E.H.useId()},i.useImperativeHandle=function(e,t,n){return E.H.useImperativeHandle(e,t,n)},i.useInsertionEffect=function(e,t){return E.H.useInsertionEffect(e,t)},i.useLayoutEffect=function(e,t){return E.H.useLayoutEffect(e,t)},i.useMemo=function(e,t){return E.H.useMemo(e,t)},i.useOptimistic=function(e,t){return E.H.useOptimistic(e,t)},i.useReducer=function(e,t,n){return E.H.useReducer(e,t,n)},i.useRef=function(e){return E.H.useRef(e)},i.useState=function(e){return E.H.useState(e)},i.useSyncExternalStore=function(e,t,n){return E.H.useSyncExternalStore(e,t,n)},i.useTransition=function(){return E.H.useTransition()},i.version="19.1.0",i}function s(){return r||(r=1,o.exports=u()),o.exports}var f,c,a={exports:{}},l={};function p(){return c||(c=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),a.exports=function(){if(f)return l;f=1;var e=s();function t(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function n(){}var r={d:{f:n,r:function(){throw Error(t(522))},D:n,C:n,L:n,m:n,X:n,S:n,M:n},p:0,findDOMNode:null},o=Symbol.for("react.portal"),i=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}return l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,l.createPortal=function(e,n){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(t(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:o,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,n,null,r)},l.flushSync=function(e){var t=i.T,n=r.p;try{if(i.T=null,r.p=2,e)return e()}finally{i.T=t,r.p=n,r.d.f()}},l.preconnect=function(e,t){"string"==typeof e&&(t=t?"string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:null,r.d.C(e,t))},l.prefetchDNS=function(e){"string"==typeof e&&r.d.D(e)},l.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,o=u(n,t.crossOrigin),i="string"==typeof t.integrity?t.integrity:void 0,s="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?r.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:o,integrity:i,fetchPriority:s}):"script"===n&&r.d.X(e,{crossOrigin:o,integrity:i,fetchPriority:s,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},l.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);r.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&r.d.M(e)},l.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,o=u(n,t.crossOrigin);r.d.L(e,n,{crossOrigin:o,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},l.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=u(t.as,t.crossOrigin);r.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else r.d.m(e)},l.requestFormReset=function(e){r.d.r(e)},l.unstable_batchedUpdates=function(e,t){return e(t)},l.useFormState=function(e,t,n){return i.H.useFormState(e,t,n)},l.useFormStatus=function(){return i.H.useHostTransitionStatus()},l.version="19.1.0",l}()),a.exports}export{p as a,e as c,t as g,s as r};
