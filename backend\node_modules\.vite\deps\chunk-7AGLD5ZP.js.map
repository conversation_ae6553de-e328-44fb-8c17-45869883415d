{"version": 3, "sources": ["../../@mui/x-date-pickers/esm/locales/utils/getPickersLocalization.js", "../../@mui/x-date-pickers/esm/locales/enUS.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const getPickersLocalization = pickersTranslations => {\n  return {\n    components: {\n      MuiLocalizationProvider: {\n        defaultProps: {\n          localeText: _extends({}, pickersTranslations)\n        }\n      }\n    }\n  };\n};", "import { getPickersLocalization } from \"./utils/getPickersLocalization.js\";\n\n// This object is not Partial<PickersLocaleText> because it is the default values\n\nconst enUSPickers = {\n  // Calendar navigation\n  previousMonth: 'Previous month',\n  nextMonth: 'Next month',\n  // View navigation\n  openPreviousView: 'Open previous view',\n  openNextView: 'Open next view',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'year view is open, switch to calendar view' : 'calendar view is open, switch to year view',\n  // DateRange labels\n  start: 'Start',\n  end: 'End',\n  startDate: 'Start date',\n  startTime: 'Start time',\n  endDate: 'End date',\n  endTime: 'End time',\n  // Action bar\n  cancelButtonLabel: 'Cancel',\n  clearButtonLabel: 'Clear',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'Today',\n  nextStepButtonLabel: 'Next',\n  // Toolbar titles\n  datePickerToolbarTitle: 'Select date',\n  dateTimePickerToolbarTitle: 'Select date & time',\n  timePickerToolbarTitle: 'Select time',\n  dateRangePickerToolbarTitle: 'Select date range',\n  timeRangePickerToolbarTitle: 'Select time range',\n  // Clock labels\n  clockLabelText: (view, formattedTime) => `Select ${view}. ${!formattedTime ? 'No time selected' : `Selected time is ${formattedTime}`}`,\n  hoursClockNumberText: hours => `${hours} hours`,\n  minutesClockNumberText: minutes => `${minutes} minutes`,\n  secondsClockNumberText: seconds => `${seconds} seconds`,\n  // Digital clock labels\n  selectViewText: view => `Select ${view}`,\n  // Calendar labels\n  calendarWeekNumberHeaderLabel: 'Week number',\n  calendarWeekNumberHeaderText: '#',\n  calendarWeekNumberAriaLabelText: weekNumber => `Week ${weekNumber}`,\n  calendarWeekNumberText: weekNumber => `${weekNumber}`,\n  // Open Picker labels\n  openDatePickerDialogue: formattedDate => formattedDate ? `Choose date, selected date is ${formattedDate}` : 'Choose date',\n  openTimePickerDialogue: formattedTime => formattedTime ? `Choose time, selected time is ${formattedTime}` : 'Choose time',\n  openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',\n  fieldClearLabel: 'Clear',\n  // Table labels\n  timeTableLabel: 'pick time',\n  dateTableLabel: 'pick date',\n  // Field section placeholders\n  fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),\n  fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',\n  fieldDayPlaceholder: () => 'DD',\n  fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',\n  fieldHoursPlaceholder: () => 'hh',\n  fieldMinutesPlaceholder: () => 'mm',\n  fieldSecondsPlaceholder: () => 'ss',\n  fieldMeridiemPlaceholder: () => 'aa',\n  // View names\n  year: 'Year',\n  month: 'Month',\n  day: 'Day',\n  weekDay: 'Week day',\n  hours: 'Hours',\n  minutes: 'Minutes',\n  seconds: 'Seconds',\n  meridiem: 'Meridiem',\n  // Common\n  empty: 'Empty'\n};\nexport const DEFAULT_LOCALE = enUSPickers;\nexport const enUS = getPickersLocalization(enUSPickers);"], "mappings": ";;;;;AACO,IAAM,yBAAyB,yBAAuB;AAC3D,SAAO;AAAA,IACL,YAAY;AAAA,MACV,yBAAyB;AAAA,QACvB,cAAc;AAAA,UACZ,YAAY,SAAS,CAAC,GAAG,mBAAmB;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACPA,IAAM,cAAc;AAAA;AAAA,EAElB,eAAe;AAAA,EACf,WAAW;AAAA;AAAA,EAEX,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,sCAAsC,UAAQ,SAAS,SAAS,+CAA+C;AAAA;AAAA,EAE/G,OAAO;AAAA,EACP,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,qBAAqB;AAAA;AAAA,EAErB,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,wBAAwB;AAAA,EACxB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA;AAAA,EAE7B,gBAAgB,CAAC,MAAM,kBAAkB,UAAU,IAAI,KAAK,CAAC,gBAAgB,qBAAqB,oBAAoB,aAAa,EAAE;AAAA,EACrI,sBAAsB,WAAS,GAAG,KAAK;AAAA,EACvC,wBAAwB,aAAW,GAAG,OAAO;AAAA,EAC7C,wBAAwB,aAAW,GAAG,OAAO;AAAA;AAAA,EAE7C,gBAAgB,UAAQ,UAAU,IAAI;AAAA;AAAA,EAEtC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,iCAAiC,gBAAc,QAAQ,UAAU;AAAA,EACjE,wBAAwB,gBAAc,GAAG,UAAU;AAAA;AAAA,EAEnD,wBAAwB,mBAAiB,gBAAgB,iCAAiC,aAAa,KAAK;AAAA,EAC5G,wBAAwB,mBAAiB,gBAAgB,iCAAiC,aAAa,KAAK;AAAA,EAC5G,yBAAyB,oBAAkB,iBAAiB,mCAAmC,cAAc,KAAK;AAAA,EAClH,iBAAiB;AAAA;AAAA,EAEjB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA;AAAA,EAEhB,sBAAsB,YAAU,IAAI,OAAO,OAAO,WAAW;AAAA,EAC7D,uBAAuB,YAAU,OAAO,gBAAgB,WAAW,SAAS;AAAA,EAC5E,qBAAqB,MAAM;AAAA,EAC3B,yBAAyB,YAAU,OAAO,gBAAgB,WAAW,SAAS;AAAA,EAC9E,uBAAuB,MAAM;AAAA,EAC7B,yBAAyB,MAAM;AAAA,EAC/B,yBAAyB,MAAM;AAAA,EAC/B,0BAA0B,MAAM;AAAA;AAAA,EAEhC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA;AAAA,EAEV,OAAO;AACT;AACO,IAAM,iBAAiB;AACvB,IAAM,OAAO,uBAAuB,WAAW;", "names": []}