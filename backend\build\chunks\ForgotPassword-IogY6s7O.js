import{c as e,j as s,a as r,D as t,e as o,A as a,z as i}from"../entries/index-xsXxT3-W.js";import{d as n,r as m}from"./router-BtYqujaw.js";import{u as l,z as c,s as d}from"./zod-4O8Zwsja.js";import{L as j}from"./Layout-DaeN7D4t.js";import{s as p}from"./reset-password-xiMRPUfP.js";import{I as h,F as f}from"./InputLabel-C8rcdOGQ.js";import{I as u}from"./Input-D1AdR9CM.js";import{B as x}from"./Button-BeKLLPpp.js";import{F as E}from"./FormHelperText-DDZ4BMA4.js";import{P as _}from"./Paper-C-atefOs.js";import"./vendor-dblfw9z9.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const S=c.object({email:c.string().email({message:r.EMAIL_NOT_VALID})}),b=()=>{const c=e.c(48),b=n(),[N,g]=m.useState(!1),[v,A]=m.useState(!1);let I;c[0]===Symbol.for("react.memo_cache_sentinel")?(I={resolver:d(S),mode:"onSubmit"},c[0]=I):I=c[0];const{register:y,handleSubmit:L,formState:P,setError:T,clearErrors:w}=l(I),{errors:C,isSubmitting:O}=P;let R;c[1]!==b?(R=e=>{e?b("/"):g(!0)},c[1]=b,c[2]=R):R=c[2];const D=R;let F;c[3]!==T?(F=async e=>{const{email:s}=e;try{if(200===await t({email:s,appType:o.APP_TYPE}))return void T("email",{message:p.EMAIL_ERROR});200===await a(s,!0,o.APP_TYPE)?A(!0):i()}catch(r){i(r)}},c[3]=T,c[4]=F):F=c[4];const H=F,M="forgot-password-form "+(N?"":"hidden");let k;c[5]===Symbol.for("react.memo_cache_sentinel")?(k=s.jsx("h1",{className:"forgot-password-title",children:p.RESET_PASSWORD_HEADING}),c[5]=k):k=c[5];const G=v?"hidden":"";let W,q,z,B;c[6]!==L||c[7]!==H?(W=L(H),c[6]=L,c[7]=H,c[8]=W):W=c[8],c[9]===Symbol.for("react.memo_cache_sentinel")?(q=s.jsx(h,{className:"required",children:r.EMAIL}),c[9]=q):q=c[9],c[10]!==y?(z=y("email"),c[10]=y,c[11]=z):z=c[11],c[12]!==w?(B=()=>w("email"),c[12]=w,c[13]=B):B=c[13];const Y=!!C.email;let J;c[14]!==B||c[15]!==Y||c[16]!==z?(J=s.jsx(u,{...z,onChange:B,type:"text",error:Y,autoComplete:"off",required:!0}),c[14]=B,c[15]=Y,c[16]=z,c[17]=J):J=c[17];const V=!!C.email,K=C.email?.message||"";let Q,U,X,Z,$,ee,se,re,te,oe;return c[18]!==V||c[19]!==K?(Q=s.jsx(E,{error:V,children:K}),c[18]=V,c[19]=K,c[20]=Q):Q=c[20],c[21]!==J||c[22]!==Q?(U=s.jsxs(f,{fullWidth:!0,margin:"dense",children:[q,J,Q]}),c[21]=J,c[22]=Q,c[23]=U):U=c[23],c[24]!==O?(X=s.jsx(x,{type:"submit",className:"btn-primary",variant:"contained",disabled:O,children:p.RESET}),c[24]=O,c[25]=X):X=c[25],c[26]!==b?(Z=s.jsx(x,{variant:"outlined",onClick:()=>b("/"),children:r.CANCEL}),c[26]=b,c[27]=Z):Z=c[27],c[28]!==X||c[29]!==Z?($=s.jsxs("div",{className:"buttons",children:[X,Z]}),c[28]=X,c[29]=Z,c[30]=$):$=c[30],c[31]!==U||c[32]!==$||c[33]!==W?(ee=s.jsxs("form",{onSubmit:W,children:[U,$]}),c[31]=U,c[32]=$,c[33]=W,c[34]=ee):ee=c[34],c[35]!==ee||c[36]!==G?(se=s.jsx("div",{className:G,children:ee}),c[35]=ee,c[36]=G,c[37]=se):se=c[37],c[38]!==b||c[39]!==v?(re=v&&s.jsxs("div",{children:[s.jsx("span",{children:p.EMAIL_SENT}),s.jsx("p",{children:s.jsx(x,{variant:"text",onClick:()=>b("/"),className:"btn-lnk",children:r.GO_TO_HOME})})]}),c[38]=b,c[39]=v,c[40]=re):re=c[40],c[41]!==se||c[42]!==re||c[43]!==M?(te=s.jsx("div",{className:"forgot-password",children:s.jsxs(_,{className:M,elevation:10,children:[k,se,re]})}),c[41]=se,c[42]=re,c[43]=M,c[44]=te):te=c[44],c[45]!==D||c[46]!==te?(oe=s.jsx(j,{onLoad:D,strict:!1,children:te}),c[45]=D,c[46]=te,c[47]=oe):oe=c[47],oe};export{b as default};
