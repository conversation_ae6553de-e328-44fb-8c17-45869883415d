import { PaymentGateway } from ':bookcars-types';
export interface IPaymentGateway {
    processPayment(amount: number, currency: string, description: string): Promise<any>;
    createCheckoutSession(bookingId: string, customerId: string, amount: number): Promise<any>;
    refundPayment(paymentId: string, amount: number): Promise<any>;
}
export declare class PaymentGatewayFactory {
    static createGateway(type: PaymentGateway): IPaymentGateway | null;
}
