import{F as e,j as s,R as i,a as r,U as a,z as t,J as n,A as o,e as l,B as m}from"../entries/index-CEzJO5Xy.js";import{d as c,r as d}from"./router-BtYqujaw.js";import{u as p,a as u,s as h}from"./zod-4O8Zwsja.js";import{C as j,s as f}from"./ContractList-CG6Ru0Kt.js";import{L as x}from"./Layout-BQBjg4Lf.js";import{s as g}from"./create-supplier-D6E0ETct.js";import{a as N,v as y,u as C}from"./SupplierService-DSnTbAgG.js";import{E as D}from"./Error-7KgmWHkR.js";import{S as R}from"./SimpleBackdrop-Bf3qjF13.js";import L from"./NoMatch-jvHCs4x8.js";import{A as E}from"./Avatar-BtfxKR-8.js";import{P as S}from"./Paper-CcwAvfvc.js";import{I as b}from"./Info-C_WcR51V.js";import{F as A,I}from"./InputLabel-BbcIE26O.js";import{I as _}from"./Input-BQdee9z7.js";import{F as v}from"./FormHelperText-DFSsjBsL.js";import{F as k,S as O}from"./Switch-BWPUOSX1.js";import{B as T}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./IconButton-CnBvmeAK.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-CtA82Ni6.js";import"./Backdrop-Bzn12VyM.js";import"./Delete-CnqjtpsJ.js";import"./DressService-J0XavNJj.js";import"./LocationService-BtQFgoWL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./SwitchBase-BIeqtL5F.js";const w=()=>{const w=c(),[P,W]=d.useState(),[B,M]=d.useState(),[F,U]=d.useState(!1),[q,V]=d.useState(!1),[z,G]=d.useState(!1),[H,Y]=d.useState(!1),[K,Q]=d.useState(""),[J,Z]=d.useState(!1),{control:$,register:X,handleSubmit:ee,setValue:se,setError:ie,clearErrors:re,setFocus:ae,formState:{errors:te,isSubmitting:ne},trigger:oe}=p({resolver:h(f),mode:"onBlur",defaultValues:{fullName:"",email:"",phone:"",location:"",bio:"",blacklisted:!1,payLater:!1,licenseRequired:!1,minimumRentalDays:"",priceChangeRate:"",supplierDressLimit:"",notifyAdminOnNewDress:!1}}),{payLater:le,licenseRequired:me,notifyAdminOnNewDress:ce,blacklisted:de}=u({control:$}),pe=e(P);return s.jsxs(x,{onLoad:async e=>{if(e&&e.verified){G(!0),W(e);const i=new URLSearchParams(window.location.search);if(i.has("c")){const r=i.get("c");if(r&&""!==r)try{const s=await N(r);if(s){if(e.type!==a.Admin&&e._id!==s._id)return G(!1),void Y(!0);M(s),se("email",s.email||""),Q(s.avatar||""),se("fullName",s.fullName||""),se("phone",s.phone||""),se("location",s.location||""),se("bio",s.bio||""),se("payLater",!!s.payLater),se("minimumRentalDays",s.minimumRentalDays?.toString()||""),se("priceChangeRate",s.priceChangeRate?.toString()||""),se("supplierDressLimit",s.supplierDressLimit?.toString()||""),se("notifyAdminOnNewDress",!!s.notifyAdminOnNewDress),se("blacklisted",!!s.blacklisted),V(!0),G(!1)}else G(!1),Y(!0)}catch(s){t(s),G(!1),U(!0),V(!1)}else G(!1),Y(!0)}else G(!1),Y(!0)}},strict:!0,children:[q&&s.jsx("div",{className:"update-supplier",children:s.jsx(S,{className:"supplier-form-update",elevation:10,children:s.jsxs("form",{onSubmit:ee((async e=>{try{if(!B)return void t();if(B.fullName!==e.fullName&&200!==await y({fullName:e.fullName}))return ie("fullName",{message:g.INVALID_SUPPLIER_NAME}),void ae("fullName");if(!K)return Z(!0),void U(!1);const s={_id:B._id,fullName:e.fullName,phone:e.phone,location:e.location,bio:e.bio,payLater:!!e.payLater,minimumRentalDays:e.minimumRentalDays?Number(e.minimumRentalDays):void 0,priceChangeRate:e.priceChangeRate?Number(e.priceChangeRate):void 0,supplierDressLimit:e.supplierDressLimit?Number(e.supplierDressLimit):void 0,notifyAdminOnNewDress:e.notifyAdminOnNewDress,blacklisted:!!e.blacklisted},i=await C(s);200===i.status?(M(i.data),m(r.UPDATED)):t()}catch(s){t(s)}}),(()=>{const e=Object.keys(te)[0];e&&ae(e)})),children:[s.jsx(E,{type:i.Supplier,mode:"update",record:B,size:"large",readonly:!1,hideDelete:!0,onBeforeUpload:()=>{G(!0)},onChange:e=>{if(B&&P){const s=n(B);if(s.avatar=e,P._id===B._id){const s=n(P);s.avatar=e,W(s)}G(!1),M(s),e&&Z(!1)}else t()},color:"disabled",className:"avatar-ctn"}),s.jsxs("div",{className:"info",children:[s.jsx(b,{}),s.jsx("span",{children:g.RECOMMENDED_IMAGE_SIZE})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:r.FULL_NAME}),s.jsx(_,{...X("fullName"),type:"text",error:!!te.fullName,required:!0,autoComplete:"off"}),s.jsx(v,{error:!!te.fullName,children:te.fullName?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:r.EMAIL}),s.jsx(_,{...X("email"),type:"text",autoComplete:"off",disabled:!0})]}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(O,{...X("blacklisted"),checked:de,onChange:e=>{se("blacklisted",e.target.checked)},color:"primary"}),label:r.BLACKLISTED,title:r.BLACKLISTED_TOOLTIP})}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(O,{...X("payLater"),checked:le,onChange:e=>{se("payLater",e.target.checked)},color:"primary"}),label:r.PAY_LATER})}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(O,{...X("licenseRequired"),checked:me,onChange:e=>{se("licenseRequired",e.target.checked)},color:"primary"}),label:r.LICENSE_REQUIRED})}),s.jsxs("div",{className:"info",children:[s.jsx(b,{}),s.jsx("span",{children:r.OPTIONAL})]}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(O,{...X("notifyAdminOnNewDress"),checked:ce,disabled:P?.type===a.Supplier,onChange:e=>{se("notifyAdminOnNewDress",e.target.checked)},color:"primary"}),label:r.NOTIFY_ADMIN_ON_NEW_DRESS})}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.SUPPLIER_DRESS_LIMIT}),s.jsx(_,{...X("supplierDressLimit"),type:"text",autoComplete:"off",error:!!te.supplierDressLimit,onChange:()=>re("supplierDressLimit")}),s.jsx(v,{error:!!te.supplierDressLimit,children:te.supplierDressLimit?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.MIN_RENTAL_DAYS}),s.jsx(_,{...X("minimumRentalDays"),type:"text",autoComplete:"off",error:!!te.minimumRentalDays,onChange:()=>re("minimumRentalDays")}),s.jsx(v,{error:!!te.minimumRentalDays,children:te.minimumRentalDays?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.PRICE_CHANGE_RATE}),s.jsx(_,{...X("priceChangeRate"),type:"text",autoComplete:"off",error:!!te.priceChangeRate,onChange:()=>re("priceChangeRate")}),s.jsx(v,{error:!!te.priceChangeRate,children:te.priceChangeRate?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.PHONE}),s.jsx(_,{...X("phone",{onBlur:()=>oe("phone")}),type:"text",autoComplete:"off",error:!!te.phone,onChange:()=>re("phone")}),s.jsx(v,{error:!!te.phone,children:te.phone?.message||""})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.LOCATION}),s.jsx(_,{...X("location"),type:"text",autoComplete:"off"})]}),s.jsxs(A,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:r.BIO}),s.jsx(_,{...X("bio"),type:"text",autoComplete:"off"})]}),s.jsx(A,{fullWidth:!0,margin:"dense",children:s.jsx(j,{supplier:B})}),pe&&s.jsx(A,{fullWidth:!0,margin:"dense",className:"resend-activation-link",children:s.jsx(T,{variant:"outlined",onClick:async()=>{if(B)try{200===await o(B.email,!1,l.APP_TYPE)?m(r.ACTIVATION_EMAIL_SENT):t()}catch(e){t(e)}},children:r.RESEND_ACTIVATION_LINK})}),s.jsxs("div",{className:"buttons",children:[s.jsx(T,{type:"submit",variant:"contained",className:"btn-primary btn-margin btn-margin-bottom",size:"small",onClick:()=>w(`/change-password?u=${B&&B._id}`),children:r.RESET_PASSWORD}),s.jsx(T,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:ne,children:r.SAVE}),s.jsx(T,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>w("/suppliers"),children:r.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[F&&s.jsx(D,{message:r.GENERIC_ERROR}),J&&s.jsx(D,{message:r.IMAGE_REQUIRED})]})]})})}),z&&s.jsx(R,{text:r.PLEASE_WAIT}),H&&s.jsx(L,{hideHeader:!0})]})};export{w as default};
