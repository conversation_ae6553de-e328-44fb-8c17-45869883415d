import{j as t,aC as e,p as a}from"../entries/index-CEzJO5Xy.js";import{c as n}from"./Grow-CjOKj0i1.js";const o=n(t.jsx("path",{d:"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5"})),s=t=>e.post("/api/validate-location",t,{withCredentials:!0}).then((t=>t.status)),i=t=>e.post("/api/create-location",t,{withCredentials:!0}).then((t=>t.status)),p=(t,a)=>e.put(`/api/update-location/${t}`,a,{withCredentials:!0}).then((t=>({status:t.status,data:t.data}))),d=t=>e.delete(`/api/delete-location/${encodeURIComponent(t)}`,{withCredentials:!0}).then((t=>t.status)),l=t=>e.get(`/api/location/${encodeURIComponent(t)}/${a()}`,{withCredentials:!0}).then((t=>t.data)),r=(t,n,o)=>e.get(`/api/locations/${n}/${o}/${a()}/?s=${encodeURIComponent(t)}`,{withCredentials:!0}).then((t=>t.data)),c=t=>e.get(`/api/check-location/${encodeURIComponent(t)}`,{withCredentials:!0}).then((t=>t.status)),h=t=>{const a=new FormData;return a.append("image",t),e.post("/api/create-location-image",a,{withCredentials:!0,headers:{"Content-Type":"multipart/form-data"}}).then((t=>t.data))},m=(t,a)=>{const n=new FormData;return n.append("image",a),e.post(`/api/update-location-image/${encodeURIComponent(t)}`,n,{withCredentials:!0,headers:{"Content-Type":"multipart/form-data"}}).then((t=>t.status))},C=t=>e.post(`/api/delete-location-image/${encodeURIComponent(t)}`,null,{withCredentials:!0}).then((t=>t.status)),u=t=>e.post(`/api/delete-temp-location-image/${encodeURIComponent(t)}`,null,{withCredentials:!0}).then((t=>t.status));export{o as L,u as a,i as b,c,d,l as e,C as f,r as g,h,m as i,p as u,s as v};
