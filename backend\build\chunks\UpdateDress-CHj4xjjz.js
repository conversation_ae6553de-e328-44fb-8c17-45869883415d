import{u as e,e as t,j as s,R as a,S as r,a as i,z as o}from"../entries/index-xsXxT3-W.js";import{d as l,r as n}from"./router-BtYqujaw.js";import{L as c}from"./Layout-DaeN7D4t.js";import{a as d,u as m}from"./DressService-DkS6e_O5.js";import{E as u}from"./Error-FiYP5RHa.js";import{E as p}from"./Error-DRzAdbbx.js";import{S as j}from"./SimpleBackdrop-CqsJhYJ4.js";import h from"./NoMatch-DMPclUW6.js";import{A as g}from"./Avatar-CvDHTACZ.js";import{S,D as x,a as f,b as N,c as b}from"./create-dress-CqJ0gf4a.js";import{L as C}from"./LocationSelectList-BP49A3oC.js";import{P as y}from"./Paper-C-atefOs.js";import{I as E}from"./Info-CNP9gYBt.js";import{F as v,I}from"./InputLabel-C8rcdOGQ.js";import{I as M}from"./Input-D1AdR9CM.js";import{F as P}from"./FormHelperText-DDZ4BMA4.js";import{T as k}from"./TextField-D_yQOTzE.js";import{F as A,S as R}from"./Switch-C5asfh_w.js";import{B as L}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-DiTut-u0.js";import"./Backdrop-Czag2Ija.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./SupplierService-9DC5V5ZJ.js";import"./MultipleSelect-DovAF4K6.js";import"./Autocomplete-CWN5GAd4.js";import"./OutlinedInput-BX8yFQbF.js";import"./useFormControl-B7jXtRD7.js";import"./Chip-MGF1mKZa.js";import"./IconButton-CxOCoGF3.js";import"./Flag-CMGasDVj.js";import"./MenuItem-P0BnGnrT.js";import"./Menu-C_-X8cS7.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-DEridHif.js";import"./listItemTextClasses-BcbgzvlE.js";import"./SwitchBase-DrUkTXjH.js";const W=()=>{const W=l(),{user:_}=e(),[D,B]=n.useState(!1),[O,T]=n.useState(!1),[U,q]=n.useState(!1),[w,G]=n.useState(!1),[F,$]=n.useState(),[z,Y]=n.useState(!1),[Z,H]=n.useState(!1),[V,Q]=n.useState(""),[J,K]=n.useState(""),[X,ee]=n.useState([]),[te,se]=n.useState(""),[ae,re]=n.useState(""),[ie,oe]=n.useState(""),[le,ne]=n.useState(""),[ce,de]=n.useState(""),[me,ue]=n.useState(""),[pe,je]=n.useState(""),[he,ge]=n.useState(""),[Se,xe]=n.useState(!1),[fe,Ne]=n.useState([]),[be,Ce]=n.useState(!0),[ye,Ee]=n.useState(!1),[ve,Ie]=n.useState(!1),[Me,Pe]=n.useState(""),[ke,Ae]=n.useState(""),[Re,Le]=n.useState(""),[We,_e]=n.useState(""),[De,Be]=n.useState(""),[Oe,Te]=n.useState(""),[Ue,qe]=n.useState(!1),[we,Ge]=n.useState(""),[Fe,$e]=n.useState(""),[ze,Ye]=n.useState(String(t.MINIMUM_AGE)),[Ze,He]=n.useState(!0),[Ve,Qe]=n.useState(!1),[Je,Ke]=n.useState(""),Xe=e=>{const s=Number.parseInt(e,10);return!Number.isNaN(s)&&s>=t.MINIMUM_AGE},et=e=>""===e?-1:Number(e),tt=e=>e&&Number(e)||null;return s.jsxs(c,{onLoad:async()=>{B(!0),Qe(!1),Y(!1),H(!1),T(!1),q(!1);try{const e=new URLSearchParams(window.location.search);if(e.has("dr")){const t=e.get("dr");if(t&&""!==t){const e=await d(t);e?($(e),Q(e.name),K(e.supplier._id||""),ee(e.locations),se(e.dailyPrice.toString()),re(e.discountedDailyPrice?.toString()||""),oe(e.biWeeklyPrice?.toString()||""),ne(e.discountedBiWeeklyPrice?.toString()||""),de(e.weeklyPrice?.toString()||""),ue(e.discountedWeeklyPrice?.toString()||""),je(e.monthlyPrice?.toString()||""),ge(e.discountedMonthlyPrice?.toString()||""),xe(e.isDateBasedPrice),Ne(e.dateBasedPrices||[]),Ce(e.available),Ee(e.fullyBooked||!1),Ie(e.comingSoon||!1),Pe(e.type),Ae(e.size),Le(e.style||""),_e(e.color),Be(e.length.toString()),Te(e.material),qe(e.customizable||!1),Ge(-1===e.cancellation?"":e.cancellation.toString()),$e(-1===e.amendments?"":e.amendments.toString()),Ye(e.minimumAge.toString()),Ke(e.deposit.toString()),G(!0)):T(!0)}else T(!0)}else T(!0)}catch(e){q(!0)}finally{B(!1)}},strict:!0,children:[!U&&!O&&s.jsx("div",{className:"create-dress",children:s.jsx(y,{className:"dress-form dress-form-wrapper",elevation:10,style:w?{}:{display:"none"},children:s.jsxs("form",{onSubmit:async e=>{try{if(e.preventDefault(),B(!0),!Xe(ze))return Qe(!0),void Y(!1);if(!F?.image)return Y(!0),void H(!1);const t={_id:F?._id,loggedUser:_?._id,name:V,supplier:J,minimumAge:Number.parseInt(ze,10),locations:X.map((e=>e._id)),dailyPrice:Number(te),discountedDailyPrice:tt(ae),biWeeklyPrice:tt(ie),discountedBiWeeklyPrice:tt(le),weeklyPrice:tt(ce),discountedWeeklyPrice:tt(me),monthlyPrice:tt(pe),discountedMonthlyPrice:tt(he),deposit:Number(Je),available:be,fullyBooked:ye,comingSoon:ve,type:Me,size:ke,color:We,length:Number(De),material:Oe,customizable:Ue,cancellation:et(we),amendments:et(Fe),isDateBasedPrice:Se,dateBasedPrices:fe,range:"",accessories:[]};200===await m(t)?W("/dresses"):o()}catch(t){o(t)}finally{B(!1)}},children:[s.jsx(g,{type:a.Dress,mode:"update",record:F,hideDelete:!0,size:"large",readonly:!1,onBeforeUpload:()=>{B(!0)},onChange:e=>{B(!1),Y(!1),H(!1)},onValidate:e=>{e||H(!0)},color:"disabled",className:"avatar-ctn"}),s.jsxs("div",{className:"info",children:[s.jsx(E,{}),s.jsx("span",{children:r.RECOMMENDED_IMAGE_SIZE})]}),s.jsxs(v,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:i.NAME}),s.jsx(M,{id:"name",type:"text",required:!0,onChange:e=>{Q(e.target.value)},autoComplete:"off",value:V})]}),s.jsx(v,{fullWidth:!0,margin:"dense",children:s.jsx(S,{label:i.SUPPLIER,required:!0,value:J?{_id:J,name:"",image:""}:void 0,onChange:e=>{K(e.length>0?e[0]._id:"")}})}),s.jsx(v,{fullWidth:!0,margin:"dense",children:s.jsx(C,{label:r.LOCATIONS,multiple:!0,required:!0,value:X,onChange:e=>{ee(e)}})}),s.jsx(v,{fullWidth:!0,margin:"dense",children:s.jsx(x,{label:r.DRESS_TYPE,required:!0,value:Me,onChange:e=>{Pe(e)}})}),s.jsx(v,{fullWidth:!0,margin:"dense",children:s.jsx(f,{label:r.DRESS_SIZE,required:!0,value:ke,onChange:e=>{Ae(e)}})}),s.jsx(v,{fullWidth:!0,margin:"dense",children:s.jsx(N,{label:r.DRESS_STYLE,required:!0,value:Re,onChange:e=>{Le(e)}})}),s.jsx(v,{fullWidth:!0,margin:"dense",children:s.jsx(b,{label:r.MATERIAL,required:!0,value:Oe,onChange:e=>{Te(e)}})}),s.jsxs(v,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:r.COLOR}),s.jsx(M,{id:"color",type:"text",required:!0,onChange:e=>{_e(e.target.value)},autoComplete:"off",value:We})]}),s.jsxs(v,{fullWidth:!0,margin:"dense",children:[s.jsxs(I,{className:"required",children:[r.LENGTH," (cm)"]}),s.jsx(M,{id:"length",type:"number",required:!0,onChange:e=>{Be(e.target.value)},autoComplete:"off",value:De})]}),s.jsxs(v,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:`${r.PRICE} (${i.CURRENCY})`}),s.jsx(M,{id:"price",type:"number",required:!0,onChange:e=>{se(e.target.value)},autoComplete:"off",value:te})]}),s.jsxs(v,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:`${r.DEPOSIT} (${i.CURRENCY})`}),s.jsx(M,{id:"deposit",type:"number",onChange:e=>{Ke(e.target.value)},autoComplete:"off",value:Je})]}),s.jsxs(v,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:i.MINIMUM_AGE}),s.jsx(M,{id:"minimum-age",type:"number",required:!0,error:!Ze,onChange:e=>{Ye(e.target.value);const t=Xe(e.target.value);He(t),t||Qe(!0)},autoComplete:"off",value:ze}),s.jsx(P,{error:!Ze,children:!Ze&&i.MINIMUM_AGE_NOT_VALID||""})]}),s.jsx(v,{fullWidth:!0,margin:"dense",children:s.jsx(k,{label:`${r.CANCELLATION} (${i.CURRENCY})`,slotProps:{htmlInput:{inputMode:"numeric",pattern:"^\\d+(\\.\\d+)?$"}},onChange:e=>{Ge(e.target.value)},variant:"standard",autoComplete:"off",value:we})}),s.jsx(v,{fullWidth:!0,margin:"dense",children:s.jsx(k,{label:`${r.AMENDMENTS} (${i.CURRENCY})`,slotProps:{htmlInput:{inputMode:"numeric",pattern:"^\\d+(\\.\\d+)?$"}},onChange:e=>{$e(e.target.value)},variant:"standard",autoComplete:"off",value:Fe})}),s.jsx(v,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(A,{control:s.jsx(R,{checked:Ue,onChange:e=>{qe(e.target.checked)},color:"primary"}),label:r.CUSTOMIZABLE,className:"checkbox-fcl"})}),s.jsx(v,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(A,{control:s.jsx(R,{checked:be,onChange:e=>{Ce(e.target.checked)},color:"primary"}),label:r.AVAILABLE,className:"checkbox-fcl"})}),s.jsx(v,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(A,{control:s.jsx(R,{checked:ye,onChange:e=>{Ee(e.target.checked)},color:"primary"}),label:r.FULLY_BOOKED,className:"checkbox-fcl"})}),s.jsx(v,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(A,{control:s.jsx(R,{checked:ve,onChange:e=>{Ie(e.target.checked)},color:"primary"}),label:r.COMING_SOON,className:"checkbox-fcl"})}),s.jsxs("div",{className:"buttons",children:[s.jsx(L,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:D,children:i.UPDATE}),s.jsx(L,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>{W("/dresses")},children:i.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[z&&s.jsx(p,{message:i.IMAGE_REQUIRED}),Z&&s.jsx(p,{message:r.IMAGE_SIZE_ERROR}),Ve&&s.jsx(p,{message:i.FORM_ERROR})]})]})})}),D&&s.jsx(j,{text:i.LOADING}),U&&s.jsx(u,{}),O&&s.jsx(h,{})]})};export{W as default};
