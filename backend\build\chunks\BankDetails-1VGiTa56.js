import{j as e,z as s}from"../entries/index-CEzJO5Xy.js";import{r as t}from"./router-BtYqujaw.js";import{g as a}from"./BankDetailsService-Od7WKGPo.js";import{s as r}from"./bank-details-form-C8d7v4La.js";import{L as o}from"./Layout-BQBjg4Lf.js";import i from"./NoMatch-jvHCs4x8.js";import{P as n}from"./Paper-CcwAvfvc.js";import{F as l,I as m}from"./InputLabel-BbcIE26O.js";import{I as d}from"./Input-BQdee9z7.js";import"./vendor-dblfw9z9.js";import"./Button-DGZYUY3P.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const j=()=>{const[j,p]=t.useState(null),[c,f]=t.useState(!1);return e.jsxs(o,{onLoad:async()=>{try{const e=await a();p(e),e?.showBankDetailsPage||f(!0)}catch(e){s(e)}},strict:!0,children:[e.jsx("div",{className:"bank-details",children:j&&!c&&e.jsxs(n,{className:"bank-details-form bank-details-form-wrapper",elevation:10,children:[e.jsx("h1",{className:"bank-details-form-title",children:r.BANK_DETAILS}),e.jsxs(l,{fullWidth:!0,margin:"dense",children:[e.jsx(m,{children:r.ACCOUNT_HOLDER}),e.jsx(d,{type:"text",readOnly:!0,autoComplete:"off",value:j.accountHolder})]}),e.jsxs(l,{fullWidth:!0,margin:"dense",children:[e.jsx(m,{children:r.BANK_NAME}),e.jsx(d,{type:"text",readOnly:!0,autoComplete:"off",value:j.bankName})]}),e.jsxs(l,{fullWidth:!0,margin:"dense",children:[e.jsx(m,{children:r.IBAN}),e.jsx(d,{type:"text",readOnly:!0,autoComplete:"off",value:j.iban})]}),e.jsxs(l,{fullWidth:!0,margin:"dense",children:[e.jsx(m,{children:r.SWIFT_BIC}),e.jsx(d,{type:"text",readOnly:!0,autoComplete:"off",value:j.swiftBic})]})]})}),c&&e.jsx(i,{hideHeader:!0})]})};export{j as default};
