import{aX as e,a$ as a,b0 as s,j as t,k as o,aZ as r,b1 as m}from"../entries/index-CEzJO5Xy.js";import{p as n,a as u,q as d}from"./Button-DGZYUY3P.js";import{r as f}from"./router-BtYqujaw.js";const i=u("MuiBox",["root"]),l=function(r={}){const{themeId:m,defaultTheme:u,defaultClassName:d="MuiBox-root",generateClassName:i}=r,l=n("div",{shouldForwardProp:e=>"theme"!==e&&"sx"!==e&&"as"!==e})(e);return f.forwardRef((function(e,r){const n=a(u),{className:f,component:c="div",...h}=s(e);return t.jsx(l,{as:c,ref:r,className:o(f,i?i(d):d),theme:m&&n[m]||n,...h})}))}({themeId:r,defaultTheme:m(),defaultClassName:i.root,generateClassName:d.generate});export{l as B};
