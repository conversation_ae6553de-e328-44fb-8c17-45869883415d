import{Q as t,j as e,R as a,e as o,z as r}from"../entries/index-xsXxT3-W.js";import{r as s}from"./router-BtYqujaw.js";import{g as n}from"./LocationService-6NvQT9iL.js";import{M as l}from"./MultipleSelect-DovAF4K6.js";const i=({value:i,multiple:c,label:u,required:p,variant:g,onChange:f})=>{const[S,m]=s.useState(!1),[d,h]=s.useState(!1),[j,v]=s.useState([]),[y,E]=s.useState(!0),[I,D]=s.useState(1),[b,x]=s.useState(""),[A,C]=s.useState([]);s.useEffect((()=>{const e=c?i:[i];i&&!t(A,e)&&C(e)}),[i,c,A]);const F=async(t,e,a)=>{try{if(y||1===t){h(!0);const r=await n(e,t,o.PAGE_SIZE),s=r&&r.length>0?r[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!s)return;const l=Array.isArray(s.pageInfo)&&s.pageInfo.length>0?s.pageInfo[0].totalRecords:0,i=1===t?s.resultData:[...j,...s.resultData];v(i),E(s.resultData.length>0),a&&a({rows:s.resultData,rowCount:l})}}catch(s){r(s)}finally{h(!1)}};return e.jsx(l,{loading:d,label:u||"",callbackFromMultipleSelect:t=>{f&&f(t)},options:j,selectedOptions:A,required:p||!1,multiple:c,type:a.Location,variant:g||"standard",ListboxProps:{onScroll:t=>{const e=t.currentTarget;if(y&&!d&&e.scrollTop+e.clientHeight>=e.scrollHeight-o.PAGE_OFFSET){const t=I+1;D(t),F(t,b)}}},onFocus:()=>{if(!S){const t=1;v([]),D(t),F(t,b,(()=>{m(!0)}))}},onInputChange:t=>{const e=t&&t.target&&"value"in t.target&&t.target.value||"";e!==b&&(v([]),D(1),x(e),F(1,e))},onClear:()=>{v([]),D(1),x(""),E(!0),F(1,"")}})};export{i as L};
