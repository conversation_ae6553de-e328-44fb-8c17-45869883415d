import{u as e,j as s,R as r,a as i,U as a,K as t,L as o,D as n,p as l,M as m,z as p}from"../entries/index-CEzJO5Xy.js";import{d as c,r as d}from"./router-BtYqujaw.js";import{u,a as j,s as h}from"./zod-4O8Zwsja.js";import{C as f,s as x}from"./ContractList-CG6Ru0Kt.js";import{L as g}from"./Layout-BQBjg4Lf.js";import{s as N}from"./create-supplier-D6E0ETct.js";import{b as y,v as R}from"./SupplierService-DSnTbAgG.js";import{E as C}from"./Error-7KgmWHkR.js";import{S as E}from"./SimpleBackdrop-Bf3qjF13.js";import{A as D}from"./Avatar-BtfxKR-8.js";import{P as L}from"./Paper-CcwAvfvc.js";import{I as A}from"./Info-C_WcR51V.js";import{F as I,I as S}from"./InputLabel-BbcIE26O.js";import{I as b}from"./Input-BQdee9z7.js";import{F as v}from"./FormHelperText-DFSsjBsL.js";import{F as _,S as O}from"./Switch-BWPUOSX1.js";import{B as w}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./IconButton-CnBvmeAK.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-CtA82Ni6.js";import"./Backdrop-Bzn12VyM.js";import"./Delete-CnqjtpsJ.js";import"./DressService-J0XavNJj.js";import"./LocationService-BtQFgoWL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./SwitchBase-BIeqtL5F.js";const W=()=>{const W=c(),{user:T}=e(),[P,k]=d.useState(!1),[B,M]=d.useState(!1),[U,F]=d.useState(!1),[q,G]=d.useState(""),[H,z]=d.useState(!1),[Y,V]=d.useState([]),{control:Q,register:K,handleSubmit:Z,setValue:J,setError:X,clearErrors:$,setFocus:ee,formState:{errors:se,isSubmitting:re},trigger:ie}=u({resolver:h(x),mode:"onBlur",defaultValues:{fullName:"",email:"",phone:"",location:"",bio:"",payLater:!1,licenseRequired:!1,minimumRentalDays:"",priceChangeRate:"",supplierDressLimit:"",notifyAdminOnNewDress:!1}}),{payLater:ae,licenseRequired:te,notifyAdminOnNewDress:oe}=j({control:Q});return s.jsxs(g,{onLoad:e=>{e&&e.verified&&k(!0)},strict:!0,admin:!0,children:[s.jsx("div",{className:"create-supplier",children:s.jsxs(L,{className:"supplier-form",elevation:10,style:P?{}:{display:"none"},children:[s.jsx("h1",{className:"supplier-form-title",children:N.CREATE_SUPPLIER_HEADING}),s.jsxs("form",{onSubmit:Z((async e=>{try{let s=await R({fullName:e.fullName});if(200!==s)return X("fullName",{message:N.INVALID_SUPPLIER_NAME}),void ee("fullName");if(s=await n({email:e.email}),200!==s)return X("email",{message:i.EMAIL_ALREADY_REGISTERED}),void ee("email");if(!q)return z(!0),void F(!1);const a={email:e.email,fullName:e.fullName,phone:e.phone,location:e.location,bio:e.bio,language:l(),type:r.Supplier,avatar:q,payLater:e.payLater,contracts:Y,minimumRentalDays:e.minimumRentalDays?Number(e.minimumRentalDays):void 0,priceChangeRate:e.priceChangeRate?Number(e.priceChangeRate):void 0,supplierDressLimit:e.supplierDressLimit?Number(e.supplierDressLimit):void 0,notifyAdminOnNewDress:e.notifyAdminOnNewDress};s=await m(a),200===s?W("/suppliers"):F(!0)}catch(s){p(s)}}),(()=>{const e=Object.keys(se)[0];e&&ee(e)})),children:[s.jsx(D,{type:r.Supplier,mode:"create",record:null,size:"large",readonly:!1,onBeforeUpload:()=>{M(!0)},onChange:e=>{M(!1),G(e),null!==e&&z(!1)},color:"disabled",className:"avatar-ctn"}),s.jsxs("div",{className:"info",children:[s.jsx(A,{}),s.jsx("span",{children:N.RECOMMENDED_IMAGE_SIZE})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{className:"required",children:i.FULL_NAME}),s.jsx(b,{...K("fullName"),type:"text",error:!!se.fullName,required:!0,autoComplete:"off"}),s.jsx(v,{error:!!se.fullName,children:se.fullName?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{className:"required",children:i.EMAIL}),s.jsx(b,{...K("email"),type:"text",autoComplete:"off",required:!0,error:!!se.email}),s.jsx(v,{error:!!se.email,children:se.email?.message||""})]}),s.jsx(I,{fullWidth:!0,margin:"dense",children:s.jsx(_,{control:s.jsx(O,{...K("payLater"),checked:ae,onChange:e=>{J("payLater",e.target.checked)},color:"primary"}),label:i.PAY_LATER})}),s.jsx(I,{fullWidth:!0,margin:"dense",children:s.jsx(_,{control:s.jsx(O,{...K("licenseRequired"),checked:te,onChange:e=>{J("licenseRequired",e.target.checked)},color:"primary"}),label:i.LICENSE_REQUIRED})}),s.jsxs("div",{className:"info",children:[s.jsx(A,{}),s.jsx("span",{children:i.OPTIONAL})]}),s.jsx(I,{fullWidth:!0,margin:"dense",children:s.jsx(_,{control:s.jsx(O,{...K("notifyAdminOnNewDress"),checked:oe,disabled:T?.type===a.Supplier,onChange:e=>{J("notifyAdminOnNewDress",e.target.checked)},color:"primary"}),label:i.NOTIFY_ADMIN_ON_NEW_CAR})}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.SUPPLIER_CAR_LIMIT}),s.jsx(b,{...K("supplierDressLimit"),type:"text",autoComplete:"off",error:!!se.supplierDressLimit,onChange:()=>$("supplierDressLimit")}),s.jsx(v,{error:!!se.supplierDressLimit,children:se.supplierDressLimit?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.MIN_RENTAL_DAYS}),s.jsx(b,{...K("minimumRentalDays"),type:"text",autoComplete:"off",error:!!se.minimumRentalDays,onChange:()=>$("minimumRentalDays")}),s.jsx(v,{error:!!se.minimumRentalDays,children:se.minimumRentalDays?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.PRICE_CHANGE_RATE}),s.jsx(b,{...K("priceChangeRate"),type:"text",autoComplete:"off",error:!!se.priceChangeRate,onChange:()=>$("priceChangeRate")}),s.jsx(v,{error:!!se.priceChangeRate,children:se.priceChangeRate?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.PHONE}),s.jsx(b,{...K("phone",{onBlur:()=>ie("phone")}),type:"text",autoComplete:"off",error:!!se.phone,onChange:()=>$("phone")}),s.jsx(v,{error:!!se.phone,children:se.phone?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.LOCATION}),s.jsx(b,{...K("location"),type:"text",autoComplete:"off"})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.BIO}),s.jsx(b,{...K("bio"),type:"text",autoComplete:"off"})]}),s.jsx(I,{fullWidth:!0,margin:"dense",children:s.jsx(f,{onUpload:(e,s)=>{const r=t(Y),i=r.find((s=>s.language===e));i?i.file=s:r.push({language:e,file:s}),V(r)},onDelete:e=>{const s=t(Y);s.find((s=>s.language===e)).file=null,V(s)}})}),s.jsxs("div",{className:"buttons",children:[s.jsx(w,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:re,children:i.CREATE}),s.jsx(w,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{try{q&&await o(q);for(const e of Y)e.file&&await y(e.file);W("/suppliers")}catch{W("/suppliers")}},children:i.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[U&&s.jsx(C,{message:i.GENERIC_ERROR}),H&&s.jsx(C,{message:i.IMAGE_REQUIRED})]})]})]})}),B&&s.jsx(E,{text:i.PLEASE_WAIT})]})};export{W as default};
