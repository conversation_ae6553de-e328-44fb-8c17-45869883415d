import{b as e,s,c as i,j as r}from"../entries/index-xsXxT3-W.js";import{L as a}from"./Layout-DaeN7D4t.js";import{P as l}from"./Paper-C-atefOs.js";import"./vendor-dblfw9z9.js";import"./router-BtYqujaw.js";import"./Button-BeKLLPpp.js";const n=new e({fr:{TITLE:"Plans tarifaires",FREE_PLAN:"Plan gratuit",FREE_PLAN_PRICE:"Gratuit",FEATURE_1:"Créer un nombre illimité de voitures",FEATURE_2:"1 voiture dans les résultats de recherche",BASIC_PLAN:"Plan de base",BASIC_PLAN_PRICE:"$10/mois",FEATURE_3:"5 voitures dans les résultats de recherche",FEATURE_4:"Support prioritaire",PREMIUM_PLAN:"Plan premium",CONTACT_US:"Contactez-nous",FEATURE_5:"Voitures illimitées dans les résultats de recherche"},en:{TITLE:"Pricing Plans",FREE_PLAN:"Free Plan",FREE_PLAN_PRICE:"Free",FEATURE_1:"Create unlimited cars",FEATURE_2:"1 car in search results",BASIC_PLAN:"Basic Plan",BASIC_PLAN_PRICE:"$10/month",FEATURE_3:"5 cars in search results",FEATURE_4:"Priority support",PREMIUM_PLAN:"Premium Plan",CONTACT_US:"Contact us",FEATURE_5:"Unlimited cars in search results"},es:{TITLE:"Planes de precios",FREE_PLAN:"Plan gratuito",FREE_PLAN_PRICE:"Gratis",FEATURE_1:"Crear coches ilimitados",FEATURE_2:"1 coche en los resultados de búsqueda",BASIC_PLAN:"Plan básico",BASIC_PLAN_PRICE:"$10/mes",FEATURE_3:"5 coches en los resultados de búsqueda",FEATURE_4:"Soporte prioritario",PREMIUM_PLAN:"Plan premium",CONTACT_US:"Contáctenos",FEATURE_5:"Coches ilimitados en los resultados de búsqueda"}});s(n);const c=()=>{const e=i.c(10),s=t;let c,o,E,_,p,m,A,d,h,P;return e[0]===Symbol.for("react.memo_cache_sentinel")?(c=r.jsx("h1",{className:"pricing-title",children:n.TITLE}),e[0]=c):c=e[0],e[1]===Symbol.for("react.memo_cache_sentinel")?(o=r.jsx("h2",{className:"pricing-plan-title",children:n.FREE_PLAN}),E=r.jsx("p",{className:"pricing-plan-price",children:n.FREE_PLAN_PRICE}),e[1]=o,e[2]=E):(o=e[1],E=e[2]),e[3]===Symbol.for("react.memo_cache_sentinel")?(_=r.jsxs(l,{className:"pricing-plan pricing-plan-wrapper",elevation:10,children:[o,E,r.jsxs("ul",{className:"pricing-plan-features",children:[r.jsx("li",{children:n.FEATURE_1}),r.jsx("li",{children:n.FEATURE_2})]})]}),e[3]=_):_=e[3],e[4]===Symbol.for("react.memo_cache_sentinel")?(p=r.jsx("h2",{className:"pricing-plan-title",children:n.BASIC_PLAN}),m=r.jsx("p",{className:"pricing-plan-price",children:n.BASIC_PLAN_PRICE}),e[4]=p,e[5]=m):(p=e[4],m=e[5]),e[6]===Symbol.for("react.memo_cache_sentinel")?(A=r.jsxs(l,{className:"pricing-plan pricing-plan-wrapper",elevation:10,children:[p,m,r.jsxs("ul",{className:"pricing-plan-features",children:[r.jsx("li",{children:n.FEATURE_1}),r.jsx("li",{children:n.FEATURE_3}),r.jsx("li",{children:n.FEATURE_4})]})]}),e[6]=A):A=e[6],e[7]===Symbol.for("react.memo_cache_sentinel")?(d=r.jsx("h2",{className:"pricing-plan-title",children:n.PREMIUM_PLAN}),h=r.jsx("p",{className:"pricing-plan-price",children:n.CONTACT_US}),e[7]=d,e[8]=h):(d=e[7],h=e[8]),e[9]===Symbol.for("react.memo_cache_sentinel")?(P=r.jsx(a,{onLoad:s,strict:!0,children:r.jsxs("div",{className:"pricing",children:[c,r.jsxs("div",{className:"pricing-plans",children:[_,A,r.jsxs(l,{className:"pricing-plan pricing-plan-wrapper",elevation:10,children:[d,h,r.jsxs("ul",{className:"pricing-plan-features",children:[r.jsx("li",{children:n.FEATURE_1}),r.jsx("li",{children:n.FEATURE_5}),r.jsx("li",{children:n.FEATURE_4})]})]})]})]})}),e[9]=P):P=e[9],P};function t(){}export{c as default};
