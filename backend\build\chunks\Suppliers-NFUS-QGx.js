import{e as s,F as e,j as t,G as r,a as o,z as a,H as i,c as l}from"../entries/index-CEzJO5Xy.js";import{d as n,r as c}from"./router-BtYqujaw.js";import{L as m}from"./Layout-BQBjg4Lf.js";import{s as d}from"./suppliers-DKbqsTuE.js";import{S as p}from"./Search-CuqoyoO6.js";import{s as u}from"./supplier-list-Br6cMD8V.js";import{g as j,d as S}from"./SupplierService-DSnTbAgG.js";import{P as h}from"./Pager-C0zDCFUN.js";import{C as x,a as I}from"./ArrowForwardIos-BMce9t8T.js";import{T as E}from"./Backdrop-Bzn12VyM.js";import{T as f}from"./Tooltip-BkJF6Mu0.js";import{I as N}from"./IconButton-CnBvmeAK.js";import{D as _}from"./Delete-CnqjtpsJ.js";import{E as T}from"./Edit-DIF9Bumd.js";import{V as D}from"./Visibility-BpW589Gm.js";import{D as P,a as g,b as C}from"./Grow-CjOKj0i1.js";import{D as L}from"./DialogTitle-BZXwroUN.js";import{B as b}from"./Button-DGZYUY3P.js";import{I as y}from"./InfoBox-Csm94Gbd.js";import"./vendor-dblfw9z9.js";import"./zod-4O8Zwsja.js";import"./TextField-BAse--ht.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-CtA82Ni6.js";import"./OutlinedInput-g8mR4MM3.js";import"./InputLabel-BbcIE26O.js";import"./isHostComponent-DR4iSCFs.js";import"./ownerWindow-ChLfdzZL.js";import"./Input-BQdee9z7.js";import"./FormHelperText-DFSsjBsL.js";import"./Menu-ZU0DMgjT.js";import"./Paper-CcwAvfvc.js";import"./mergeSlotProps-Cay5TZBz.js";import"./Clear-BpXDeTL8.js";import"./Search-BNrZEqND.js";import"./Info-C_WcR51V.js";const w=({user:l,keyword:m,onDelete:d,onLoad:p})=>{const y=n(),[w,A]=c.useState(m),[v,O]=c.useState(!0),[R,M]=c.useState(!1),[F,G]=c.useState(!1),[k,U]=c.useState([]),[$,H]=c.useState(0),[B,W]=c.useState(0),[z,V]=c.useState(1),[Y,Z]=c.useState(!1),[q,K]=c.useState(""),[J,Q]=c.useState(-1),X=async(e,t)=>{try{M(!0);const r=await j(t||"",e,s.PAGE_SIZE),o=r&&r.length>0?r[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!o)return void a();const l=Array.isArray(o.pageInfo)&&o.pageInfo.length>0?o.pageInfo[0].totalRecords:0;let n=[];n=s.PAGINATION_MODE===i.PAGINATION_MODE.INFINITE_SCROLL||s.isMobile?1===e?o.resultData:[...k,...o.resultData]:o.resultData,U(n),H((e-1)*s.PAGE_SIZE+n.length),W(l),G(o.resultData.length>0),((s.PAGINATION_MODE===i.PAGINATION_MODE.INFINITE_SCROLL||s.isMobile)&&1===e||s.PAGINATION_MODE===i.PAGINATION_MODE.CLASSIC&&!s.isMobile)&&window.scrollTo(0,0),p&&p({rows:o.resultData,rowCount:l})}catch(r){a(r)}finally{M(!1),O(!1)}};c.useEffect((()=>{m!==w&&X(1,m),A(m||"")}),[m,w]),c.useEffect((()=>{X(z,w)}),[z]),c.useEffect((()=>{if(s.isMobile){const e=document.querySelector("body");e&&(e.onscroll=()=>{F&&!R&&window.scrollY>0&&window.scrollY+window.innerHeight+s.INFINITE_SCROLL_OFFSET>=document.body.scrollHeight&&(M(!0),V(z+1))})}}),[F,R,z,w]);const ss=s=>{const e=s.currentTarget.getAttribute("data-id"),t=Number(s.currentTarget.getAttribute("data-index"));Z(!0),K(e),Q(t)},es=e(l);return t.jsxs(t.Fragment,{children:[t.jsxs("section",{className:"supplier-list",children:[0===k.length?!v&&!R&&t.jsx(x,{variant:"outlined",className:"empty-list",children:t.jsx(I,{children:t.jsx(E,{color:"textSecondary",children:u.EMPTY_LIST})})}):k.map(((e,a)=>{const i=es||l&&l._id===e._id,n=es;return t.jsxs("article",{children:[t.jsxs("div",{className:"supplier-item",children:[t.jsx("div",{className:"supplier-item-avatar",children:t.jsx("img",{src:r(s.CDN_USERS,e.avatar),alt:e.fullName})}),t.jsx("span",{className:"supplier-item-title",children:e.fullName}),null!=e.dressCount?t.jsx("span",{className:"supplier-item-subtitle",children:`${e.dressCount} ${e.dressCount>1?o.DRESSES:o.DRESS}`}):null]}),t.jsxs("div",{className:"supplier-actions",children:[n&&t.jsx(f,{title:o.DELETE,children:t.jsx(N,{"data-id":e._id,"data-index":a,onClick:ss,children:t.jsx(_,{})})}),i&&t.jsx(f,{title:o.UPDATE,children:t.jsx(N,{onClick:()=>y(`/update-supplier?c=${e._id}`),children:t.jsx(T,{})})}),t.jsx(f,{title:u.VIEW_SUPPLIER,children:t.jsx(N,{onClick:()=>y(`/supplier?c=${e._id}`),children:t.jsx(D,{})})})]})]},e._id)})),t.jsxs(P,{disableEscapeKeyDown:!0,maxWidth:"xs",open:Y,children:[t.jsx(L,{className:"dialog-header",children:o.CONFIRM_TITLE}),t.jsx(g,{children:u.DELETE_SUPPLIER}),t.jsxs(C,{className:"dialog-actions",children:[t.jsx(b,{onClick:()=>{Z(!1),K(""),Q(-1)},variant:"contained",className:"btn-secondary",children:o.CANCEL}),t.jsx(b,{onClick:async()=>{try{if(""!==q&&J>-1)if(M(!1),Z(!1),200===await S(q)){const s=$-1;k.splice(J,1),U(k),H(s),W(B-1),K(""),Q(-1),M(!1),d&&d(s)}else a(),K(""),Q(-1),M(!1);else a(),Z(!1),K(""),Q(-1),M(!1)}catch(s){a(s)}},variant:"contained",color:"error",children:o.DELETE})]})]})]}),!s.isMobile&&t.jsx(h,{page:z,pageSize:s.PAGE_SIZE,rowCount:$,totalRecords:B,onNext:()=>V(z+1),onPrevious:()=>V(z-1)})]})},A=()=>{const s=l.c(14),r=n(),[o,a]=c.useState(),[i,u]=c.useState(""),[j,S]=c.useState(-1);let h;s[0]===Symbol.for("react.memo_cache_sentinel")?(h=s=>{u(s)},s[0]=h):h=s[0];const x=h;let I;s[1]===Symbol.for("react.memo_cache_sentinel")?(I=s=>{s&&S(s.rowCount)},s[1]=I):I=s[1];const E=I;let f;s[2]===Symbol.for("react.memo_cache_sentinel")?(f=s=>{S(s)},s[2]=f):f=s[2];const N=f;let _;s[3]===Symbol.for("react.memo_cache_sentinel")?(_=s=>{a(s)},s[3]=_):_=s[3];const T=_;let D;s[4]!==o?(D=e(o),s[4]=o,s[5]=D):D=s[5];const P=D;let g,C;return s[6]!==P||s[7]!==i||s[8]!==r||s[9]!==j||s[10]!==o?(g=o&&t.jsxs("div",{className:"suppliers",children:[t.jsx("div",{className:"col-1",children:t.jsxs("div",{className:"col-1-container",children:[t.jsx(p,{className:"search",onSubmit:x}),j>-1&&P&&t.jsx(b,{type:"submit",variant:"contained",className:"btn-primary new-supplier",size:"small",onClick:()=>r("/create-supplier"),children:d.NEW_SUPPLIER}),j>0&&t.jsx(y,{value:`${j} ${j>1?d.SUPPLIERS:d.SUPPLIER}`,className:"supplier-count"})]})}),t.jsx("div",{className:"col-2",children:t.jsx(w,{user:o,keyword:i,onLoad:E,onDelete:N})})]}),s[6]=P,s[7]=i,s[8]=r,s[9]=j,s[10]=o,s[11]=g):g=s[11],s[12]!==g?(C=t.jsx(m,{onLoad:T,strict:!0,children:g}),s[12]=g,s[13]=C):C=s[13],C};export{A as default};
