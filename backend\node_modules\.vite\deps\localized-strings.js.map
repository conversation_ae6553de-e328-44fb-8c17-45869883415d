{"version": 3, "sources": ["../../localized-strings/lib/LocalizedStrings.es.js"], "sourcesContent": ["var f = Object.defineProperty;\nvar c = (n, e, t) => e in n ? f(n, e, { enumerable: !0, configurable: !0, writable: !0, value: t }) : n[e] = t;\nvar l = (n, e, t) => c(n, typeof e != \"symbol\" ? e + \"\" : e, t);\nfunction h() {\n  const n = \"en-US\";\n  if (typeof navigator > \"u\")\n    return n;\n  const e = navigator;\n  if (e) {\n    if (e.language)\n      return e.language;\n    if (e.languages && e.languages[0])\n      return e.languages[0];\n    if (\"userLanguage\" in e)\n      return e.userLanguage;\n    if (\"browserLanguage\" in e)\n      return e.browserLanguage;\n  }\n  return n;\n}\nfunction _(n, e) {\n  if (e[n]) return n;\n  const t = n.indexOf(\"-\"), a = t >= 0 ? n.substring(0, t) : n;\n  return e[a] ? a : Object.keys(e)[0];\n}\nfunction d(n) {\n  const e = [\n    \"_interfaceLanguage\",\n    \"_language\",\n    \"_defaultLanguage\",\n    \"_defaultLanguageFirstLevelKeys\",\n    \"_props\"\n  ];\n  n.forEach((t) => {\n    if (e.indexOf(t) !== -1)\n      throw new Error(`${t} cannot be used as a key. It is a reserved word.`);\n  });\n}\nfunction L(n) {\n  let e = \"\";\n  const t = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n  for (let a = 0; a < n; a += 1)\n    e += t.charAt(Math.floor(Math.random() * t.length));\n  return e;\n}\nconst r = /(\\{[\\d|\\w]+\\})/, o = /(\\$ref\\{[\\w|.]+\\})/;\nclass b {\n  /**\n   * Constructor used to provide the strings objects in various language and the optional callback to get\n   * the interface language\n   * @param props - the strings object\n   * @param options - configuration options\n   */\n  constructor(e, t) {\n    l(this, \"_opts\");\n    l(this, \"_interfaceLanguage\");\n    l(this, \"_language\");\n    l(this, \"_defaultLanguage\");\n    l(this, \"_defaultLanguageFirstLevelKeys\");\n    l(this, \"_props\");\n    l(this, \"_availableLanguages\");\n    typeof t == \"function\" && (t = { customLanguageInterface: t }), this._opts = {\n      customLanguageInterface: h,\n      pseudo: !1,\n      pseudoMultipleLanguages: !1,\n      logsEnabled: !0,\n      ...t\n    }, this._interfaceLanguage = this._opts.customLanguageInterface(), this._language = this._interfaceLanguage, this.setContent(e);\n  }\n  /**\n   * Set the strings objects based on the parameter passed in the constructor\n   */\n  setContent(e) {\n    const [t] = Object.keys(e);\n    this._defaultLanguage = t, this._defaultLanguageFirstLevelKeys = [], this._props = e, d(Object.keys(e[this._defaultLanguage])), Object.keys(this._props[this._defaultLanguage]).forEach((a) => {\n      typeof this._props[this._defaultLanguage][a] == \"string\" && this._defaultLanguageFirstLevelKeys.push(a);\n    }), this.setLanguage(this._interfaceLanguage), this._opts.pseudo && this._pseudoAllValues(this._props);\n  }\n  /**\n   * Replace all strings to pseudo value\n   */\n  _pseudoAllValues(e) {\n    Object.keys(e).forEach((t) => {\n      if (typeof e[t] == \"object\")\n        this._pseudoAllValues(e[t]);\n      else if (typeof e[t] == \"string\") {\n        if (e[t].indexOf(\"[\") === 0 && e[t].lastIndexOf(\"]\") === e[t].length - 1)\n          return;\n        const a = e[t].split(\" \");\n        for (let s = 0; s < a.length; s += 1) {\n          if (a[s].match(r) || a[s].match(o))\n            continue;\n          let i = a[s].length;\n          this._opts.pseudoMultipleLanguages && (i = Math.floor(i * 1.4)), a[s] = L(i);\n        }\n        e[t] = `[${a.join(\" \")}]`;\n      }\n    });\n  }\n  /**\n   * Can be used from outside the class to force a particular language\n   * independently from the interface one\n   */\n  setLanguage(e) {\n    const t = _(e, this._props), a = Object.keys(this._props)[0];\n    if (this._language = t, this._props[t]) {\n      for (const i of this._defaultLanguageFirstLevelKeys)\n        delete this[i];\n      let s = { ...this._props[this._language] };\n      Object.keys(s).forEach((i) => {\n        this[i] = s[i];\n      }), a !== this._language && (s = this._props[a], this._fallbackValues(s, this));\n    }\n  }\n  /**\n   * Load fallback values for missing translations\n   */\n  _fallbackValues(e, t) {\n    Object.keys(e).forEach((a) => {\n      Object.prototype.hasOwnProperty.call(e, a) && !t[a] && t[a] !== \"\" ? (t[a] = e[a], this._opts.logsEnabled && console.log(\n        `🚧 👷 key '${a}' not found in localizedStrings for language ${this._language} 🚧`\n      )) : typeof t[a] != \"string\" && this._fallbackValues(e[a], t[a]);\n    });\n  }\n  getLanguage() {\n    return this._language;\n  }\n  getInterfaceLanguage() {\n    return this._interfaceLanguage;\n  }\n  getAvailableLanguages() {\n    return this._availableLanguages || (this._availableLanguages = Object.keys(this._props)), this._availableLanguages;\n  }\n  formatString(e, ...t) {\n    let a = e || \"\";\n    return typeof a == \"string\" && (a = this.getString(e, null, !0) || a), a.split(o).filter(Boolean).map((i) => {\n      if (i.match(o)) {\n        const g = i.slice(5, -1), u = this.getString(g);\n        return u || (this._opts.logsEnabled && console.log(\n          `No Localization ref found for '${i}' in string '${e}'`\n        ), `$ref(id:${g})`);\n      }\n      return i;\n    }).join(\"\").split(r).filter(Boolean).map((i) => {\n      if (i.match(r)) {\n        const g = i.slice(1, -1);\n        let u = t[g];\n        return u === void 0 && t[0] && (u = t[0][g]), u;\n      }\n      return i;\n    }).join(\"\");\n  }\n  getString(e, t, a = !1) {\n    try {\n      let s = this._props[t || this._language];\n      const i = e.split(\".\");\n      for (const g of i) {\n        if (s[g] === void 0)\n          throw new Error(g);\n        s = s[g];\n      }\n      return s;\n    } catch (s) {\n      !a && this._opts.logsEnabled && console.log(\n        `No localization found for key '${e}' and language '${t}', failed on ${s.message}`\n      );\n    }\n    return null;\n  }\n  getContent() {\n    return this._props;\n  }\n}\nexport {\n  b as default\n};\n"], "mappings": ";;;AAAA,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,EAAE,GAAG,GAAG,EAAE,YAAY,MAAI,cAAc,MAAI,UAAU,MAAI,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAC7G,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,EAAE,GAAG,OAAO,KAAK,WAAW,IAAI,KAAK,GAAG,CAAC;AAC9D,SAAS,IAAI;AACX,QAAM,IAAI;AACV,MAAI,OAAO,YAAY;AACrB,WAAO;AACT,QAAM,IAAI;AACV,MAAI,GAAG;AACL,QAAI,EAAE;AACJ,aAAO,EAAE;AACX,QAAI,EAAE,aAAa,EAAE,UAAU,CAAC;AAC9B,aAAO,EAAE,UAAU,CAAC;AACtB,QAAI,kBAAkB;AACpB,aAAO,EAAE;AACX,QAAI,qBAAqB;AACvB,aAAO,EAAE;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,EAAE,GAAG,GAAG;AACf,MAAI,EAAE,CAAC,EAAG,QAAO;AACjB,QAAM,IAAI,EAAE,QAAQ,GAAG,GAAG,IAAI,KAAK,IAAI,EAAE,UAAU,GAAG,CAAC,IAAI;AAC3D,SAAO,EAAE,CAAC,IAAI,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;AACpC;AACA,SAAS,EAAE,GAAG;AACZ,QAAM,IAAI;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,IAAE,QAAQ,CAAC,MAAM;AACf,QAAI,EAAE,QAAQ,CAAC,MAAM;AACnB,YAAM,IAAI,MAAM,GAAG,CAAC,kDAAkD;AAAA,EAC1E,CAAC;AACH;AACA,SAAS,EAAE,GAAG;AACZ,MAAI,IAAI;AACR,QAAM,IAAI;AACV,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,SAAK,EAAE,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,MAAM,CAAC;AACpD,SAAO;AACT;AACA,IAAM,IAAI;AAAV,IAA4B,IAAI;AAChC,IAAM,IAAN,MAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAON,YAAY,GAAG,GAAG;AAChB,MAAE,MAAM,OAAO;AACf,MAAE,MAAM,oBAAoB;AAC5B,MAAE,MAAM,WAAW;AACnB,MAAE,MAAM,kBAAkB;AAC1B,MAAE,MAAM,gCAAgC;AACxC,MAAE,MAAM,QAAQ;AAChB,MAAE,MAAM,qBAAqB;AAC7B,WAAO,KAAK,eAAe,IAAI,EAAE,yBAAyB,EAAE,IAAI,KAAK,QAAQ;AAAA,MAC3E,yBAAyB;AAAA,MACzB,QAAQ;AAAA,MACR,yBAAyB;AAAA,MACzB,aAAa;AAAA,MACb,GAAG;AAAA,IACL,GAAG,KAAK,qBAAqB,KAAK,MAAM,wBAAwB,GAAG,KAAK,YAAY,KAAK,oBAAoB,KAAK,WAAW,CAAC;AAAA,EAChI;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,GAAG;AACZ,UAAM,CAAC,CAAC,IAAI,OAAO,KAAK,CAAC;AACzB,SAAK,mBAAmB,GAAG,KAAK,iCAAiC,CAAC,GAAG,KAAK,SAAS,GAAG,EAAE,OAAO,KAAK,EAAE,KAAK,gBAAgB,CAAC,CAAC,GAAG,OAAO,KAAK,KAAK,OAAO,KAAK,gBAAgB,CAAC,EAAE,QAAQ,CAAC,MAAM;AAC7L,aAAO,KAAK,OAAO,KAAK,gBAAgB,EAAE,CAAC,KAAK,YAAY,KAAK,+BAA+B,KAAK,CAAC;AAAA,IACxG,CAAC,GAAG,KAAK,YAAY,KAAK,kBAAkB,GAAG,KAAK,MAAM,UAAU,KAAK,iBAAiB,KAAK,MAAM;AAAA,EACvG;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,GAAG;AAClB,WAAO,KAAK,CAAC,EAAE,QAAQ,CAAC,MAAM;AAC5B,UAAI,OAAO,EAAE,CAAC,KAAK;AACjB,aAAK,iBAAiB,EAAE,CAAC,CAAC;AAAA,eACnB,OAAO,EAAE,CAAC,KAAK,UAAU;AAChC,YAAI,EAAE,CAAC,EAAE,QAAQ,GAAG,MAAM,KAAK,EAAE,CAAC,EAAE,YAAY,GAAG,MAAM,EAAE,CAAC,EAAE,SAAS;AACrE;AACF,cAAM,IAAI,EAAE,CAAC,EAAE,MAAM,GAAG;AACxB,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AACpC,cAAI,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC;AAC/B;AACF,cAAI,IAAI,EAAE,CAAC,EAAE;AACb,eAAK,MAAM,4BAA4B,IAAI,KAAK,MAAM,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC7E;AACA,UAAE,CAAC,IAAI,IAAI,EAAE,KAAK,GAAG,CAAC;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,GAAG;AACb,UAAM,IAAI,EAAE,GAAG,KAAK,MAAM,GAAG,IAAI,OAAO,KAAK,KAAK,MAAM,EAAE,CAAC;AAC3D,QAAI,KAAK,YAAY,GAAG,KAAK,OAAO,CAAC,GAAG;AACtC,iBAAW,KAAK,KAAK;AACnB,eAAO,KAAK,CAAC;AACf,UAAI,IAAI,EAAE,GAAG,KAAK,OAAO,KAAK,SAAS,EAAE;AACzC,aAAO,KAAK,CAAC,EAAE,QAAQ,CAAC,MAAM;AAC5B,aAAK,CAAC,IAAI,EAAE,CAAC;AAAA,MACf,CAAC,GAAG,MAAM,KAAK,cAAc,IAAI,KAAK,OAAO,CAAC,GAAG,KAAK,gBAAgB,GAAG,IAAI;AAAA,IAC/E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,GAAG,GAAG;AACpB,WAAO,KAAK,CAAC,EAAE,QAAQ,CAAC,MAAM;AAC5B,aAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,MAAM,eAAe,QAAQ;AAAA,QACnH,cAAc,CAAC,gDAAgD,KAAK,SAAS;AAAA,MAC/E,KAAK,OAAO,EAAE,CAAC,KAAK,YAAY,KAAK,gBAAgB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACjE,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,wBAAwB,KAAK,sBAAsB,OAAO,KAAK,KAAK,MAAM,IAAI,KAAK;AAAA,EACjG;AAAA,EACA,aAAa,MAAM,GAAG;AACpB,QAAI,IAAI,KAAK;AACb,WAAO,OAAO,KAAK,aAAa,IAAI,KAAK,UAAU,GAAG,MAAM,IAAE,KAAK,IAAI,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,IAAI,CAAC,MAAM;AAC3G,UAAI,EAAE,MAAM,CAAC,GAAG;AACd,cAAM,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG,IAAI,KAAK,UAAU,CAAC;AAC9C,eAAO,MAAM,KAAK,MAAM,eAAe,QAAQ;AAAA,UAC7C,kCAAkC,CAAC,gBAAgB,CAAC;AAAA,QACtD,GAAG,WAAW,CAAC;AAAA,MACjB;AACA,aAAO;AAAA,IACT,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,IAAI,CAAC,MAAM;AAC9C,UAAI,EAAE,MAAM,CAAC,GAAG;AACd,cAAM,IAAI,EAAE,MAAM,GAAG,EAAE;AACvB,YAAI,IAAI,EAAE,CAAC;AACX,eAAO,MAAM,UAAU,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,MAChD;AACA,aAAO;AAAA,IACT,CAAC,EAAE,KAAK,EAAE;AAAA,EACZ;AAAA,EACA,UAAU,GAAG,GAAG,IAAI,OAAI;AACtB,QAAI;AACF,UAAI,IAAI,KAAK,OAAO,KAAK,KAAK,SAAS;AACvC,YAAM,IAAI,EAAE,MAAM,GAAG;AACrB,iBAAW,KAAK,GAAG;AACjB,YAAI,EAAE,CAAC,MAAM;AACX,gBAAM,IAAI,MAAM,CAAC;AACnB,YAAI,EAAE,CAAC;AAAA,MACT;AACA,aAAO;AAAA,IACT,SAAS,GAAG;AACV,OAAC,KAAK,KAAK,MAAM,eAAe,QAAQ;AAAA,QACtC,kCAAkC,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,OAAO;AAAA,MAClF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AACF;", "names": []}