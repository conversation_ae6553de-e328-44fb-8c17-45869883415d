import{r as t}from"./router-BtYqujaw.js";import{u as e}from"./Paper-CcwAvfvc.js";import{j as n}from"../entries/index-CEzJO5Xy.js";import{a as r,g as i,b as s,r as o}from"./Backdrop-Bzn12VyM.js";import{u as a}from"./Button-DGZYUY3P.js";import{a as l}from"./ownerWindow-ChLfdzZL.js";import{d as f}from"./isHostComponent-DR4iSCFs.js";function p(t,e,n){var r;const i=function(t,e,n){const r=e.getBoundingClientRect(),i=n&&n.getBoundingClientRect(),s=l(e);let o;if(e.fakeTransform)o=e.fakeTransform;else{const t=s.getComputedStyle(e);o=t.getPropertyValue("-webkit-transform")||t.getPropertyValue("transform")}let a=0,f=0;if(o&&"none"!==o&&"string"==typeof o){const t=o.split("(")[1].split(")")[0].split(",");a=parseInt(t[4],10),f=parseInt(t[5],10)}return"left"===t?i?`translateX(${i.right+a-r.left}px)`:`translateX(${s.innerWidth+a-r.left}px)`:"right"===t?i?`translateX(-${r.right-i.left-a}px)`:`translateX(-${r.left+r.width-a}px)`:"up"===t?i?`translateY(${i.bottom+f-r.top}px)`:`translateY(${s.innerHeight+f-r.top}px)`:i?`translateY(-${r.top-i.top+r.height-f}px)`:`translateY(-${r.top+r.height-f}px)`}(t,e,"function"==typeof(r=n)?r():r);i&&(e.style.webkitTransform=i,e.style.transform=i)}const c=t.forwardRef((function(c,m){const u=e(),d={enter:u.transitions.easing.easeOut,exit:u.transitions.easing.sharp},g={enter:u.transitions.duration.enteringScreen,exit:u.transitions.duration.leavingScreen},{addEndListener:x,appear:y=!0,children:E,container:h,direction:w="down",easing:b=d,in:k,onEnter:j,onEntered:T,onEntering:v,onExit:$,onExited:C,onExiting:R,style:L,timeout:S=g,TransitionComponent:B=r,...X}=c,Y=t.useRef(null),H=a(i(E),Y,m),P=t=>e=>{t&&(void 0===e?t(Y.current):t(Y.current,e))},z=P(((t,e)=>{p(w,t,h),o(t),j&&j(t,e)})),I=P(((t,e)=>{const n=s({timeout:S,style:L,easing:b},{mode:"enter"});t.style.webkitTransition=u.transitions.create("-webkit-transform",{...n}),t.style.transition=u.transitions.create("transform",{...n}),t.style.webkitTransform="none",t.style.transform="none",v&&v(t,e)})),V=P(T),W=P(R),O=P((t=>{const e=s({timeout:S,style:L,easing:b},{mode:"exit"});t.style.webkitTransition=u.transitions.create("-webkit-transform",e),t.style.transition=u.transitions.create("transform",e),p(w,t,h),$&&$(t)})),q=P((t=>{t.style.webkitTransition="",t.style.transition="",C&&C(t)})),A=t.useCallback((()=>{Y.current&&p(w,Y.current,h)}),[w,h]);return t.useEffect((()=>{if(k||"down"===w||"right"===w)return;const t=f((()=>{Y.current&&p(w,Y.current,h)})),e=l(Y.current);return e.addEventListener("resize",t),()=>{t.clear(),e.removeEventListener("resize",t)}}),[w,k,h]),t.useEffect((()=>{k||A()}),[k,A]),n.jsx(B,{nodeRef:Y,onEnter:z,onEntered:V,onEntering:I,onExit:O,onExited:q,onExiting:W,addEndListener:t=>{x&&x(Y.current,t)},appear:y,in:k,timeout:S,...X,children:(e,{ownerState:n,...r})=>t.cloneElement(E,{ref:H,style:{visibility:"exited"!==e||k?void 0:"hidden",...L,...E.props.style},...r})})}));export{c as S};
