import{r}from"./router-BtYqujaw.js";import{i as t,k as a,j as o,l as n}from"../entries/index-xsXxT3-W.js";import{u as i}from"./AccountCircle-DdIeIbov.js";import{a as e,g as s,s as l,c,m as g,b as p}from"./Button-BeKLLPpp.js";import{u as h}from"./useSlot-DiTut-u0.js";function m(r){return s("MuiBadge",r)}const d=e("MuiBadge",["root","badge","dot","standard","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft","invisible","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","overlapRectangular","overlapCircular","anchorOriginTopLeftCircular","anchorOriginTopLeftRectangular","anchorOriginTopRightCircular","anchorOriginTopRightRectangular","anchorOriginBottomLeftCircular","anchorOriginBottomLeftRectangular","anchorOriginBottomRightCircular","anchorOriginBottomRightRectangular"]),f=l("span",{name:"MuiBadge",slot:"Root"})({position:"relative",display:"inline-flex",verticalAlign:"middle",flexShrink:0}),v=l("span",{name:"MuiBadge",slot:"Badge",overridesResolver:(r,t)=>{const{ownerState:a}=r;return[t.badge,t[a.variant],t[`anchorOrigin${n(a.anchorOrigin.vertical)}${n(a.anchorOrigin.horizontal)}${n(a.overlap)}`],"default"!==a.color&&t[`color${n(a.color)}`],a.invisible&&t.invisible]}})(g((({theme:r})=>({display:"flex",flexDirection:"row",flexWrap:"wrap",justifyContent:"center",alignContent:"center",alignItems:"center",position:"absolute",boxSizing:"border-box",fontFamily:r.typography.fontFamily,fontWeight:r.typography.fontWeightMedium,fontSize:r.typography.pxToRem(12),minWidth:20,lineHeight:1,padding:"0 6px",height:20,borderRadius:10,zIndex:1,transition:r.transitions.create("transform",{easing:r.transitions.easing.easeInOut,duration:r.transitions.duration.enteringScreen}),variants:[...Object.entries(r.palette).filter(p(["contrastText"])).map((([t])=>({props:{color:t},style:{backgroundColor:(r.vars||r).palette[t].main,color:(r.vars||r).palette[t].contrastText}}))),{props:{variant:"dot"},style:{borderRadius:4,height:8,minWidth:8,padding:0}},{props:({ownerState:r})=>"top"===r.anchorOrigin.vertical&&"right"===r.anchorOrigin.horizontal&&"rectangular"===r.overlap,style:{top:0,right:0,transform:"scale(1) translate(50%, -50%)",transformOrigin:"100% 0%",[`&.${d.invisible}`]:{transform:"scale(0) translate(50%, -50%)"}}},{props:({ownerState:r})=>"bottom"===r.anchorOrigin.vertical&&"right"===r.anchorOrigin.horizontal&&"rectangular"===r.overlap,style:{bottom:0,right:0,transform:"scale(1) translate(50%, 50%)",transformOrigin:"100% 100%",[`&.${d.invisible}`]:{transform:"scale(0) translate(50%, 50%)"}}},{props:({ownerState:r})=>"top"===r.anchorOrigin.vertical&&"left"===r.anchorOrigin.horizontal&&"rectangular"===r.overlap,style:{top:0,left:0,transform:"scale(1) translate(-50%, -50%)",transformOrigin:"0% 0%",[`&.${d.invisible}`]:{transform:"scale(0) translate(-50%, -50%)"}}},{props:({ownerState:r})=>"bottom"===r.anchorOrigin.vertical&&"left"===r.anchorOrigin.horizontal&&"rectangular"===r.overlap,style:{bottom:0,left:0,transform:"scale(1) translate(-50%, 50%)",transformOrigin:"0% 100%",[`&.${d.invisible}`]:{transform:"scale(0) translate(-50%, 50%)"}}},{props:({ownerState:r})=>"top"===r.anchorOrigin.vertical&&"right"===r.anchorOrigin.horizontal&&"circular"===r.overlap,style:{top:"14%",right:"14%",transform:"scale(1) translate(50%, -50%)",transformOrigin:"100% 0%",[`&.${d.invisible}`]:{transform:"scale(0) translate(50%, -50%)"}}},{props:({ownerState:r})=>"bottom"===r.anchorOrigin.vertical&&"right"===r.anchorOrigin.horizontal&&"circular"===r.overlap,style:{bottom:"14%",right:"14%",transform:"scale(1) translate(50%, 50%)",transformOrigin:"100% 100%",[`&.${d.invisible}`]:{transform:"scale(0) translate(50%, 50%)"}}},{props:({ownerState:r})=>"top"===r.anchorOrigin.vertical&&"left"===r.anchorOrigin.horizontal&&"circular"===r.overlap,style:{top:"14%",left:"14%",transform:"scale(1) translate(-50%, -50%)",transformOrigin:"0% 0%",[`&.${d.invisible}`]:{transform:"scale(0) translate(-50%, -50%)"}}},{props:({ownerState:r})=>"bottom"===r.anchorOrigin.vertical&&"left"===r.anchorOrigin.horizontal&&"circular"===r.overlap,style:{bottom:"14%",left:"14%",transform:"scale(1) translate(-50%, 50%)",transformOrigin:"0% 100%",[`&.${d.invisible}`]:{transform:"scale(0) translate(-50%, 50%)"}}},{props:{invisible:!0},style:{transition:r.transitions.create("transform",{easing:r.transitions.easing.easeInOut,duration:r.transitions.duration.leavingScreen})}}]}))));function u(r){return{vertical:r?.vertical??"top",horizontal:r?.horizontal??"right"}}const b=r.forwardRef((function(r,e){const s=t({props:r,name:"MuiBadge"}),{anchorOrigin:l,className:g,classes:p,component:d,components:b={},componentsProps:O={},children:y,overlap:x="rectangular",color:w="default",invisible:$=!1,max:S=99,badgeContent:C,slots:R,slotProps:z,showZero:B=!1,variant:T="standard",...j}=s,{badgeContent:P,invisible:L,max:M,displayValue:W}=function(r){const{badgeContent:t,invisible:a=!1,max:o=99,showZero:n=!1}=r,e=i({badgeContent:t,max:o});let s=a;!1!==a||0!==t||n||(s=!0);const{badgeContent:l,max:c=o}=s?e:r;return{badgeContent:l,invisible:s,max:c,displayValue:l&&Number(l)>c?`${c}+`:l}}({max:S,invisible:$,badgeContent:C,showZero:B}),I=i({anchorOrigin:u(l),color:w,overlap:x,variant:T,badgeContent:C}),F=L||null==P&&"dot"!==T,{color:N=w,overlap:Z=x,anchorOrigin:k,variant:V=T}=F?I:s,A=u(k),D="dot"!==V?W:void 0,E={...s,badgeContent:P,invisible:F,max:M,displayValue:D,showZero:B,anchorOrigin:A,color:N,overlap:Z,variant:V},H=(r=>{const{color:t,anchorOrigin:a,invisible:o,overlap:i,variant:e,classes:s={}}=r,l={root:["root"],badge:["badge",e,o&&"invisible",`anchorOrigin${n(a.vertical)}${n(a.horizontal)}`,`anchorOrigin${n(a.vertical)}${n(a.horizontal)}${n(i)}`,`overlap${n(i)}`,"default"!==t&&`color${n(t)}`]};return c(l,m,s)})(E),J={slots:{root:R?.root??b.Root,badge:R?.badge??b.Badge},slotProps:{root:z?.root??O.root,badge:z?.badge??O.badge}},[q,G]=h("root",{elementType:f,externalForwardedProps:{...J,...j},ownerState:E,className:a(H.root,g),ref:e,additionalProps:{as:d}}),[K,Q]=h("badge",{elementType:v,externalForwardedProps:J,ownerState:E,className:H.badge});return o.jsxs(q,{...G,children:[y,o.jsx(K,{...Q,children:D})]})}));export{b as B};
