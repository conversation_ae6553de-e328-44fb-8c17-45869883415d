import { Schema, model } from 'mongoose';
const settingSchema = new Schema({
  accountHolder: {
    type: String,
    required: true
  },
  bankName: {
    type: String,
    required: true
  },
  iban: {
    type: String,
    required: true
  },
  swiftBic: {
    type: String,
    required: true
  },
  showBankDetailsPage: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  strict: true,
  collection: 'BankDetails'
});
const BankDetails = model('BankDetails', settingSchema);
export default BankDetails;