import{a9 as e,j as s,F as r,a as t,z as i}from"../entries/index-CEzJO5Xy.js";import{d as a,r as o}from"./router-BtYqujaw.js";import{L as n}from"./Layout-BQBjg4Lf.js";import{c as l}from"./BookingService-BJ4R0IJT.js";import{s as m,U as d,D as c}from"./create-booking-CE8s_Uic.js";import{L as j}from"./LocationSelectList-DYJOB_U9.js";import{P as u}from"./Paper-CcwAvfvc.js";import{F as p,I as h}from"./InputLabel-BbcIE26O.js";import{L as x,A as g}from"./useMobilePicker-Cpitw7qm.js";import{D as f}from"./DateTimePicker-Di47kkTW.js";import{S}from"./TextField-BAse--ht.js";import{M as b}from"./MenuItem-suKfXYI2.js";import{I as C}from"./Input-BQdee9z7.js";import{F as v,S as N}from"./Switch-BWPUOSX1.js";import{B as I}from"./Button-DGZYUY3P.js";import{F as L}from"./FormHelperText-DFSsjBsL.js";import"./vendor-dblfw9z9.js";import"./MultipleSelect-C7xTvWe9.js";import"./Autocomplete-CviOU_ku.js";import"./OutlinedInput-g8mR4MM3.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-CtA82Ni6.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./Backdrop-Bzn12VyM.js";import"./AccountCircle-khVEeiad.js";import"./Chip-CAtDqtgp.js";import"./IconButton-CnBvmeAK.js";import"./Avatar-Dix3YM8x.js";import"./LocationService-BtQFgoWL.js";import"./Flag-BR6CpE1z.js";import"./DressService-J0XavNJj.js";import"./ListItemAvatar-Bv6onK36.js";import"./Menu-ZU0DMgjT.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-Cay5TZBz.js";import"./ListItemText-DBn_RuMq.js";import"./listItemTextClasses-DFwCkkgK.js";import"./getThemeProps-gt86ccpv.js";import"./format-4arn0GRM.js";import"./ListItem-D1VHRhQp.js";import"./KeyboardArrowRight-BV-h2cWM.js";import"./SwitchBase-BIeqtL5F.js";const P=()=>{const P=a(),[E,R]=o.useState(),[k,O]=o.useState(!1),[A,D]=o.useState(!1),[T,F]=o.useState(""),[W,y]=o.useState(""),[_,q]=o.useState(""),[M,w]=o.useState(""),[B,K]=o.useState(""),[G,U]=o.useState(null),[z,H]=o.useState(null),[V,J]=o.useState(e.Pending),[Q,Z]=o.useState(!1),[$,X]=o.useState(!1),[Y,ee]=o.useState(""),[se,re]=o.useState(!1),[te,ie]=o.useState(!1);return s.jsx(n,{onLoad:e=>{e&&e.verified&&(R(e),D(!0))},strict:!0,children:s.jsx("div",{className:"create-booking",children:s.jsxs(u,{className:"booking-form booking-form-wrapper",elevation:10,style:A?{}:{display:"none"},children:[s.jsx("h1",{className:"booking-form-title",children:m.NEW_BOOKING}),s.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(!(T&&W&&_&&M&&B&&G&&z&&Y))return re(!0),void ie(!1);O(!0),re(!1),ie(!1);const e={booking:{supplier:T,dress:W,customer:_,pickupLocation:M,dropOffLocation:B,from:G,to:z,status:V,cancellation:Q,amendments:$,price:Number(Y)}},s=await l(e);s&&s._id?P("/"):i()}catch(s){i(s)}finally{O(!1)}},children:[r(E)&&s.jsx(p,{fullWidth:!0,margin:"dense",children:s.jsx(d,{label:t.SUPPLIER,required:!0,onChange:e=>{F(e.length>0?e[0]._id:"")}})}),s.jsx(p,{fullWidth:!0,margin:"dense",children:s.jsx(c,{label:m.DRESS,required:!0,supplier:T,onChange:e=>{y(e.length>0?e[0]._id:"")}})}),s.jsx(p,{fullWidth:!0,margin:"dense",children:s.jsx(d,{label:m.DRIVER,required:!0,onChange:e=>{q(e.length>0?e[0]._id:"")}})}),s.jsx(p,{fullWidth:!0,margin:"dense",children:s.jsx(j,{label:m.PICKUP_LOCATION,required:!0,onChange:e=>{w(e.length>0?e[0]._id:"")}})}),s.jsx(p,{fullWidth:!0,margin:"dense",children:s.jsx(j,{label:m.DROP_OFF_LOCATION,required:!0,onChange:e=>{K(e.length>0?e[0]._id:"")}})}),s.jsxs(x,{dateAdapter:g,children:[s.jsx(p,{fullWidth:!0,margin:"dense",children:s.jsx(f,{label:m.FROM,value:G,onChange:U,slotProps:{textField:{required:!0,variant:"standard"}}})}),s.jsx(p,{fullWidth:!0,margin:"dense",children:s.jsx(f,{label:m.TO,value:z,onChange:H,slotProps:{textField:{required:!0,variant:"standard"}}})})]}),s.jsxs(p,{fullWidth:!0,margin:"dense",children:[s.jsx(h,{className:"required",children:m.STATUS}),s.jsxs(S,{value:V,onChange:e=>{J(e.target.value)},variant:"standard",required:!0,children:[s.jsx(b,{value:e.Pending,children:m.PENDING}),s.jsx(b,{value:e.Deposit,children:m.DEPOSIT}),s.jsx(b,{value:e.Paid,children:m.PAID}),s.jsx(b,{value:e.Reserved,children:m.RESERVED}),s.jsx(b,{value:e.Cancelled,children:m.CANCELLED})]})]}),s.jsxs(p,{fullWidth:!0,margin:"dense",children:[s.jsx(h,{className:"required",children:m.PRICE}),s.jsx(C,{type:"number",required:!0,value:Y,onChange:e=>ee(e.target.value),inputProps:{min:0,step:.01}})]}),s.jsx(p,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(v,{control:s.jsx(N,{checked:Q,onChange:e=>Z(e.target.checked),color:"primary"}),label:m.CANCELLATION})}),s.jsx(p,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(v,{control:s.jsx(N,{checked:$,onChange:e=>X(e.target.checked),color:"primary"}),label:m.AMENDMENTS})}),s.jsxs("div",{className:"buttons",children:[s.jsx(I,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:k,children:t.CREATE}),s.jsx(I,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>P("/"),children:t.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[se&&s.jsx(L,{error:!0,children:t.FORM_ERROR}),te&&s.jsx(L,{error:!0,children:t.GENERIC_ERROR})]})]})]})})})};export{P as default};
