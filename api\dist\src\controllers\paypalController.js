import * as paypal from "../paypal.js";
import i18n from "../lang/i18n.js";
import * as logger from "../common/logger.js";
import * as bookcarsTypes from "../../../../packages/bookcars-types/index.js";
import * as env from "../config/env.config.js";
import Booking from "../models/Booking.js";
import User from "../models/User.js";
import Dress from "../models/Dress.js";
import * as bookingController from "./bookingController.js";
import * as ipinfoHelper from "../common/ipinfoHelper.js";
/**
 * Create PayPal order.
 *
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const createPayPalOrder = async (req, res) => {
  try {
    const {
      bookingId,
      amount,
      currency,
      name,
      description
    } = req.body;
    const clientIp = ipinfoHelper.getClientIp(req);
    const countryCode = await ipinfoHelper.getCountryCode(clientIp);
    const orderId = await paypal.createOrder(bookingId, amount, currency, name, description, countryCode);
    res.json(orderId);
  } catch (err) {
    logger.error(`[paypal.createPayPalOrder] ${i18n.t('ERROR')}`, err);
    res.status(400).send(i18n.t('ERROR') + err);
  }
};
/**
 * Check Paypal order and update booking if the payment succeeded.
 *
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const checkPayPalOrder = async (req, res) => {
  try {
    const {
      bookingId,
      orderId
    } = req.params;
    //
    // 1. Retrieve Checkout Sesssion and Booking
    //
    const booking = await Booking.findOne({
      _id: bookingId,
      expireAt: {
        $ne: null
      }
    });
    if (!booking) {
      const msg = `Booking with id ${bookingId} not found`;
      logger.info(`[paypal.checkPayPalOrder] ${msg}`);
      res.status(204).send(msg);
      return;
    }
    let order;
    try {
      order = await paypal.getOrder(orderId);
    } catch (err) {
      logger.error(`[paypal.checkPayPalOrder] retrieve paypal order error: ${orderId}`, err);
    }
    if (!order) {
      const msg = `Order ${order} not found`;
      logger.info(`[paypal.checkPayPalOrder] ${msg}`);
      res.status(204).send(msg);
      return;
    }
    //
    // 2. Update Booking if the payment succeeded
    // (Set BookingStatus to Paid and remove expireAt TTL index)
    //
    if (order.status === 'COMPLETED') {
      booking.paypalOrderId = orderId;
      booking.expireAt = undefined;
      booking.status = booking.isDeposit ? bookcarsTypes.BookingStatus.Deposit : bookcarsTypes.BookingStatus.Paid;
      await booking.save();
      const dress = await Dress.findById(booking.dress);
      if (!dress) {
        throw new Error(`Dress ${booking.dress} not found`);
      }
      // if (env.MARK_DRESS_AS_FULLY_BOOKED_ON_CHECKOUT) {
      //   dress.fullyBooked = true
      // }
      dress.rentals += 1;
      await dress.save();
      const supplier = await User.findById(booking.supplier);
      if (!supplier) {
        throw new Error(`Supplier ${booking.supplier} not found`);
      }
      // Send confirmation email to customer
      const user = await User.findById(booking.customer);
      if (!user) {
        throw new Error(`Customer ${booking.customer} not found`);
      }
      user.expireAt = undefined;
      await user.save();
      if (!(await bookingController.confirm(user, supplier, booking, false))) {
        res.sendStatus(400);
        return;
      }
      // Notify supplier
      i18n.locale = supplier.language;
      let message = i18n.t('BOOKING_PAID_NOTIFICATION');
      await bookingController.notify(user, booking.id, supplier, message);
      // Notify admin
      const admin = !!env.ADMIN_EMAIL && (await User.findOne({
        email: env.ADMIN_EMAIL,
        type: bookcarsTypes.UserType.Admin
      }));
      if (admin) {
        i18n.locale = admin.language;
        message = i18n.t('BOOKING_PAID_NOTIFICATION');
        await bookingController.notify(user, booking.id, admin, message);
      }
      res.sendStatus(200);
      return;
    }
    //
    // 3. Delete Booking if the payment didn't succeed
    //
    await booking.deleteOne();
    res.status(400).send(order.status);
  } catch (err) {
    logger.error(`[paypal.checkPayPalOrder] ${i18n.t('ERROR')}`, err);
    res.status(400).send(i18n.t('ERROR') + err);
  }
};