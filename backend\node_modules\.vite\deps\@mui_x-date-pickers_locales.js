import "./chunk-EGRHWZRV.js";
import {
  DEFAULT_LOCALE,
  enUS,
  getPickersLocalization
} from "./chunk-7AGLD5ZP.js";
import "./chunk-EQCCHGRT.js";
import "./chunk-DC5AMYBS.js";

// node_modules/@mui/x-date-pickers/esm/locales/beBY.js
var views = {
  // maps TimeView to its translation
  hours: "гадзіны",
  minutes: "хвіліны",
  seconds: "секунды",
  meridiem: "мерыдыем"
};
var beBYPickers = {
  // Calendar navigation
  previousMonth: "Папярэдні месяц",
  nextMonth: "Наступны месяц",
  // View navigation
  openPreviousView: "Aдкрыць папярэдні выгляд",
  openNextView: "Aдкрыць наступны выгляд",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "гадавы выгляд адкрыты, перайсці да каляндарнага выгляду" : "каляндарны выгляд адкрыты, перайсці да гадавога выгляду",
  // DateRange labels
  start: "Пачатак",
  end: "Канец",
  // startDate: 'Start date',
  // startTime: 'Start time',
  // endDate: 'End date',
  // endTime: 'End time',
  // Action bar
  cancelButtonLabel: "Адмена",
  clearButtonLabel: "Ачысціць",
  okButtonLabel: "OK",
  todayButtonLabel: "Сёння",
  nextStepButtonLabel: "Наступны",
  // Toolbar titles
  datePickerToolbarTitle: "Абраць дату",
  dateTimePickerToolbarTitle: "Абраць дату і час",
  timePickerToolbarTitle: "Абраць час",
  dateRangePickerToolbarTitle: "Абраць каляндарны перыяд",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Абярыце ${views[view]}. ${!formattedTime ? "Час не абраны" : `Абраны час ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} гадзін`,
  minutesClockNumberText: (minutes) => `${minutes} хвілін`,
  secondsClockNumberText: (seconds) => `${seconds} секунд`,
  // Digital clock labels
  selectViewText: (view) => `Абярыце ${views[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Нумар тыдня",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Тыдзень ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Абраць дату, абрана дата  ${formattedDate}` : "Абраць дату",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Абраць час, абрыны час  ${formattedTime}` : "Абраць час",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  // fieldClearLabel: 'Clear',
  // Table labels
  timeTableLabel: "абраць час",
  dateTableLabel: "абраць дату",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa"
  // View names
  // year: 'Year',
  // month: 'Month',
  // day: 'Day',
  // weekDay: 'Week day',
  // hours: 'Hours',
  // minutes: 'Minutes',
  // seconds: 'Seconds',
  // meridiem: 'Meridiem',
  // Common
  // empty: 'Empty',
};
var beBY = getPickersLocalization(beBYPickers);

// node_modules/@mui/x-date-pickers/esm/locales/bgBG.js
var views2 = {
  hours: "часове",
  minutes: "минути",
  seconds: "секунди",
  meridiem: "преди обяд/след обяд"
};
var bgBGPickers = {
  // Calendar navigation
  previousMonth: "Предишен месец",
  nextMonth: "Следващ месец",
  // View navigation
  openPreviousView: "Отвори предишен изглед",
  openNextView: "Отвори следващ изглед",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "отворен е изглед на година, премини на изглед на календар" : "отворен е изглед на календар, премини на изглед на година",
  // DateRange labels
  start: "Начало",
  end: "Край",
  startDate: "Начална дата",
  startTime: "Начален час",
  endDate: "Крайна дата",
  endTime: "Краен час",
  // Action bar
  cancelButtonLabel: "Отказ",
  clearButtonLabel: "Изчисти",
  okButtonLabel: "ОК",
  todayButtonLabel: "Днес",
  nextStepButtonLabel: "Следващ",
  // Toolbar titles
  datePickerToolbarTitle: "Избери дата",
  dateTimePickerToolbarTitle: "Избери дата и час",
  timePickerToolbarTitle: "Избери час",
  dateRangePickerToolbarTitle: "Избери времеви период",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Избери ${views2[view]}. ${!formattedTime ? "Не е избран час" : `Избраният час е ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} часа`,
  minutesClockNumberText: (minutes) => `${minutes} минути`,
  secondsClockNumberText: (seconds) => `${seconds} секунди`,
  // Digital clock labels
  selectViewText: (view) => `Избери ${views2[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Седмица",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Седмица ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Избери дата, избраната дата е ${formattedDate}` : "Избери дата",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Избери час, избраният час е ${formattedTime}` : "Избери час",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Изчисти стойност",
  // Table labels
  timeTableLabel: "избери час",
  dateTableLabel: "избери дата",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Г".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "ММММ" : "ММ",
  fieldDayPlaceholder: () => "ДД",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "СССС" : "СС",
  fieldHoursPlaceholder: () => "чч",
  fieldMinutesPlaceholder: () => "мм",
  fieldSecondsPlaceholder: () => "сс",
  fieldMeridiemPlaceholder: () => "пс",
  // View names
  year: "Година",
  month: "Месец",
  day: "Ден",
  weekDay: "Ден от седмицата",
  hours: "Часове",
  minutes: "Минути",
  seconds: "Секунди",
  meridiem: "Преди обяд/след обяд",
  // Common
  empty: "Празно"
};
var bgBG = getPickersLocalization(bgBGPickers);

// node_modules/@mui/x-date-pickers/esm/locales/bnBD.js
var views3 = {
  hours: "ঘণ্টা",
  minutes: "মিনিট",
  seconds: "সেকেন্ড",
  meridiem: "এএম/পিএম"
};
var bnBDPickers = {
  // Calendar navigation
  previousMonth: "আগের মাস",
  nextMonth: "পরের মাস",
  // View navigation
  openPreviousView: "আগের ভিউ খুলুন",
  openNextView: "পরের ভিউ খুলুন",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "বছরের ভিউ খোলা আছে, ক্যালেন্ডার ভিউতে পরিবর্তন করুন" : "ক্যালেন্ডার ভিউ খোলা আছে, বছরের ভিউতে পরিবর্তন করুন",
  // DateRange labels
  start: "শুরু",
  end: "শেষ",
  startDate: "শুরুর তারিখ",
  startTime: "শুরুর সময়",
  endDate: "শেষের তারিখ",
  endTime: "শেষের সময়",
  // Action bar
  cancelButtonLabel: "বাতিল",
  clearButtonLabel: "পরিষ্কার",
  okButtonLabel: "ঠিক আছে",
  todayButtonLabel: "আজ",
  nextStepButtonLabel: "পরের",
  // Toolbar titles
  datePickerToolbarTitle: "তারিখ নির্বাচন করুন",
  dateTimePickerToolbarTitle: "তারিখ ও সময় নির্বাচন করুন",
  timePickerToolbarTitle: "সময় নির্বাচন করুন",
  dateRangePickerToolbarTitle: "তারিখের পরিসীমা নির্বাচন করুন",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `নির্বাচন করুন ${views3[view]}. ${!formattedTime ? "কোনও সময় নির্বাচন করা হয়নি" : `নির্বাচিত সময় ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} ঘণ্টা`,
  minutesClockNumberText: (minutes) => `${minutes} মিনিট`,
  secondsClockNumberText: (seconds) => `${seconds} সেকেন্ড`,
  // Digital clock labels
  selectViewText: (view) => `${views3[view]} নির্বাচন করুন`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "সপ্তাহ সংখ্যা",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `সপ্তাহ ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `তারিখ নির্বাচন করুন, নির্বাচিত তারিখ ${formattedDate}` : "তারিখ নির্বাচন করুন",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `সময় নির্বাচন করুন, নির্বাচিত সময় ${formattedTime}` : "সময় নির্বাচন করুন",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "পরিষ্কার",
  // Table labels
  timeTableLabel: "সময় নির্বাচন করুন",
  dateTableLabel: "তারিখ নির্বাচন করুন",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "ব".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "ঘন্টা",
  fieldMinutesPlaceholder: () => "মিনিট",
  fieldSecondsPlaceholder: () => "সেকেন্ড",
  fieldMeridiemPlaceholder: () => "এএম/পিএম",
  // View names
  year: "বছর",
  month: "মাস",
  day: "দিন",
  weekDay: "সপ্তাহের দিন",
  hours: "ঘণ্টা",
  minutes: "মিনিট",
  seconds: "সেকেন্ড",
  meridiem: "এএম/পিএম",
  // Common
  empty: "ফাঁকা"
};
var bnBD = getPickersLocalization(bnBDPickers);

// node_modules/@mui/x-date-pickers/esm/locales/caES.js
var views4 = {
  hours: "Hores",
  minutes: "Minuts",
  seconds: "Segons",
  meridiem: "Meridià"
};
var caESPickers = {
  // Calendar navigation
  previousMonth: "Mes anterior",
  nextMonth: "Mes següent",
  // View navigation
  openPreviousView: "Obrir l'última vista",
  openNextView: "Obrir la següent vista",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "la vista anual està oberta, canvia a la vista de calendari" : "la vista de calendari està oberta, canvia a la vista anual",
  // DateRange labels
  start: "Començar",
  end: "Terminar",
  startDate: "Data inicial",
  startTime: "Hora inicial",
  endDate: "Data final",
  endTime: "Hora final",
  // Action bar
  cancelButtonLabel: "Cancel·lar",
  clearButtonLabel: "Netejar",
  okButtonLabel: "OK",
  todayButtonLabel: "Avuí",
  nextStepButtonLabel: "Següent",
  // Toolbar titles
  datePickerToolbarTitle: "Seleccionar data",
  dateTimePickerToolbarTitle: "Seleccionar data i hora",
  timePickerToolbarTitle: "Seleccionar hora",
  dateRangePickerToolbarTitle: "Seleccionar rang de dates",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Selecciona ${views4[view]}. ${!formattedTime ? "Hora no seleccionada" : `L'hora seleccionada és ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} hores`,
  minutesClockNumberText: (minutes) => `${minutes} minuts`,
  secondsClockNumberText: (seconds) => `${seconds} segons`,
  // Digital clock labels
  selectViewText: (view) => `Seleccionar ${views4[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Número de la setmana",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Setmana ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Tria la data, la data triada és ${formattedDate}` : "Tria la data",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Tria l'hora, l'hora triada és ${formattedTime}` : "Tria l'hora",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Netega el valor",
  // Table labels
  timeTableLabel: "tria la data",
  dateTableLabel: "tria l'hora",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Any",
  month: "Mes",
  day: "Dia",
  weekDay: "Dia de la setmana",
  hours: "Hores",
  minutes: "Minuts",
  seconds: "Segons",
  meridiem: "Meridià",
  // Common
  empty: "Buit"
};
var caES = getPickersLocalization(caESPickers);

// node_modules/@mui/x-date-pickers/esm/locales/csCZ.js
var timeViews = {
  hours: "Hodiny",
  minutes: "Minuty",
  seconds: "Sekundy",
  meridiem: "Odpoledne"
};
var csCZPickers = {
  // Calendar navigation
  previousMonth: "Předchozí měsíc",
  nextMonth: "Další měsíc",
  // View navigation
  openPreviousView: "Otevřít předchozí zobrazení",
  openNextView: "Otevřít další zobrazení",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "roční zobrazení otevřeno, přepněte do zobrazení kalendáře" : "zobrazení kalendáře otevřeno, přepněte do zobrazení roku",
  // DateRange labels
  start: "Začátek",
  end: "Konec",
  startDate: "Datum začátku",
  startTime: "Čas začátku",
  endDate: "Datum konce",
  endTime: "Čas konce",
  // Action bar
  cancelButtonLabel: "Zrušit",
  clearButtonLabel: "Vymazat",
  okButtonLabel: "Potvrdit",
  todayButtonLabel: "Dnes",
  nextStepButtonLabel: "Další",
  // Toolbar titles
  datePickerToolbarTitle: "Vyberte datum",
  dateTimePickerToolbarTitle: "Vyberte datum a čas",
  timePickerToolbarTitle: "Vyberte čas",
  dateRangePickerToolbarTitle: "Vyberte rozmezí dat",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `${timeViews[view] ?? view} vybrány. ${!formattedTime ? "Není vybrán čas" : `Vybraný čas je ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} hodin`,
  minutesClockNumberText: (minutes) => `${minutes} minut`,
  secondsClockNumberText: (seconds) => `${seconds} sekund`,
  // Digital clock labels
  selectViewText: (view) => `Vyberte ${timeViews[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Týden v roce",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `${weekNumber} týden v roce`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Vyberte datum, vybrané datum je ${formattedDate}` : "Vyberte datum",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Vyberte čas, vybraný čas je ${formattedTime}` : "Vyberte čas",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Vymazat",
  // Table labels
  timeTableLabel: "vyberte čas",
  dateTableLabel: "vyberte datum",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Rok",
  month: "Měsíc",
  day: "Den",
  weekDay: "Pracovní den",
  hours: "Hodiny",
  minutes: "Minuty",
  seconds: "Sekundy",
  meridiem: "Odpoledne",
  // Common
  empty: "Prázdný"
};
var csCZ = getPickersLocalization(csCZPickers);

// node_modules/@mui/x-date-pickers/esm/locales/daDK.js
var timeViews2 = {
  hours: "Timer",
  minutes: "Minutter",
  seconds: "Sekunder",
  meridiem: "Meridiem"
};
var daDKPickers = {
  // Calendar navigation
  previousMonth: "Forrige måned",
  nextMonth: "Næste måned",
  // View navigation
  openPreviousView: "Åben forrige visning",
  openNextView: "Åben næste visning",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "årsvisning er åben, skift til kalendervisning" : "kalendervisning er åben, skift til årsvisning",
  // DateRange labels
  start: "Start",
  end: "Slut",
  startDate: "Start dato",
  startTime: "Start tid",
  endDate: "Slut date",
  endTime: "Slut tid",
  // Action bar
  cancelButtonLabel: "Annuller",
  clearButtonLabel: "Ryd",
  okButtonLabel: "OK",
  todayButtonLabel: "I dag",
  nextStepButtonLabel: "Næste",
  // Toolbar titles
  datePickerToolbarTitle: "Vælg dato",
  dateTimePickerToolbarTitle: "Vælg dato & tidspunkt",
  timePickerToolbarTitle: "Vælg tidspunkt",
  dateRangePickerToolbarTitle: "Vælg datointerval",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Vælg ${timeViews2[view] ?? view}. ${!formattedTime ? "Intet tidspunkt valgt" : `Valgte tidspunkt er ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} timer`,
  minutesClockNumberText: (minutes) => `${minutes} minutter`,
  secondsClockNumberText: (seconds) => `${seconds} sekunder`,
  // Digital clock labels
  selectViewText: (view) => `Vælg ${timeViews2[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Ugenummer",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Uge ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Vælg dato, valgte dato er ${formattedDate}` : "Vælg dato",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Vælg tidspunkt, valgte tidspunkt er ${formattedTime}` : "Vælg tidspunkt",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "ryd felt",
  // Table labels
  timeTableLabel: "vælg tidspunkt",
  dateTableLabel: "vælg dato",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "år",
  month: "måned",
  day: "dag",
  weekDay: "ugedag",
  hours: "timer",
  minutes: "minutter",
  seconds: "sekunder",
  meridiem: "middag",
  // Common
  empty: "tom"
};
var daDK = getPickersLocalization(daDKPickers);

// node_modules/@mui/x-date-pickers/esm/locales/deDE.js
var timeViews3 = {
  hours: "Stunden",
  minutes: "Minuten",
  seconds: "Sekunden",
  meridiem: "Meridiem"
};
var deDEPickers = {
  // Calendar navigation
  previousMonth: "Letzter Monat",
  nextMonth: "Nächster Monat",
  // View navigation
  openPreviousView: "Letzte Ansicht öffnen",
  openNextView: "Nächste Ansicht öffnen",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "Jahresansicht ist geöffnet, zur Kalenderansicht wechseln" : "Kalenderansicht ist geöffnet, zur Jahresansicht wechseln",
  // DateRange labels
  start: "Beginn",
  end: "Ende",
  startDate: "Startdatum",
  startTime: "Startzeit",
  endDate: "Enddatum",
  endTime: "Endzeit",
  // Action bar
  cancelButtonLabel: "Abbrechen",
  clearButtonLabel: "Löschen",
  okButtonLabel: "OK",
  todayButtonLabel: "Heute",
  nextStepButtonLabel: "Nächster",
  // Toolbar titles
  datePickerToolbarTitle: "Datum auswählen",
  dateTimePickerToolbarTitle: "Datum & Uhrzeit auswählen",
  timePickerToolbarTitle: "Uhrzeit auswählen",
  dateRangePickerToolbarTitle: "Datumsbereich auswählen",
  timeRangePickerToolbarTitle: "Zeitspanne auswählen",
  // Clock labels
  clockLabelText: (view, formattedTime) => `${timeViews3[view] ?? view} auswählen. ${!formattedTime ? "Keine Uhrzeit ausgewählt" : `Gewählte Uhrzeit ist ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} ${timeViews3.hours}`,
  minutesClockNumberText: (minutes) => `${minutes} ${timeViews3.minutes}`,
  secondsClockNumberText: (seconds) => `${seconds}  ${timeViews3.seconds}`,
  // Digital clock labels
  selectViewText: (view) => `${timeViews3[view]} auswählen`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Kalenderwoche",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Woche ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Datum auswählen, gewähltes Datum ist ${formattedDate}` : "Datum auswählen",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Uhrzeit auswählen, gewählte Uhrzeit ist ${formattedTime}` : "Uhrzeit auswählen",
  openRangePickerDialogue: (formattedRange) => formattedRange ? `Zeitspanne auswählen, die aktuell ausgewählte Zeitspanne ist ${formattedRange}` : "Zeitspanne auswählen",
  fieldClearLabel: "Wert leeren",
  // Table labels
  timeTableLabel: "Uhrzeit auswählen",
  dateTableLabel: "Datum auswählen",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "J".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "TT",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Jahr",
  month: "Monat",
  day: "Tag",
  weekDay: "Wochentag",
  hours: "Stunden",
  minutes: "Minuten",
  seconds: "Sekunden",
  meridiem: "Tageszeit",
  // Common
  empty: "Leer"
};
var deDE = getPickersLocalization(deDEPickers);

// node_modules/@mui/x-date-pickers/esm/locales/elGR.js
var views5 = {
  hours: "ώρες",
  minutes: "λεπτά",
  seconds: "δευτερόλεπτα",
  meridiem: "μεσημβρία"
};
var elGRPickers = {
  // Calendar navigation
  previousMonth: "Προηγούμενος μήνας",
  nextMonth: "Επόμενος μήνας",
  // View navigation
  openPreviousView: "Άνοίγμα προηγούμενης προβολή",
  openNextView: "Άνοίγμα επόμενης προβολή",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "η προβολή έτους είναι ανοιχτή, μεταβείτε στην προβολή ημερολογίου" : "η προβολή ημερολογίου είναι ανοιχτή, μεταβείτε στην προβολή έτους",
  // DateRange labels
  start: "Αρχή",
  end: "Τέλος",
  // startDate: 'Start date',
  // startTime: 'Start time',
  // endDate: 'End date',
  // endTime: 'End time',
  // Action bar
  cancelButtonLabel: "Άκυρο",
  clearButtonLabel: "Καθαρισμός",
  okButtonLabel: "OK",
  todayButtonLabel: "Σήμερα",
  nextStepButtonLabel: "Επόμενος",
  // Toolbar titles
  datePickerToolbarTitle: "Επιλέξτε ημερομηνία",
  dateTimePickerToolbarTitle: "Επιλέξτε ημερομηνία και ώρα",
  timePickerToolbarTitle: "Επιλέξτε ώρα",
  dateRangePickerToolbarTitle: "Επιλέξτε εύρος ημερομηνιών",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Επιλέξτε ${views5[view]}. ${!formattedTime ? "Δεν έχει επιλεγεί ώρα" : `Η επιλεγμένη ώρα είναι ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} ώρες`,
  minutesClockNumberText: (minutes) => `${minutes} λεπτά`,
  secondsClockNumberText: (seconds) => `${seconds} δευτερόλεπτα`,
  // Digital clock labels
  selectViewText: (view) => `Επιλέξτε ${views5[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Αριθμός εβδομάδας",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Εβδομάδα ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Επιλέξτε ημερομηνία, η επιλεγμένη ημερομηνία είναι ${formattedDate}` : "Επιλέξτε ημερομηνία",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Επιλέξτε ώρα, η επιλεγμένη ώρα είναι ${formattedTime}` : "Επιλέξτε ώρα",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  // fieldClearLabel: 'Clear',
  // Table labels
  timeTableLabel: "επιλέξτε ώρα",
  dateTableLabel: "επιλέξτε ημερομηνία",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Χρόνος",
  month: "Μήνας",
  day: "Ημέρα",
  weekDay: "Καθημερινή",
  hours: "Ώρες",
  minutes: "Λεπτά",
  seconds: "Δευτερόλεπτα",
  meridiem: "Προ Μεσημβρίας"
  // Common
  // empty: 'Empty',
};
var elGR = getPickersLocalization(elGRPickers);

// node_modules/@mui/x-date-pickers/esm/locales/esES.js
var views6 = {
  hours: "Horas",
  minutes: "Minutos",
  seconds: "Segundos",
  meridiem: "Meridiano"
};
var esESPickers = {
  // Calendar navigation
  previousMonth: "Mes anterior",
  nextMonth: "Mes siguiente",
  // View navigation
  openPreviousView: "Abrir la última vista",
  openNextView: "Abrir la siguiente vista",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "la vista anual está abierta, cambie a la vista de calendario" : "la vista de calendario está abierta, cambie a la vista anual",
  // DateRange labels
  start: "Empezar",
  end: "Terminar",
  startDate: "Fecha inicio",
  startTime: "Hora inicio",
  endDate: "Fecha final",
  endTime: "Hora final",
  // Action bar
  cancelButtonLabel: "Cancelar",
  clearButtonLabel: "Limpiar",
  okButtonLabel: "OK",
  todayButtonLabel: "Hoy",
  nextStepButtonLabel: "Siguiente",
  // Toolbar titles
  datePickerToolbarTitle: "Seleccionar fecha",
  dateTimePickerToolbarTitle: "Seleccionar fecha y hora",
  timePickerToolbarTitle: "Seleccionar hora",
  dateRangePickerToolbarTitle: "Seleccionar rango de fecha",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Seleccione ${views6[view]}. ${!formattedTime ? "No hay hora seleccionada" : `La hora seleccionada es ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} horas`,
  minutesClockNumberText: (minutes) => `${minutes} minutos`,
  secondsClockNumberText: (seconds) => `${seconds} segundos`,
  // Digital clock labels
  selectViewText: (view) => `Seleccionar ${views6[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Número de semana",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Semana ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Elige fecha, la fecha elegida es ${formattedDate}` : "Elige fecha",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Elige hora, la hora elegida es ${formattedTime}` : "Elige hora",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Limpiar valor",
  // Table labels
  timeTableLabel: "elige hora",
  dateTableLabel: "elige fecha",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "A".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Año",
  month: "Mes",
  day: "Dia",
  weekDay: "Dia de la semana",
  hours: "Horas",
  minutes: "Minutos",
  seconds: "Segundos",
  meridiem: "Meridiano",
  // Common
  empty: "Vacío"
};
var esES = getPickersLocalization(esESPickers);

// node_modules/@mui/x-date-pickers/esm/locales/eu.js
var views7 = {
  hours: "orduak",
  minutes: "minutuak",
  seconds: "segunduak",
  meridiem: "meridianoa"
};
var euPickers = {
  // Calendar navigation
  previousMonth: "Azken hilabetea",
  nextMonth: "Hurrengo hilabetea",
  // View navigation
  openPreviousView: "azken bista ireki",
  openNextView: "hurrengo bista ireki",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "urteko bista irekita dago, aldatu egutegi bistara" : "egutegi bista irekita dago, aldatu urteko bistara",
  // DateRange labels
  start: "Hasi",
  end: "Bukatu",
  // startDate: 'Start date',
  // startTime: 'Start time',
  // endDate: 'End date',
  // endTime: 'End time',
  // Action bar
  cancelButtonLabel: "Utxi",
  clearButtonLabel: "Garbitu",
  okButtonLabel: "OK",
  todayButtonLabel: "Gaur",
  nextStepButtonLabel: "Hurrengo",
  // Toolbar titles
  datePickerToolbarTitle: "Data aukeratu",
  dateTimePickerToolbarTitle: "Data eta ordua aukeratu",
  timePickerToolbarTitle: "Ordua aukeratu",
  dateRangePickerToolbarTitle: "Data tartea aukeratu",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Aukeratu ${views7[view]}. ${!formattedTime ? "Ez da ordurik aukertau" : `Aukeratutako ordua ${formattedTime} da`}`,
  hoursClockNumberText: (hours) => `${hours} ordu`,
  minutesClockNumberText: (minutes) => `${minutes} minutu`,
  secondsClockNumberText: (seconds) => `${seconds} segundu`,
  // Digital clock labels
  selectViewText: (view) => `Aukeratu ${views7[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Astea zenbakia",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `${weekNumber} astea`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Data aukeratu, aukeratutako data ${formattedDate} da` : "Data aukeratu",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Ordua aukeratu, aukeratutako ordua ${formattedTime} da` : "Ordua aukeratu",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Balioa garbitu",
  // Table labels
  timeTableLabel: "ordua aukeratu",
  dateTableLabel: "data aukeratu",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa"
  // View names
  // year: 'Year',
  // month: 'Month',
  // day: 'Day',
  // weekDay: 'Week day',
  // hours: 'Hours',
  // minutes: 'Minutes',
  // seconds: 'Seconds',
  // meridiem: 'Meridiem',
  // Common
  // empty: 'Empty',
};
var eu = getPickersLocalization(euPickers);

// node_modules/@mui/x-date-pickers/esm/locales/faIR.js
var timeViews4 = {
  hours: "ساعت‌ها",
  minutes: "دقیقه‌ها",
  seconds: "ثانیه‌ها",
  meridiem: "بعد از ظهر"
};
var faIRPickers = {
  // Calendar navigation
  previousMonth: "ماه گذشته",
  nextMonth: "ماه آینده",
  // View navigation
  openPreviousView: "نمای قبلی",
  openNextView: "نمای بعدی",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "نمای سال باز است، رفتن به نمای تقویم" : "نمای تقویم باز است، رفتن به نمای سال",
  // DateRange labels
  start: "شروع",
  end: "پایان",
  startDate: "تاریخ شروع",
  startTime: "ساعت شروع",
  endDate: "تاریخ پایان",
  endTime: "ساعت پایان",
  // Action bar
  cancelButtonLabel: "لغو",
  clearButtonLabel: "پاک کردن",
  okButtonLabel: "اوکی",
  todayButtonLabel: "امروز",
  nextStepButtonLabel: "آینده",
  // Toolbar titles
  datePickerToolbarTitle: "تاریخ را انتخاب کنید",
  dateTimePickerToolbarTitle: "تاریخ و ساعت را انتخاب کنید",
  timePickerToolbarTitle: "ساعت را انتخاب کنید",
  dateRangePickerToolbarTitle: "محدوده تاریخ را انتخاب کنید",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => ` را انتخاب کنید ${timeViews4[view]}. ${!formattedTime ? "هیچ ساعتی انتخاب نشده است" : `ساعت انتخاب ${formattedTime} می باشد`}`,
  hoursClockNumberText: (hours) => `${hours} ساعت‌ها`,
  minutesClockNumberText: (minutes) => `${minutes} دقیقه‌ها`,
  secondsClockNumberText: (seconds) => `${seconds} ثانیه‌ها`,
  // Digital clock labels
  selectViewText: (view) => ` را انتخاب کنید ${timeViews4[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "عدد هفته",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `هفته ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `تاریخ را انتخاب کنید، تاریخ انتخاب شده ${formattedDate} می‌باشد` : "تاریخ را انتخاب کنید",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `ساعت را انتخاب کنید، ساعت انتخاب شده ${formattedTime} می‌باشد` : "ساعت را انتخاب کنید",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "پاک کردن مقدار",
  // Table labels
  timeTableLabel: "انتخاب تاریخ",
  dateTableLabel: "انتخاب ساعت",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "سال",
  month: "ماه",
  day: "روز",
  weekDay: "روز هفته",
  hours: "ساعت‌ها",
  minutes: "دقیقه‌ها",
  seconds: "ثانیه‌ها",
  meridiem: "نیم‌روز",
  // Common
  empty: "خالی"
};
var faIR = getPickersLocalization(faIRPickers);

// node_modules/@mui/x-date-pickers/esm/locales/fiFI.js
var views8 = {
  hours: "tunnit",
  minutes: "minuutit",
  seconds: "sekuntit",
  meridiem: "iltapäivä"
};
var fiFIPickers = {
  // Calendar navigation
  previousMonth: "Edellinen kuukausi",
  nextMonth: "Seuraava kuukausi",
  // View navigation
  openPreviousView: "Avaa edellinen näkymä",
  openNextView: "Avaa seuraava näkymä",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "vuosinäkymä on auki, vaihda kalenterinäkymään" : "kalenterinäkymä on auki, vaihda vuosinäkymään",
  // DateRange labels
  start: "Alku",
  end: "Loppu",
  startDate: "Alkamispäivämäärä",
  startTime: "Alkamisaika",
  endDate: "Päättymispäivämäärä",
  endTime: "Päättymisaika",
  // Action bar
  cancelButtonLabel: "Peruuta",
  clearButtonLabel: "Tyhjennä",
  okButtonLabel: "OK",
  todayButtonLabel: "Tänään",
  nextStepButtonLabel: "Seuraava",
  // Toolbar titles
  datePickerToolbarTitle: "Valitse päivä",
  dateTimePickerToolbarTitle: "Valitse päivä ja aika",
  timePickerToolbarTitle: "Valitse aika",
  dateRangePickerToolbarTitle: "Valitse aikaväli",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Valitse ${views8[view]}. ${!formattedTime ? "Ei aikaa valittuna" : `Valittu aika on ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} tuntia`,
  minutesClockNumberText: (minutes) => `${minutes} minuuttia`,
  secondsClockNumberText: (seconds) => `${seconds} sekuntia`,
  // Digital clock labels
  selectViewText: (view) => `Valitse ${views8[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Viikko",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Viikko ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Valitse päivä, valittu päivä on ${formattedDate}` : "Valitse päivä",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Valitse aika, valittu aika on ${formattedTime}` : "Valitse aika",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Tyhjennä arvo",
  // Table labels
  timeTableLabel: "valitse aika",
  dateTableLabel: "valitse päivä",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "V".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "KKKK" : "KK",
  fieldDayPlaceholder: () => "PP",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "tt",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Vuosi",
  month: "Kuukausi",
  day: "Päivä",
  weekDay: "Viikonpäivä",
  hours: "Tunnit",
  minutes: "Minuutit",
  seconds: "Sekunnit",
  meridiem: "Iltapäivä",
  // Common
  empty: "Tyhjä"
};
var fiFI = getPickersLocalization(fiFIPickers);

// node_modules/@mui/x-date-pickers/esm/locales/frFR.js
var views9 = {
  hours: "heures",
  minutes: "minutes",
  seconds: "secondes",
  meridiem: "méridien"
};
var frFRPickers = {
  // Calendar navigation
  previousMonth: "Mois précédent",
  nextMonth: "Mois suivant",
  // View navigation
  openPreviousView: "Ouvrir la vue précédente",
  openNextView: "Ouvrir la vue suivante",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "La vue année est ouverte, ouvrir la vue calendrier" : "La vue calendrier est ouverte, ouvrir la vue année",
  // DateRange labels
  start: "Début",
  end: "Fin",
  startDate: "Date de début",
  startTime: "Heure de début",
  endDate: "Date de fin",
  endTime: "Heure de fin",
  // Action bar
  cancelButtonLabel: "Annuler",
  clearButtonLabel: "Vider",
  okButtonLabel: "OK",
  todayButtonLabel: "Aujourd'hui",
  nextStepButtonLabel: "Suivant",
  // Toolbar titles
  datePickerToolbarTitle: "Choisir une date",
  dateTimePickerToolbarTitle: "Choisir la date et l'heure",
  timePickerToolbarTitle: "Choisir l'heure",
  dateRangePickerToolbarTitle: "Choisir la plage de dates",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Choix des ${views9[view]}. ${!formattedTime ? "Aucune heure choisie" : `L'heure choisie est ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} heures`,
  minutesClockNumberText: (minutes) => `${minutes} minutes`,
  secondsClockNumberText: (seconds) => `${seconds} secondes`,
  // Digital clock labels
  selectViewText: (view) => `Choisir ${views9[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Semaine",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Semaine ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Choisir la date, la date sélectionnée est ${formattedDate}` : "Choisir la date",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Choisir l'heure, l'heure sélectionnée est ${formattedTime}` : "Choisir l'heure",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Effacer la valeur",
  // Table labels
  timeTableLabel: "choix de l'heure",
  dateTableLabel: "choix de la date",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "A".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "JJ",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Année",
  month: "Mois",
  day: "Jour",
  weekDay: "Jour de la semaine",
  hours: "Heures",
  minutes: "Minutes",
  seconds: "Secondes",
  meridiem: "Méridien",
  // Common
  empty: "Vider"
};
var frFR = getPickersLocalization(frFRPickers);

// node_modules/@mui/x-date-pickers/esm/locales/heIL.js
var views10 = {
  hours: "שעות",
  minutes: "דקות",
  seconds: "שניות",
  meridiem: "מרידיאם"
};
var heILPickers = {
  // Calendar navigation
  previousMonth: "חודש קודם",
  nextMonth: "חודש הבא",
  // View navigation
  openPreviousView: "תצוגה קודמת",
  openNextView: "תצוגה הבאה",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "תצוגת שנה פתוחה, מעבר לתצוגת לוח שנה" : "תצוגת לוח שנה פתוחה, מעבר לתצוגת שנה",
  // DateRange labels
  start: "תחילה",
  end: "סיום",
  startDate: "תאריך תחילה",
  startTime: "שעת תחילה",
  endDate: "תאריך סיום",
  endTime: "שעת סיום",
  // Action bar
  cancelButtonLabel: "ביטול",
  clearButtonLabel: "ניקוי",
  okButtonLabel: "אישור",
  todayButtonLabel: "היום",
  nextStepButtonLabel: "הבא",
  // Toolbar titles
  datePickerToolbarTitle: "בחירת תאריך",
  dateTimePickerToolbarTitle: "בחירת תאריך ושעה",
  timePickerToolbarTitle: "בחירת שעה",
  dateRangePickerToolbarTitle: "בחירת טווח תאריכים",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `בחירת ${views10[view]}. ${!formattedTime ? "לא נבחרה שעה" : `השעה הנבחרת היא ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} שעות`,
  minutesClockNumberText: (minutes) => `${minutes} דקות`,
  secondsClockNumberText: (seconds) => `${seconds} שניות`,
  // Digital clock labels
  selectViewText: (view) => `בחירת ${views10[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "שבוע מספר",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `שבוע ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `בחירת תאריך, התאריך שנבחר הוא ${formattedDate}` : "בחירת תאריך",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `בחירת שעה, השעה שנבחרה היא ${formattedTime}` : "בחירת שעה",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "נקה ערך",
  // Table labels
  timeTableLabel: "בחירת שעה",
  dateTableLabel: "בחירת תאריך",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "שנה",
  month: "חודש",
  day: "יום",
  weekDay: "יום בשבוע",
  hours: "שעות",
  minutes: "דקות",
  seconds: "שניות",
  meridiem: "יחידת זמן",
  // Common
  empty: "ריק"
};
var heIL = getPickersLocalization(heILPickers);

// node_modules/@mui/x-date-pickers/esm/locales/hrHR.js
var timeViews5 = {
  hours: "sati",
  minutes: "minute",
  seconds: "sekunde",
  meridiem: "meridiem"
};
var hrHRPickers = {
  // Calendar navigation
  previousMonth: "Prethodni mjesec",
  nextMonth: "Naredni mjesec",
  // View navigation
  openPreviousView: "Otvori prethodni prikaz",
  openNextView: "Otvori naredni prikaz",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "Otvoren je godišnji prikaz, promijeni na kalendarski prikaz" : "Otvoren je kalendarski prikaz, promijeni na godišnji prikaz",
  // DateRange labels
  start: "Početak",
  end: "Kraj",
  startDate: "Početni datum",
  startTime: "Početno vrijeme",
  endDate: "Krajnji datum",
  endTime: "Krajnje vrijeme",
  // Action bar
  cancelButtonLabel: "Otkaži",
  clearButtonLabel: "Izbriši",
  okButtonLabel: "U redu",
  todayButtonLabel: "Danas",
  nextStepButtonLabel: "Naredni",
  // Toolbar titles
  datePickerToolbarTitle: "Odaberi datum",
  dateTimePickerToolbarTitle: "Odaberi datum i vrijeme",
  timePickerToolbarTitle: "Odaberi vrijeme",
  dateRangePickerToolbarTitle: "Odaberi vremenski okvir",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Odaberi ${timeViews5[view] ?? view}. ${!formattedTime ? "Vrijeme nije odabrano" : `Odabrano vrijeme je ${formattedTime}`}`,
  hoursClockNumberText: (hours) => {
    let suffix = "sati";
    if (Number(hours) === 1) {
      suffix = "sat";
    } else if (Number(hours) < 5) {
      suffix = "sata";
    }
    return `${hours} ${suffix}`;
  },
  minutesClockNumberText: (minutes) => `${minutes} ${Number(minutes) > 1 && Number(minutes) < 5 ? "minute" : "minuta"}`,
  secondsClockNumberText: (seconds) => {
    let suffix = "sekundi";
    if (Number(seconds) === 1) {
      suffix = "sekunda";
    } else if (Number(seconds) < 5) {
      suffix = "sekunde";
    }
    return `${seconds} ${suffix}`;
  },
  // Digital clock labels
  selectViewText: (view) => `Odaberi ${timeViews5[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Broj tjedna",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Tjedan ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Odaberi datum, odabrani datum je ${formattedDate}` : "Odaberi datum",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Odaberi vrijeme, odabrano vrijeme je ${formattedTime}` : "Odaberi vrijeme",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Izbriši",
  // Table labels
  timeTableLabel: "Odaberi vrijeme",
  dateTableLabel: "Odaberi datum",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "G".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Godina",
  month: "Mjesec",
  day: "Dan",
  weekDay: "Dan u tjednu",
  hours: "Sati",
  minutes: "Minute",
  seconds: "Sekunde",
  meridiem: "Meridiem",
  // Common
  empty: "Isprazni"
};
var hrHR = getPickersLocalization(hrHRPickers);

// node_modules/@mui/x-date-pickers/esm/locales/huHU.js
var timeViews6 = {
  hours: "Óra",
  minutes: "Perc",
  seconds: "Másodperc",
  meridiem: "Délután"
};
var huHUPickers = {
  // Calendar navigation
  previousMonth: "Előző hónap",
  nextMonth: "Következő hónap",
  // View navigation
  openPreviousView: "Előző nézet megnyitása",
  openNextView: "Következő nézet megnyitása",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "az évválasztó már nyitva, váltson a naptárnézetre" : "a naptárnézet már nyitva, váltson az évválasztóra",
  // DateRange labels
  start: "Kezdő dátum",
  end: "Záró dátum",
  startDate: "Kezdő dátum",
  startTime: "Kezdő idő",
  endDate: "Záró dátum",
  endTime: "Záró idő",
  // Action bar
  cancelButtonLabel: "Mégse",
  clearButtonLabel: "Törlés",
  okButtonLabel: "OK",
  todayButtonLabel: "Ma",
  nextStepButtonLabel: "Következő",
  // Toolbar titles
  datePickerToolbarTitle: "Dátum kiválasztása",
  dateTimePickerToolbarTitle: "Dátum és idő kiválasztása",
  timePickerToolbarTitle: "Idő kiválasztása",
  dateRangePickerToolbarTitle: "Dátumhatárok kiválasztása",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `${timeViews6[view] ?? view} kiválasztása. ${!formattedTime ? "Nincs kiválasztva idő" : `A kiválasztott idő ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} ${timeViews6.hours.toLowerCase()}`,
  minutesClockNumberText: (minutes) => `${minutes} ${timeViews6.minutes.toLowerCase()}`,
  secondsClockNumberText: (seconds) => `${seconds}  ${timeViews6.seconds.toLowerCase()}`,
  // Digital clock labels
  selectViewText: (view) => `${timeViews6[view]} kiválasztása`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Hét",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `${weekNumber}. hét`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Válasszon dátumot, a kiválasztott dátum: ${formattedDate}` : "Válasszon dátumot",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Válasszon időt, a kiválasztott idő: ${formattedTime}` : "Válasszon időt",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Tartalom ürítése",
  // Table labels
  timeTableLabel: "válasszon időt",
  dateTableLabel: "válasszon dátumot",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "É".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "HHHH" : "HH",
  fieldDayPlaceholder: () => "NN",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "NNNN" : "NN",
  fieldHoursPlaceholder: () => "óó",
  fieldMinutesPlaceholder: () => "pp",
  fieldSecondsPlaceholder: () => "mm",
  fieldMeridiemPlaceholder: () => "dd",
  // View names
  year: "Év",
  month: "Hónap",
  day: "Nap",
  weekDay: "Hétköznap",
  hours: timeViews6.hours,
  minutes: timeViews6.minutes,
  seconds: timeViews6.seconds,
  meridiem: timeViews6.meridiem,
  // Common
  empty: "Üres"
};
var huHU = getPickersLocalization(huHUPickers);

// node_modules/@mui/x-date-pickers/esm/locales/isIS.js
var timeViews7 = {
  hours: "klukkustundir",
  minutes: "mínútur",
  seconds: "sekúndur",
  meridiem: "eftirmiðdagur"
};
var isISPickers = {
  // Calendar navigation
  previousMonth: "Fyrri mánuður",
  nextMonth: "Næsti mánuður",
  // View navigation
  openPreviousView: "Opna fyrri skoðun",
  openNextView: "Opna næstu skoðun",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "ársskoðun er opin, skipta yfir í dagatalsskoðun" : "dagatalsskoðun er opin, skipta yfir í ársskoðun",
  // DateRange labels
  start: "Upphaf",
  end: "Endir",
  // startDate: 'Start date',
  // startTime: 'Start time',
  // endDate: 'End date',
  // endTime: 'End time',
  // Action bar
  cancelButtonLabel: "Hætta við",
  clearButtonLabel: "Hreinsa",
  okButtonLabel: "OK",
  todayButtonLabel: "Í dag",
  nextStepButtonLabel: "Næsti",
  // Toolbar titles
  datePickerToolbarTitle: "Velja dagsetningu",
  dateTimePickerToolbarTitle: "Velja dagsetningu og tíma",
  timePickerToolbarTitle: "Velja tíma",
  dateRangePickerToolbarTitle: "Velja tímabil",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Velja ${timeViews7[view]}. ${!formattedTime ? "Enginn tími valinn" : `Valinn tími er ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} klukkustundir`,
  minutesClockNumberText: (minutes) => `${minutes} mínútur`,
  secondsClockNumberText: (seconds) => `${seconds} sekúndur`,
  // Digital clock labels
  selectViewText: (view) => `Velja ${timeViews7[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Vikunúmer",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Vika ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Velja dagsetningu, valin dagsetning er ${formattedDate}` : "Velja dagsetningu",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Velja tíma, valinn tími er ${formattedTime}` : "Velja tíma",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  // fieldClearLabel: 'Clear',
  // Table labels
  timeTableLabel: "velja tíma",
  dateTableLabel: "velja dagsetningu",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Á".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "kk",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "ee"
  // View names
  // year: 'Year',
  // month: 'Month',
  // day: 'Day',
  // weekDay: 'Week day',
  // hours: 'Hours',
  // minutes: 'Minutes',
  // seconds: 'Seconds',
  // meridiem: 'Meridiem',
  // Common
  // empty: 'Empty',
};
var isIS = getPickersLocalization(isISPickers);

// node_modules/@mui/x-date-pickers/esm/locales/itIT.js
var views11 = {
  hours: "le ore",
  minutes: "i minuti",
  seconds: "i secondi",
  meridiem: "il meridiano"
};
var itITPickers = {
  // Calendar navigation
  previousMonth: "Mese precedente",
  nextMonth: "Mese successivo",
  // View navigation
  openPreviousView: "Apri la vista precedente",
  openNextView: "Apri la vista successiva",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "la vista dell'anno è aperta, passare alla vista del calendario" : "la vista dell'calendario è aperta, passare alla vista dell'anno",
  // DateRange labels
  start: "Inizio",
  end: "Fine",
  startDate: "Data di inizio",
  startTime: "Ora di inizio",
  endDate: "Data di fine",
  endTime: "Ora di fine",
  // Action bar
  cancelButtonLabel: "Annulla",
  clearButtonLabel: "Pulisci",
  okButtonLabel: "OK",
  todayButtonLabel: "Oggi",
  nextStepButtonLabel: "Successivo",
  // Toolbar titles
  datePickerToolbarTitle: "Seleziona data",
  dateTimePickerToolbarTitle: "Seleziona data e orario",
  timePickerToolbarTitle: "Seleziona orario",
  dateRangePickerToolbarTitle: "Seleziona intervallo di date",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Seleziona ${views11[view]}. ${!formattedTime ? "Nessun orario selezionato" : `L'ora selezionata è ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} ore`,
  minutesClockNumberText: (minutes) => `${minutes} minuti`,
  secondsClockNumberText: (seconds) => `${seconds} secondi`,
  // Digital clock labels
  selectViewText: (view) => `Seleziona ${views11[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Numero settimana",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Settimana ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Scegli la data, la data selezionata è ${formattedDate}` : "Scegli la data",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Scegli l'ora, l'ora selezionata è ${formattedTime}` : "Scegli l'ora",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Cancella valore",
  // Table labels
  timeTableLabel: "scegli un'ora",
  dateTableLabel: "scegli una data",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "A".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "GG",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "GGGG" : "GG",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Anno",
  month: "Mese",
  day: "Giorno",
  weekDay: "Giorno della settimana",
  hours: "Ore",
  minutes: "Minuti",
  seconds: "Secondi",
  meridiem: "Meridiano",
  // Common
  empty: "Vuoto"
};
var itIT = getPickersLocalization(itITPickers);

// node_modules/@mui/x-date-pickers/esm/locales/jaJP.js
var timeViews8 = {
  hours: "時間",
  minutes: "分",
  seconds: "秒",
  meridiem: "メリディム"
};
var jaJPPickers = {
  // Calendar navigation
  previousMonth: "先月",
  nextMonth: "来月",
  // View navigation
  openPreviousView: "前の表示を開く",
  openNextView: "次の表示を開く",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "年選択表示からカレンダー表示に切り替える" : "カレンダー表示から年選択表示に切り替える",
  // DateRange labels
  start: "開始",
  end: "終了",
  startDate: "開始日",
  startTime: "開始時間",
  endDate: "終了日",
  endTime: "終了時間",
  // Action bar
  cancelButtonLabel: "キャンセル",
  clearButtonLabel: "クリア",
  okButtonLabel: "確定",
  todayButtonLabel: "今日",
  nextStepButtonLabel: "来",
  // Toolbar titles
  datePickerToolbarTitle: "日付を選択",
  dateTimePickerToolbarTitle: "日時を選択",
  timePickerToolbarTitle: "時間を選択",
  dateRangePickerToolbarTitle: "日付の範囲を選択",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `${timeViews8[view] ?? view}を選択してください ${!formattedTime ? "時間が選択されていません" : `選択した時間は ${formattedTime} です`}`,
  hoursClockNumberText: (hours) => `${hours} ${timeViews8.hours}`,
  minutesClockNumberText: (minutes) => `${minutes} ${timeViews8.minutes}`,
  secondsClockNumberText: (seconds) => `${seconds} ${timeViews8.seconds}`,
  // Digital clock labels
  selectViewText: (view) => `を選択 ${timeViews8[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "週番号",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `${weekNumber}週目`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `日付を選択してください。選択した日付は ${formattedDate} です` : "日付を選択してください",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `時間を選択してください。選択した時間は ${formattedTime} です` : "時間を選択してください",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "クリア",
  // Table labels
  timeTableLabel: "時間を選択",
  dateTableLabel: "日付を選択",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "年",
  month: "月",
  day: "日",
  weekDay: "平日",
  hours: "時間",
  minutes: "分",
  seconds: "秒",
  meridiem: "メリディム",
  // Common
  empty: "空"
};
var jaJP = getPickersLocalization(jaJPPickers);

// node_modules/@mui/x-date-pickers/esm/locales/koKR.js
var views12 = {
  hours: "시간을",
  minutes: "분을",
  seconds: "초를",
  meridiem: "오전/오후를"
};
var koKRPickers = {
  // Calendar navigation
  previousMonth: "이전 달",
  nextMonth: "다음 달",
  // View navigation
  openPreviousView: "이전 화면 보기",
  openNextView: "다음 화면 보기",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "연도 선택 화면에서 달력 화면으로 전환하기" : "달력 화면에서 연도 선택 화면으로 전환하기",
  // DateRange labels
  start: "시작",
  end: "종료",
  startDate: "시작 날짜",
  startTime: "시작 시간",
  endDate: "종료 날짜",
  endTime: "종료 시간",
  // Action bar
  cancelButtonLabel: "취소",
  clearButtonLabel: "초기화",
  okButtonLabel: "확인",
  todayButtonLabel: "오늘",
  nextStepButtonLabel: "다음",
  // Toolbar titles
  datePickerToolbarTitle: "날짜 선택하기",
  dateTimePickerToolbarTitle: "날짜 & 시간 선택하기",
  timePickerToolbarTitle: "시간 선택하기",
  dateRangePickerToolbarTitle: "날짜 범위 선택하기",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `${views12[view]} 선택하세요. ${!formattedTime ? "시간을 선택하지 않았습니다." : `현재 선택된 시간은 ${formattedTime}입니다.`}`,
  hoursClockNumberText: (hours) => `${hours}시`,
  minutesClockNumberText: (minutes) => `${minutes}분`,
  secondsClockNumberText: (seconds) => `${seconds}초`,
  // Digital clock labels
  selectViewText: (view) => `${views12[view]} 선택하기`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "주 번호",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `${weekNumber}번째 주`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `날짜를 선택하세요. 현재 선택된 날짜는 ${formattedDate}입니다.` : "날짜를 선택하세요",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `시간을 선택하세요. 현재 선택된 시간은 ${formattedTime}입니다.` : "시간을 선택하세요",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "지우기",
  // Table labels
  timeTableLabel: "선택한 시간",
  dateTableLabel: "선택한 날짜",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "년",
  month: "월",
  day: "일",
  weekDay: "평일",
  hours: "시간",
  minutes: "분",
  seconds: "초",
  // meridiem: 'Meridiem',
  // Common
  empty: "공란"
};
var koKR = getPickersLocalization(koKRPickers);

// node_modules/@mui/x-date-pickers/esm/locales/kzKZ.js
var timeViews9 = {
  hours: "Сағатты",
  minutes: "Минутты",
  seconds: "Секундты",
  meridiem: "Меридием"
};
var kzKZPickers = {
  // Calendar navigation
  previousMonth: "Алдыңғы ай",
  nextMonth: "Келесі ай",
  // View navigation
  openPreviousView: "Алдыңғы көріністі ашу",
  openNextView: "Келесі көріністі ашу",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "жылдық көріністі ашу, күнтізбе көрінісіне ауысу" : "күнтізбе көрінісін ашу, жылдық көрінісіне ауысу",
  // DateRange labels
  start: "Бастау",
  end: "Cоңы",
  // startDate: 'Start date',
  // startTime: 'Start time',
  // endDate: 'End date',
  // endTime: 'End time',
  // Action bar
  cancelButtonLabel: "Бас тарту",
  clearButtonLabel: "Тазарту",
  okButtonLabel: "Ок",
  todayButtonLabel: "Бүгін",
  nextStepButtonLabel: "Келесі",
  // Toolbar titles
  datePickerToolbarTitle: "Күнді таңдау",
  dateTimePickerToolbarTitle: "Күн мен уақытты таңдау",
  timePickerToolbarTitle: "Уақытты таңдау",
  dateRangePickerToolbarTitle: "Кезеңді таңдаңыз",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `${timeViews9[view]} таңдау. ${!formattedTime ? "Уақыт таңдалмаған" : `Таңдалған уақыт ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} сағат`,
  minutesClockNumberText: (minutes) => `${minutes} минут`,
  secondsClockNumberText: (seconds) => `${seconds} секунд`,
  // Digital clock labels
  selectViewText: (view) => `${timeViews9[view]} таңдау`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Апта нөмірі",
  calendarWeekNumberHeaderText: "№",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Апта ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Күнді таңдаңыз, таңдалған күн ${formattedDate}` : "Күнді таңдаңыз",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Уақытты таңдаңыз, таңдалған уақыт ${formattedTime}` : "Уақытты таңдаңыз",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  // fieldClearLabel: 'Clear',
  // Table labels
  timeTableLabel: "уақытты таңдау",
  dateTableLabel: "күнді таңдау",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Ж".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "AAAA" : "AA",
  fieldDayPlaceholder: () => "КК",
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  fieldHoursPlaceholder: () => "сс",
  fieldMinutesPlaceholder: () => "мм",
  fieldSecondsPlaceholder: () => "сс",
  fieldMeridiemPlaceholder: () => "(т|к)"
  // View names
  // year: 'Year',
  // month: 'Month',
  // day: 'Day',
  // weekDay: 'Week day',
  // hours: 'Hours',
  // minutes: 'Minutes',
  // seconds: 'Seconds',
  // meridiem: 'Meridiem',
  // Common
  // empty: 'Empty',
};
var kzKZ = getPickersLocalization(kzKZPickers);

// node_modules/@mui/x-date-pickers/esm/locales/mk.js
var mkPickers = {
  // Calendar navigation
  previousMonth: "Предходен месец",
  nextMonth: "Следен месец",
  // View navigation
  openPreviousView: "отвори претходен приказ",
  openNextView: "отвори следен приказ",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "годишен приказ, отвори календарски приказ" : "календарски приказ, отвори годишен приказ",
  // DateRange labels
  start: "Почеток",
  end: "Крај",
  // startDate: 'Start date',
  // startTime: 'Start time',
  // endDate: 'End date',
  // endTime: 'End time',
  // Action bar
  cancelButtonLabel: "Откажи",
  clearButtonLabel: "Избриши",
  okButtonLabel: "OK",
  todayButtonLabel: "Денес",
  nextStepButtonLabel: "Следен",
  // Toolbar titles
  datePickerToolbarTitle: "Избери датум",
  dateTimePickerToolbarTitle: "Избери датум и време",
  timePickerToolbarTitle: "Избери време",
  dateRangePickerToolbarTitle: "Избери временски опсег",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Select ${view}. ${!formattedTime ? "Нема избрано време" : `Избраното време е ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} часа`,
  minutesClockNumberText: (minutes) => `${minutes} минути`,
  secondsClockNumberText: (seconds) => `${seconds} секунди`,
  // Digital clock labels
  selectViewText: (view) => `Избери ${view}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Недела број",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Недела ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Избери датум, избраниот датум е ${formattedDate}` : "Избери датум",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Избери време, избраното време е ${formattedTime}` : "Избери време",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Избриши",
  // Table labels
  timeTableLabel: "одбери време",
  dateTableLabel: "одбери датум",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Г".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "ДД",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "чч",
  fieldMinutesPlaceholder: () => "мм",
  fieldSecondsPlaceholder: () => "сс",
  fieldMeridiemPlaceholder: () => "aa"
  // View names
  // year: 'Year',
  // month: 'Month',
  // day: 'Day',
  // weekDay: 'Week day',
  // hours: 'Hours',
  // minutes: 'Minutes',
  // seconds: 'Seconds',
  // meridiem: 'Meridiem',
  // Common
  // empty: 'Empty',
};
var mk = getPickersLocalization(mkPickers);

// node_modules/@mui/x-date-pickers/esm/locales/nbNO.js
var timeViews10 = {
  hours: "timer",
  minutes: "minutter",
  seconds: "sekunder",
  meridiem: "meridiem"
};
var nbNOPickers = {
  // Calendar navigation
  previousMonth: "Forrige måned",
  nextMonth: "Neste måned",
  // View navigation
  openPreviousView: "Åpne forrige visning",
  openNextView: "Åpne neste visning",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "årsvisning er åpen, bytt til kalendervisning" : "kalendervisning er åpen, bytt til årsvisning",
  // DateRange labels
  start: "Start",
  end: "Slutt",
  startDate: "Startdato",
  startTime: "Starttid",
  endDate: "Sluttdato",
  endTime: "Slutttid",
  // Action bar
  cancelButtonLabel: "Avbryt",
  clearButtonLabel: "Fjern",
  okButtonLabel: "OK",
  todayButtonLabel: "I dag",
  nextStepButtonLabel: "Neste",
  // Toolbar titles
  datePickerToolbarTitle: "Velg dato",
  dateTimePickerToolbarTitle: "Velg dato & klokkeslett",
  timePickerToolbarTitle: "Velg klokkeslett",
  dateRangePickerToolbarTitle: "Velg datoperiode",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Velg ${timeViews10[view]}. ${!formattedTime ? "Ingen tid valgt" : `Valgt tid er ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} timer`,
  minutesClockNumberText: (minutes) => `${minutes} minutter`,
  secondsClockNumberText: (seconds) => `${seconds} sekunder`,
  // Digital clock labels
  selectViewText: (view) => `Velg ${timeViews10[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Ukenummer",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Uke ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Velg dato, valgt dato er ${formattedDate}` : "Velg dato",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Velg tid, valgt tid er ${formattedTime}` : "Velg tid",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Slett",
  // Table labels
  timeTableLabel: "velg tid",
  dateTableLabel: "velg dato",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Å".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "tt",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "År",
  month: "Måned",
  day: "Dag",
  weekDay: "Ukedag",
  hours: "Timer",
  minutes: "Minutter",
  seconds: "Sekunder",
  meridiem: "Meridiem",
  // Common
  empty: "Tøm"
};
var nbNO = getPickersLocalization(nbNOPickers);

// node_modules/@mui/x-date-pickers/esm/locales/nlNL.js
var timeViews11 = {
  hours: "uren",
  minutes: "minuten",
  seconds: "seconden",
  meridiem: "meridium"
};
var nlNLPickers = {
  // Calendar navigation
  previousMonth: "Vorige maand",
  nextMonth: "Volgende maand",
  // View navigation
  openPreviousView: "Open vorige view",
  openNextView: "Open volgende view",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "jaarweergave is geopend, schakel over naar kalenderweergave" : "kalenderweergave is geopend, switch naar jaarweergave",
  // DateRange labels
  start: "Start",
  end: "Einde",
  startDate: "Startdatum",
  startTime: "Starttijd",
  endDate: "Einddatum",
  endTime: "Eindtijd",
  // Action bar
  cancelButtonLabel: "Annuleren",
  clearButtonLabel: "Resetten",
  okButtonLabel: "OK",
  todayButtonLabel: "Vandaag",
  nextStepButtonLabel: "Volgende",
  // Toolbar titles
  datePickerToolbarTitle: "Selecteer datum",
  dateTimePickerToolbarTitle: "Selecteer datum & tijd",
  timePickerToolbarTitle: "Selecteer tijd",
  dateRangePickerToolbarTitle: "Selecteer datumbereik",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Selecteer ${timeViews11[view]}. ${!formattedTime ? "Geen tijd geselecteerd" : `Geselecteerde tijd is ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} uren`,
  minutesClockNumberText: (minutes) => `${minutes} minuten`,
  secondsClockNumberText: (seconds) => `${seconds} seconden`,
  // Digital clock labels
  selectViewText: (view) => `Selecteer ${timeViews11[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Weeknummer",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Week ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Kies datum, geselecteerde datum is ${formattedDate}` : "Kies datum",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Kies tijd, geselecteerde tijd is ${formattedTime}` : "Kies tijd",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Wissen",
  // Table labels
  timeTableLabel: "kies tijd",
  dateTableLabel: "kies datum",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "J".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "uu",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Jaar",
  month: "Maand",
  day: "Dag",
  weekDay: "Weekdag",
  hours: "Uren",
  minutes: "Minuten",
  seconds: "Seconden",
  meridiem: "Middag",
  // Common
  empty: "Leeg"
};
var nlNL = getPickersLocalization(nlNLPickers);

// node_modules/@mui/x-date-pickers/esm/locales/nnNO.js
var timeViews12 = {
  hours: "timar",
  minutes: "minuttar",
  seconds: "sekundar",
  meridiem: "meridiem"
};
var nnNOPickers = {
  // Calendar navigation
  previousMonth: "Forrige månad",
  nextMonth: "Neste månad",
  // View navigation
  openPreviousView: "Opne forrige visning",
  openNextView: "Opne neste visning",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "årsvisning er open, byt til kalendervisning" : "kalendervisning er open, byt til årsvisning",
  // DateRange labels
  start: "Start",
  end: "Slutt",
  startDate: "Startdato",
  startTime: "Starttid",
  endDate: "Sluttdato",
  endTime: "Slutttid",
  // Action bar
  cancelButtonLabel: "Avbryt",
  clearButtonLabel: "Fjern",
  okButtonLabel: "OK",
  todayButtonLabel: "I dag",
  nextStepButtonLabel: "Neste",
  // Toolbar titles
  datePickerToolbarTitle: "Vel dato",
  dateTimePickerToolbarTitle: "Vel dato & klokkeslett",
  timePickerToolbarTitle: "Vel klokkeslett",
  dateRangePickerToolbarTitle: "Vel datoperiode",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Vel ${timeViews12[view]}. ${!formattedTime ? "Ingen tid vald" : `Vald tid er ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} timar`,
  minutesClockNumberText: (minutes) => `${minutes} minuttar`,
  secondsClockNumberText: (seconds) => `${seconds} sekundar`,
  // Digital clock labels
  selectViewText: (view) => `Vel ${timeViews12[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Vekenummer",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Veke ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Vel dato, vald dato er ${formattedDate}` : "Vel dato",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Vel tid, vald tid er ${formattedTime}` : "Vel tid",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Fjern verdi",
  // Table labels
  timeTableLabel: "vel tid",
  dateTableLabel: "vel dato",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Å".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "tt",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "År",
  month: "Månad",
  day: "Dag",
  weekDay: "Vekedag",
  hours: "Timar",
  minutes: "Minuttar",
  seconds: "Sekundar",
  meridiem: "Meridiem",
  // Common
  empty: "Tom"
};
var nnNO = getPickersLocalization(nnNOPickers);

// node_modules/@mui/x-date-pickers/esm/locales/plPL.js
var timeViews13 = {
  hours: "godzin",
  minutes: "minut",
  seconds: "sekund",
  meridiem: "popołudnie"
};
var plPLPickers = {
  // Calendar navigation
  previousMonth: "Poprzedni miesiąc",
  nextMonth: "Następny miesiąc",
  // View navigation
  openPreviousView: "Otwórz poprzedni widok",
  openNextView: "Otwórz następny widok",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "otwarty jest widok roku, przełącz na widok kalendarza" : "otwarty jest widok kalendarza, przełącz na widok roku",
  // DateRange labels
  start: "Początek",
  end: "Koniec",
  startDate: "Data rozpoczęcia",
  startTime: "Czas rozpoczęcia",
  endDate: "Data zakończenia",
  endTime: "Czas zakończenia",
  // Action bar
  cancelButtonLabel: "Anuluj",
  clearButtonLabel: "Wyczyść",
  okButtonLabel: "Zatwierdź",
  todayButtonLabel: "Dzisiaj",
  nextStepButtonLabel: "Następny",
  // Toolbar titles
  datePickerToolbarTitle: "Wybierz datę",
  dateTimePickerToolbarTitle: "Wybierz datę i czas",
  timePickerToolbarTitle: "Wybierz czas",
  dateRangePickerToolbarTitle: "Wybierz zakres dat",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Wybierz ${timeViews13[view]}. ${!formattedTime ? "Nie wybrano czasu" : `Wybrany czas to ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} godzin`,
  minutesClockNumberText: (minutes) => `${minutes} minut`,
  secondsClockNumberText: (seconds) => `${seconds} sekund`,
  // Digital clock labels
  selectViewText: (view) => `Wybierz ${timeViews13[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Numer tygodnia",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Tydzień ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Wybierz datę, obecnie wybrana data to ${formattedDate}` : "Wybierz datę",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Wybierz czas, obecnie wybrany czas to ${formattedTime}` : "Wybierz czas",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Wyczyść",
  // Table labels
  timeTableLabel: "wybierz czas",
  dateTableLabel: "wybierz datę",
  // Field section placeholders
  // fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),
  // fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',
  // fieldDayPlaceholder: () => 'DD',
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  // fieldHoursPlaceholder: () => 'hh',
  // fieldMinutesPlaceholder: () => 'mm',
  // fieldSecondsPlaceholder: () => 'ss',
  // fieldMeridiemPlaceholder: () => 'aa',
  // View names
  year: "Rok",
  month: "Miesiąc",
  day: "Dzień",
  weekDay: "Dzień tygodnia",
  hours: "Godzin",
  minutes: "Minut",
  seconds: "Sekund"
  // meridiem: 'Meridiem',
  // Common
  // empty: 'Empty',
};
var plPL = getPickersLocalization(plPLPickers);

// node_modules/@mui/x-date-pickers/esm/locales/ptBR.js
var timeViews14 = {
  hours: "horas",
  minutes: "minutos",
  seconds: "segundos",
  meridiem: "meridiano"
};
var ptBRPickers = {
  // Calendar navigation
  previousMonth: "Mês anterior",
  nextMonth: "Próximo mês",
  // View navigation
  openPreviousView: "Abrir seleção anterior",
  openNextView: "Abrir próxima seleção",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "Seleção de ano está aberta, alternando para seleção de calendário" : "Seleção de calendários está aberta, alternando para seleção de ano",
  // DateRange labels
  start: "Início",
  end: "Fim",
  startDate: "Data de início",
  startTime: "Hora de início",
  endDate: "Data de Término",
  endTime: "Hora de Término",
  // Action bar
  cancelButtonLabel: "Cancelar",
  clearButtonLabel: "Limpar",
  okButtonLabel: "OK",
  todayButtonLabel: "Hoje",
  nextStepButtonLabel: "Próximo",
  // Toolbar titles
  datePickerToolbarTitle: "Selecione a data",
  dateTimePickerToolbarTitle: "Selecione data e hora",
  timePickerToolbarTitle: "Selecione a hora",
  dateRangePickerToolbarTitle: "Selecione o intervalo entre datas",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Selecione ${timeViews14[view]}. ${!formattedTime ? "Hora não selecionada" : `Selecionado a hora ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} horas`,
  minutesClockNumberText: (minutes) => `${minutes} minutos`,
  secondsClockNumberText: (seconds) => `${seconds} segundos`,
  // Digital clock labels
  selectViewText: (view) => `Selecione ${timeViews14[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Número da semana",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Semana ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => {
    return formattedDate ? `Escolha uma data, data selecionada ${formattedDate}` : "Escolha uma data";
  },
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Escolha uma hora, hora selecionada ${formattedTime}` : "Escolha uma hora",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Limpar valor",
  // Table labels
  timeTableLabel: "escolha uma hora",
  dateTableLabel: "escolha uma data",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "A".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "SSSS" : "SS",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Ano",
  month: "Mês",
  day: "Dia",
  weekDay: "Dia da Semana",
  hours: "Horas",
  minutes: "Minutos",
  seconds: "Segundos",
  meridiem: "Meio dia",
  // Common
  empty: "Vazio"
};
var ptBR = getPickersLocalization(ptBRPickers);

// node_modules/@mui/x-date-pickers/esm/locales/ptPT.js
var timeViews15 = {
  hours: "horas",
  minutes: "minutos",
  seconds: "segundos",
  meridiem: "meridiano"
};
var ptPTPickers = {
  // Calendar navigation
  previousMonth: "Mês anterior",
  nextMonth: "Próximo mês",
  // View navigation
  openPreviousView: "Abrir seleção anterior",
  openNextView: "Abrir próxima seleção",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "A seleção do ano está aberta, altere para a seleção do calendário" : "A seleção do calendários está aberta, altere para a seleção do ano",
  // DateRange labels
  start: "Início",
  end: "Fim",
  startDate: "Data de início",
  startTime: "Hora de início",
  endDate: "Data de fim",
  endTime: "Hora de fim",
  // Action bar
  cancelButtonLabel: "Cancelar",
  clearButtonLabel: "Limpar",
  okButtonLabel: "OK",
  todayButtonLabel: "Hoje",
  nextStepButtonLabel: "Próximo",
  // Toolbar titles
  datePickerToolbarTitle: "Selecione a data",
  dateTimePickerToolbarTitle: "Selecione a data e a hora",
  timePickerToolbarTitle: "Selecione a hora",
  dateRangePickerToolbarTitle: "Selecione o intervalo de datas",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Selecione ${timeViews15[view]}. ${!formattedTime ? "Hora não selecionada" : `Selecionado a hora ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} horas`,
  minutesClockNumberText: (minutes) => `${minutes} minutos`,
  secondsClockNumberText: (seconds) => `${seconds} segundos`,
  // Digital clock labels
  selectViewText: (view) => `Selecione ${timeViews15[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Número da semana",
  calendarWeekNumberHeaderText: "N.º",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Semana ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Escolha uma data, a data selecionada é ${formattedDate}` : "Escolha uma data",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Escolha uma hora, a hora selecionada é ${formattedTime}` : "Escolha uma hora",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Limpar valor",
  // Table labels
  timeTableLabel: "escolha uma hora",
  dateTableLabel: "escolha uma data",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "A".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "SSSS" : "SS",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Ano",
  month: "Mês",
  day: "Dia",
  weekDay: "Dia da Semana",
  hours: "Horas",
  minutes: "Minutos",
  seconds: "Segundos",
  meridiem: "Meridiano",
  // Common
  empty: "Vazio"
};
var ptPT = getPickersLocalization(ptPTPickers);

// node_modules/@mui/x-date-pickers/esm/locales/roRO.js
var timeViews16 = {
  hours: "Ore",
  minutes: "Minute",
  seconds: "Secunde",
  meridiem: "Meridiane"
};
var roROPickers = {
  // Calendar navigation
  previousMonth: "Luna anterioară",
  nextMonth: "Luna următoare",
  // View navigation
  openPreviousView: "Deschideți vizualizarea anterioară",
  openNextView: "Deschideți vizualizarea următoare",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "Vizualizarea anuală este deschisă, comutați la vizualizarea calendarului" : "Vizualizarea calendarului este deschisă, comutați la vizualizarea anuală",
  // DateRange labels
  start: "Început",
  end: "Sfârșit",
  startDate: "Data de început",
  startTime: "Ora de început",
  endDate: "Data de sfârșit",
  endTime: "Ora de sfârșit",
  // Action bar
  cancelButtonLabel: "Anulare",
  clearButtonLabel: "Ștergere",
  okButtonLabel: "OK",
  todayButtonLabel: "Astăzi",
  nextStepButtonLabel: "Următoare",
  // Toolbar titles
  datePickerToolbarTitle: "Selectați data",
  dateTimePickerToolbarTitle: "Selectați data și ora",
  timePickerToolbarTitle: "Selectați ora",
  dateRangePickerToolbarTitle: "Selectați intervalul de date",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Selectați ${timeViews16[view] ?? view}. ${!formattedTime ? "Nicio oră selectată" : `Ora selectată este ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} ${timeViews16.hours}`,
  minutesClockNumberText: (minutes) => `${minutes} ${timeViews16.minutes}`,
  secondsClockNumberText: (seconds) => `${seconds}  ${timeViews16.seconds}`,
  // Digital clock labels
  selectViewText: (view) => `Selectați ${timeViews16[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Număr săptămână",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Săptămâna ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Selectați data, data selectată este ${formattedDate}` : "Selectați data",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Selectați ora, ora selectată este ${formattedTime}` : "Selectați ora",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Golire conținut",
  // Table labels
  timeTableLabel: "Selectați ora",
  dateTableLabel: "Selectați data",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "A".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "LLLL" : "LL",
  fieldDayPlaceholder: () => "ZZ",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "ZZZZ" : "ZZ",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "An",
  month: "Luna",
  day: "Ziua",
  weekDay: "Ziua saptămânii",
  hours: "Ore",
  minutes: "Minute",
  seconds: "Secunde",
  meridiem: "Meridiem",
  // Common
  empty: "Gol"
};
var roRO = getPickersLocalization(roROPickers);

// node_modules/@mui/x-date-pickers/esm/locales/ruRU.js
var timeViews17 = {
  hours: "часы",
  minutes: "минуты",
  seconds: "секунды",
  meridiem: "меридием"
};
var ruRUPickers = {
  // Calendar navigation
  previousMonth: "Предыдущий месяц",
  nextMonth: "Следующий месяц",
  // View navigation
  openPreviousView: "Открыть предыдущий вид",
  openNextView: "Открыть следующий вид",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "открыт годовой вид, переключить на календарный вид" : "открыт календарный вид, переключить на годовой вид",
  // DateRange labels
  start: "Начало",
  end: "Конец",
  startDate: "Начальная дата",
  startTime: "Начальное время",
  endDate: "Конечная дата",
  endTime: "Конечное время",
  // Action bar
  cancelButtonLabel: "Отмена",
  clearButtonLabel: "Очистить",
  okButtonLabel: "Ок",
  todayButtonLabel: "Сегодня",
  nextStepButtonLabel: "Следующий",
  // Toolbar titles
  datePickerToolbarTitle: "Выбрать дату",
  dateTimePickerToolbarTitle: "Выбрать дату и время",
  timePickerToolbarTitle: "Выбрать время",
  dateRangePickerToolbarTitle: "Выбрать период",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Выбрать ${timeViews17[view]}. ${!formattedTime ? "Время не выбрано" : `Выбрано время ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} часов`,
  minutesClockNumberText: (minutes) => `${minutes} минут`,
  secondsClockNumberText: (seconds) => `${seconds} секунд`,
  // Digital clock labels
  selectViewText: (view) => `Выбрать ${timeViews17[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Номер недели",
  calendarWeekNumberHeaderText: "№",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Неделя ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Выберите дату, выбрана дата ${formattedDate}` : "Выберите дату",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Выберите время, выбрано время ${formattedTime}` : "Выберите время",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Очистить значение",
  // Table labels
  timeTableLabel: "выбрать время",
  dateTableLabel: "выбрать дату",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Г".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "ММММ" : "ММ",
  fieldDayPlaceholder: () => "ДД",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "ДДДД" : "ДД",
  fieldHoursPlaceholder: () => "чч",
  fieldMinutesPlaceholder: () => "мм",
  fieldSecondsPlaceholder: () => "сс",
  fieldMeridiemPlaceholder: () => "(д|п)п",
  // View names
  year: "Год",
  month: "Месяц",
  day: "День",
  weekDay: "День недели",
  hours: "Часы",
  minutes: "Минуты",
  seconds: "Секунды",
  meridiem: "Меридием",
  // Common
  empty: "Пустой"
};
var ruRU = getPickersLocalization(ruRUPickers);

// node_modules/@mui/x-date-pickers/esm/locales/skSK.js
var timeViews18 = {
  hours: "Hodiny",
  minutes: "Minúty",
  seconds: "Sekundy",
  meridiem: "Popoludnie"
};
var skSKPickers = {
  // Calendar navigation
  previousMonth: "Predchádzajúci mesiac",
  nextMonth: "Ďalší mesiac",
  // View navigation
  openPreviousView: "Otvoriť predchádzajúce zobrazenie",
  openNextView: "Otvoriť ďalšie zobrazenie",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "ročné zobrazenie otvorené, prepnite do zobrazenia kalendára" : "zobrazenie kalendára otvorené, prepnite do zobrazenia roka",
  // DateRange labels
  start: "Začiatok",
  end: "Koniec",
  // startDate: 'Start date',
  // startTime: 'Start time',
  // endDate: 'End date',
  // endTime: 'End time',
  // Action bar
  cancelButtonLabel: "Zrušiť",
  clearButtonLabel: "Vymazať",
  okButtonLabel: "Potvrdiť",
  todayButtonLabel: "Dnes",
  nextStepButtonLabel: "Ďalší",
  // Toolbar titles
  datePickerToolbarTitle: "Vyberte dátum",
  dateTimePickerToolbarTitle: "Vyberte dátum a čas",
  timePickerToolbarTitle: "Vyberte čas",
  dateRangePickerToolbarTitle: "Vyberete rozmedzie dátumov",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `${timeViews18[view] ?? view} vybraný. ${!formattedTime ? "Nie je vybraný čas" : `Vybraný čas je ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} hodín`,
  minutesClockNumberText: (minutes) => `${minutes} minút`,
  secondsClockNumberText: (seconds) => `${seconds} sekúnd`,
  // Digital clock labels
  selectViewText: (view) => `Vyberte ${timeViews18[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Týždeň v roku",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `${weekNumber} týždeň v roku`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Vyberte dátum, vybraný dátum je ${formattedDate}` : "Vyberte dátum",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Vyberte čas, vybraný čas je ${formattedTime}` : "Vyberte čas",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  // fieldClearLabel: 'Clear',
  // Table labels
  timeTableLabel: "vyberte čas",
  dateTableLabel: "vyberte dátum",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa"
  // View names
  // year: 'Year',
  // month: 'Month',
  // day: 'Day',
  // weekDay: 'Week day',
  // hours: 'Hours',
  // minutes: 'Minutes',
  // seconds: 'Seconds',
  // meridiem: 'Meridiem',
  // Common
  // empty: 'Empty',
};
var skSK = getPickersLocalization(skSKPickers);

// node_modules/@mui/x-date-pickers/esm/locales/svSE.js
var timeViews19 = {
  hours: "timmar",
  minutes: "minuter",
  seconds: "sekunder",
  meridiem: "meridiem"
};
var svSEPickers = {
  // Calendar navigation
  previousMonth: "Föregående månad",
  nextMonth: "Nästa månad",
  // View navigation
  openPreviousView: "Öppna föregående vy",
  openNextView: "Öppna nästa vy",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "årsvyn är öppen, byt till kalendervy" : "kalendervyn är öppen, byt till årsvy",
  // DateRange labels
  start: "Start",
  end: "Slut",
  startDate: "Startdatum",
  startTime: "Starttid",
  endDate: "Slutdatum",
  endTime: "Sluttid",
  // Action bar
  cancelButtonLabel: "Avbryt",
  clearButtonLabel: "Rensa",
  okButtonLabel: "OK",
  todayButtonLabel: "Idag",
  nextStepButtonLabel: "Nästa",
  // Toolbar titles
  datePickerToolbarTitle: "Välj datum",
  dateTimePickerToolbarTitle: "Välj datum & tid",
  timePickerToolbarTitle: "Välj tid",
  dateRangePickerToolbarTitle: "Välj datumintervall",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Välj ${timeViews19[view]}. ${!formattedTime ? "Ingen tid vald" : `Vald tid är ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} timmar`,
  minutesClockNumberText: (minutes) => `${minutes} minuter`,
  secondsClockNumberText: (seconds) => `${seconds} sekunder`,
  // Digital clock labels
  selectViewText: (view) => `Välj ${timeViews19[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Vecka nummer",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Vecka ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Välj datum, valt datum är ${formattedDate}` : "Välj datum",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Välj tid, vald tid är ${formattedTime}` : "Välj tid",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Rensa värde",
  // Table labels
  timeTableLabel: "välj tid",
  dateTableLabel: "välj datum",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Å".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "tt",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "År",
  month: "Månad",
  day: "Dag",
  weekDay: "Veckodag",
  hours: "Timmar",
  minutes: "Minuter",
  seconds: "Sekunder",
  meridiem: "Meridiem",
  // Common
  empty: "Tom"
};
var svSE = getPickersLocalization(svSEPickers);

// node_modules/@mui/x-date-pickers/esm/locales/trTR.js
var timeViews20 = {
  hours: "saat",
  minutes: "dakika",
  seconds: "saniye",
  meridiem: "öğleden sonra"
};
var trTRPickers = {
  // Calendar navigation
  previousMonth: "Önceki ay",
  nextMonth: "Sonraki ay",
  // View navigation
  openPreviousView: "Sonraki görünüm",
  openNextView: "Önceki görünüm",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "yıl görünümü açık, takvim görünümüne geç" : "takvim görünümü açık, yıl görünümüne geç",
  // DateRange labels
  start: "Başlangıç",
  end: "Bitiş",
  // startDate: 'Start date',
  // startTime: 'Start time',
  // endDate: 'End date',
  // endTime: 'End time',
  // Action bar
  cancelButtonLabel: "iptal",
  clearButtonLabel: "Temizle",
  okButtonLabel: "Tamam",
  todayButtonLabel: "Bugün",
  nextStepButtonLabel: "Sonraki",
  // Toolbar titles
  datePickerToolbarTitle: "Tarih Seç",
  dateTimePickerToolbarTitle: "Tarih & Saat seç",
  timePickerToolbarTitle: "Saat seç",
  dateRangePickerToolbarTitle: "Tarih aralığı seçin",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `${timeViews20[view]} seç.  ${!formattedTime ? "Zaman seçilmedi" : `Seçilen zaman: ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} saat`,
  minutesClockNumberText: (minutes) => `${minutes} dakika`,
  secondsClockNumberText: (seconds) => `${seconds} saniye`,
  // Digital clock labels
  selectViewText: (view) => `Seç ${timeViews20[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Hafta numarası",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Hafta ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Tarih seçin, seçilen tarih: ${formattedDate}` : "Tarih seç",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Saat seçin, seçilen saat: ${formattedTime}` : "Saat seç",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  // fieldClearLabel: 'Clear',
  // Table labels
  timeTableLabel: "saat seç",
  dateTableLabel: "tarih seç",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "AAA" : "AA",
  fieldDayPlaceholder: () => "GG",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "HHH" : "HH",
  fieldHoursPlaceholder: () => "ss",
  fieldMinutesPlaceholder: () => "dd",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa"
  // View names
  // year: 'Year',
  // month: 'Month',
  // day: 'Day',
  // weekDay: 'Week day',
  // hours: 'Hours',
  // minutes: 'Minutes',
  // seconds: 'Seconds',
  // meridiem: 'Meridiem',
  // Common
  // empty: 'Empty',
};
var trTR = getPickersLocalization(trTRPickers);

// node_modules/@mui/x-date-pickers/esm/locales/ukUA.js
var timeViews21 = {
  hours: "годин",
  minutes: "хвилин",
  seconds: "секунд",
  meridiem: "Південь"
};
var ukUAPickers = {
  // Calendar navigation
  previousMonth: "Попередній місяць",
  nextMonth: "Наступний місяць",
  // View navigation
  openPreviousView: "Відкрити попередній вигляд",
  openNextView: "Відкрити наступний вигляд",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "річний вигляд відкрито, перейти до календарного вигляду" : "календарний вигляд відкрито, перейти до річного вигляду",
  // DateRange labels
  start: "Початок",
  end: "Кінець",
  startDate: "День початку",
  startTime: "Час початку",
  endDate: "День закінчення",
  endTime: "Час закінчення",
  // Action bar
  cancelButtonLabel: "Відміна",
  clearButtonLabel: "Очистити",
  okButtonLabel: "OK",
  todayButtonLabel: "Сьогодні",
  nextStepButtonLabel: "Наступний",
  // Toolbar titles
  datePickerToolbarTitle: "Вибрати дату",
  dateTimePickerToolbarTitle: "Вибрати дату і час",
  timePickerToolbarTitle: "Вибрати час",
  dateRangePickerToolbarTitle: "Вибрати календарний період",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Вибрати ${timeViews21[view]}. ${!formattedTime ? "Час не вибраний" : `Вибрано час ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} годин`,
  minutesClockNumberText: (minutes) => `${minutes} хвилин`,
  secondsClockNumberText: (seconds) => `${seconds} секунд`,
  // Digital clock labels
  selectViewText: (view) => `Вибрати ${timeViews21[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Номер тижня",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Тиждень ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Оберіть дату, обрана дата  ${formattedDate}` : "Оберіть дату",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Оберіть час, обраний час  ${formattedTime}` : "Оберіть час",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Очистити дані",
  // Table labels
  timeTableLabel: "оберіть час",
  dateTableLabel: "оберіть дату",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Рік",
  month: "Місяць",
  day: "День",
  weekDay: "День тижня",
  hours: "Годин",
  minutes: "Хвилин",
  seconds: "Секунд",
  meridiem: "Меридіем",
  // Common
  empty: "Порожній"
};
var ukUA = getPickersLocalization(ukUAPickers);

// node_modules/@mui/x-date-pickers/esm/locales/urPK.js
var timeViews22 = {
  hours: "گھنٹے",
  minutes: "منٹ",
  seconds: "سیکنڈ",
  meridiem: "میریڈیم"
};
var urPKPickers = {
  // Calendar navigation
  previousMonth: "پچھلا مہینہ",
  nextMonth: "اگلا مہینہ",
  // View navigation
  openPreviousView: "پچھلا ویو کھولیں",
  openNextView: "اگلا ویو کھولیں",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "سال والا ویو کھلا ہے۔ کیلنڈر والا ویو کھولیں" : "کیلنڈر والا ویو کھلا ہے۔ سال والا ویو کھولیں",
  // DateRange labels
  start: "شروع",
  end: "ختم",
  // startDate: 'Start date',
  // startTime: 'Start time',
  // endDate: 'End date',
  // endTime: 'End time',
  // Action bar
  cancelButtonLabel: "کینسل",
  clearButtonLabel: "کلئیر",
  okButtonLabel: "اوکے",
  todayButtonLabel: "آج",
  nextStepButtonLabel: "مہینہ",
  // Toolbar titles
  datePickerToolbarTitle: "تاریخ منتخب کریں",
  dateTimePickerToolbarTitle: "تاریخ اور وقت منتخب کریں",
  timePickerToolbarTitle: "وقت منتخب کریں",
  dateRangePickerToolbarTitle: "تاریخوں کی رینج منتخب کریں",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `${timeViews22[view]} منتخب کریں ${!formattedTime ? "کوئی وقت منتخب نہیں" : `منتخب وقت ہے ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} گھنٹے`,
  minutesClockNumberText: (minutes) => `${minutes} منٹ`,
  secondsClockNumberText: (seconds) => `${seconds} سیکنڈ`,
  // Digital clock labels
  selectViewText: (view) => `${timeViews22[view]} منتخب کریں`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "ہفتہ نمبر",
  calendarWeekNumberHeaderText: "نمبر",
  calendarWeekNumberAriaLabelText: (weekNumber) => `ہفتہ ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `تاریخ منتخب کریں، منتخب شدہ تاریخ ہے ${formattedDate}` : "تاریخ منتخب کریں",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `وقت منتخب کریں، منتخب شدہ وقت ہے ${formattedTime}` : "وقت منتخب کریں",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  // fieldClearLabel: 'Clear',
  // Table labels
  timeTableLabel: "وقت منتخب کریں",
  dateTableLabel: "تاریخ منتخب کریں"
  // Field section placeholders
  // fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),
  // fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',
  // fieldDayPlaceholder: () => 'DD',
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  // fieldHoursPlaceholder: () => 'hh',
  // fieldMinutesPlaceholder: () => 'mm',
  // fieldSecondsPlaceholder: () => 'ss',
  // fieldMeridiemPlaceholder: () => 'aa',
  // View names
  // year: 'Year',
  // month: 'Month',
  // day: 'Day',
  // weekDay: 'Week day',
  // hours: 'Hours',
  // minutes: 'Minutes',
  // seconds: 'Seconds',
  // meridiem: 'Meridiem',
  // Common
  // empty: 'Empty',
};
var urPK = getPickersLocalization(urPKPickers);

// node_modules/@mui/x-date-pickers/esm/locales/viVN.js
var views13 = {
  hours: "giờ",
  minutes: "phút",
  seconds: "giây",
  meridiem: "buổi"
};
var viVNPickers = {
  // Calendar navigation
  previousMonth: "Tháng trước",
  nextMonth: "Tháng sau",
  // View navigation
  openPreviousView: "Mở xem trước",
  openNextView: "Mở xem sau",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "đang mở xem năm, chuyển sang xem lịch" : "đang mở xem lịch, chuyển sang xem năm",
  // DateRange labels
  start: "Bắt đầu",
  end: "Kết thúc",
  startDate: "Ngày bắt đầu",
  startTime: "Thời gian bắt đầu",
  endDate: "Ngày kết thúc",
  endTime: "Thời gian kết thúc",
  // Action bar
  cancelButtonLabel: "Hủy",
  clearButtonLabel: "Xóa",
  okButtonLabel: "OK",
  todayButtonLabel: "Hôm nay",
  nextStepButtonLabel: "Sau",
  // Toolbar titles
  datePickerToolbarTitle: "Chọn ngày",
  dateTimePickerToolbarTitle: "Chọn ngày và giờ",
  timePickerToolbarTitle: "Chọn giờ",
  dateRangePickerToolbarTitle: "Chọn khoảng ngày",
  // timeRangePickerToolbarTitle: 'Select time range',
  // Clock labels
  clockLabelText: (view, formattedTime) => `Chọn ${views13[view]}. ${!formattedTime ? "Không có giờ được chọn" : `Giờ được chọn là ${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours} giờ`,
  minutesClockNumberText: (minutes) => `${minutes} phút`,
  secondsClockNumberText: (seconds) => `${seconds} giây`,
  // Digital clock labels
  selectViewText: (view) => `Chọn ${views13[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "Số tuần",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `Tuần ${weekNumber}`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `Chọn ngày, ngày đã chọn là ${formattedDate}` : "Chọn ngày",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `Chọn giờ, giờ đã chọn là ${formattedTime}` : "Chọn giờ",
  // openRangePickerDialogue: formattedRange => formattedRange ? `Choose range, selected range is ${formattedRange}` : 'Choose range',
  fieldClearLabel: "Xóa giá trị",
  // Table labels
  timeTableLabel: "chọn giờ",
  dateTableLabel: "chọn ngày",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "Năm",
  month: "Tháng",
  day: "Ngày",
  weekDay: "Thứ",
  hours: "Giờ",
  minutes: "Phút",
  seconds: "Giây",
  meridiem: "Buổi",
  // Common
  empty: "Trống"
};
var viVN = getPickersLocalization(viVNPickers);

// node_modules/@mui/x-date-pickers/esm/locales/zhCN.js
var views14 = {
  hours: "小时",
  minutes: "分钟",
  seconds: "秒",
  meridiem: "十二小时制"
};
var zhCNPickers = {
  // Calendar navigation
  previousMonth: "上个月",
  nextMonth: "下个月",
  // View navigation
  openPreviousView: "前一个视图",
  openNextView: "下一个视图",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "年视图已打开，切换为日历视图" : "日历视图已打开，切换为年视图",
  // DateRange labels
  start: "开始",
  end: "结束",
  startDate: "开始日期",
  startTime: "开始时间",
  endDate: "结束日期",
  endTime: "结束时间",
  // Action bar
  cancelButtonLabel: "取消",
  clearButtonLabel: "清除",
  okButtonLabel: "确认",
  todayButtonLabel: "今天",
  nextStepButtonLabel: "下个",
  // Toolbar titles
  datePickerToolbarTitle: "选择日期",
  dateTimePickerToolbarTitle: "选择日期和时间",
  timePickerToolbarTitle: "选择时间",
  dateRangePickerToolbarTitle: "选择日期范围",
  timeRangePickerToolbarTitle: "选择时间范围",
  // Clock labels
  clockLabelText: (view, formattedTime) => `选择 ${views14[view]}. ${!formattedTime ? "未选择时间" : `已选择${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours}小时`,
  minutesClockNumberText: (minutes) => `${minutes}分钟`,
  secondsClockNumberText: (seconds) => `${seconds}秒`,
  // Digital clock labels
  selectViewText: (view) => `选择 ${views14[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "周数",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `第${weekNumber}周`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `选择日期，已选择${formattedDate}` : "选择日期",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `选择时间，已选择${formattedTime}` : "选择时间",
  openRangePickerDialogue: (formattedRange) => formattedRange ? `选择范围，已选范围是 ${formattedRange}` : "选择范围",
  fieldClearLabel: "清除",
  // Table labels
  timeTableLabel: "选择时间",
  dateTableLabel: "选择日期",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "年份",
  month: "月份",
  day: "日期",
  weekDay: "星期",
  hours: "时",
  minutes: "分",
  seconds: "秒",
  meridiem: "十二小时制",
  // Common
  empty: "空"
};
var zhCN = getPickersLocalization(zhCNPickers);

// node_modules/@mui/x-date-pickers/esm/locales/zhHK.js
var views15 = {
  hours: "小時",
  minutes: "分鐘",
  seconds: "秒",
  meridiem: "子午線"
};
var zhHKPickers = {
  // Calendar navigation
  previousMonth: "上個月",
  nextMonth: "下個月",
  // View navigation
  openPreviousView: "前一個檢視表",
  openNextView: "下一個檢視表",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "年份檢視表已打開，切換以檢視日曆" : "日曆檢視表已打開，切換以檢視年份",
  // DateRange labels
  start: "開始",
  end: "結束",
  startDate: "開始日期",
  startTime: "開始時間",
  endDate: "結束日期",
  endTime: "結束時間",
  // Action bar
  cancelButtonLabel: "取消",
  clearButtonLabel: "清除",
  okButtonLabel: "確認",
  todayButtonLabel: "今日",
  nextStepButtonLabel: "下個",
  // Toolbar titles
  datePickerToolbarTitle: "選擇日期",
  dateTimePickerToolbarTitle: "選擇日期和時間",
  timePickerToolbarTitle: "選擇時間",
  dateRangePickerToolbarTitle: "選擇日期範圍",
  timeRangePickerToolbarTitle: "選擇時間範圍",
  // Clock labels
  clockLabelText: (view, formattedTime) => `選擇 ${views15[view]}. ${!formattedTime ? "未選擇時間" : `已選擇${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours}小時`,
  minutesClockNumberText: (minutes) => `${minutes}分鐘`,
  secondsClockNumberText: (seconds) => `${seconds}秒`,
  // Digital clock labels
  selectViewText: (view) => `選擇 ${views15[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "週數",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `第${weekNumber}週`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `選擇日期，已選擇${formattedDate}` : "選擇日期",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `選擇時間，已選擇${formattedTime}` : "選擇時間",
  openRangePickerDialogue: (formattedRange) => formattedRange ? `選擇範圍，已選擇嘅範圍係 ${formattedRange}` : "選擇範圍",
  fieldClearLabel: "清除",
  // Table labels
  timeTableLabel: "選擇時間",
  dateTableLabel: "選擇日期",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "年",
  month: "月",
  day: "日",
  weekDay: "星期",
  hours: "小時",
  minutes: "分鐘",
  seconds: "秒",
  meridiem: "子午線",
  // Common
  empty: "空值"
};
var zhHK = getPickersLocalization(zhHKPickers);

// node_modules/@mui/x-date-pickers/esm/locales/zhTW.js
var views16 = {
  hours: "小時",
  minutes: "分鐘",
  seconds: "秒",
  meridiem: "十二小時制"
};
var zhTWPickers = {
  // Calendar navigation
  previousMonth: "上個月",
  nextMonth: "下個月",
  // View navigation
  openPreviousView: "前一個視圖",
  openNextView: "下一個視圖",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "年視圖已打開，切換為日曆視圖" : "日曆視圖已打開，切換為年視圖",
  // DateRange labels
  start: "開始",
  end: "結束",
  startDate: "開始日期",
  startTime: "開始時間",
  endDate: "結束日期",
  endTime: "結束時間",
  // Action bar
  cancelButtonLabel: "取消",
  clearButtonLabel: "清除",
  okButtonLabel: "確認",
  todayButtonLabel: "今天",
  nextStepButtonLabel: "下個",
  // Toolbar titles
  datePickerToolbarTitle: "選擇日期",
  dateTimePickerToolbarTitle: "選擇日期和時間",
  timePickerToolbarTitle: "選擇時間",
  dateRangePickerToolbarTitle: "選擇日期範圍",
  timeRangePickerToolbarTitle: "選擇時間範圍",
  // Clock labels
  clockLabelText: (view, formattedTime) => `選擇 ${views16[view]}. ${!formattedTime ? "未選擇時間" : `已選擇${formattedTime}`}`,
  hoursClockNumberText: (hours) => `${hours}小時`,
  minutesClockNumberText: (minutes) => `${minutes}分鐘`,
  secondsClockNumberText: (seconds) => `${seconds}秒`,
  // Digital clock labels
  selectViewText: (view) => `選擇 ${views16[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: "週數",
  calendarWeekNumberHeaderText: "#",
  calendarWeekNumberAriaLabelText: (weekNumber) => `第${weekNumber}週`,
  calendarWeekNumberText: (weekNumber) => `${weekNumber}`,
  // Open Picker labels
  openDatePickerDialogue: (formattedDate) => formattedDate ? `選擇日期，已選擇${formattedDate}` : "選擇日期",
  openTimePickerDialogue: (formattedTime) => formattedTime ? `選擇時間，已選擇${formattedTime}` : "選擇時間",
  openRangePickerDialogue: (formattedRange) => formattedRange ? `選擇範圍，已選擇的範圍是 ${formattedRange}` : "選擇範圍",
  fieldClearLabel: "清除",
  // Table labels
  timeTableLabel: "選擇時間",
  dateTableLabel: "選擇日期",
  // Field section placeholders
  fieldYearPlaceholder: (params) => "Y".repeat(params.digitAmount),
  fieldMonthPlaceholder: (params) => params.contentType === "letter" ? "MMMM" : "MM",
  fieldDayPlaceholder: () => "DD",
  fieldWeekDayPlaceholder: (params) => params.contentType === "letter" ? "EEEE" : "EE",
  fieldHoursPlaceholder: () => "hh",
  fieldMinutesPlaceholder: () => "mm",
  fieldSecondsPlaceholder: () => "ss",
  fieldMeridiemPlaceholder: () => "aa",
  // View names
  year: "年份",
  month: "月份",
  day: "日期",
  weekDay: "星期",
  hours: "時",
  minutes: "分",
  seconds: "秒",
  meridiem: "十二小時制",
  // Common
  empty: "空"
};
var zhTW = getPickersLocalization(zhTWPickers);
export {
  DEFAULT_LOCALE,
  beBY,
  bgBG,
  bnBD,
  caES,
  csCZ,
  daDK,
  deDE,
  elGR,
  enUS,
  esES,
  eu,
  faIR,
  fiFI,
  frFR,
  heIL,
  hrHR,
  huHU,
  isIS,
  itIT,
  jaJP,
  koKR,
  kzKZ,
  mk,
  nbNO,
  nlNL,
  nnNO,
  plPL,
  ptBR,
  ptPT,
  roRO,
  ruRU,
  skSK,
  svSE,
  trTR,
  ukUA,
  urPK,
  viVN,
  zhCN,
  zhHK,
  zhTW
};
//# sourceMappingURL=@mui_x-date-pickers_locales.js.map
