import express from 'express';
import userRoutes from "./userRoutes.js";
import dressRoutes from "./dressRoutes.js"; // Changed from carRoutes
import bookingRoutes from "./bookingRoutes.js";
import locationRoutes from "./locationRoutes.js";
import notificationRoutes from "./notificationRoutes.js"; // import paymentRoutes from './paymentRoutes'
const router = express.Router();
// Register all routes
router.use(userRoutes);
router.use(dressRoutes); // Changed from carRoutes
router.use(bookingRoutes);
router.use(locationRoutes);
router.use(notificationRoutes);
// router.use(paymentRoutes) // Commented out payment routes
export default router;