import{u as e,j as t,e as s,R as o,a,J as r,z as i,P as n,B as l}from"../entries/index-xsXxT3-W.js";import{r as m}from"./router-BtYqujaw.js";import{L as c}from"./Layout-DaeN7D4t.js";import{s as p,C as d,P as u,a as j}from"./ParkingSpotEditList-52LgTzWs.js";import{a as h,v as f,b as g}from"./LocationService-6NvQT9iL.js";import{A as x}from"./Avatar-CvDHTACZ.js";import{S as v}from"./SimpleBackdrop-CqsJhYJ4.js";import{P as S}from"./Paper-C-atefOs.js";import{F as C,I as N}from"./InputLabel-C8rcdOGQ.js";import{I as A}from"./Input-D1AdR9CM.js";import{F as I}from"./FormHelperText-DDZ4BMA4.js";import{B as L}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./CountryService-CPWL_VJK.js";import"./MultipleSelect-DovAF4K6.js";import"./Autocomplete-CWN5GAd4.js";import"./OutlinedInput-BX8yFQbF.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-DiTut-u0.js";import"./TextField-D_yQOTzE.js";import"./Backdrop-Czag2Ija.js";import"./Menu-C_-X8cS7.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-DEridHif.js";import"./AccountCircle-DdIeIbov.js";import"./Chip-MGF1mKZa.js";import"./IconButton-CxOCoGF3.js";import"./Avatar-Dvwllg8p.js";import"./Flag-CMGasDVj.js";import"./DressService-DkS6e_O5.js";import"./Badge-zckTAo43.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";const b=()=>{const{user:b}=e(),[E,T]=m.useState(!1),[y,O]=m.useState([]),[P,_]=m.useState([]),[w,B]=m.useState(),[D,F]=m.useState(!1),[G,W]=m.useState(),[U,k]=m.useState(""),[R,z]=m.useState(""),[H,M]=m.useState([]);return t.jsxs(c,{onLoad:()=>{T(!0)},strict:!0,children:[t.jsx("div",{className:"create-location",children:t.jsxs(S,{className:"location-form location-form-wrapper",elevation:10,style:E?{}:{display:"none"},children:[t.jsx("h1",{className:"location-form-title",children:p.NEW_LOCATION_HEADING}),t.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(!w)return void i();let e=!0;const t=r(P);for(let s=0;s<P.length;s+=1)t[s]=!1;for(let s=0;s<y.length;s+=1){const o=y[s],a=200===await f({language:o.language,name:o.name});e=e&&a,a||(t[s]=!0)}if(_(t),e){const e={country:w?._id,latitude:R?Number(R):void 0,longitude:U?Number(U):void 0,names:y,image:G,parkingSpots:H,supplier:n(b)?b?._id:void 0};if(200===await g(e)){const e=r(y);for(let t=0;t<y.length;t+=1)e[t].name="";O(e),W(void 0),B(null),k(""),z(""),M([]),l(p.LOCATION_CREATED)}else i()}}catch(t){i(t)}},children:[t.jsx(x,{type:o.Location,avatar:G,mode:"create",record:null,size:"large",readonly:!1,onBeforeUpload:()=>{F(!0)},onChange:e=>{F(!1),W(e)},color:"disabled",className:"avatar-ctn"}),t.jsx(C,{fullWidth:!0,margin:"dense",children:t.jsx(d,{label:p.COUNTRY,variant:"standard",onChange:e=>{B(e.length>0?e[0]:null)},value:w,required:!0})}),s._LANGUAGES.map(((e,s)=>t.jsxs(C,{fullWidth:!0,margin:"dense",children:[t.jsx(N,{className:"required",children:`${a.NAME} (${e.label})`}),t.jsx(A,{type:"text",value:y[s]&&y[s].name||"",error:P[s],required:!0,onChange:t=>{const o=r(y);o[s]={language:e.code,name:t.target.value},O(o);const a=r(P);a[s]=!1,_(a)},autoComplete:"off"}),t.jsx(I,{error:P[s],children:P[s]&&p.INVALID_LOCATION||""})]},e.code))),t.jsxs(C,{fullWidth:!0,margin:"dense",children:[t.jsx(N,{children:a.LATITUDE}),t.jsx(u,{value:R,onChange:e=>{z(e.target.value)}})]}),t.jsxs(C,{fullWidth:!0,margin:"dense",children:[t.jsx(N,{children:a.LONGITUDE}),t.jsx(u,{value:U,onChange:e=>{k(e.target.value)}})]}),t.jsx(j,{title:p.PARKING_SPOTS,values:H,onAdd:e=>{const t=r(H);t.push(e),M(t)},onUpdate:(e,t)=>{const s=r(H);s[t]=e,M(s)},onDelete:(e,t)=>{const s=r(H);s.splice(t,1),M(s)}}),t.jsxs("div",{className:"buttons",children:[t.jsx(L,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",children:a.CREATE}),t.jsx(L,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{G&&await h(G),window.location.href="/locations"},children:a.CANCEL})]})]})]})}),D&&t.jsx(v,{text:a.PLEASE_WAIT})]})};export{b as default};
