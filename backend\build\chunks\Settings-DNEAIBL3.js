import{b as e,s,j as t,a,B as i,z as o,u as n,U as r,F as l,J as c,aw as m,aB as d,N as p}from"../entries/index-xsXxT3-W.js";import{d as u,r as j}from"./router-BtYqujaw.js";import{u as f,s as h,a as N,z as S}from"./zod-4O8Zwsja.js";import{L as b}from"./Layout-DaeN7D4t.js";import{u as x,g}from"./BankDetailsService-Cq-yeaEa.js";import{S as T}from"./SimpleBackdrop-CqsJhYJ4.js";import{A as E}from"./Avatar-CvDHTACZ.js";import{s as I}from"./bank-details-form-BJmytqf_.js";import{P as A}from"./Paper-C-atefOs.js";import{F as _,I as C}from"./InputLabel-C8rcdOGQ.js";import{I as D}from"./Input-D1AdR9CM.js";import{F as y,S as B}from"./Switch-C5asfh_w.js";import{B as k}from"./Button-BeKLLPpp.js";import{F as w}from"./FormHelperText-DDZ4BMA4.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Czag2Ija.js";import"./useSlot-DiTut-u0.js";import"./DressService-DkS6e_O5.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./SwitchBase-DrUkTXjH.js";const O=new e({fr:{SETTINGS_UPDATED:"Paramètres modifiés avec succès.",NETWORK_SETTINGS:"Paramètres Réseau",SETTINGS_EMAIL_NOTIFICATIONS:"Activer les notifications par email"},en:{SETTINGS_UPDATED:"Settings updated successfully.",NETWORK_SETTINGS:"Network settings",SETTINGS_EMAIL_NOTIFICATIONS:"Enable email notifications"},es:{SETTINGS_UPDATED:"Configuración actualizada correctamente.",NETWORK_SETTINGS:"Configuración de red",SETTINGS_EMAIL_NOTIFICATIONS:"Habilitar notificaciones por correo electrónico"}});s(O);const P=S.object({accountHolder:S.string(),bankName:S.string(),iban:S.string(),swiftBic:S.string(),showBankDetailsPage:S.boolean()}),v=({bankDetails:e,onSubmit:s})=>{const n=u(),{register:r,control:l,handleSubmit:c,formState:{isSubmitting:m},setValue:d}=f({resolver:h(P),mode:"onSubmit"}),{showBankDetailsPage:p}=N({control:l});return j.useEffect((()=>{e&&(d("accountHolder",e.accountHolder),d("bankName",e.bankName),d("iban",e.iban),d("swiftBic",e.swiftBic),d("showBankDetailsPage",e.showBankDetailsPage))}),[e,d]),t.jsx(A,{className:"settings-form settings-form-wrapper",elevation:10,children:t.jsxs("form",{onSubmit:c((async t=>{try{const a={_id:e?._id,accountHolder:t.accountHolder,bankName:t.bankName,iban:t.iban,swiftBic:t.swiftBic,showBankDetailsPage:t.showBankDetailsPage},{status:n,data:r}=await x(a);200===n?(s&&s(r),i(O.SETTINGS_UPDATED)):o()}catch(a){o(a)}})),children:[t.jsx("h1",{className:"settings-form-title",children:I.BANK_DETAILS}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:I.ACCOUNT_HOLDER}),t.jsx(D,{...r("accountHolder"),type:"text",required:!0,autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:I.BANK_NAME}),t.jsx(D,{...r("bankName"),type:"text",required:!0,autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:I.IBAN}),t.jsx(D,{...r("iban"),type:"text",required:!0,autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:I.SWIFT_BIC}),t.jsx(D,{...r("swiftBic"),type:"text",required:!0,autoComplete:"off"})]}),t.jsx(_,{component:"fieldset",children:t.jsx(y,{control:t.jsx(B,{checked:p||!1,onChange:e=>d("showBankDetailsPage",e.target.checked)}),label:I.SHOW_BANK_DETAILS_PAGE})}),t.jsxs("div",{className:"buttons",children:[t.jsx(k,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:m,children:a.SAVE}),t.jsx(k,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>n("/"),children:a.CANCEL})]})]})})},L=S.object({fullName:S.string().min(1),email:S.string().email({message:a.EMAIL_NOT_VALID}),phone:S.string().refine((e=>!e||p.isMobilePhone(e)),{message:a.PHONE_NOT_VALID}).optional(),location:S.string().optional(),bio:S.string().optional()}),W=()=>{const e=u(),{user:s,setUser:p}=n(),[N,S]=j.useState(!1),[x,I]=j.useState(!1),[P,W]=j.useState(!0),[G,H]=j.useState(!1),[F,U]=j.useState(null),{register:q,handleSubmit:z,formState:{errors:M,isSubmitting:R},clearErrors:K,setValue:V}=f({resolver:h(L),mode:"onSubmit"});return t.jsxs(b,{onLoad:async e=>{if(e){p(e),S(l(e)),V("email",e.email),V("fullName",e.fullName),V("phone",e.phone||""),V("location",e.location||""),V("bio",e.bio||""),H(e.enableEmailNotifications||!1);const s=await g();s&&U(s),I(!0),W(!1)}},strict:!0,children:[x&&s&&t.jsxs("div",{className:"settings",children:[t.jsx(A,{className:"settings-form settings-form-wrapper",elevation:10,children:t.jsxs("form",{onSubmit:z((async e=>{try{if(!s)return;const t={_id:s._id,fullName:e.fullName,phone:e.phone||"",location:e.location||"",bio:e.bio||""};200===await m(t)?i(O.SETTINGS_UPDATED):o()}catch(t){o(t)}})),children:[t.jsx(E,{type:s.type,mode:"update",record:s,size:"large",readonly:!1,onBeforeUpload:()=>{W(!0)},onChange:e=>{const t=c(s);t.avatar=e,p(t),W(!1)},hideDelete:!N,color:"disabled",className:"avatar-ctn"}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:a.FULL_NAME}),t.jsx(D,{...q("fullName"),type:"text",required:!0,autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{className:"required",children:a.EMAIL}),t.jsx(D,{...q("email"),type:"text",disabled:!0})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",error:!!M.phone,children:[t.jsx(C,{children:a.PHONE}),t.jsx(D,{...q("phone"),type:"text",autoComplete:"off",onChange:()=>K()}),t.jsx(w,{children:M.phone?.message||""})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{children:a.LOCATION}),t.jsx(D,{...q("location"),type:"text",autoComplete:"off"})]}),t.jsxs(_,{fullWidth:!0,margin:"dense",children:[t.jsx(C,{children:a.BIO}),t.jsx(D,{...q("bio"),type:"text",autoComplete:"off"})]}),t.jsxs("div",{className:"buttons",children:[t.jsx(k,{variant:"contained",className:"btn-primary btn-margin btn-margin-bottom",size:"small",onClick:()=>e("/change-password"),children:a.RESET_PASSWORD}),t.jsx(k,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:R,children:a.SAVE}),t.jsx(k,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>e("/"),children:a.CANCEL})]})]})}),t.jsxs(A,{className:"settings-net settings-net-wrapper",elevation:10,children:[t.jsx("h1",{className:"settings-form-title",children:O.NETWORK_SETTINGS}),t.jsx(_,{component:"fieldset",children:t.jsx(y,{control:t.jsx(B,{checked:G,onChange:async e=>{try{if(s){H(e.target.checked);const t=c(s);t.enableEmailNotifications=e.target.checked;const a={_id:s._id,enableEmailNotifications:t.enableEmailNotifications};200===await d(a)?(p(t),i(O.SETTINGS_UPDATED)):o()}else o()}catch(t){o(t)}}}),label:O.SETTINGS_EMAIL_NOTIFICATIONS})})]}),s.type===r.Admin&&t.jsx(v,{bankDetails:F,onSubmit:e=>U(e)})]}),P&&t.jsx(T,{text:a.PLEASE_WAIT})]})};export{W as default};
