import{f as t,a as n,r as e}from"./router-BtYqujaw.js";import{i as o,aQ as i,j as r,k as a,l as s}from"../entries/index-xsXxT3-W.js";import{l as p,_ as l,n as u,a as c,g as h,s as d,c as f,m,b as g,u as x}from"./Button-BeKLLPpp.js";import{u as E}from"./useSlot-DiTut-u0.js";import{u as y}from"./Paper-C-atefOs.js";var v=function(t){return t.scrollTop},b="unmounted",S="exited",T="entering",k="entered",C="exiting",w=function(e){function o(t,n){var o;o=e.call(this,t,n)||this;var i,r=n&&!n.isMounting?t.enter:t.appear;return o.appearStatus=null,t.in?r?(i=S,o.appearStatus=T):i=k:i=t.unmountOnExit||t.mountOnEnter?b:S,o.state={status:i},o.nextCallback=null,o}p(o,e),o.getDerivedStateFromProps=function(t,n){return t.in&&n.status===b?{status:S}:null};var i=o.prototype;return i.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},i.componentDidUpdate=function(t){var n=null;if(t!==this.props){var e=this.state.status;this.props.in?e!==T&&e!==k&&(n=T):e!==T&&e!==k||(n=C)}this.updateStatus(!1,n)},i.componentWillUnmount=function(){this.cancelNextCallback()},i.getTimeouts=function(){var t,n,e,o=this.props.timeout;return t=n=e=o,null!=o&&"number"!=typeof o&&(t=o.exit,n=o.enter,e=void 0!==o.appear?o.appear:n),{exit:t,enter:n,appear:e}},i.updateStatus=function(n,e){if(void 0===n&&(n=!1),null!==e)if(this.cancelNextCallback(),e===T){if(this.props.unmountOnExit||this.props.mountOnEnter){var o=this.props.nodeRef?this.props.nodeRef.current:t.findDOMNode(this);o&&v(o)}this.performEnter(n)}else this.performExit();else this.props.unmountOnExit&&this.state.status===S&&this.setState({status:b})},i.performEnter=function(n){var e=this,o=this.props.enter,i=this.context?this.context.isMounting:n,r=this.props.nodeRef?[i]:[t.findDOMNode(this),i],a=r[0],s=r[1],p=this.getTimeouts(),l=i?p.appear:p.enter;n||o?(this.props.onEnter(a,s),this.safeSetState({status:T},(function(){e.props.onEntering(a,s),e.onTransitionEnd(l,(function(){e.safeSetState({status:k},(function(){e.props.onEntered(a,s)}))}))}))):this.safeSetState({status:k},(function(){e.props.onEntered(a)}))},i.performExit=function(){var n=this,e=this.props.exit,o=this.getTimeouts(),i=this.props.nodeRef?void 0:t.findDOMNode(this);e?(this.props.onExit(i),this.safeSetState({status:C},(function(){n.props.onExiting(i),n.onTransitionEnd(o.exit,(function(){n.safeSetState({status:S},(function(){n.props.onExited(i)}))}))}))):this.safeSetState({status:S},(function(){n.props.onExited(i)}))},i.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},i.safeSetState=function(t,n){n=this.setNextCallback(n),this.setState(t,n)},i.setNextCallback=function(t){var n=this,e=!0;return this.nextCallback=function(o){e&&(e=!1,n.nextCallback=null,t(o))},this.nextCallback.cancel=function(){e=!1},this.nextCallback},i.onTransitionEnd=function(n,e){this.setNextCallback(e);var o=this.props.nodeRef?this.props.nodeRef.current:t.findDOMNode(this),i=null==n&&!this.props.addEndListener;if(o&&!i){if(this.props.addEndListener){var r=this.props.nodeRef?[this.nextCallback]:[o,this.nextCallback],a=r[0],s=r[1];this.props.addEndListener(a,s)}null!=n&&setTimeout(this.nextCallback,n)}else setTimeout(this.nextCallback,0)},i.render=function(){var t=this.state.status;if(t===b)return null;var e=this.props,o=e.children;e.in,e.mountOnEnter,e.unmountOnExit,e.appear,e.enter,e.exit,e.timeout,e.addEndListener,e.onEnter,e.onEntering,e.onEntered,e.onExit,e.onExiting,e.onExited,e.nodeRef;var i=l(e,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return n.createElement(u.Provider,{value:null},"function"==typeof o?o(t,i):n.cloneElement(n.Children.only(o),i))},o}(n.Component);function R(){}w.contextType=u,w.propTypes={},w.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:R,onEntering:R,onEntered:R,onExit:R,onExiting:R,onExited:R},w.UNMOUNTED=b,w.EXITED=S,w.ENTERING=T,w.ENTERED=k,w.EXITING=C;const N=t=>t.scrollTop;function O(t,n){const{timeout:e,easing:o,style:i={}}=t;return{duration:i.transitionDuration??("number"==typeof e?e:e[n.mode]||0),easing:i.transitionTimingFunction??("object"==typeof o?o[n.mode]:o),delay:i.transitionDelay}}function M(t){return h("MuiTypography",t)}const j=c("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]),B={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},D=i(),P=d("span",{name:"MuiTypography",slot:"Root",overridesResolver:(t,n)=>{const{ownerState:e}=t;return[n.root,e.variant&&n[e.variant],"inherit"!==e.align&&n[`align${s(e.align)}`],e.noWrap&&n.noWrap,e.gutterBottom&&n.gutterBottom,e.paragraph&&n.paragraph]}})(m((({theme:t})=>({margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(t.typography).filter((([t,n])=>"inherit"!==t&&n&&"object"==typeof n)).map((([t,n])=>({props:{variant:t},style:n}))),...Object.entries(t.palette).filter(g()).map((([n])=>({props:{color:n},style:{color:(t.vars||t).palette[n].main}}))),...Object.entries(t.palette?.text||{}).filter((([,t])=>"string"==typeof t)).map((([n])=>({props:{color:`text${s(n)}`},style:{color:(t.vars||t).palette.text[n]}}))),{props:({ownerState:t})=>"inherit"!==t.align,style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:t})=>t.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:t})=>t.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:t})=>t.paragraph,style:{marginBottom:16}}]})))),W={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},L=e.forwardRef((function(t,n){const{color:e,...i}=o({props:t,name:"MuiTypography"}),p=D({...i,...!B[e]&&{color:e}}),{align:l="inherit",className:u,component:c,gutterBottom:h=!1,noWrap:d=!1,paragraph:m=!1,variant:g="body1",variantMapping:x=W,...E}=p,y={...p,align:l,color:e,className:u,component:c,gutterBottom:h,noWrap:d,paragraph:m,variant:g,variantMapping:x},v=c||(m?"p":x[g]||W[g])||"span",b=(t=>{const{align:n,gutterBottom:e,noWrap:o,paragraph:i,variant:r,classes:a}=t,p={root:["root",r,"inherit"!==t.align&&`align${s(n)}`,e&&"gutterBottom",o&&"noWrap",i&&"paragraph"]};return f(p,M,a)})(y);return r.jsx(P,{as:v,ref:n,className:a(b.root,u),...E,ownerState:y,style:{..."inherit"!==l&&{"--Typography-textAlign":l},...E.style}})}));function F(t){return parseInt(e.version,10)>=19?t?.props?.ref||null:t?.ref||null}const I={entering:{opacity:1},entered:{opacity:1}},U=e.forwardRef((function(t,n){const o=y(),i={enter:o.transitions.duration.enteringScreen,exit:o.transitions.duration.leavingScreen},{addEndListener:a,appear:s=!0,children:p,easing:l,in:u,onEnter:c,onEntered:h,onEntering:d,onExit:f,onExited:m,onExiting:g,style:E,timeout:v=i,TransitionComponent:b=w,...S}=t,T=e.useRef(null),k=x(T,F(p),n),C=t=>n=>{if(t){const e=T.current;void 0===n?t(e):t(e,n)}},R=C(d),M=C(((t,n)=>{N(t);const e=O({style:E,timeout:v,easing:l},{mode:"enter"});t.style.webkitTransition=o.transitions.create("opacity",e),t.style.transition=o.transitions.create("opacity",e),c&&c(t,n)})),j=C(h),B=C(g),D=C((t=>{const n=O({style:E,timeout:v,easing:l},{mode:"exit"});t.style.webkitTransition=o.transitions.create("opacity",n),t.style.transition=o.transitions.create("opacity",n),f&&f(t)})),P=C(m);return r.jsx(b,{appear:s,in:u,nodeRef:T,onEnter:M,onEntered:j,onEntering:R,onExit:D,onExited:P,onExiting:B,addEndListener:t=>{a&&a(T.current,t)},timeout:v,...S,children:(t,{ownerState:n,...o})=>e.cloneElement(p,{style:{opacity:0,visibility:"exited"!==t||u?void 0:"hidden",...I[t],...E,...p.props.style},ref:k,...o})})}));function A(t){return h("MuiBackdrop",t)}c("MuiBackdrop",["root","invisible"]);const $=d("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(t,n)=>{const{ownerState:e}=t;return[n.root,e.invisible&&n.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),G=e.forwardRef((function(t,n){const e=o({props:t,name:"MuiBackdrop"}),{children:i,className:s,component:p="div",invisible:l=!1,open:u,components:c={},componentsProps:h={},slotProps:d={},slots:m={},TransitionComponent:g,transitionDuration:x,...y}=e,v={...e,component:p,invisible:l},b=(t=>{const{classes:n,invisible:e}=t;return f({root:["root",e&&"invisible"]},A,n)})(v),S={slots:{transition:g,root:c.Root,...m},slotProps:{...h,...d}},[T,k]=E("root",{elementType:$,externalForwardedProps:S,className:a(b.root,s),ownerState:v}),[C,w]=E("transition",{elementType:U,externalForwardedProps:S,ownerState:v});return r.jsx(C,{in:u,timeout:x,...y,...w,children:r.jsx(T,{"aria-hidden":!0,...k,classes:b,ref:n,children:i})})}));export{G as B,U as F,L as T,w as a,O as b,v as f,F as g,N as r,j as t};
