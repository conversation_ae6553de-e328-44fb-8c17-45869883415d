import{b as e,s,j as a,F as t,a as r,e as o,J as i,K as n,z as l,B as u}from"../entries/index-CEzJO5Xy.js";import{d as c,r as m}from"./router-BtYqujaw.js";import{L as p}from"./Layout-BQBjg4Lf.js";import{s as d}from"./create-country-dW4Zyn1R.js";import{s as f}from"./suppliers-DKbqsTuE.js";import{b as j,v,u as h}from"./CountryService-DnJKuIXr.js";import g from"./NoMatch-jvHCs4x8.js";import{E as U}from"./Error-koMug0_G.js";import{S as y}from"./SimpleBackdrop-Bf3qjF13.js";import{S as N}from"./SupplierBadge-C4yEjxHC.js";import{P as T}from"./Paper-CcwAvfvc.js";import{F as x,a as S,I as A}from"./InputLabel-BbcIE26O.js";import{I as C}from"./Input-BQdee9z7.js";import{F as E}from"./FormHelperText-DFSsjBsL.js";import{B as P}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const D=new e({fr:{UPDATE_COUNTRY:"Modification du pays",COUNTRY_UPDATED:"Pays modifié avec succès."},en:{UPDATE_COUNTRY:"Country update",COUNTRY_UPDATED:"Country updated successfully."},es:{UPDATE_COUNTRY:"Actualización del país",COUNTRY_UPDATED:"País actualizado correctamente."},ar:{UPDATE_COUNTRY:"تحديث البلد",COUNTRY_UPDATED:"تم تحديث البلد بنجاح."}});s(D);const _=()=>{const e=c(),[s,_]=m.useState(),[b,R]=m.useState(!1),[L,O]=m.useState(!1),[Y,w]=m.useState([]),[I,B]=m.useState([]),[z,F]=m.useState(!1),[H,k]=m.useState(!1),[G,W]=m.useState(),[M,q]=m.useState(!1),J=()=>{let e=!1;if(!G||!G.values)return l(),e;for(let s=0;s<Y.length;s+=1)if(Y[s].name!==G.values[s].value){e=!0;break}return q(e),e};return a.jsxs(p,{onLoad:async e=>{if(e&&e.verified){O(!0),_(e);const a=new URLSearchParams(window.location.search);if(a.has("loc")){const r=a.get("loc");if(r&&""!==r)try{const s=await j(r);if(!t(e)&&e._id!==s.supplier?._id)return O(!1),void F(!0);if(s&&s.values){o._LANGUAGES.forEach((e=>{s.values&&!s.values.some((s=>s.language===e.code))&&s.values.push({language:e.code,value:""})}));const e=s.values.map((e=>({language:e.language||"",name:e.value||""})));W(s),w(e),R(!0),O(!1)}else O(!1),F(!0)}catch(s){l(s),O(!1),k(!0),R(!1)}else O(!1),F(!0)}else O(!1),F(!0)}},strict:!0,children:[!H&&!z&&G&&G.values&&a.jsx("div",{className:"update-country",children:a.jsxs(T,{className:"country-form country-form-wrapper",elevation:10,style:b?{}:{display:"none"},children:[a.jsx("h1",{className:"country-form-title",children:D.UPDATE_COUNTRY}),a.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(!G||!G.values)return void l();if(!J())return;let e=!0;const s=i(I);for(let a=0;a<I.length;a+=1)s[a]=!1;for(let a=0;a<Y.length;a+=1){const t=Y[a];if(t.name!==G.values[a].value){const r=200===await v(t);e=e&&r,r||(s[a]=!0)}}if(B(s),e)if(200===await h(G._id,Y)){const e=i(G);for(let s=0;s<Y.length;s+=1){const a=Y[s];e.values[s].value=a.name}W(e),u(D.COUNTRY_UPDATED)}else O(!1),l()}catch(s){l(s)}},children:[t(s)&&G.supplier&&a.jsxs(x,{fullWidth:!0,margin:"dense",children:[a.jsx(S,{children:f.SUPPLIER}),a.jsx(N,{supplier:G.supplier})]}),G.values.map(((e,s)=>a.jsxs(x,{fullWidth:!0,margin:"dense",children:[a.jsx(A,{className:"required",children:`${r.NAME} (${o._LANGUAGES.filter((s=>s.code===e.language))[0].label})`}),a.jsx(C,{type:"text",value:Y[s]&&Y[s].name||"",error:I[s],required:!0,onChange:e=>{const a=i(Y);a[s].name=e.target.value;const t=n(I);t[s]=!1,J(),w(a),B(t)},autoComplete:"off"}),a.jsx(E,{error:I[s],children:I[s]&&d.INVALID_COUNTRY||""})]},e.language))),a.jsxs("div",{className:"buttons",children:[a.jsx(P,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:!M,children:r.SAVE}),a.jsx(P,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>e("/countries"),children:r.CANCEL})]})]})]})}),L&&a.jsx(y,{text:r.PLEASE_WAIT}),H&&a.jsx(U,{}),z&&a.jsx(g,{hideHeader:!0})]})};export{_ as default};
