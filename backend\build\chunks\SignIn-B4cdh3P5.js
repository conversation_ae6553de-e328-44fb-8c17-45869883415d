import{b as s,s as e,c as o,u as r,j as t,a,e as i,d as n,f as c,g as m,h as l,v as d}from"../entries/index-CEzJO5Xy.js";import{d as p,r as S}from"./router-BtYqujaw.js";import{u as I,z as j,s as N}from"./zod-4O8Zwsja.js";import _ from"./Header-Bin7axs4.js";import{E as u}from"./Error-7KgmWHkR.js";import{P as h}from"./Paper-CcwAvfvc.js";import{F as E,I as w}from"./InputLabel-BbcIE26O.js";import{I as g}from"./Input-BQdee9z7.js";import{F as C}from"./FormHelperText-DFSsjBsL.js";import{B as R}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./BankDetailsService-Od7WKGPo.js";import"./Avatar-BtfxKR-8.js";import"./DressService-J0XavNJj.js";import"./LocationService-BtQFgoWL.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-CtA82Ni6.js";import"./Backdrop-Bzn12VyM.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";import"./MenuItem-suKfXYI2.js";import"./Menu-ZU0DMgjT.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-Cay5TZBz.js";import"./listItemTextClasses-DFwCkkgK.js";import"./Toolbar-CNUITE_K.js";import"./IconButton-CnBvmeAK.js";import"./Slide-DrC6paxq.js";import"./ListItem-D1VHRhQp.js";import"./ListItemText-DBn_RuMq.js";import"./Flag-BR6CpE1z.js";import"./useFormControl-B7jXtRD7.js";const x=new s({fr:{SIGN_IN_HEADING:"Connexion",SIGN_IN:"Se connecter",ERROR_IN_SIGN_IN:"E-mail ou mot de passe incorrect.",IS_BLACKLISTED:"Votre compte est suspendu.",RESET_PASSWORD:"Mot de passe oublié ?",STAY_CONNECTED:"Rester connecté"},en:{SIGN_IN_HEADING:"Sign in",SIGN_IN:"Sign in",ERROR_IN_SIGN_IN:"Incorrect email or password.",IS_BLACKLISTED:"Your account is suspended.",RESET_PASSWORD:"Forgot password?",STAY_CONNECTED:"Stay connected"},es:{SIGN_IN_HEADING:"Iniciar sesión",SIGN_IN:"Iniciar sesión",ERROR_IN_SIGN_IN:"Correo electrónico o contraseña incorrectos.",IS_BLACKLISTED:"Tu cuenta está suspendida.",RESET_PASSWORD:"¿Olvidaste tu contraseña?",STAY_CONNECTED:"Mantenerse conectado"},ar:{SIGN_IN_HEADING:"تسجيل الدخول",SIGN_IN:"تسجيل الدخول",ERROR_IN_SIGN_IN:"البريد الإلكتروني أو كلمة المرور غير صحيحة.",IS_BLACKLISTED:"حسابك معلق.",RESET_PASSWORD:"نسيت كلمة المرور؟",STAY_CONNECTED:"البقاء متصلاً"}});e(x);const T=j.object({email:j.string().email({message:a.EMAIL_NOT_VALID}),password:j.string().min(i.PASSWORD_MIN_LENGTH,{message:a.PASSWORD_ERROR}),stayConnected:j.boolean().optional()}),f=()=>{const s=o.c(26),i=p(),{setUser:j,setUserLoaded:f}=r(),[A,D]=S.useState(!1);let b,G;s[0]===Symbol.for("react.memo_cache_sentinel")?(b=N(T),s[0]=b):b=s[0],s[1]===Symbol.for("react.memo_cache_sentinel")?(G={resolver:b,mode:"onSubmit",defaultValues:{stayConnected:!1}},s[1]=G):G=s[1];const{register:O,setValue:L,handleSubmit:v,formState:y,setError:P,clearErrors:B}=I(G),{errors:W,isSubmitting:k}=y;let F;s[2]!==P?(F=()=>{P("root",{message:x.ERROR_IN_SIGN_IN})},s[2]=P,s[3]=F):F=s[3];const H=F;let M;s[4]!==i||s[5]!==P||s[6]!==j||s[7]!==f||s[8]!==H?(M=async s=>{const{email:e,password:o,stayConnected:r}=s;try{const s={email:e,password:o,stayConnected:r},t=await n(s);if(200===t.status)if(t.data.blacklisted)await c(!1),P("root",{message:x.IS_BLACKLISTED});else{const s=await m(t.data._id);j(s),f(!0);const e=new URLSearchParams(window.location.search);e.has("u")?i(`/user${window.location.search}`):e.has("c")?i(`/supplier${window.location.search}`):e.has("cr")?i(`/car${window.location.search}`):e.has("b")?i(`/update-booking${window.location.search}`):i("/")}else H()}catch{H()}},s[4]=i,s[5]=P,s[6]=j,s[7]=f,s[8]=H,s[9]=M):M=s[9];const Y=M;let K,$,V,U,q;return s[10]!==i?(K=()=>{(async()=>{try{e(x);const s=l();s?200===await d()&&(await m(s._id)?i(`/${window.location.search}`):await c()):D(!0)}catch{await c()}})()},$=[i],s[10]=i,s[11]=K,s[12]=$):(K=s[11],$=s[12]),S.useEffect(K,$),s[13]===Symbol.for("react.memo_cache_sentinel")?(V=t.jsx(_,{}),s[13]=V):V=s[13],s[14]!==B||s[15]!==W||s[16]!==v||s[17]!==k||s[18]!==i||s[19]!==Y||s[20]!==O||s[21]!==L||s[22]!==A?(U=A&&t.jsx("div",{className:"signin",children:t.jsx(h,{className:"signin-form "+(A?"":"hidden"),elevation:10,children:t.jsxs("form",{onSubmit:v(Y),children:[t.jsx("h1",{className:"signin-form-title",children:x.SIGN_IN_HEADING}),t.jsxs(E,{fullWidth:!0,margin:"dense",error:!!W.email,children:[t.jsx(w,{htmlFor:"email",children:a.EMAIL}),t.jsx(g,{...O("email"),onChange:s=>{B(),L("email",s.target.value)},autoComplete:"email",required:!0}),t.jsx(C,{error:!!W.email,children:W.email?.message||""})]}),t.jsxs(E,{fullWidth:!0,margin:"dense",error:!!W.password,children:[t.jsx(w,{htmlFor:"password",children:a.PASSWORD}),t.jsx(g,{...O("password"),onChange:s=>{B(),L("password",s.target.value)},type:"password",autoComplete:"password",required:!0}),t.jsx(C,{error:!!W.password,children:W.password?.message||""})]}),t.jsxs("div",{className:"stay-connected",children:[t.jsx("input",{id:"stay-connected",type:"checkbox",onChange:s=>{L("stayConnected",s.currentTarget.checked)}}),t.jsx("label",{htmlFor:"stay-connected",children:x.STAY_CONNECTED})]}),t.jsx("div",{className:"forgot-password",children:t.jsx(R,{variant:"text",onClick:()=>i("/forgot-password"),className:"btn-lnk",children:x.RESET_PASSWORD})}),t.jsx("div",{className:"signin-buttons",children:t.jsx(R,{type:"submit",variant:"contained",size:"small",className:"btn-primary",disabled:k,children:x.SIGN_IN})}),t.jsx("div",{className:"form-error",children:W.root&&t.jsx(u,{message:W.root.message})})]})})}),s[14]=B,s[15]=W,s[16]=v,s[17]=k,s[18]=i,s[19]=Y,s[20]=O,s[21]=L,s[22]=A,s[23]=U):U=s[23],s[24]!==U?(q=t.jsxs("div",{children:[V,U]}),s[24]=U,s[25]=q):q=s[25],q};export{f as default};
