/**
 * Migration script to update the database from driver to customer terminology.
 * 
 * This script:
 * 1. Updates the Booking collection to rename driver field to customer
 * 2. Removes additional driver related fields
 * 3. Removes license requirements from User collection
 * 4. Updates rental count logic for dresses
 * 
 * Usage: node migrate-driver-to-customer.js
 */

const mongoose = require('mongoose')
require('dotenv').config({ path: '../.env' })

const DB_HOST = process.env.BC_DB_HOST
const DB_PORT = process.env.BC_DB_PORT
const DB_NAME = process.env.BC_DB_NAME
const DB_USER = process.env.BC_DB_USER
const DB_PASS = process.env.BC_DB_PASS

async function connectToDatabase() {
  try {
    let uri = `mongodb://${DB_HOST}:${DB_PORT}/${DB_NAME}`
    if (DB_USER && DB_PASS) {
      uri = `mongodb://${DB_USER}:${DB_PASS}@${DB_HOST}:${DB_PORT}/${DB_NAME}`
    }
    
    await mongoose.connect(uri)
    console.log('Connected to MongoDB')
  } catch (error) {
    console.error('Database connection failed:', error)
    process.exit(1)
  }
}

async function runMigration() {
  try {
    console.log('Starting driver-to-customer migration...')

    // Step 1: Update Booking collection - rename driver to customer
    console.log('Step 1: Updating Booking collection...')
    
    const bookingRenameResult = await mongoose.connection.db.collection('Booking').updateMany(
      { driver: { $exists: true } },
      [
        { 
          $addFields: { 
            customer: "$driver" 
          } 
        },
        { 
          $project: { 
            driver: 0,
            additionalDriver: 0,
            _additionalDriver: 0
          } 
        }
      ]
    )
    console.log(`Updated ${bookingRenameResult.modifiedCount} bookings (renamed driver to customer)`)

    // Step 2: Remove additional driver related fields from all bookings
    const cleanupResult = await mongoose.connection.db.collection('Booking').updateMany(
      {},
      {
        $unset: {
          additionalDriver: "",
          _additionalDriver: ""
        }
      }
    )
    console.log(`Cleaned up ${cleanupResult.modifiedCount} bookings from additional driver fields`)

    // Step 3: Update User collection - remove license related fields
    console.log('Step 3: Updating User collection...')
    
    const userCleanupResult = await mongoose.connection.db.collection('User').updateMany(
      {},
      {
        $unset: {
          licenseRequired: "",
          license: ""
        }
      }
    )
    console.log(`Cleaned up ${userCleanupResult.modifiedCount} users from license fields`)

    // Step 4: Remove AdditionalDriver collection entirely
    console.log('Step 4: Removing AdditionalDriver collection...')
    
    try {
      await mongoose.connection.db.collection('AdditionalDriver').drop()
      console.log('AdditionalDriver collection removed')
    } catch (error) {
      if (error.message.includes('ns not found')) {
        console.log('AdditionalDriver collection does not exist, skipping...')
      } else {
        throw error
      }
    }

    // Step 5: Update dress rental counts based on completed bookings
    console.log('Step 5: Updating dress rental counts...')
    
    const dresses = await mongoose.connection.db.collection('Dress').find({}).toArray()
    
    for (const dress of dresses) {
      const completedBookingsCount = await mongoose.connection.db.collection('Booking').countDocuments({
        dress: dress._id,
        status: { $in: ['paid', 'completed'] }
      })
      
      await mongoose.connection.db.collection('Dress').updateOne(
        { _id: dress._id },
        { 
          $set: { 
            rentals: completedBookingsCount,
            rentalsCount: completedBookingsCount // Keep both for compatibility
          } 
        }
      )
    }
    console.log(`Updated rental counts for ${dresses.length} dresses`)

    // Step 6: Create indexes for the new customer field
    console.log('Step 6: Creating indexes...')
    
    await mongoose.connection.db.collection('Booking').createIndex({ customer: 1 })
    await mongoose.connection.db.collection('Booking').createIndex({ 'customer._id': 1 })
    console.log('Created indexes for customer field')

    console.log('Migration completed successfully!')
    process.exit(0)
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  }
}

async function main() {
  await connectToDatabase()
  await runMigration()
}

main()
