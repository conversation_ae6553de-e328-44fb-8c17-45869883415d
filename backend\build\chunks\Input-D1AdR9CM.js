import{r as t}from"./router-BtYqujaw.js";import{a as e,g as o,s as r,c as n,r as s,m as a,b as i}from"./Button-BeKLLPpp.js";import{i as l,b as p,c as d,r as u,d as m,e as b}from"./InputLabel-C8rcdOGQ.js";import{i as f,b8 as c,j as v}from"../entries/index-xsXxT3-W.js";function h(t){return o("MuiInput",t)}const g={...l,...e("MuiInput",["root","underline","input"])},$=r(d,{shouldForwardProp:t=>s(t)||"classes"===t,name:"MuiInput",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[...u(t,e),!o.disableUnderline&&e.underline]}})(a((({theme:t})=>{let e="light"===t.palette.mode?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return t.vars&&(e=`rgba(${t.vars.palette.common.onBackgroundChannel} / ${t.vars.opacity.inputUnderline})`),{position:"relative",variants:[{props:({ownerState:t})=>t.formControl,style:{"label + &":{marginTop:16}}},{props:({ownerState:t})=>!t.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:t.transitions.create("transform",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${g.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${g.error}`]:{"&::before, &::after":{borderBottomColor:(t.vars||t).palette.error.main}},"&::before":{borderBottom:`1px solid ${e}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:t.transitions.create("border-bottom-color",{duration:t.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${g.disabled}, .${g.error}):before`]:{borderBottom:`2px solid ${(t.vars||t).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${e}`}},[`&.${g.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(t.palette).filter(i()).map((([e])=>({props:{color:e,disableUnderline:!1},style:{"&::after":{borderBottom:`2px solid ${(t.vars||t).palette[e].main}`}}})))]}}))),I=r(m,{name:"MuiInput",slot:"Input",overridesResolver:b})({}),x=t.forwardRef((function(t,e){const o=f({props:t,name:"MuiInput"}),{disableUnderline:r=!1,components:s={},componentsProps:a,fullWidth:i=!1,inputComponent:l="input",multiline:d=!1,slotProps:u,slots:m={},type:b="text",...g}=o,x=(t=>{const{classes:e,disableUnderline:o}=t,r=n({root:["root",!o&&"underline"],input:["input"]},h,e);return{...e,...r}})(o),y={root:{ownerState:{disableUnderline:r}}},B=u??a?c(u??a,y):y,j=m.root??s.Root??$,U=m.input??s.Input??I;return v.jsx(p,{slots:{root:j,input:U},slotProps:B,fullWidth:i,inputComponent:l,multiline:d,ref:e,type:b,...g,classes:x})}));x.muiName="Input";export{x as I,g as i};
