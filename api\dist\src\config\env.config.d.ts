import { Document, Types } from 'mongoose';
import { CookieOptions } from 'express';
import * as bookcarsTypes from ':bookcars-types';
/**
 * Get environment variable value.
 *
 * @param {string} name
 * @param {?boolean} [required]
 * @param {?string} [defaultValue]
 * @returns {string}
 */
export declare const __env__: (name: string, required?: boolean, defaultValue?: string) => string;
/**
 * ISO 639-1 language codes supported
 * https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes
 *
 * @type {string[]}
 */
export declare const LANGUAGES: string[];
/**
 * Website Name.
 *
 * @type {string}
 */
export declare const WEBSITE_NAME: string;
/**
 * Server Port. Default is 4002.
 *
 * @type {number}
 */
export declare const PORT: number;
/**
 * Indicate whether HTTPS is enabled or not.
 *
 * @type {boolean}
 */
export declare const HTTPS: boolean;
/**
 * Private SSL key filepath.
 *
 * @type {string}
 */
export declare const PRIVATE_KEY: string;
/**
 * Private SSL certificate filepath.
 *
 * @type {string}
 */
export declare const CERTIFICATE: string;
/**
 * MongoDB database URI. Default is: mongodb://127.0.0.1:27017/bookcars?authSource=admin&appName=bookcars
 *
 * @type {string}
 */
export declare const DB_URI: string;
/**
 * Indicate whether MongoDB SSL is enabled or not.
 *
 * @type {boolean}
 */
export declare const DB_SSL: boolean;
/**
 * MongoDB SSL certificate filepath.
 *
 * @type {string}
 */
export declare const DB_SSL_CERT: string;
/**
 * MongoDB SSL CA certificate filepath.
 *
 * @type {string}
 */
export declare const DB_SSL_CA: string;
/**
 * Indicate whether MongoDB debug is enabled or not.
 *
 * @type {boolean}
 */
export declare const DB_DEBUG: boolean;
/**
 * Cookie secret. It should at least be 32 characters long, but the longer the better.
 *
 * @type {string}
 */
export declare const COOKIE_SECRET: string;
/**
 * Authentication cookie domain.
 * Default is localhost.
 *
 * @type {string}
 */
export declare const AUTH_COOKIE_DOMAIN: string;
/**
 * Cookie options.
 *
 * On production, authentication cookies are httpOnly, signed, secure and strict sameSite.
 * This will prevent XSS attacks by not allowing access to the cookie via JavaScript.
 * This will prevent CSRF attacks by not allowing the browser to send the cookie along with cross-site requests.
 * This will prevent MITM attacks by only allowing the cookie to be sent over HTTPS.
 * Authentication cookies are protected against XST attacks as well by disabling TRACE HTTP method via allowedMethods middleware.
 *
 * @type {CookieOptions}
 */
export declare const COOKIE_OPTIONS: CookieOptions;
/**
 * frontend authentication cookie name.
 *
 * @type {"bc-x-access-token-frontend"}
 */
export declare const FRONTEND_AUTH_COOKIE_NAME = "bc-x-access-token-frontend";
/**
 * Backend authentication cookie name.
 *
 * @type {"bc-x-access-token-frontend"}
 */
export declare const BACKEND_AUTH_COOKIE_NAME = "bc-x-access-token-backend";
/**
 * Mobile App and unit tests authentication header name.
 *
 * @type {"x-access-token"}
 */
export declare const X_ACCESS_TOKEN = "x-access-token";
/**
 * JWT secret. It should at least be 32 characters long, but the longer the better.
 *
 * @type {string}
 */
export declare const JWT_SECRET: string;
/**
 * JWT expiration in seconds. Default is 86400 seconds (1 day).
 *
 * @type {number}
 */
export declare const JWT_EXPIRE_AT: number;
/**
 * Validation Token expiration in seconds. Default is 86400 seconds (1 day).
 *
 * @type {number}
 */
export declare const TOKEN_EXPIRE_AT: number;
/**
 * SMTP host.
 *
 * @type {string}
 */
export declare const SMTP_HOST: string;
/**
 * SMTP port.
 *
 * @type {number}
 */
export declare const SMTP_PORT: number;
/**
 * SMTP username.
 *
 * @type {string}
 */
export declare const SMTP_USER: string;
/**
 * SMTP password.
 *
 * @type {string}
 */
export declare const SMTP_PASS: string;
/**
 * SMTP from email.
 *
 * @type {string}
 */
export declare const SMTP_FROM: string;
/**
 * CDN root folder path.
 *
 * @type {string}
 */
export declare const CDN_ROOT: string;
/**
 * Users' cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_USERS: string;
/**
 * Users' temp cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_TEMP_USERS: string;
/**
 * Dresses' cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_DRESSES: string;
/**
 * Dresses' temp cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_TEMP_DRESSES: string;
/**
 * Locations' cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_LOCATIONS: string;
/**
 * Locations' temp cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_TEMP_LOCATIONS: string;
/**
 * Contracts' cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_CONTRACTS: string;
/**
 * Contracts' temp cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_TEMP_CONTRACTS: string;
/**
 * Licenses' cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_LICENSES: string;
/**
 * Licenses' temp cdn folder path.
 *
 * @type {string}
 */
export declare const CDN_TEMP_LICENSES: string;
/**
 * Backend host.
 *
 * @type {string}
 */
export declare const BACKEND_HOST: string;
/**
 * Frontend host.
 *
 * @type {string}
 */
export declare const FRONTEND_HOST: string;
/**
 * Default language. Default is en. Available options: en, fr, es.
 *
 * @type {string}
 */
export declare const DEFAULT_LANGUAGE: string;
/**
 * Default Minimum age for rental. Default is 21 years.
 *
 * @type {number}
 */
export declare const MINIMUM_AGE: number;
/**
 * Expo push access token.
 *
 * @type {string}
 */
export declare const EXPO_ACCESS_TOKEN: string;
/**
 * Stripe secret key.
 *
 * @type {string}
 */
export declare const STRIPE_SECRET_KEY: string;
/**
 * Stripe Checkout Session expiration in seconds. Should be at least 1800 seconds (30min) and max 82800 seconds. Default is 82800 seconds (~23h).
 * If the value is lower than 1800 seconds, it wil be set to 1800 seconds.
 * If the value is greater than 82800 seconds, it wil be set to 82800 seconds.
 *
 * @type {number}
 */
export declare const STRIPE_SESSION_EXPIRE_AT: number;
/**
 * Indicates whether PayPal is used in sandbox mode or production.
 *
 * @type {boolean}
 */
export declare const PAYPAL_SANDBOX: boolean;
/**
 * PayPal client ID.
 *
 * @type {string}
 */
export declare const PAYPAL_CLIENT_ID: string;
/**
 * PayPal client secret.
 *
 * @type {string}
 */
export declare const PAYPAL_CLIENT_SECRET: string;
/**
 * Booking expiration in seconds.
 * Bookings created from checkout with Stripe are temporary and are automatically deleted if the payment checkout session expires.
 *
 * @type {number}
 */
export declare const BOOKING_EXPIRE_AT: number;
/**
 * User expiration in seconds.
 * Non verified and active users created from checkout with Stripe are temporary and are automatically deleted if the payment checkout session expires.
 *
 *
 * @type {number}
 */
export declare const USER_EXPIRE_AT: number;
/**
 * Admin email.
 *
 * @type {string}
 */
export declare const ADMIN_EMAIL: string;
/**
 * Google reCAPTCHA v3 secret key.
 *
 * @type {string}
 */
export declare const RECAPTCHA_SECRET: string;
/**
 * Timezone for cenverting dates from UTC to local time.
 * Must be a valid TZ idenfidier: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
 * Default is UTC.
 *
 * @type {string}
 */
export declare const TIMEZONE: string;
/**
 * ipinfo.io API key.
 * Required for more tha, 1000 requests/day.
 *
 * @type {string}
 */
export declare const IPINFO_API_KEY: string;
/**
 * Default ISO 2 country code ipinfo.io.
 *
 * @type {string}
 */
export declare const IPINFO_DEFAULT_COUNTRY: string;
/**
 * User Document.
 *
 * @export
 * @interface User
 * @typedef {User}
 * @extends {Document}
 */
export interface User extends Document {
    supplier?: Types.ObjectId;
    fullName: string;
    email: string;
    phone?: string;
    password?: string;
    birthDate?: Date;
    verified?: boolean;
    verifiedAt?: Date;
    active?: boolean;
    language: string;
    enableEmailNotifications?: boolean;
    avatar?: string;
    bio?: string;
    location?: string;
    type?: bookcarsTypes.UserType;
    blacklisted?: boolean;
    payLater?: boolean;
    customerId?: string;
    contracts?: bookcarsTypes.Contract[];
    licenseRequired?: boolean;
    license?: string | null;
    minimumRentalDays?: number;
    expireAt?: Date;
    priceChangeRate?: number;
    supplierDressLimit?: number;
    notifyAdminOnNewDress?: boolean;
}
/**
 * UserInfo.
 *
 * @export
 * @interface UserInfo
 * @typedef {UserInfo}
 */
export interface UserInfo {
    _id?: Types.ObjectId;
    supplier?: Types.ObjectId;
    fullName: string;
    email?: string;
    phone?: string;
    password?: string;
    birthDate?: Date;
    verified?: boolean;
    verifiedAt?: Date;
    active?: boolean;
    language?: string;
    enableEmailNotifications?: boolean;
    avatar?: string;
    bio?: string;
    location?: string;
    type?: string;
    blacklisted?: boolean;
    payLater?: boolean;
    licenseRequired?: boolean;
    license?: string;
    priceChangeRate?: number;
    supplierDressLimit?: number;
    notifyAdminOnNewDress?: boolean;
}
/**
 * AdditionalDriver.
 *
 * @export
 * @interface AdditionalDriver
 * @typedef {AdditionalDriver}
 */
export interface AdditionalDriver {
    fullName: string;
    email: string;
    phone: string;
    birthDate: Date;
}
/**
 * Booking Document.
 *
 * @export
 * @interface Booking
 * @typedef {Booking}
 * @extends {Document}
 */
export interface Booking extends Document {
    _id: Types.ObjectId;
    supplier: Types.ObjectId;
    dress?: Types.ObjectId;
    customer: Types.ObjectId;
    pickupLocation: Types.ObjectId;
    dropOffLocation: Types.ObjectId;
    from: Date;
    to: Date;
    status: bookcarsTypes.BookingStatus;
    cancellation?: boolean;
    amendments?: boolean;
    cancelRequest?: boolean;
    price: number;
    sessionId?: string;
    paymentIntentId?: string;
    customerId?: string;
    expireAt?: Date;
    isDeposit: boolean;
    paypalOrderId?: string;
}
/**
 * DateBasedPrice Document.
 *
 * @export
 * @interface DateBasedPrice
 * @typedef {DateBasedPrice}
 * @extends {Document}
 */
export interface DateBasedPrice extends Document {
    startDate: Date;
    endDate: Date;
    dailyPrice: number;
}
/**
 * Dress Document.
 *
 * @export
 * @interface Dress
 * @typedef {Dress}
 * @extends {Document}
 */
export interface Dress extends Document {
    name: string;
    supplier: Types.ObjectId;
    locations: Types.ObjectId[];
    dailyPrice: number;
    discountedDailyPrice: number | null;
    biWeeklyPrice: number | null;
    discountedBiWeeklyPrice: number | null;
    weeklyPrice: number | null;
    discountedWeeklyPrice: number | null;
    monthlyPrice: number | null;
    discountedMonthlyPrice: number | null;
    isDateBasedPrice: boolean;
    dateBasedPrices: Types.ObjectId[];
    deposit: number;
    available: boolean;
    fullyBooked?: boolean;
    comingSoon?: boolean;
    type: bookcarsTypes.DressType;
    size: bookcarsTypes.DressSize;
    customizable: boolean;
    image: string | null;
    color: string;
    length: number;
    material: bookcarsTypes.DressMaterial;
    cancellation: number;
    amendments: number;
    range: string;
    accessories: string[];
    rating?: number;
    rentals: number;
    designerName?: string;
}
/**
 * DressInfo.
 *
 * @export
 * @interface DressInfo
 * @typedef {DressInfo}
 */
export interface DressInfo {
    _id?: Types.ObjectId;
    name: string;
    supplier: UserInfo;
    locations: Types.ObjectId[];
    price: number;
    deposit: number;
    available: boolean;
    type: bookcarsTypes.DressType;
    size: bookcarsTypes.DressSize;
    customizable?: boolean;
    image?: string;
    color: string;
    length: number;
    material: bookcarsTypes.DressMaterial;
    cancellation: number;
    amendments: number;
    designerName?: string;
}
/**
 * BookingInfo.
 *
 * @export
 * @interface BookingInfo
 * @typedef {BookingInfo}
 */
export interface BookingInfo {
    _id?: Types.ObjectId;
    supplier: UserInfo;
    dress?: Dress;
    customer: UserInfo;
    pickupLocation: Types.ObjectId;
    dropOffLocation: Types.ObjectId;
    from: Date;
    to: Date;
    status: bookcarsTypes.BookingStatus;
    cancellation?: boolean;
    amendments?: boolean;
    cancelRequest?: boolean;
    price: number;
}
/**
 * LocationValue Document.
 *
 * @export
 * @interface LocationValue
 * @typedef {LocationValue}
 * @extends {Document}
 */
export interface LocationValue extends Document {
    language: string;
    value: string;
}
/**
 * Country Document.
 *
 * @export
 * @interface Country
 * @typedef {Country}
 * @extends {Document}
 */
export interface Country extends Document {
    values: Types.ObjectId[];
    name?: string;
    supplier?: Types.ObjectId;
}
/**
 *CountryInfo.
 *
 * @export
 * @interface CountryInfo
 * @typedef {CountryInfo}
 */
export interface CountryInfo {
    _id?: Types.ObjectId;
    name?: string;
    values: LocationValue[];
}
/**
 * Location Document.
 *
 * @export
 * @interface Location
 * @typedef {Location}
 * @extends {Document}
 */
export interface Location extends Document {
    country: Types.ObjectId;
    longitude?: number;
    latitude?: number;
    values: Types.ObjectId[];
    name?: string;
    image?: string | null;
    parkingSpots?: Types.ObjectId[] | null;
    supplier?: Types.ObjectId;
}
/**
 *LocationInfo.
 *
 * @export
 * @interface LocationInfo
 * @typedef {LocationInfo}
 */
export interface LocationInfo {
    _id?: Types.ObjectId;
    longitude: number;
    latitude: number;
    name?: string;
    image?: string | null;
    values: LocationValue[];
}
/**
 * ParkingSpot Document.
 *
 * @export
 * @interface ParkingSpot
 * @typedef {ParkingSpot}
 * @extends {Document}
 */
export interface ParkingSpot extends Document {
    longitude: number;
    latitude: number;
    values: (Types.ObjectId | LocationValue)[];
    name?: string;
}
/**
 * Notification Document.
 *
 * @export
 * @interface Notification
 * @typedef {Notification}
 * @extends {Document}
 */
export interface Notification extends Document {
    user: Types.ObjectId;
    message: string;
    booking: Types.ObjectId;
    dress?: Types.ObjectId;
    isRead?: boolean;
}
/**
 * NotificationCounter Document.
 *
 * @export
 * @interface NotificationCounter
 * @typedef {NotificationCounter}
 * @extends {Document}
 */
export interface NotificationCounter extends Document {
    user: Types.ObjectId;
    count?: number;
}
/**
 * PushToken Document.
 *
 * @export
 * @interface PushToken
 * @typedef {PushToken}
 * @extends {Document}
 */
export interface PushToken extends Document {
    user: Types.ObjectId;
    token: string;
}
/**
 * Token Document.
 *
 * @export
 * @interface Token
 * @typedef {Token}
 * @extends {Document}
 */
export interface Token extends Document {
    user: Types.ObjectId;
    token: string;
    expireAt?: Date;
}
/**
 * BankDetails Document.
 *
 * @export
 * @interface BankDetails
 * @typedef {BankDetails}
 * @extends {Document}
 */
export interface BankDetails extends Document {
    accountHolder: string;
    bankName: string;
    iban: string;
    swiftBic: string;
    showBankDetailsPage: boolean;
}
