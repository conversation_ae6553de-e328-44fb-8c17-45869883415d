import{j as s,a as r,aH as e,aI as a,aJ as o,B as t,z as n,e as i}from"../entries/index-CEzJO5Xy.js";import{d,r as m}from"./router-BtYqujaw.js";import{u as c,s as l,z as p}from"./zod-4O8Zwsja.js";import{L as w}from"./Layout-BQBjg4Lf.js";import{s as S}from"./change-password-CyUFObAm.js";import{S as j}from"./SimpleBackdrop-Bf3qjF13.js";import{P as u}from"./Paper-CcwAvfvc.js";import{F as P,I as R}from"./InputLabel-BbcIE26O.js";import{I as f}from"./Input-BQdee9z7.js";import{F as h}from"./FormHelperText-DFSsjBsL.js";import{B as x}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const g=p.object({currentPassword:p.string().min(i.PASSWORD_MIN_LENGTH,{message:r.PASSWORD_ERROR}).optional(),newPassword:p.string().min(i.PASSWORD_MIN_LENGTH,{message:r.PASSWORD_ERROR}),confirmPassword:p.string()}).refine((s=>s.newPassword===s.confirmPassword),{path:["confirmPassword"],message:r.PASSWORDS_DONT_MATCH}),_=()=>{const i=d(),[p,_]=m.useState(),[N,A]=m.useState(),[E,O]=m.useState(!0),[W,b]=m.useState(!1),[D,C]=m.useState(!1),{register:y,handleSubmit:T,formState:{errors:I,isSubmitting:L},clearErrors:H,setError:v,reset:q}=c({resolver:l(g),mode:"onSubmit"});return s.jsxs(w,{onLoad:async s=>{if(s){const r=new URLSearchParams(window.location.search);let a=s?._id;r.has("u")&&(a=r.get("u")||void 0,A(a));const o=await e(a);C(200===o),_(s),O(!1),b(!0)}},strict:!0,children:[s.jsx("div",{className:"password-reset",style:W?{}:{display:"none"},children:s.jsxs(u,{className:"password-reset-form password-reset-form-wrapper",elevation:10,children:[s.jsx("h1",{className:"password-reset-form-title",children:S.CHANGE_PASSWORD_HEADING}),s.jsxs("form",{className:"form",onSubmit:T((async({currentPassword:s,newPassword:r})=>{try{if(!N&&!p)return;if(200===(D?await a(N||p?._id,s):200)){const e={_id:N||p?._id,password:s||"",newPassword:r,strict:D};200===await o(e)?(C(!0),q(),t(S.PASSWORD_UPDATE)):n(null,S.PASSWORD_UPDATE_ERROR)}else v("currentPassword",{message:S.CURRENT_PASSWORD_ERROR})}catch(e){n(e)}})),children:[D&&s.jsxs(P,{fullWidth:!0,margin:"dense",error:!!I.currentPassword,children:[s.jsx(R,{className:"required",children:S.CURRENT_PASSWORD}),s.jsx(f,{...y("currentPassword"),type:"password",required:!0,onChange:()=>H()}),s.jsx(h,{children:I.currentPassword?.message||""})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",error:!!I.newPassword,children:[s.jsx(R,{className:"required",children:S.NEW_PASSWORD}),s.jsx(f,{...y("newPassword"),type:"password",required:!0,onChange:()=>H()}),s.jsx(h,{children:I.newPassword?.message||""})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",error:!!I.confirmPassword,children:[s.jsx(R,{className:"required",children:r.CONFIRM_PASSWORD}),s.jsx(f,{...y("confirmPassword"),type:"password",required:!0,onChange:()=>H()}),s.jsx(h,{children:I.confirmPassword?.message||""})]}),s.jsxs("div",{className:"buttons",children:[s.jsx(x,{type:"submit",className:"btn-primary btn-margin btn-margin-bottom",size:"small",variant:"contained",disabled:L,children:r.RESET_PASSWORD}),s.jsx(x,{className:"btn-secondary btn-margin-bottom",size:"small",variant:"contained",onClick:()=>i("/"),children:r.CANCEL})]})]})]})}),E&&s.jsx(j,{text:r.PLEASE_WAIT})]})};export{_ as default};
