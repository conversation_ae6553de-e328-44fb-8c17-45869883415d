import{c as e,j as s,a as r,D as t,e as o,A as a,z as i}from"../entries/index-CEzJO5Xy.js";import{d as n,r as m}from"./router-BtYqujaw.js";import{u as l,z as c,s as d}from"./zod-4O8Zwsja.js";import{L as j}from"./Layout-BQBjg4Lf.js";import{s as p}from"./reset-password-CKyLhs8Q.js";import{I as h,F as f}from"./InputLabel-BbcIE26O.js";import{I as u}from"./Input-BQdee9z7.js";import{B as x}from"./Button-DGZYUY3P.js";import{F as E}from"./FormHelperText-DFSsjBsL.js";import{P as _}from"./Paper-CcwAvfvc.js";import"./vendor-dblfw9z9.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const b=c.object({email:c.string().email({message:r.EMAIL_NOT_VALID})}),S=()=>{const c=e.c(48),S=n(),[N,g]=m.useState(!1),[v,A]=m.useState(!1);let I;c[0]===Symbol.for("react.memo_cache_sentinel")?(I={resolver:d(b),mode:"onSubmit"},c[0]=I):I=c[0];const{register:L,handleSubmit:y,formState:P,setError:T,clearErrors:w}=l(I),{errors:C,isSubmitting:R}=P;let O;c[1]!==S?(O=e=>{e?S("/"):g(!0)},c[1]=S,c[2]=O):O=c[2];const F=O;let H;c[3]!==T?(H=async e=>{const{email:s}=e;try{if(200===await t({email:s,appType:o.APP_TYPE}))return void T("email",{message:p.EMAIL_ERROR});200===await a(s,!0,o.APP_TYPE)?A(!0):i()}catch(r){i(r)}},c[3]=T,c[4]=H):H=c[4];const M=H,D="forgot-password-form "+(N?"":"hidden");let k;c[5]===Symbol.for("react.memo_cache_sentinel")?(k=s.jsx("h1",{className:"forgot-password-title",children:p.RESET_PASSWORD_HEADING}),c[5]=k):k=c[5];const W=v?"hidden":"";let q,z,B,G;c[6]!==y||c[7]!==M?(q=y(M),c[6]=y,c[7]=M,c[8]=q):q=c[8],c[9]===Symbol.for("react.memo_cache_sentinel")?(z=s.jsx(h,{className:"required",children:r.EMAIL}),c[9]=z):z=c[9],c[10]!==L?(B=L("email"),c[10]=L,c[11]=B):B=c[11],c[12]!==w?(G=()=>w("email"),c[12]=w,c[13]=G):G=c[13];const Y=!!C.email;let J;c[14]!==G||c[15]!==Y||c[16]!==B?(J=s.jsx(u,{...B,onChange:G,type:"text",error:Y,autoComplete:"off",required:!0}),c[14]=G,c[15]=Y,c[16]=B,c[17]=J):J=c[17];const K=!!C.email,V=C.email?.message||"";let Q,U,X,Z,$,ee,se,re,te,oe;return c[18]!==K||c[19]!==V?(Q=s.jsx(E,{error:K,children:V}),c[18]=K,c[19]=V,c[20]=Q):Q=c[20],c[21]!==J||c[22]!==Q?(U=s.jsxs(f,{fullWidth:!0,margin:"dense",children:[z,J,Q]}),c[21]=J,c[22]=Q,c[23]=U):U=c[23],c[24]!==R?(X=s.jsx(x,{type:"submit",className:"btn-primary",variant:"contained",disabled:R,children:p.RESET}),c[24]=R,c[25]=X):X=c[25],c[26]!==S?(Z=s.jsx(x,{variant:"outlined",onClick:()=>S("/"),children:r.CANCEL}),c[26]=S,c[27]=Z):Z=c[27],c[28]!==X||c[29]!==Z?($=s.jsxs("div",{className:"buttons",children:[X,Z]}),c[28]=X,c[29]=Z,c[30]=$):$=c[30],c[31]!==U||c[32]!==$||c[33]!==q?(ee=s.jsxs("form",{onSubmit:q,children:[U,$]}),c[31]=U,c[32]=$,c[33]=q,c[34]=ee):ee=c[34],c[35]!==ee||c[36]!==W?(se=s.jsx("div",{className:W,children:ee}),c[35]=ee,c[36]=W,c[37]=se):se=c[37],c[38]!==S||c[39]!==v?(re=v&&s.jsxs("div",{children:[s.jsx("span",{children:p.EMAIL_SENT}),s.jsx("p",{children:s.jsx(x,{variant:"text",onClick:()=>S("/"),className:"btn-lnk",children:r.GO_TO_HOME})})]}),c[38]=S,c[39]=v,c[40]=re):re=c[40],c[41]!==se||c[42]!==re||c[43]!==D?(te=s.jsx("div",{className:"forgot-password",children:s.jsxs(_,{className:D,elevation:10,children:[k,se,re]})}),c[41]=se,c[42]=re,c[43]=D,c[44]=te):te=c[44],c[45]!==F||c[46]!==te?(oe=s.jsx(j,{onLoad:F,strict:!1,children:te}),c[45]=F,c[46]=te,c[47]=oe):oe=c[47],oe};export{S as default};
