import{r as e}from"./router-BtYqujaw.js";import{av as r,aq as n,i as o,j as t,k as a,l as s}from"../entries/index-CEzJO5Xy.js";import{u as i}from"./Paper-CcwAvfvc.js";import{a as l,g as u,s as p,c,m as d,b as m,k as y}from"./Button-DGZYUY3P.js";import{T as b}from"./Backdrop-Bzn12VyM.js";function v(e){return u("MuiLink",e)}const x=l("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]),f=({theme:e,ownerState:o})=>{const t=o.color,a=r(e,`palette.${t}.main`,!1)||r(e,`palette.${t}`,!1)||o.color,s=r(e,`palette.${t}.mainChannel`)||r(e,`palette.${t}Channel`);return"vars"in e&&s?`rgba(${s} / 0.4)`:n(a,.4)},h={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},k=p(b,{name:"MuiLink",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:n}=e;return[r.root,r[`underline${s(n.underline)}`],"button"===n.component&&r.button]}})(d((({theme:e})=>({variants:[{props:{underline:"none"},style:{textDecoration:"none"}},{props:{underline:"hover"},style:{textDecoration:"none","&:hover":{textDecoration:"underline"}}},{props:{underline:"always"},style:{textDecoration:"underline","&:hover":{textDecorationColor:"inherit"}}},{props:({underline:e,ownerState:r})=>"always"===e&&"inherit"!==r.color,style:{textDecorationColor:"var(--Link-underlineColor)"}},...Object.entries(e.palette).filter(m()).map((([r])=>({props:{underline:"always",color:r},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette[r].mainChannel} / 0.4)`:n(e.palette[r].main,.4)}}))),{props:{underline:"always",color:"textPrimary"},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.4)`:n(e.palette.text.primary,.4)}},{props:{underline:"always",color:"textSecondary"},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette.text.secondaryChannel} / 0.4)`:n(e.palette.text.secondary,.4)}},{props:{underline:"always",color:"textDisabled"},style:{"--Link-underlineColor":(e.vars||e).palette.text.disabled}},{props:{component:"button"},style:{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},[`&.${x.focusVisible}`]:{outline:"auto"}}}]})))),w=e.forwardRef((function(r,n){const l=o({props:r,name:"MuiLink"}),u=i(),{className:p,color:d="primary",component:m="a",onBlur:b,onFocus:x,TypographyClasses:w,underline:C="always",variant:g="inherit",sx:L,...$}=l,[S,j]=e.useState(!1),D={...l,color:d,component:m,focusVisible:S,underline:C,variant:g},A=(e=>{const{classes:r,component:n,focusVisible:o,underline:t}=e,a={root:["root",`underline${s(t)}`,"button"===n&&"button",o&&"focusVisible"]};return c(a,v,r)})(D);return t.jsx(k,{color:d,className:a(A.root,p),classes:w,component:m,onBlur:e=>{y(e.target)||j(!1),b&&b(e)},onFocus:e=>{y(e.target)&&j(!0),x&&x(e)},ref:n,ownerState:D,variant:g,...$,sx:[...void 0===h[d]?[{color:d}]:[],...Array.isArray(L)?L:[L]],style:{...$.style,..."always"===C&&"inherit"!==d&&!h[d]&&{"--Link-underlineColor":f({theme:u,ownerState:D})}}})}));export{w as L};
