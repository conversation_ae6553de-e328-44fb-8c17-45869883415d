import{b as e,s as a,Q as t,j as s,R as l,e as r,z as n,c as o,a as i,J as c}from"../entries/index-xsXxT3-W.js";import{r as u}from"./router-BtYqujaw.js";import{g as d}from"./CountryService-CPWL_VJK.js";import{M as N}from"./MultipleSelect-DovAF4K6.js";import{I as p}from"./Input-D1AdR9CM.js";import{F as g,I as m}from"./InputLabel-C8rcdOGQ.js";import{B as A}from"./Button-BeKLLPpp.js";const I=new e({fr:{NEW_LOCATION_HEADING:"Nouveau lieu",LOCATION_NAME:"Lieu",INVALID_LOCATION:"Ce lieu existe déjà.",LOCATION_CREATED:"Lieu créé avec succès.",COUNTRY:"Pays",PARKING_SPOTS:"Places de parking"},en:{NEW_LOCATION_HEADING:"New location",LOCATION_NAME:"Location",INVALID_LOCATION:"This location already exists.",LOCATION_CREATED:"Location created successfully.",COUNTRY:"Country",PARKING_SPOTS:"Parking spots"},es:{NEW_LOCATION_HEADING:"Nuevo lugar",LOCATION_NAME:"Lugar",INVALID_LOCATION:"Este lugar ya existe.",LOCATION_CREATED:"Lugar creado con éxito.",COUNTRY:"País",PARKING_SPOTS:"Plazas de aparcamiento"}});a(I);const O=({value:e,multiple:a,label:o,required:i,variant:c,onChange:p})=>{const[g,m]=u.useState(!1),[A,I]=u.useState(!1),[O,v]=u.useState([]),[h,E]=u.useState(!0),[C,T]=u.useState(1),[_,f]=u.useState(""),[j,x]=u.useState([]);u.useEffect((()=>{const s=a?e:[e];e&&!t(j,s)&&x(s),(null===e||Array.isArray(e)&&0===e.length)&&j.length>0&&x([])}),[e,a,j]);const L=async(e,a,t)=>{try{if(h||1===e){I(!0);const s=await d(a,e,r.PAGE_SIZE),l=s&&s.length>0?s[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!l)return;const n=Array.isArray(l.pageInfo)&&l.pageInfo.length>0?l.pageInfo[0].totalRecords:0,o=1===e?l.resultData:[...O,...l.resultData];v(o),E(l.resultData.length>0),t&&t({rows:l.resultData,rowCount:n})}}catch(s){n(s)}finally{I(!1)}};return s.jsx(N,{loading:A,label:o||"",callbackFromMultipleSelect:e=>{p&&p(e)},options:O,selectedOptions:j,required:i||!1,multiple:a,type:l.Country,variant:c||"standard",ListboxProps:{onScroll:e=>{const a=e.currentTarget;if(h&&!A&&a.scrollTop+a.clientHeight>=a.scrollHeight-r.PAGE_OFFSET){const e=C+1;T(e),L(e,_)}}},onFocus:()=>{if(!g){const e=1;v([]),T(e),L(e,_,(()=>{m(!0)}))}},onInputChange:e=>{const a=e&&e.target&&"value"in e.target&&e.target.value||"";a!==_&&(v([]),T(1),f(a),L(1,a))},onClear:()=>{v([]),T(1),f(""),E(!0),L(1,"")}})},v=new e({fr:{NEW_PARKING_SPOT:"Nouvelle place de parking"},en:{NEW_PARKING_SPOT:"New parking spot"},es:{NEW_PARKING_SPOT:"Nueva plaza de aparcamiento"}});a(v);const h=e=>{const a=o.c(3);let t,l;return a[0]===Symbol.for("react.memo_cache_sentinel")?(t={pattern:"(-)?[0-9]+(\\.[0-9]+)?"},a[0]=t):t=a[0],a[1]!==e?(l=s.jsx(p,{type:"text",inputProps:t,...e}),a[1]=e,a[2]=l):l=a[2],l},E=e=>{const a=o.c(20),{title:t,values:l,onAdd:n,onUpdate:d,onDelete:N}=e;let I;a[0]!==l?(I=l||[],a[0]=l,a[1]=I):I=a[1];const[O,E]=u.useState(I);let C,T,_,f,j,x,L;return a[2]!==l?(C=()=>{l&&E(l)},T=[l],a[2]=l,a[3]=C,a[4]=T):(C=a[3],T=a[4]),u.useEffect(C,T),a[5]!==t?(_=t&&s.jsx("span",{className:"title",children:t}),a[5]=t,a[6]=_):_=a[6],a[7]!==N||a[8]!==d||a[9]!==O?(f=O.map(((e,a)=>s.jsxs("div",{className:"row",children:[e.values&&r._LANGUAGES.map(((t,l)=>s.jsxs(g,{fullWidth:!0,margin:"dense",children:[s.jsx(m,{className:"required",children:`${i.NAME} (${t.label})`}),s.jsx(p,{type:"text",value:e.values[l]&&e.values[l].value||"",required:!0,onChange:e=>{const s=c(O),r=s[a];r._id?r.values[l].value=e.target.value:r.values[l]={language:t.code,value:e.target.value},d&&d(r,a),E(s)},autoComplete:"off"})]},t.code))),s.jsxs(g,{fullWidth:!0,margin:"dense",children:[s.jsx(m,{className:"required",children:i.LATITUDE}),s.jsx(h,{value:e.latitude,required:!0,onChange:e=>{const t=c(O);t[a].latitude=e.target.value,E(t),d&&d(t[a],a)}})]}),s.jsxs(g,{fullWidth:!0,margin:"dense",children:[s.jsx(m,{className:"required",children:i.LONGITUDE}),s.jsx(h,{value:e.longitude,required:!0,onChange:e=>{const t=c(O);t[a].longitude=e.target.value,E(t),d&&d(t[a],a)}})]}),s.jsx("div",{className:"row-actions",children:s.jsx(A,{variant:"outlined",className:"btn-margin-bottom",size:"small",color:"error",onClick:()=>{N?N(e,a):(O.splice(a,1),E(c(O)))},children:i.DELETE})})]},e._id||a))),a[7]=N,a[8]=d,a[9]=O,a[10]=f):f=a[10],a[11]!==f?(j=s.jsx("div",{className:"rows",children:f}),a[11]=f,a[12]=j):j=a[12],a[13]!==n||a[14]!==O?(x=s.jsx("div",{className:"global-actions",children:s.jsx(A,{variant:"outlined",className:"btn-margin-bottom",size:"small",color:"inherit",onClick:()=>{const e={latitude:"",longitude:"",values:[]};n?n(e):O.push(e)},children:v.NEW_PARKING_SPOT})}),a[13]=n,a[14]=O,a[15]=x):x=a[15],a[16]!==_||a[17]!==j||a[18]!==x?(L=s.jsxs("div",{className:"parking-spot-edit-list",children:[_,j,x]}),a[16]=_,a[17]=j,a[18]=x,a[19]=L):L=a[19],L};export{O as C,h as P,E as a,I as s};
