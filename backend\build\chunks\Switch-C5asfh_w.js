import{r as e}from"./router-BtYqujaw.js";import{i as t,j as a,k as r,l as o,ao as s,ak as l,al as i}from"../entries/index-xsXxT3-W.js";import{a as n,g as c,s as d,c as p,m,b as h}from"./Button-BeKLLPpp.js";import{f as u}from"./InputLabel-C8rcdOGQ.js";import{u as b}from"./useSlot-DiTut-u0.js";import{u as g}from"./useFormControl-B7jXtRD7.js";import{T as w}from"./Backdrop-Czag2Ija.js";import{S as v}from"./SwitchBase-DrUkTXjH.js";function f(e){return c("MuiFormControlLabel",e)}const k=n("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]),y=d("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[{[`& .${k.label}`]:t.label},t.root,t[`labelPlacement${o(a.labelPlacement)}`]]}})(m((({theme:e})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${k.disabled}`]:{cursor:"default"},[`& .${k.label}`]:{[`&.${k.disabled}`]:{color:(e.vars||e).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:e})=>"start"===e||"top"===e||"bottom"===e,style:{marginLeft:16}}]})))),$=d("span",{name:"MuiFormControlLabel",slot:"Asterisk"})(m((({theme:e})=>({[`&.${k.error}`]:{color:(e.vars||e).palette.error.main}})))),x=e.forwardRef((function(s,l){const i=t({props:s,name:"MuiFormControlLabel"}),{checked:n,className:c,componentsProps:d={},control:m,disabled:h,disableTypography:v,inputRef:k,label:x,labelPlacement:S="end",name:P,onChange:C,required:j,slots:B={},slotProps:z={},value:R,...M}=i,F=g(),T=h??m.props.disabled??F?.disabled,L=j??m.props.required,N={disabled:T,required:L};["checked","name","onChange","value","inputRef"].forEach((e=>{void 0===m.props[e]&&void 0!==i[e]&&(N[e]=i[e])}));const q=u({props:i,muiFormControl:F,states:["error"]}),D={...i,disabled:T,labelPlacement:S,required:L,error:q.error},I=(e=>{const{classes:t,disabled:a,labelPlacement:r,error:s,required:l}=e,i={root:["root",a&&"disabled",`labelPlacement${o(r)}`,s&&"error",l&&"required"],label:["label",a&&"disabled"],asterisk:["asterisk",s&&"error"]};return p(i,f,t)})(D),O={slots:B,slotProps:{...d,...z}},[A,E]=b("typography",{elementType:w,externalForwardedProps:O,ownerState:D});let X=x;return null==X||X.type===w||v||(X=a.jsx(A,{component:"span",...E,className:r(I.label,E?.className),children:X})),a.jsxs(y,{className:r(I.root,c),ownerState:D,ref:l,...M,children:[e.cloneElement(m,N),L?a.jsxs("div",{children:[X,a.jsxs($,{ownerState:D,"aria-hidden":!0,className:I.asterisk,children:[" ","*"]})]}):X]})}));function S(e){return c("MuiSwitch",e)}const P=n("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]),C=d("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.root,a.edge&&t[`edge${o(a.edge)}`],t[`size${o(a.size)}`]]}})({display:"inline-flex",width:58,height:38,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${P.thumb}`]:{width:16,height:16},[`& .${P.switchBase}`]:{padding:4,[`&.${P.checked}`]:{transform:"translateX(16px)"}}}}]}),j=d(v,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.switchBase,{[`& .${P.input}`]:t.input},"default"!==a.color&&t[`color${o(a.color)}`]]}})(m((({theme:e})=>({position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:`${"light"===e.palette.mode?e.palette.common.white:e.palette.grey[300]}`,transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),[`&.${P.checked}`]:{transform:"translateX(20px)"},[`&.${P.disabled}`]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:`${"light"===e.palette.mode?e.palette.grey[100]:e.palette.grey[600]}`},[`&.${P.checked} + .${P.track}`]:{opacity:.5},[`&.${P.disabled} + .${P.track}`]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:""+("light"===e.palette.mode?.12:.2)},[`& .${P.input}`]:{left:"-100%",width:"300%"}}))),m((({theme:e})=>({"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:s(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(e.palette).filter(h(["light"])).map((([t])=>({props:{color:t},style:{[`&.${P.checked}`]:{color:(e.vars||e).palette[t].main,"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:s(e.palette[t].main,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${P.disabled}`]:{color:e.vars?e.vars.palette.Switch[`${t}DisabledColor`]:`${"light"===e.palette.mode?l(e.palette[t].main,.62):i(e.palette[t].main,.55)}`}},[`&.${P.checked} + .${P.track}`]:{backgroundColor:(e.vars||e).palette[t].main}}})))]})))),B=d("span",{name:"MuiSwitch",slot:"Track"})(m((({theme:e})=>({height:"100%",width:"100%",borderRadius:7,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:`${"light"===e.palette.mode?e.palette.common.black:e.palette.common.white}`,opacity:e.vars?e.vars.opacity.switchTrack:""+("light"===e.palette.mode?.38:.3)})))),z=d("span",{name:"MuiSwitch",slot:"Thumb"})(m((({theme:e})=>({boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"})))),R=e.forwardRef((function(e,s){const l=t({props:e,name:"MuiSwitch"}),{className:i,color:n="primary",edge:c=!1,size:d="medium",sx:m,slots:h={},slotProps:u={},...g}=l,w={...l,color:n,edge:c,size:d},v=(e=>{const{classes:t,edge:a,size:r,color:s,checked:l,disabled:i}=e,n={root:["root",a&&`edge${o(a)}`,`size${o(r)}`],switchBase:["switchBase",`color${o(s)}`,l&&"checked",i&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},c=p(n,S,t);return{...t,...c}})(w),f={slots:h,slotProps:u},[k,y]=b("root",{className:r(v.root,i),elementType:C,externalForwardedProps:f,ownerState:w,additionalProps:{sx:m}}),[$,x]=b("thumb",{className:v.thumb,elementType:z,externalForwardedProps:f,ownerState:w}),P=a.jsx($,{...x}),[R,M]=b("track",{className:v.track,elementType:B,externalForwardedProps:f,ownerState:w});return a.jsxs(k,{...y,children:[a.jsx(j,{type:"checkbox",icon:P,checkedIcon:P,ref:s,ownerState:w,...g,classes:{...v,root:v.switchBase},slots:{...h.switchBase&&{root:h.switchBase},...h.input&&{input:h.input}},slotProps:{...u.switchBase&&{root:"function"==typeof u.switchBase?u.switchBase(w):u.switchBase},...u.input&&{input:"function"==typeof u.input?u.input(w):u.input}}}),a.jsx(R,{...M})]})}));export{x as F,R as S,k as f};
