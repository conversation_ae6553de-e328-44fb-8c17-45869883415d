import{b as s,s as e,c as r,j as t,a as i}from"../entries/index-xsXxT3-W.js";import{L as n}from"./Layout-DaeN7D4t.js";import{B as o}from"./Button-BeKLLPpp.js";import{d as a}from"./router-BtYqujaw.js";import"./vendor-dblfw9z9.js";const c=new s({fr:{NO_MATCH:"Rien à voir ici !"},en:{NO_MATCH:"Nothing to see here!"},es:{NO_MATCH:"¡Nada que ver aquí!"}});e(c);const d=s=>{const e=r.c(5),{hideHeader:d}=s,l=a();let m;e[0]!==l?(m=()=>t.jsxs("div",{className:"msg",children:[t.jsx("h2",{children:c.NO_MATCH}),t.jsx("p",{children:t.jsx(o,{variant:"text",onClick:()=>l("/"),className:"btn-lnk",children:i.GO_TO_HOME})})]}),e[0]=l,e[1]=m):m=e[1];const j=m;let h;return e[2]!==d||e[3]!==j?(h=d?j():t.jsx(n,{strict:!1,children:j()}),e[2]=d,e[3]=j,e[4]=h):h=e[4],h};export{d as default};
