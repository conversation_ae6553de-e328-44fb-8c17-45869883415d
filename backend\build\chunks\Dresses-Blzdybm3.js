import{j as e,c as t,S as s,a as r,T as c,V as n,W as a,X as l,e as o,Y as i,J as d,z as u,h as m,F as h}from"../entries/index-CEzJO5Xy.js";import{r as j,d as p}from"./router-BtYqujaw.js";import{h as x}from"./SupplierService-DSnTbAgG.js";import{L as S}from"./Layout-BQBjg4Lf.js";import{D as f}from"./DressList-P4mtcbk2.js";import{I as v,F as g}from"./InputLabel-BbcIE26O.js";import{M as b}from"./MenuItem-suKfXYI2.js";import{S as _,I as k,T}from"./TextField-BAse--ht.js";import{A as N}from"./Accordion-D82axyqp.js";import{I as L}from"./IconButton-CnBvmeAK.js";import{S as E}from"./Search-BNrZEqND.js";import{C}from"./Clear-BpXDeTL8.js";import{B as I}from"./Button-DGZYUY3P.js";import{c as y}from"./Grow-CjOKj0i1.js";import"./vendor-dblfw9z9.js";import"./DressService-J0XavNJj.js";import"./Pager-C0zDCFUN.js";import"./ArrowForwardIos-BMce9t8T.js";import"./Paper-CcwAvfvc.js";import"./SimpleBackdrop-Bf3qjF13.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./SupplierBadge-C4yEjxHC.js";import"./Tooltip-BkJF6Mu0.js";import"./Checkroom-Bt6MiDKF.js";import"./Straighten-6ibCitj5.js";import"./Check-D745pofy.js";import"./Visibility-BpW589Gm.js";import"./Edit-DIF9Bumd.js";import"./Delete-CnqjtpsJ.js";import"./DialogTitle-BZXwroUN.js";import"./Info-C_WcR51V.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";import"./Menu-ZU0DMgjT.js";import"./mergeSlotProps-Cay5TZBz.js";import"./listItemTextClasses-DFwCkkgK.js";import"./OutlinedInput-g8mR4MM3.js";import"./Input-BQdee9z7.js";import"./FormHelperText-DFSsjBsL.js";const A=y(e.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),R=n=>{const a=t.c(11),{className:l,onChange:o}=n,[i,d]=j.useState("");let u;a[0]!==o?(u=e=>{const t=e.target.value;d(t),o&&o(t)},a[0]=o,a[1]=u):u=a[1];const m=u;let h,p,x,S,f;return a[2]===Symbol.for("react.memo_cache_sentinel")?(h=e.jsx(v,{id:"type-label",children:s.DRESS_TYPE}),a[2]=h):h=a[2],a[3]===Symbol.for("react.memo_cache_sentinel")?(p=e.jsx(b,{value:"",children:r.ALL}),x=c().map(D),a[3]=p,a[4]=x):(p=a[3],x=a[4]),a[5]!==m||a[6]!==i?(S=e.jsxs(_,{labelId:"type-label",value:i,label:s.DRESS_TYPE,onChange:m,autoWidth:!0,children:[p,x]}),a[5]=m,a[6]=i,a[7]=S):S=a[7],a[8]!==l||a[9]!==S?(f=e.jsxs(g,{className:l,children:[h,S]}),a[8]=l,a[9]=S,a[10]=f):f=a[10],f};function D(t){return e.jsx(b,{value:t,children:n(t)},t)}const U=c=>{const n=t.c(14),{className:o,onChange:i}=c,[d,u]=j.useState("");let m;n[0]!==i?(m=e=>{const t=e.target.value;u(t),i&&i(t)},n[0]=i,n[1]=m):m=n[1];const h=m;let p,x,S,f,k,T,N,L;return n[2]===Symbol.for("react.memo_cache_sentinel")?(p=e.jsx(v,{id:"size-label",children:s.DRESS_SIZE}),n[2]=p):p=n[2],n[3]===Symbol.for("react.memo_cache_sentinel")?(x=e.jsx(b,{value:"",children:r.ALL}),n[3]=x):x=n[3],n[4]===Symbol.for("react.memo_cache_sentinel")?(S=e.jsx(b,{value:l.Small,children:a(l.Small)}),n[4]=S):S=n[4],n[5]===Symbol.for("react.memo_cache_sentinel")?(f=e.jsx(b,{value:l.Medium,children:a(l.Medium)}),n[5]=f):f=n[5],n[6]===Symbol.for("react.memo_cache_sentinel")?(k=e.jsx(b,{value:l.Large,children:a(l.Large)}),n[6]=k):k=n[6],n[7]===Symbol.for("react.memo_cache_sentinel")?(T=e.jsx(b,{value:l.ExtraLarge,children:a(l.ExtraLarge)}),n[7]=T):T=n[7],n[8]!==h||n[9]!==d?(N=e.jsxs(_,{labelId:"size-label",value:d,label:s.DRESS_SIZE,onChange:h,autoWidth:!0,children:[x,S,f,k,T]}),n[8]=h,n[9]=d,n[10]=N):N=n[10],n[11]!==o||n[12]!==N?(L=e.jsxs(g,{className:o,children:[p,N]}),n[11]=o,n[12]=N,n[13]=L):L=n[13],L},V=c=>{const n=t.c(14),{className:a,onChange:l}=c,[o,i]=j.useState("");let d;n[0]!==l?(d=e=>{const t=e.target.value;i(t),l&&l(t)},n[0]=l,n[1]=d):d=n[1];const u=d;let m,h,p,x,S,f,k,T;return n[2]===Symbol.for("react.memo_cache_sentinel")?(m=e.jsx(v,{id:"style-label",children:s.DRESS_STYLE}),n[2]=m):m=n[2],n[3]===Symbol.for("react.memo_cache_sentinel")?(h=e.jsx(b,{value:"",children:r.ALL}),p=e.jsx(b,{value:"traditional",children:s.STYLE_TRADITIONAL}),x=e.jsx(b,{value:"modern",children:s.STYLE_MODERN}),S=e.jsx(b,{value:"vintage",children:s.STYLE_VINTAGE}),f=e.jsx(b,{value:"designer",children:s.STYLE_DESIGNER}),n[3]=h,n[4]=p,n[5]=x,n[6]=S,n[7]=f):(h=n[3],p=n[4],x=n[5],S=n[6],f=n[7]),n[8]!==u||n[9]!==o?(k=e.jsxs(_,{labelId:"style-label",value:o,label:s.DRESS_STYLE,onChange:u,autoWidth:!0,children:[h,p,x,S,f]}),n[8]=u,n[9]=o,n[10]=k):k=n[10],n[11]!==a||n[12]!==k?(T=e.jsxs(g,{className:a,children:[m,k]}),n[11]=a,n[12]=k,n[13]=T):T=n[13],T},w=c=>{const n=t.c(55),{className:a,collapse:l,onChange:i}=c,d=j.useRef(null),u=j.useRef(null),m=j.useRef(null),h=j.useRef(null);let p,x,S;n[0]===Symbol.for("react.memo_cache_sentinel")?(p=()=>{h.current&&(h.current.checked=!0)},x=[],n[0]=p,n[1]=x):(p=n[0],x=n[1]),j.useEffect(p,x),n[2]!==i?(S=e=>{if("checked"in e.currentTarget&&e.currentTarget.checked){const e=-1;d.current&&(d.current.checked=!1),u.current&&(u.current.checked=!1),m.current&&(m.current.checked=!1),i&&i(e)}},n[2]=i,n[3]=S):S=n[3];const f=S;let v;n[4]!==f?(v=e=>{const t=e.currentTarget.previousSibling;if(!t.checked){t.checked=!t.checked;const s=e;s.currentTarget=t,f(s)}},n[4]=f,n[5]=v):v=n[5];const g=v;let b;n[6]!==i?(b=e=>{if("checked"in e.currentTarget&&e.currentTarget.checked){const e=o.DEPOSIT_FILTER_VALUE_1;h.current&&(h.current.checked=!1),u.current&&(u.current.checked=!1),m.current&&(m.current.checked=!1),i&&i(e)}},n[6]=i,n[7]=b):b=n[7];const _=b;let k;n[8]!==_?(k=e=>{const t=e.currentTarget.previousSibling;if(!t.checked){t.checked=!t.checked;const s=e;s.currentTarget=t,_(s)}},n[8]=_,n[9]=k):k=n[9];const T=k;let L;n[10]!==i?(L=e=>{if("checked"in e.currentTarget&&e.currentTarget.checked){const e=o.DEPOSIT_FILTER_VALUE_2;h.current&&(h.current.checked=!1),d.current&&(d.current.checked=!1),m.current&&(m.current.checked=!1),i&&i(e)}},n[10]=i,n[11]=L):L=n[11];const E=L;let C;n[12]!==E?(C=e=>{const t=e.currentTarget.previousSibling;if(!t.checked){t.checked=!t.checked;const s=e;s.currentTarget=t,E(s)}},n[12]=E,n[13]=C):C=n[13];const I=C;let y;n[14]!==i?(y=e=>{if("checked"in e.currentTarget&&e.currentTarget.checked){const e=o.DEPOSIT_FILTER_VALUE_3;h.current&&(h.current.checked=!1),d.current&&(d.current.checked=!1),u.current&&(u.current.checked=!1),i&&i(e)}},n[14]=i,n[15]=y):y=n[15];const A=y;let R;n[16]!==A?(R=e=>{const t=e.currentTarget.previousSibling;if(!t.checked){t.checked=!t.checked;const s=e;s.currentTarget=t,A(s)}},n[16]=A,n[17]=R):R=n[17];const D=R,U=(a?`${a} `:"")+"deposit-filter";let V,w,H,Y,O,P,z,B,F,M,W,G,K,$;return n[18]!==_?(V=e.jsx("input",{ref:d,type:"radio",className:"deposit-radio",onChange:_}),n[18]=_,n[19]=V):V=n[19],n[20]!==T?(w=e.jsx("span",{onClick:T,role:"button",tabIndex:0,children:s.LESS_THAN_VALUE_1}),n[20]=T,n[21]=w):w=n[21],n[22]!==V||n[23]!==w?(H=e.jsxs("div",{className:"filter-element",children:[V,w]}),n[22]=V,n[23]=w,n[24]=H):H=n[24],n[25]!==E?(Y=e.jsx("input",{ref:u,type:"radio",className:"deposit-radio",onChange:E}),n[25]=E,n[26]=Y):Y=n[26],n[27]!==I?(O=e.jsx("span",{onClick:I,role:"button",tabIndex:0,children:s.LESS_THAN_VALUE_2}),n[27]=I,n[28]=O):O=n[28],n[29]!==Y||n[30]!==O?(P=e.jsxs("div",{className:"filter-element",children:[Y,O]}),n[29]=Y,n[30]=O,n[31]=P):P=n[31],n[32]!==A?(z=e.jsx("input",{ref:m,type:"radio",className:"deposit-radio",onChange:A}),n[32]=A,n[33]=z):z=n[33],n[34]!==D?(B=e.jsx("span",{onClick:D,role:"button",tabIndex:0,children:s.LESS_THAN_VALUE_3}),n[34]=D,n[35]=B):B=n[35],n[36]!==z||n[37]!==B?(F=e.jsxs("div",{className:"filter-element",children:[z,B]}),n[36]=z,n[37]=B,n[38]=F):F=n[38],n[39]!==f?(M=e.jsx("input",{ref:h,type:"radio",className:"deposit-radio",onChange:f}),n[39]=f,n[40]=M):M=n[40],n[41]!==g?(W=e.jsx("span",{onClick:g,role:"button",tabIndex:0,children:r.ALL}),n[41]=g,n[42]=W):W=n[42],n[43]!==M||n[44]!==W?(G=e.jsxs("div",{className:"filter-element",children:[M,W]}),n[43]=M,n[44]=W,n[45]=G):G=n[45],n[46]!==H||n[47]!==P||n[48]!==F||n[49]!==G?(K=e.jsxs("div",{className:"filter-elements",children:[H,P,F,G]}),n[46]=H,n[47]=P,n[48]=F,n[49]=G,n[50]=K):K=n[50],n[51]!==l||n[52]!==U||n[53]!==K?($=e.jsx(N,{title:s.DEPOSIT,collapse:l,className:U,children:K}),n[51]=l,n[52]=U,n[53]=K,n[54]=$):$=n[54],$},H=[i.Available,i.Unavailable],Y=c=>{const n=t.c(43),{className:a,onChange:l}=c,[o,m]=j.useState(!1);let h;n[0]===Symbol.for("react.memo_cache_sentinel")?(h=[],n[0]=h):h=n[0];const[p,x]=j.useState(h),S=j.useRef(null),f=j.useRef(null);let v,g,b;n[1]!==o?(v=()=>{o&&S.current&&f.current&&(S.current.checked=!0,f.current.checked=!0)},g=[o],n[1]=o,n[2]=v,n[3]=g):(v=n[2],g=n[3]),j.useEffect(v,g),n[4]!==l?(b=e=>{l&&l(d(0===e.length?H:e))},n[4]=l,n[5]=b):b=n[5];const _=b;let k;n[6]!==_||n[7]!==p?(k=e=>{e.currentTarget instanceof HTMLInputElement?(e.currentTarget.checked?(p.push(i.Available),2===p.length&&m(!0)):(p.splice(p.findIndex(O),1),0===p.length&&m(!1)),x(p),_(p)):u()},n[6]=_,n[7]=p,n[8]=k):k=n[8];const T=k;let L;n[9]!==T?(L=e=>{const t=e.currentTarget.previousSibling;t.checked=!t.checked;const s=e;s.currentTarget=t,T(s)},n[9]=T,n[10]=L):L=n[10];const E=L;let C;n[11]!==_||n[12]!==p?(C=e=>{e.currentTarget instanceof HTMLInputElement?(e.currentTarget.checked?(p.push(i.Unavailable),2===p.length&&m(!0)):(p.splice(p.findIndex(P),1),0===p.length&&m(!1)),x(p),_(p)):u()},n[11]=_,n[12]=p,n[13]=C):C=n[13];const I=C;let y;n[14]!==I?(y=e=>{const t=e.currentTarget.previousSibling;t.checked=!t.checked;const s=e;s.currentTarget=t,I(s)},n[14]=I,n[15]=y):y=n[15];const A=y;let R;n[16]!==o||n[17]!==l?(R=()=>{if(S.current&&f.current)if(o)S.current.checked=!1,f.current.checked=!1,m(!1),x([]);else{S.current.checked=!0,f.current.checked=!0;const e=[i.Available,i.Unavailable];m(!0),x(e),l&&l(d(e))}else u()},n[16]=o,n[17]=l,n[18]=R):R=n[18];const D=R,U=(a?`${a} `:"")+"availability-filter";let V,w,Y,z,B,F;n[19]!==T?(V=e.jsx("input",{ref:S,type:"checkbox",className:"availability-checkbox",onChange:T}),n[19]=T,n[20]=V):V=n[20],n[21]!==E?(w=e.jsx("span",{role:"button",tabIndex:0,onClick:E,children:s.AVAILABLE}),n[21]=E,n[22]=w):w=n[22],n[23]!==V||n[24]!==w?(Y=e.jsxs("div",{className:"filter-element",children:[V,w]}),n[23]=V,n[24]=w,n[25]=Y):Y=n[25],n[26]!==I?(z=e.jsx("input",{ref:f,type:"checkbox",className:"availability-checkbox",onChange:I}),n[26]=I,n[27]=z):z=n[27],n[28]!==A?(B=e.jsx("span",{role:"button",tabIndex:0,onClick:A,children:s.UNAVAILABLE}),n[28]=A,n[29]=B):B=n[29],n[30]!==z||n[31]!==B?(F=e.jsxs("div",{className:"filter-element",children:[z,B]}),n[30]=z,n[31]=B,n[32]=F):F=n[32];const M=o?r.UNCHECK_ALL:r.CHECK_ALL;let W,G,K;return n[33]!==D||n[34]!==M?(W=e.jsx("div",{className:"filter-actions",children:e.jsx("span",{role:"button",tabIndex:0,onClick:D,className:"uncheckall",children:M})}),n[33]=D,n[34]=M,n[35]=W):W=n[35],n[36]!==Y||n[37]!==F||n[38]!==W?(G=e.jsxs("div",{className:"filter-elements",children:[Y,F,W]}),n[36]=Y,n[37]=F,n[38]=W,n[39]=G):G=n[39],n[40]!==U||n[41]!==G?(K=e.jsx(N,{title:s.AVAILABILITY,className:U,children:G}),n[40]=U,n[41]=G,n[42]=K):K=n[42],K};function O(e){return e===i.Available}function P(e){return e===i.Unavailable}const z=c=>{const n=t.c(15),{className:a,onChange:l}=c,[o,i]=j.useState("");let d;n[0]!==l?(d=e=>{const t=e.target.value;i(t),l&&l(t)},n[0]=l,n[1]=d):d=n[1];const u=d;let m,h,p,x,S,f,k,T,N;return n[2]===Symbol.for("react.memo_cache_sentinel")?(m=e.jsx(v,{id:"rentals-count-label",children:s.RENTALS_COUNT}),n[2]=m):m=n[2],n[3]===Symbol.for("react.memo_cache_sentinel")?(h=e.jsx(b,{value:"",children:r.ALL}),p=e.jsx(b,{value:"0",children:"0"}),x=e.jsx(b,{value:"1-5",children:"1-5"}),S=e.jsx(b,{value:"6-10",children:"6-10"}),f=e.jsx(b,{value:"11-20",children:"11-20"}),k=e.jsx(b,{value:"20+",children:"20+"}),n[3]=h,n[4]=p,n[5]=x,n[6]=S,n[7]=f,n[8]=k):(h=n[3],p=n[4],x=n[5],S=n[6],f=n[7],k=n[8]),n[9]!==u||n[10]!==o?(T=e.jsxs(_,{labelId:"rentals-count-label",value:o,label:s.RENTALS_COUNT,onChange:u,autoWidth:!0,children:[h,p,x,S,f,k]}),n[9]=u,n[10]=o,n[11]=T):T=n[11],n[12]!==a||n[13]!==T?(N=e.jsxs(g,{className:a,children:[m,T]}),n[12]=a,n[13]=T,n[14]=N):N=n[14],N},B=s=>{const r=t.c(23),{value:c,placeholder:n,onChange:a,onSubmit:l,className:o}=s,i=void 0===c?"":c,d=void 0===n?"Search...":n,[u,m]=j.useState(i);let h;r[0]!==a?(h=e=>{const t=e.target.value;m(t),a&&a(t)},r[0]=a,r[1]=h):h=r[1];const p=h;let x;r[2]!==l||r[3]!==u?(x=e=>{e.preventDefault(),l&&l(u)},r[2]=l,r[3]=u,r[4]=x):x=r[4];const S=x;let f;r[5]!==a||r[6]!==l?(f=()=>{m(""),a&&a(""),l&&l("")},r[5]=a,r[6]=l,r[7]=f):f=r[7];const v=f;let g,b,_,N,I;return r[8]===Symbol.for("react.memo_cache_sentinel")?(g=e.jsx(k,{position:"start",children:e.jsx(L,{type:"submit",size:"small",children:e.jsx(E,{})})}),r[8]=g):g=r[8],r[9]!==v||r[10]!==u?(b=u&&e.jsx(k,{position:"end",children:e.jsx(L,{onClick:v,size:"small",children:e.jsx(C,{})})}),r[9]=v,r[10]=u,r[11]=b):b=r[11],r[12]!==b?(_={startAdornment:g,endAdornment:b},r[12]=b,r[13]=_):_=r[13],r[14]!==p||r[15]!==d||r[16]!==u||r[17]!==_?(N=e.jsx(T,{value:u,onChange:p,placeholder:d,variant:"outlined",size:"small",fullWidth:!0,InputProps:_}),r[14]=p,r[15]=d,r[16]=u,r[17]=_,r[18]=N):N=r[18],r[19]!==o||r[20]!==S||r[21]!==N?(I=e.jsx("form",{onSubmit:S,className:o,children:N}),r[19]=o,r[20]=S,r[21]=N,r[22]=I):I=r[22],I},F=()=>{const t=p(),[r,c]=j.useState(),[n,a]=j.useState(!1),[l,o]=j.useState([]),[i,d]=j.useState([]),[v,g]=j.useState(""),[b,_]=j.useState(""),[k,T]=j.useState(""),[N,L]=j.useState(""),[E,C]=j.useState(""),[y,D]=j.useState(""),[H,O]=j.useState(""),[P,F]=j.useState(!1),[M,W]=j.useState(!0),[G,K]=j.useState(0);return j.useEffect((()=>{(async()=>{try{const e=m();if(e){const t=h(e);c(e),a(t);const s=await x(),r=t?s.map((e=>e._id)):[e._id];o(s),d(r.filter((e=>void 0!==e)))}}catch(e){u(e)}finally{W(!1)}})()}),[]),e.jsx(S,{children:r&&e.jsxs("div",{className:"dresses",children:[e.jsx("div",{className:"col-1",children:e.jsxs("div",{className:"col-1-container",children:[e.jsx(B,{className:"dress-search",onSubmit:e=>{O(e),F(!0)}}),e.jsx(I,{variant:"contained",className:"btn-primary new-dress",startIcon:e.jsx(A,{}),onClick:()=>{t("/create-dress")},children:s.NEW_DRESS}),e.jsxs("div",{className:"dress-filters",children:[e.jsx(R,{className:"dress-filter",onChange:e=>{g(e),F(!0)}}),e.jsx(U,{className:"dress-filter",onChange:e=>{_(e),F(!0)}}),e.jsx(V,{className:"dress-filter",onChange:e=>{T(e),F(!0)}}),e.jsx(w,{className:"dress-filter",onChange:e=>{L(e.toString()),F(!0)}}),e.jsx(z,{className:"dress-filter",onChange:e=>{D(e),F(!0)}}),n&&e.jsx(Y,{className:"dress-filter",onChange:e=>{C(e.join(",")),F(!0)}})]})]})}),e.jsx("div",{className:"col-2",children:e.jsx("div",{className:"col-2-container",children:e.jsx(f,{user:r,suppliers:i,dressType:v?[v]:[],dressSize:b?[b]:[],dressStyle:k?[k]:[],deposit:N?Number(N):void 0,availability:E?E.split(","):[],rentalsCount:y,keyword:H,loading:M,onLoad:e=>{e&&void 0!==e.rowCount&&K(e.rowCount)},onDelete:e=>{K(e)}})})})]})})};export{F as default};
