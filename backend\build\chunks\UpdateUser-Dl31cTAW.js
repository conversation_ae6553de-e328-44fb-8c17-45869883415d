import{b as e,s as t,R as r,j as s,a,ac as i,U as n,e as o,g as l,F as u,z as d,p as m,ax as p,J as c,B as j,aw as h,A as f,N as x}from"../entries/index-CEzJO5Xy.js";import{d as S,r as g}from"./router-BtYqujaw.js";import{L as v}from"./Layout-BQBjg4Lf.js";import{s as E}from"./create-supplier-D6E0ETct.js";import{s as A,i as N}from"./create-user-yOc6o3tp.js";import{v as _}from"./SupplierService-DSnTbAgG.js";import I from"./NoMatch-jvHCs4x8.js";import{E as y}from"./Error-7KgmWHkR.js";import{S as C}from"./SimpleBackdrop-Bf3qjF13.js";import{A as D}from"./Avatar-BtfxKR-8.js";import{D as T}from"./DatePicker-B527VDUP.js";import{P as b}from"./Paper-CcwAvfvc.js";import{I as L}from"./Info-C_WcR51V.js";import{F as P,I as R}from"./InputLabel-BbcIE26O.js";import{S as U}from"./TextField-BAse--ht.js";import{M}from"./MenuItem-suKfXYI2.js";import{I as w}from"./Input-BQdee9z7.js";import{F as O}from"./FormHelperText-DFSsjBsL.js";import{F as k,S as W}from"./Switch-BWPUOSX1.js";import{B}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./normalizeInterval-a5pcD5yp.js";import"./format-4arn0GRM.js";import"./useMobilePicker-Cpitw7qm.js";import"./getThemeProps-gt86ccpv.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./IconButton-CnBvmeAK.js";import"./useFormControl-B7jXtRD7.js";import"./ListItem-D1VHRhQp.js";import"./isHostComponent-DR4iSCFs.js";import"./Menu-ZU0DMgjT.js";import"./mergeSlotProps-Cay5TZBz.js";import"./Chip-CAtDqtgp.js";import"./DressService-J0XavNJj.js";import"./LocationService-BtQFgoWL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";import"./DatePicker-CyKPL9FL.js";import"./fr-CaQg1DLH.js";import"./OutlinedInput-g8mR4MM3.js";import"./listItemTextClasses-DFwCkkgK.js";import"./SwitchBase-BIeqtL5F.js";const G=new e({fr:{UPDATE_USER_HEADING:"Modification de l'utilisateur"},en:{UPDATE_USER_HEADING:"User update"},es:{UPDATE_USER_HEADING:"Actualización del usuario"},ar:{UPDATE_USER_HEADING:"تحديث المستخدم"}});t(G);const H=()=>{const e=S(),[t,H]=g.useState(),[F,z]=g.useState(),[V,Y]=g.useState(!1),[q,K]=g.useState(!1),[$,Z]=g.useState(!1),[J,Q]=g.useState(""),[X,ee]=g.useState(""),[te,re]=g.useState(""),[se,ae]=g.useState(""),[ie,ne]=g.useState(""),[oe,le]=g.useState(!1),[ue,de]=g.useState(!1),[me,pe]=g.useState(!1),[ce,je]=g.useState(""),[he,fe]=g.useState(!1),[xe,Se]=g.useState(""),[ge,ve]=g.useState(),[Ee,Ae]=g.useState(!0),[Ne,_e]=g.useState(!0),[Ie,ye]=g.useState(!1),[Ce,De]=g.useState(""),[Te,be]=g.useState(""),[Le,Pe]=g.useState(""),[Re,Ue]=g.useState(!1),[Me,we]=g.useState(!1),Oe=async(e,t=!0)=>{const r=e||J;if(!r||!t&&(t||r===F?.fullName))return pe(!1),!0;try{return 200===await _({fullName:r})?(pe(!1),le(!1),!0):(pe(!0),fe(!1),le(!1),!1)}catch(s){return d(s),!0}},ke=e=>{if(e){const t=x.isMobilePhone(e);return _e(t),t}return _e(!0),!0},We=e=>{if(e&&h(e)&&xe===r.User){const t=(N({start:e,end:new Date}).years??0)>=o.MINIMUM_AGE;return Ae(t),t}return Ae(!0),!0},Be=xe===r.Supplier,Ge=xe===r.User,He=$||t&&F&&t.type===r.Supplier&&F.type===r.User&&F.supplier===t._id;return s.jsxs(v,{onLoad:async e=>{if(e&&e.verified){de(!0);const r=new URLSearchParams(window.location.search);if(r.has("u")){const s=r.get("u");if(s&&""!==s)try{const t=await l(s);if(t){if(!(e.type===n.Admin||t.type===n.Supplier&&e._id===t._id||t.type===n.User&&e._id===t.supplier))return de(!1),void K(!0);H(e),z(t),Z(u(e)),Se(t.type||""),ee(t.email||""),je(t.avatar||""),Q(t.fullName||""),re(t.phone||""),ae(t.location||""),ne(t.bio||""),ve(t&&t.birthDate?new Date(t.birthDate):void 0),ye(t.payLater||!1),De(t.minimumRentalDays?.toString()||""),be(t.priceChangeRate?.toString()||""),Pe(t.supplierDressLimit?.toString()||""),Ue(!!t.notifyAdminOnNewDress),we(!!t.blacklisted),Y(!0),de(!1)}else de(!1),K(!0)}catch(t){d(t),de(!1),Y(!1)}else de(!1),K(!0)}else de(!1),K(!0)}},strict:!0,children:[t&&F&&V&&s.jsx("div",{className:"update-user",children:s.jsxs(b,{className:"user-form user-form-wrapper",elevation:10,children:[s.jsx("h1",{className:"user-form-title",children:G.UPDATE_USER_HEADING}),s.jsxs("form",{onSubmit:async e=>{try{if(e.preventDefault(),!F)return void d();if(xe===r.Supplier){if(!(await Oe(J,!1)))return}else pe(!1);if(!ke(te))return;if(!We(ge))return;if(xe===r.Supplier&&!ce)return fe(!0),void le(!1);const t=m(),s={_id:F._id,phone:te,location:se,bio:ie,fullName:J,language:t,type:xe,avatar:ce,birthDate:ge,minimumRentalDays:Ce?Number(Ce):void 0,priceChangeRate:Te?Number(Te):void 0,supplierDressLimit:Le?Number(Le):void 0,notifyAdminOnNewDress:xe===r.Supplier?Re:void 0,blacklisted:Me};if(xe===r.Supplier&&(s.payLater=Ie),200===await p(s)){const e=c(F);e.fullName=J,e.type=xe,z(e),j(a.UPDATED)}else d(),le(!1)}catch(t){d(t)}},children:[s.jsx(D,{type:xe,mode:"update",record:F,size:"large",readonly:!1,onBeforeUpload:()=>{de(!0)},onChange:e=>{if(t&&F&&t._id===F._id){const r=c(t);r.avatar=e,H(r)}const s=c(F);s.avatar=e,de(!1),z(s),je(e),null!==e&&xe===r.Supplier&&fe(!1)},color:"disabled",className:"avatar-ctn",hideDelete:xe===r.Supplier}),Be&&s.jsxs("div",{className:"info",children:[s.jsx(L,{}),s.jsx("span",{children:E.RECOMMENDED_IMAGE_SIZE})]}),$&&s.jsxs(P,{fullWidth:!0,margin:"dense",style:{marginTop:Be?0:39},children:[s.jsx(R,{className:"required",children:a.TYPE}),s.jsxs(U,{label:a.TYPE,value:xe,onChange:async e=>{const t=e.target.value;Se(e.target.value),t===r.Supplier?await Oe(J):pe(!1)},variant:"standard",required:!0,fullWidth:!0,children:[s.jsx(M,{value:r.Admin,children:i(n.Admin)}),s.jsx(M,{value:r.Supplier,children:i(n.Supplier)}),s.jsx(M,{value:r.User,children:i(n.User)})]})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(R,{className:"required",children:a.FULL_NAME}),s.jsx(w,{id:"full-name",type:"text",error:me,required:!0,onBlur:async e=>{xe===r.Supplier?await Oe(e.target.value):pe(!1)},onChange:e=>{Q(e.target.value),e.target.value||pe(!1)},autoComplete:"off",value:J}),s.jsx(O,{error:me,children:me&&E.INVALID_SUPPLIER_NAME||""})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(R,{className:"required",children:a.EMAIL}),s.jsx(w,{id:"email",type:"text",value:X,disabled:!0})]}),s.jsx(P,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(W,{checked:Me,onChange:e=>{we(e.target.checked)},color:"primary"}),label:a.BLACKLISTED,title:a.BLACKLISTED_TOOLTIP})}),Ge&&s.jsx(s.Fragment,{children:s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(T,{label:A.BIRTH_DATE,value:ge,required:!0,onChange:e=>{if(e){const t=We(e);ve(e),Ae(t)}},language:F&&F.language||o.DEFAULT_LANGUAGE}),s.jsx(O,{error:!Ee,children:!Ee&&a.BIRTH_DATE_NOT_VALID||""})]})}),Be&&s.jsxs(s.Fragment,{children:[s.jsx(P,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(W,{checked:Ie,onChange:e=>{ye(e.target.checked)},color:"primary"}),label:a.PAY_LATER})}),s.jsx(P,{fullWidth:!0,margin:"dense",children:s.jsx(k,{control:s.jsx(W,{checked:Re,disabled:t?.type===n.Supplier,onChange:e=>{Ue(e.target.checked)},color:"primary"}),label:a.NOTIFY_ADMIN_ON_NEW_CAR})}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(R,{children:a.SUPPLIER_CAR_LIMIT}),s.jsx(w,{type:"text",onChange:e=>{Pe(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^\\d+$"}},value:Le,disabled:t?.type===n.Supplier})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(R,{children:a.MIN_RENTAL_DAYS}),s.jsx(w,{type:"text",onChange:e=>{De(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^\\d+$"}},value:Ce})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(R,{children:a.PRICE_CHANGE_RATE}),s.jsx(w,{type:"text",onChange:e=>{be(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^-?\\d+(\\.\\d+)?$"}},value:Te,disabled:t?.type===n.Supplier})]})]}),s.jsxs("div",{className:"info",children:[s.jsx(L,{}),s.jsx("span",{children:a.OPTIONAL})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(R,{children:a.PHONE}),s.jsx(w,{id:"phone",type:"text",onChange:e=>{re(e.target.value),e.target.value||_e(!0)},onBlur:e=>{ke(e.target.value)},autoComplete:"off",value:te,error:!Ne}),s.jsx(O,{error:!Ne,children:!Ne&&a.PHONE_NOT_VALID||""})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(R,{children:a.LOCATION}),s.jsx(w,{id:"location",type:"text",onChange:e=>{ae(e.target.value)},autoComplete:"off",value:se})]}),s.jsxs(P,{fullWidth:!0,margin:"dense",children:[s.jsx(R,{children:a.BIO}),s.jsx(w,{id:"bio",type:"text",onChange:e=>{ne(e.target.value)},autoComplete:"off",value:ie})]}),He&&s.jsx(P,{fullWidth:!0,margin:"dense",className:"resend-activation-link",children:s.jsx(B,{variant:"outlined",onClick:async()=>{try{200===await f(X,!1,xe===r.User?"frontend":"backend")?j(a.ACTIVATION_EMAIL_SENT):d()}catch(e){d(e)}},children:a.RESEND_ACTIVATION_LINK})}),s.jsxs("div",{className:"buttons",children:[s.jsx(B,{variant:"contained",className:"btn-primary btn-margin btn-margin-bottom",size:"small",onClick:()=>e(`/change-password?u=${F._id}`),children:a.RESET_PASSWORD}),s.jsx(B,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",children:a.SAVE}),s.jsx(B,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{e("/users")},children:a.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[oe&&s.jsx(y,{message:a.GENERIC_ERROR}),he&&s.jsx(y,{message:a.IMAGE_REQUIRED})]})]})]})}),ue&&s.jsx(C,{text:a.PLEASE_WAIT}),q&&s.jsx(I,{hideHeader:!0})]})};export{H as default};
