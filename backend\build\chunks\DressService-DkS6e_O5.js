import{j as e,aF as t,p as s}from"../entries/index-xsXxT3-W.js";import{c as a}from"./Grow-Cp8xsNYl.js";const i=a(e.jsx("path",{d:"M21.6 18.2 13 11.75v-.91c1.65-.49 2.8-2.17 2.43-4.05-.26-1.31-1.3-2.4-2.61-2.7C10.54 3.57 8.5 5.3 8.5 7.5h2c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5c0 .84-.69 1.52-1.53 1.5-.54-.01-.97.45-.97.99v1.76L2.4 18.2c-.77.58-.36 1.8.6 1.8h18c.96 0 1.37-1.22.6-1.8M6 18l6-4.5 6 4.5z"})),d=e=>t.post("/api/create-dress",e,{withCredentials:!0}).then((e=>e.data)),n=e=>t.put("/api/update-dress",e,{withCredentials:!0}).then((e=>e.status)),r=e=>t.delete(`/api/delete-dress/${e}`,{withCredentials:!0}).then((e=>e.status)),p=e=>t.get(`/api/dress/${e}/${s()}`,{withCredentials:!0}).then((e=>e.data)),h=(e,s,a,i)=>t.post(`/api/dresses/${a}/${i}/?s=${encodeURIComponent(e)}`,s,{withCredentials:!0}).then((e=>e.data)),o=e=>t.get(`/api/check-dress/${e}`,{withCredentials:!0}).then((e=>e.status)),l=e=>{const s=new FormData;return s.append("image",e),t.post("/api/create-dress-image",s,{withCredentials:!0}).then((e=>e.data))},c=(e,s)=>{const a=new FormData;return a.append("image",s),t.put(`/api/update-dress-image/${e}`,a,{withCredentials:!0}).then((e=>e.status))},m=e=>t.delete(`/api/delete-dress-image/${e}`,{withCredentials:!0}).then((e=>e.status)),u=e=>t.post(`/api/delete-temp-dress-image/${encodeURIComponent(e)}`,null,{withCredentials:!0}).then((e=>e.status));export{i as D,p as a,u as b,o as c,r as d,d as e,m as f,h as g,l as h,c as i,n as u};
