import{r as e,e as t}from"./router-BtYqujaw.js";import{i as n,j as o,k as r,l as i,aq as a,m as s}from"../entries/index-xsXxT3-W.js";import{g as l,a as c,s as d,c as p,m as u,u as f,e as m,f as h,i as v}from"./Button-BeKLLPpp.js";import{o as g,a as y}from"./ownerWindow-ChLfdzZL.js";import{r as b,m as x,a as w,e as S,u as E}from"./useSlot-DiTut-u0.js";import{u as P,P as O}from"./Paper-C-atefOs.js";import{g as R,B as k,F as T,T as M,a as j,b as A,r as D}from"./Backdrop-Czag2Ija.js";function W(...e){return e.reduce(((e,t)=>null==t?e:function(...n){e.apply(this,n),t.apply(this,n)}),(()=>{}))}function C(e){return l("MuiSvgIcon",e)}c("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const B=d("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,"inherit"!==n.color&&t[`color${i(n.color)}`],t[`fontSize${i(n.fontSize)}`]]}})(u((({theme:e})=>({userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:e.transitions?.create?.("fill",{duration:(e.vars??e).transitions?.duration?.shorter}),variants:[{props:e=>!e.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:e.typography?.pxToRem?.(20)||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:e.typography?.pxToRem?.(24)||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:e.typography?.pxToRem?.(35)||"2.1875rem"}},...Object.entries((e.vars??e).palette).filter((([,e])=>e&&e.main)).map((([t])=>({props:{color:t},style:{color:(e.vars??e).palette?.[t]?.main}}))),{props:{color:"action"},style:{color:(e.vars??e).palette?.action?.active}},{props:{color:"disabled"},style:{color:(e.vars??e).palette?.action?.disabled}},{props:{color:"inherit"},style:{color:void 0}}]})))),F=e.forwardRef((function(t,a){const s=n({props:t,name:"MuiSvgIcon"}),{children:l,className:c,color:d="inherit",component:u="svg",fontSize:f="medium",htmlColor:m,inheritViewBox:h=!1,titleAccess:v,viewBox:g="0 0 24 24",...y}=s,b=e.isValidElement(l)&&"svg"===l.type,x={...s,color:d,component:u,fontSize:f,instanceFontSize:t.fontSize,inheritViewBox:h,viewBox:g,hasSvgAsChild:b},w={};h||(w.viewBox=g);const S=(e=>{const{color:t,fontSize:n,classes:o}=e,r={root:["root","inherit"!==t&&`color${i(t)}`,`fontSize${i(n)}`]};return p(r,C,o)})(x);return o.jsxs(B,{as:u,className:r(S.root,c),focusable:"false",color:m,"aria-hidden":!v||void 0,role:v?"img":void 0,ref:a,...w,...y,...b&&l.props,ownerState:x,children:[b?l.props.children:l,v?o.jsx("title",{children:v}):null]})}));function I(t,n){function r(e,n){return o.jsx(F,{"data-testid":void 0,ref:n,...e,children:t})}return r.muiName=F.muiName,e.memo(e.forwardRef(r))}function N(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function L(t){const{controlled:n,default:o,name:r,state:i="value"}=t,{current:a}=e.useRef(void 0!==n),[s,l]=e.useState(o);return[a?n:s,e.useCallback((e=>{a||l(e)}),[])]}F.muiName="SvgIcon";var z="top",H="bottom",$="right",V="left",q="auto",K=[z,H,$,V],U="start",Y="end",X="viewport",_="popper",G=K.reduce((function(e,t){return e.concat([t+"-"+U,t+"-"+Y])}),[]),J=[].concat(K,[q]).reduce((function(e,t){return e.concat([t,t+"-"+U,t+"-"+Y])}),[]),Q=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function Z(e){return e?(e.nodeName||"").toLowerCase():null}function ee(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function te(e){return e instanceof ee(e).Element||e instanceof Element}function ne(e){return e instanceof ee(e).HTMLElement||e instanceof HTMLElement}function oe(e){return"undefined"!=typeof ShadowRoot&&(e instanceof ee(e).ShadowRoot||e instanceof ShadowRoot)}const re={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},o=t.attributes[e]||{},r=t.elements[e];ne(r)&&Z(r)&&(Object.assign(r.style,n),Object.keys(o).forEach((function(e){var t=o[e];!1===t?r.removeAttribute(e):r.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var o=t.elements[e],r=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});ne(o)&&Z(o)&&(Object.assign(o.style,i),Object.keys(r).forEach((function(e){o.removeAttribute(e)})))}))}},requires:["computeStyles"]};function ie(e){return e.split("-")[0]}var ae=Math.max,se=Math.min,le=Math.round;function ce(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function de(){return!/^((?!chrome|android).)*safari/i.test(ce())}function pe(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var o=e.getBoundingClientRect(),r=1,i=1;t&&ne(e)&&(r=e.offsetWidth>0&&le(o.width)/e.offsetWidth||1,i=e.offsetHeight>0&&le(o.height)/e.offsetHeight||1);var a=(te(e)?ee(e):window).visualViewport,s=!de()&&n,l=(o.left+(s&&a?a.offsetLeft:0))/r,c=(o.top+(s&&a?a.offsetTop:0))/i,d=o.width/r,p=o.height/i;return{width:d,height:p,top:c,right:l+d,bottom:c+p,left:l,x:l,y:c}}function ue(e){var t=pe(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function fe(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&oe(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function me(e){return ee(e).getComputedStyle(e)}function he(e){return["table","td","th"].indexOf(Z(e))>=0}function ve(e){return((te(e)?e.ownerDocument:e.document)||window.document).documentElement}function ge(e){return"html"===Z(e)?e:e.assignedSlot||e.parentNode||(oe(e)?e.host:null)||ve(e)}function ye(e){return ne(e)&&"fixed"!==me(e).position?e.offsetParent:null}function be(e){for(var t=ee(e),n=ye(e);n&&he(n)&&"static"===me(n).position;)n=ye(n);return n&&("html"===Z(n)||"body"===Z(n)&&"static"===me(n).position)?t:n||function(e){var t=/firefox/i.test(ce());if(/Trident/i.test(ce())&&ne(e)&&"fixed"===me(e).position)return null;var n=ge(e);for(oe(n)&&(n=n.host);ne(n)&&["html","body"].indexOf(Z(n))<0;){var o=me(n);if("none"!==o.transform||"none"!==o.perspective||"paint"===o.contain||-1!==["transform","perspective"].indexOf(o.willChange)||t&&"filter"===o.willChange||t&&o.filter&&"none"!==o.filter)return n;n=n.parentNode}return null}(e)||t}function xe(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function we(e,t,n){return ae(e,se(t,n))}function Se(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Ee(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}const Pe={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,r=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=ie(n.placement),l=xe(s),c=[V,$].indexOf(s)>=0?"height":"width";if(i&&a){var d=function(e,t){return Se("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Ee(e,K))}(r.padding,n),p=ue(i),u="y"===l?z:V,f="y"===l?H:$,m=n.rects.reference[c]+n.rects.reference[l]-a[l]-n.rects.popper[c],h=a[l]-n.rects.reference[l],v=be(i),g=v?"y"===l?v.clientHeight||0:v.clientWidth||0:0,y=m/2-h/2,b=d[u],x=g-p[c]-d[f],w=g/2-p[c]/2+y,S=we(b,w,x),E=l;n.modifiersData[o]=((t={})[E]=S,t.centerOffset=S-w,t)}},effect:function(e){var t=e.state,n=e.options.element,o=void 0===n?"[data-popper-arrow]":n;null!=o&&("string"!=typeof o||(o=t.elements.popper.querySelector(o)))&&fe(t.elements.popper,o)&&(t.elements.arrow=o)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Oe(e){return e.split("-")[1]}var Re={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ke(e){var t,n=e.popper,o=e.popperRect,r=e.placement,i=e.variation,a=e.offsets,s=e.position,l=e.gpuAcceleration,c=e.adaptive,d=e.roundOffsets,p=e.isFixed,u=a.x,f=void 0===u?0:u,m=a.y,h=void 0===m?0:m,v="function"==typeof d?d({x:f,y:h}):{x:f,y:h};f=v.x,h=v.y;var g=a.hasOwnProperty("x"),y=a.hasOwnProperty("y"),b=V,x=z,w=window;if(c){var S=be(n),E="clientHeight",P="clientWidth";S===ee(n)&&"static"!==me(S=ve(n)).position&&"absolute"===s&&(E="scrollHeight",P="scrollWidth"),(r===z||(r===V||r===$)&&i===Y)&&(x=H,h-=(p&&S===w&&w.visualViewport?w.visualViewport.height:S[E])-o.height,h*=l?1:-1),r!==V&&(r!==z&&r!==H||i!==Y)||(b=$,f-=(p&&S===w&&w.visualViewport?w.visualViewport.width:S[P])-o.width,f*=l?1:-1)}var O,R=Object.assign({position:s},c&&Re),k=!0===d?function(e,t){var n=e.x,o=e.y,r=t.devicePixelRatio||1;return{x:le(n*r)/r||0,y:le(o*r)/r||0}}({x:f,y:h},ee(n)):{x:f,y:h};return f=k.x,h=k.y,l?Object.assign({},R,((O={})[x]=y?"0":"",O[b]=g?"0":"",O.transform=(w.devicePixelRatio||1)<=1?"translate("+f+"px, "+h+"px)":"translate3d("+f+"px, "+h+"px, 0)",O)):Object.assign({},R,((t={})[x]=y?h+"px":"",t[b]=g?f+"px":"",t.transform="",t))}var Te={passive:!0},Me={left:"right",right:"left",bottom:"top",top:"bottom"};function je(e){return e.replace(/left|right|bottom|top/g,(function(e){return Me[e]}))}var Ae={start:"end",end:"start"};function De(e){return e.replace(/start|end/g,(function(e){return Ae[e]}))}function We(e){var t=ee(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Ce(e){return pe(ve(e)).left+We(e).scrollLeft}function Be(e){var t=me(e),n=t.overflow,o=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+o)}function Fe(e){return["html","body","#document"].indexOf(Z(e))>=0?e.ownerDocument.body:ne(e)&&Be(e)?e:Fe(ge(e))}function Ie(e,t){var n;void 0===t&&(t=[]);var o=Fe(e),r=o===(null==(n=e.ownerDocument)?void 0:n.body),i=ee(o),a=r?[i].concat(i.visualViewport||[],Be(o)?o:[]):o,s=t.concat(a);return r?s:s.concat(Ie(ge(a)))}function Ne(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Le(e,t,n){return t===X?Ne(function(e,t){var n=ee(e),o=ve(e),r=n.visualViewport,i=o.clientWidth,a=o.clientHeight,s=0,l=0;if(r){i=r.width,a=r.height;var c=de();(c||!c&&"fixed"===t)&&(s=r.offsetLeft,l=r.offsetTop)}return{width:i,height:a,x:s+Ce(e),y:l}}(e,n)):te(t)?function(e,t){var n=pe(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):Ne(function(e){var t,n=ve(e),o=We(e),r=null==(t=e.ownerDocument)?void 0:t.body,i=ae(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),a=ae(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),s=-o.scrollLeft+Ce(e),l=-o.scrollTop;return"rtl"===me(r||n).direction&&(s+=ae(n.clientWidth,r?r.clientWidth:0)-i),{width:i,height:a,x:s,y:l}}(ve(e)))}function ze(e){var t,n=e.reference,o=e.element,r=e.placement,i=r?ie(r):null,a=r?Oe(r):null,s=n.x+n.width/2-o.width/2,l=n.y+n.height/2-o.height/2;switch(i){case z:t={x:s,y:n.y-o.height};break;case H:t={x:s,y:n.y+n.height};break;case $:t={x:n.x+n.width,y:l};break;case V:t={x:n.x-o.width,y:l};break;default:t={x:n.x,y:n.y}}var c=i?xe(i):null;if(null!=c){var d="y"===c?"height":"width";switch(a){case U:t[c]=t[c]-(n[d]/2-o[d]/2);break;case Y:t[c]=t[c]+(n[d]/2-o[d]/2)}}return t}function He(e,t){void 0===t&&(t={});var n=t,o=n.placement,r=void 0===o?e.placement:o,i=n.strategy,a=void 0===i?e.strategy:i,s=n.boundary,l=void 0===s?"clippingParents":s,c=n.rootBoundary,d=void 0===c?X:c,p=n.elementContext,u=void 0===p?_:p,f=n.altBoundary,m=void 0!==f&&f,h=n.padding,v=void 0===h?0:h,g=Se("number"!=typeof v?v:Ee(v,K)),y=u===_?"reference":_,b=e.rects.popper,x=e.elements[m?y:u],w=function(e,t,n,o){var r="clippingParents"===t?function(e){var t=Ie(ge(e)),n=["absolute","fixed"].indexOf(me(e).position)>=0&&ne(e)?be(e):e;return te(n)?t.filter((function(e){return te(e)&&fe(e,n)&&"body"!==Z(e)})):[]}(e):[].concat(t),i=[].concat(r,[n]),a=i[0],s=i.reduce((function(t,n){var r=Le(e,n,o);return t.top=ae(r.top,t.top),t.right=se(r.right,t.right),t.bottom=se(r.bottom,t.bottom),t.left=ae(r.left,t.left),t}),Le(e,a,o));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}(te(x)?x:x.contextElement||ve(e.elements.popper),l,d,a),S=pe(e.elements.reference),E=ze({reference:S,element:b,placement:r}),P=Ne(Object.assign({},b,E)),O=u===_?P:S,R={top:w.top-O.top+g.top,bottom:O.bottom-w.bottom+g.bottom,left:w.left-O.left+g.left,right:O.right-w.right+g.right},k=e.modifiersData.offset;if(u===_&&k){var T=k[r];Object.keys(R).forEach((function(e){var t=[$,H].indexOf(e)>=0?1:-1,n=[z,H].indexOf(e)>=0?"y":"x";R[e]+=T[n]*t}))}return R}function $e(e,t){void 0===t&&(t={});var n=t,o=n.placement,r=n.boundary,i=n.rootBoundary,a=n.padding,s=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?J:l,d=Oe(o),p=d?s?G:G.filter((function(e){return Oe(e)===d})):K,u=p.filter((function(e){return c.indexOf(e)>=0}));0===u.length&&(u=p);var f=u.reduce((function(t,n){return t[n]=He(e,{placement:n,boundary:r,rootBoundary:i,padding:a})[ie(n)],t}),{});return Object.keys(f).sort((function(e,t){return f[e]-f[t]}))}const Ve={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var r=n.mainAxis,i=void 0===r||r,a=n.altAxis,s=void 0===a||a,l=n.fallbackPlacements,c=n.padding,d=n.boundary,p=n.rootBoundary,u=n.altBoundary,f=n.flipVariations,m=void 0===f||f,h=n.allowedAutoPlacements,v=t.options.placement,g=ie(v),y=l||(g!==v&&m?function(e){if(ie(e)===q)return[];var t=je(e);return[De(e),t,De(t)]}(v):[je(v)]),b=[v].concat(y).reduce((function(e,n){return e.concat(ie(n)===q?$e(t,{placement:n,boundary:d,rootBoundary:p,padding:c,flipVariations:m,allowedAutoPlacements:h}):n)}),[]),x=t.rects.reference,w=t.rects.popper,S=new Map,E=!0,P=b[0],O=0;O<b.length;O++){var R=b[O],k=ie(R),T=Oe(R)===U,M=[z,H].indexOf(k)>=0,j=M?"width":"height",A=He(t,{placement:R,boundary:d,rootBoundary:p,altBoundary:u,padding:c}),D=M?T?$:V:T?H:z;x[j]>w[j]&&(D=je(D));var W=je(D),C=[];if(i&&C.push(A[k]<=0),s&&C.push(A[D]<=0,A[W]<=0),C.every((function(e){return e}))){P=R,E=!1;break}S.set(R,C)}if(E)for(var B=function(e){var t=b.find((function(t){var n=S.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return P=t,"break"},F=m?3:1;F>0&&"break"!==B(F);F--);t.placement!==P&&(t.modifiersData[o]._skip=!0,t.placement=P,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function qe(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Ke(e){return[z,$,H,V].some((function(t){return e[t]>=0}))}function Ue(e,t,n){void 0===n&&(n=!1);var o,r,i=ne(t),a=ne(t)&&function(e){var t=e.getBoundingClientRect(),n=le(t.width)/e.offsetWidth||1,o=le(t.height)/e.offsetHeight||1;return 1!==n||1!==o}(t),s=ve(t),l=pe(e,a,n),c={scrollLeft:0,scrollTop:0},d={x:0,y:0};return(i||!i&&!n)&&(("body"!==Z(t)||Be(s))&&(c=(o=t)!==ee(o)&&ne(o)?{scrollLeft:(r=o).scrollLeft,scrollTop:r.scrollTop}:We(o)),ne(t)?((d=pe(t,!0)).x+=t.clientLeft,d.y+=t.clientTop):s&&(d.x=Ce(s))),{x:l.left+c.scrollLeft-d.x,y:l.top+c.scrollTop-d.y,width:l.width,height:l.height}}function Ye(e){var t=new Map,n=new Set,o=[];function r(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var o=t.get(e);o&&r(o)}})),o.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||r(e)})),o}var Xe={placement:"bottom",modifiers:[],strategy:"absolute"};function _e(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function Ge(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,o=void 0===n?[]:n,r=t.defaultOptions,i=void 0===r?Xe:r;return function(e,t,n){void 0===n&&(n=i);var r,a,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},Xe,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,d={state:s,setOptions:function(n){var r="function"==typeof n?n(s.options):n;p(),s.options=Object.assign({},i,s.options,r),s.scrollParents={reference:te(e)?Ie(e):e.contextElement?Ie(e.contextElement):[],popper:Ie(t)};var a,c,u=function(e){var t=Ye(e);return Q.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((a=[].concat(o,s.options.modifiers),c=a.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(c).map((function(e){return c[e]}))));return s.orderedModifiers=u.filter((function(e){return e.enabled})),s.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,o=void 0===n?{}:n,r=e.effect;if("function"==typeof r){var i=r({state:s,name:t,instance:d,options:o});l.push(i||function(){})}})),d.update()},forceUpdate:function(){if(!c){var e=s.elements,t=e.reference,n=e.popper;if(_e(t,n)){s.rects={reference:Ue(t,be(n),"fixed"===s.options.strategy),popper:ue(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach((function(e){return s.modifiersData[e.name]=Object.assign({},e.data)}));for(var o=0;o<s.orderedModifiers.length;o++)if(!0!==s.reset){var r=s.orderedModifiers[o],i=r.fn,a=r.options,l=void 0===a?{}:a,p=r.name;"function"==typeof i&&(s=i({state:s,options:l,name:p,instance:d})||s)}else s.reset=!1,o=-1}}},update:(r=function(){return new Promise((function(e){d.forceUpdate(),e(s)}))},function(){return a||(a=new Promise((function(e){Promise.resolve().then((function(){a=void 0,e(r())}))}))),a}),destroy:function(){p(),c=!0}};if(!_e(e,t))return d;function p(){l.forEach((function(e){return e()})),l=[]}return d.setOptions(n).then((function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)})),d}}var Je=Ge({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,r=o.scroll,i=void 0===r||r,a=o.resize,s=void 0===a||a,l=ee(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach((function(e){e.addEventListener("scroll",n.update,Te)})),s&&l.addEventListener("resize",n.update,Te),function(){i&&c.forEach((function(e){e.removeEventListener("scroll",n.update,Te)})),s&&l.removeEventListener("resize",n.update,Te)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=ze({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,r=void 0===o||o,i=n.adaptive,a=void 0===i||i,s=n.roundOffsets,l=void 0===s||s,c={placement:ie(t.placement),variation:Oe(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ke(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ke(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},re,{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,r=n.offset,i=void 0===r?[0,0]:r,a=J.reduce((function(e,n){return e[n]=function(e,t,n){var o=ie(e),r=[V,z].indexOf(o)>=0?-1:1,i="function"==typeof n?n(Object.assign({},t,{placement:e})):n,a=i[0],s=i[1];return a=a||0,s=(s||0)*r,[V,$].indexOf(o)>=0?{x:s,y:a}:{x:a,y:s}}(n,t.rects,i),e}),{}),s=a[t.placement],l=s.x,c=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[o]=a}},Ve,{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,r=n.mainAxis,i=void 0===r||r,a=n.altAxis,s=void 0!==a&&a,l=n.boundary,c=n.rootBoundary,d=n.altBoundary,p=n.padding,u=n.tether,f=void 0===u||u,m=n.tetherOffset,h=void 0===m?0:m,v=He(t,{boundary:l,rootBoundary:c,padding:p,altBoundary:d}),g=ie(t.placement),y=Oe(t.placement),b=!y,x=xe(g),w="x"===x?"y":"x",S=t.modifiersData.popperOffsets,E=t.rects.reference,P=t.rects.popper,O="function"==typeof h?h(Object.assign({},t.rects,{placement:t.placement})):h,R="number"==typeof O?{mainAxis:O,altAxis:O}:Object.assign({mainAxis:0,altAxis:0},O),k=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,T={x:0,y:0};if(S){if(i){var M,j="y"===x?z:V,A="y"===x?H:$,D="y"===x?"height":"width",W=S[x],C=W+v[j],B=W-v[A],F=f?-P[D]/2:0,I=y===U?E[D]:P[D],N=y===U?-P[D]:-E[D],L=t.elements.arrow,q=f&&L?ue(L):{width:0,height:0},K=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},Y=K[j],X=K[A],_=we(0,E[D],q[D]),G=b?E[D]/2-F-_-Y-R.mainAxis:I-_-Y-R.mainAxis,J=b?-E[D]/2+F+_+X+R.mainAxis:N+_+X+R.mainAxis,Q=t.elements.arrow&&be(t.elements.arrow),Z=Q?"y"===x?Q.clientTop||0:Q.clientLeft||0:0,ee=null!=(M=null==k?void 0:k[x])?M:0,te=W+J-ee,ne=we(f?se(C,W+G-ee-Z):C,W,f?ae(B,te):B);S[x]=ne,T[x]=ne-W}if(s){var oe,re="x"===x?z:V,le="x"===x?H:$,ce=S[w],de="y"===w?"height":"width",pe=ce+v[re],fe=ce-v[le],me=-1!==[z,V].indexOf(g),he=null!=(oe=null==k?void 0:k[w])?oe:0,ve=me?pe:ce-E[de]-P[de]-he+R.altAxis,ge=me?ce+E[de]+P[de]-he-R.altAxis:fe,ye=f&&me?function(e,t,n){var o=we(e,t,n);return o>n?n:o}(ve,ce,ge):we(f?ve:pe,ce,f?ge:fe);S[w]=ye,T[w]=ye-ce}t.modifiersData[o]=T}},requiresIfExists:["offset"]},Pe,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,r=t.rects.popper,i=t.modifiersData.preventOverflow,a=He(t,{elementContext:"reference"}),s=He(t,{altBoundary:!0}),l=qe(a,o),c=qe(s,r,i),d=Ke(l),p=Ke(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:d,hasPopperEscaped:p},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":p})}}]});function Qe(e){const{elementType:t,externalSlotProps:n,ownerState:o,skipResolvingSlotProps:r=!1,...i}=e,a=r?{}:b(n,o),{props:s,internalRef:l}=x({...i,externalSlotProps:a}),c=f(l,a?.ref,e.additionalProps?.ref);return w(t,{...s,ref:c},o)}const Ze=e.forwardRef((function(n,o){const{children:r,container:i,disablePortal:s=!1}=n,[l,c]=e.useState(null),d=f(e.isValidElement(r)?R(r):null,o);if(a((()=>{s||c(function(e){return"function"==typeof e?e():e}(i)||document.body)}),[i,s]),a((()=>{if(l&&!s)return N(o,l),()=>{N(o,null)}}),[o,l,s]),s){if(e.isValidElement(r)){const t={ref:d};return e.cloneElement(r,t)}return r}return l?t.createPortal(r,l):l}));function et(e){return l("MuiPopper",e)}function tt(e){return"function"==typeof e?e():e}c("MuiPopper",["root"]);const nt={},ot=e.forwardRef((function(t,n){const{anchorEl:r,children:i,direction:s,disablePortal:l,modifiers:c,open:d,placement:u,popperOptions:m,popperRef:h,slotProps:v={},slots:g={},TransitionProps:y,ownerState:b,...x}=t,w=e.useRef(null),S=f(w,n),E=e.useRef(null),P=f(E,h),O=e.useRef(P);a((()=>{O.current=P}),[P]),e.useImperativeHandle(h,(()=>E.current),[]);const R=function(e,t){if("ltr"===t)return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}(u,s),[k,T]=e.useState(R),[M,j]=e.useState(tt(r));e.useEffect((()=>{E.current&&E.current.forceUpdate()})),e.useEffect((()=>{r&&j(tt(r))}),[r]),a((()=>{if(!M||!d)return;let e=[{name:"preventOverflow",options:{altBoundary:l}},{name:"flip",options:{altBoundary:l}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:e})=>{T(e.placement)}}];null!=c&&(e=e.concat(c)),m&&null!=m.modifiers&&(e=e.concat(m.modifiers));const t=Je(M,w.current,{placement:R,...m,modifiers:e});return O.current(t),()=>{t.destroy(),O.current(null)}}),[M,l,c,d,m,R]);const A={placement:k};null!==y&&(A.TransitionProps=y);const D=(e=>{const{classes:t}=e;return p({root:["root"]},et,t)})(t),W=g.root??"div",C=Qe({elementType:W,externalSlotProps:v.root,externalForwardedProps:x,additionalProps:{role:"tooltip",ref:S},ownerState:t,className:D.root});return o.jsx(W,{...C,children:"function"==typeof i?i(A):i})})),rt=d(e.forwardRef((function(t,n){const{anchorEl:r,children:i,container:a,direction:s="ltr",disablePortal:l=!1,keepMounted:c=!1,modifiers:d,open:p,placement:u="bottom",popperOptions:f=nt,popperRef:m,style:h,transition:v=!1,slotProps:y={},slots:b={},...x}=t,[w,S]=e.useState(!0);if(!c&&!p&&(!v||w))return null;let E;if(a)E=a;else if(r){const e=tt(r);E=e&&void 0!==e.nodeType?g(e).body:g(null).body}const P=p||!c||v&&!w?void 0:"none",O=v?{in:p,onEnter:()=>{S(!1)},onExited:()=>{S(!0)}}:void 0;return o.jsx(Ze,{disablePortal:l,container:E,children:o.jsx(ot,{anchorEl:r,direction:s,disablePortal:l,modifiers:d,ref:n,open:v?!w:p,placement:u,popperOptions:f,popperRef:m,slotProps:y,slots:b,...x,style:{position:"fixed",top:0,left:0,display:P,...h},TransitionProps:O,children:i})})})),{name:"MuiPopper",slot:"Root"})({}),it=e.forwardRef((function(e,t){const r=s(),i=n({props:e,name:"MuiPopper"}),{anchorEl:a,component:l,components:c,componentsProps:d,container:p,disablePortal:u,keepMounted:f,modifiers:m,open:h,placement:v,popperOptions:g,popperRef:y,transition:b,slots:x,slotProps:w,...S}=i,E=x?.root??c?.Root,P={anchorEl:a,container:p,disablePortal:u,keepMounted:f,modifiers:m,open:h,placement:v,popperOptions:g,popperRef:y,transition:b,...S};return o.jsx(rt,{as:l,direction:r?"rtl":"ltr",slots:{root:E},slotProps:w??d,...P,ref:t})}));function at(e=window){const t=e.document.documentElement.clientWidth;return e.innerWidth-t}function st(e,t){t?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function lt(e){return parseInt(y(e).getComputedStyle(e).paddingRight,10)||0}function ct(e,t,n,o,r){const i=[t,n,...o];[].forEach.call(e.children,(e=>{const t=!i.includes(e),n=!function(e){const t=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(e.tagName),n="INPUT"===e.tagName&&"hidden"===e.getAttribute("type");return t||n}(e);t&&n&&st(e,r)}))}function dt(e,t){let n=-1;return e.some(((e,o)=>!!t(e)&&(n=o,!0))),n}const pt=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function ut(e){const t=[],n=[];return Array.from(e.querySelectorAll(pt)).forEach(((e,o)=>{const r=function(e){const t=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(t)?"true"===e.contentEditable||("AUDIO"===e.nodeName||"VIDEO"===e.nodeName||"DETAILS"===e.nodeName)&&null===e.getAttribute("tabindex")?0:e.tabIndex:t}(e);-1!==r&&function(e){return!(e.disabled||"INPUT"===e.tagName&&"hidden"===e.type||function(e){if("INPUT"!==e.tagName||"radio"!==e.type)return!1;if(!e.name)return!1;const t=t=>e.ownerDocument.querySelector(`input[type="radio"]${t}`);let n=t(`[name="${e.name}"]:checked`);return n||(n=t(`[name="${e.name}"]`)),n!==e}(e))}(e)&&(0===r?t.push(e):n.push({documentOrder:o,tabIndex:r,node:e}))})),n.sort(((e,t)=>e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex)).map((e=>e.node)).concat(t)}function ft(){return!0}function mt(t){const{children:n,disableAutoFocus:r=!1,disableEnforceFocus:i=!1,disableRestoreFocus:a=!1,getTabbable:s=ut,isEnabled:l=ft,open:c}=t,d=e.useRef(!1),p=e.useRef(null),u=e.useRef(null),m=e.useRef(null),h=e.useRef(null),v=e.useRef(!1),y=e.useRef(null),b=f(R(n),y),x=e.useRef(null);e.useEffect((()=>{c&&y.current&&(v.current=!r)}),[r,c]),e.useEffect((()=>{if(!c||!y.current)return;const e=g(y.current);return y.current.contains(e.activeElement)||(y.current.hasAttribute("tabIndex")||y.current.setAttribute("tabIndex","-1"),v.current&&y.current.focus()),()=>{a||(m.current&&m.current.focus&&(d.current=!0,m.current.focus()),m.current=null)}}),[c]),e.useEffect((()=>{if(!c||!y.current)return;const e=g(y.current),t=t=>{x.current=t,!i&&l()&&"Tab"===t.key&&e.activeElement===y.current&&t.shiftKey&&(d.current=!0,u.current&&u.current.focus())},n=()=>{const t=y.current;if(null===t)return;if(!e.hasFocus()||!l()||d.current)return void(d.current=!1);if(t.contains(e.activeElement))return;if(i&&e.activeElement!==p.current&&e.activeElement!==u.current)return;if(e.activeElement!==h.current)h.current=null;else if(null!==h.current)return;if(!v.current)return;let n=[];if(e.activeElement!==p.current&&e.activeElement!==u.current||(n=s(y.current)),n.length>0){const e=Boolean(x.current?.shiftKey&&"Tab"===x.current?.key),t=n[0],o=n[n.length-1];"string"!=typeof t&&"string"!=typeof o&&(e?o.focus():t.focus())}else t.focus()};e.addEventListener("focusin",n),e.addEventListener("keydown",t,!0);const o=setInterval((()=>{e.activeElement&&"BODY"===e.activeElement.tagName&&n()}),50);return()=>{clearInterval(o),e.removeEventListener("focusin",n),e.removeEventListener("keydown",t,!0)}}),[r,i,a,l,c,s]);const w=e=>{null===m.current&&(m.current=e.relatedTarget),v.current=!0};return o.jsxs(e.Fragment,{children:[o.jsx("div",{tabIndex:c?0:-1,onFocus:w,ref:p,"data-testid":"sentinelStart"}),e.cloneElement(n,{ref:b,onFocus:e=>{null===m.current&&(m.current=e.relatedTarget),v.current=!0,h.current=e.target;const t=n.props.onFocus;t&&t(e)}}),o.jsx("div",{tabIndex:c?0:-1,onFocus:w,ref:u,"data-testid":"sentinelEnd"})]})}const ht=()=>{},vt=new class{constructor(){this.modals=[],this.containers=[]}add(e,t){let n=this.modals.indexOf(e);if(-1!==n)return n;n=this.modals.length,this.modals.push(e),e.modalRef&&st(e.modalRef,!1);const o=function(e){const t=[];return[].forEach.call(e.children,(e=>{"true"===e.getAttribute("aria-hidden")&&t.push(e)})),t}(t);ct(t,e.mount,e.modalRef,o,!0);const r=dt(this.containers,(e=>e.container===t));return-1!==r?(this.containers[r].modals.push(e),n):(this.containers.push({modals:[e],container:t,restore:null,hiddenSiblings:o}),n)}mount(e,t){const n=dt(this.containers,(t=>t.modals.includes(e))),o=this.containers[n];o.restore||(o.restore=function(e,t){const n=[],o=e.container;if(!t.disableScrollLock){if(function(e){const t=g(e);return t.body===e?y(e).innerWidth>t.documentElement.clientWidth:e.scrollHeight>e.clientHeight}(o)){const e=at(y(o));n.push({value:o.style.paddingRight,property:"padding-right",el:o}),o.style.paddingRight=`${lt(o)+e}px`;const t=g(o).querySelectorAll(".mui-fixed");[].forEach.call(t,(t=>{n.push({value:t.style.paddingRight,property:"padding-right",el:t}),t.style.paddingRight=`${lt(t)+e}px`}))}let e;if(o.parentNode instanceof DocumentFragment)e=g(o).body;else{const t=o.parentElement,n=y(o);e="HTML"===t?.nodeName&&"scroll"===n.getComputedStyle(t).overflowY?t:o}n.push({value:e.style.overflow,property:"overflow",el:e},{value:e.style.overflowX,property:"overflow-x",el:e},{value:e.style.overflowY,property:"overflow-y",el:e}),e.style.overflow="hidden"}return()=>{n.forEach((({value:e,el:t,property:n})=>{e?t.style.setProperty(n,e):t.style.removeProperty(n)}))}}(o,t))}remove(e,t=!0){const n=this.modals.indexOf(e);if(-1===n)return n;const o=dt(this.containers,(t=>t.modals.includes(e))),r=this.containers[o];if(r.modals.splice(r.modals.indexOf(e),1),this.modals.splice(n,1),0===r.modals.length)r.restore&&r.restore(),e.modalRef&&st(e.modalRef,t),ct(r.container,e.mount,e.modalRef,r.hiddenSiblings,!1),this.containers.splice(o,1);else{const e=r.modals[r.modals.length-1];e.modalRef&&st(e.modalRef,!1)}return n}isTopModal(e){return this.modals.length>0&&this.modals[this.modals.length-1]===e}};function gt(e){return l("MuiModal",e)}c("MuiModal",["root","hidden","backdrop"]);const yt=d("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.open&&n.exited&&t.hidden]}})(u((({theme:e})=>({position:"fixed",zIndex:(e.vars||e).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:e})=>!e.open&&e.exited,style:{visibility:"hidden"}}]})))),bt=d(k,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),xt=e.forwardRef((function(t,i){const a=n({name:"MuiModal",props:t}),{BackdropComponent:s=bt,BackdropProps:l,classes:c,className:d,closeAfterTransition:u=!1,children:h,container:v,component:y,components:b={},componentsProps:x={},disableAutoFocus:w=!1,disableEnforceFocus:P=!1,disableEscapeKeyDown:O=!1,disablePortal:R=!1,disableRestoreFocus:k=!1,disableScrollLock:T=!1,hideBackdrop:M=!1,keepMounted:j=!1,onClose:A,onTransitionEnter:D,onTransitionExited:C,open:B,slotProps:F={},slots:I={},theme:N,...L}=a,z={...a,closeAfterTransition:u,disableAutoFocus:w,disableEnforceFocus:P,disableEscapeKeyDown:O,disablePortal:R,disableRestoreFocus:k,disableScrollLock:T,hideBackdrop:M,keepMounted:j},{getRootProps:H,getBackdropProps:$,getTransitionProps:V,portalRef:q,isTopModal:K,exited:U,hasTransition:Y}=function(t){const{container:n,disableEscapeKeyDown:o=!1,disableScrollLock:r=!1,closeAfterTransition:i=!1,onTransitionEnter:a,onTransitionExited:s,children:l,onClose:c,open:d,rootRef:p}=t,u=e.useRef({}),h=e.useRef(null),v=e.useRef(null),y=f(v,p),[b,x]=e.useState(!d),w=function(e){return!!e&&e.props.hasOwnProperty("in")}(l);let E=!0;"false"!==t["aria-hidden"]&&!1!==t["aria-hidden"]||(E=!1);const P=()=>(u.current.modalRef=v.current,u.current.mount=h.current,u.current),O=()=>{vt.mount(P(),{disableScrollLock:r}),v.current&&(v.current.scrollTop=0)},R=m((()=>{const e=function(e){return"function"==typeof e?e():e}(n)||g(h.current).body;vt.add(P(),e),v.current&&O()})),k=()=>vt.isTopModal(P()),T=m((e=>{h.current=e,e&&(d&&k()?O():v.current&&st(v.current,E))})),M=e.useCallback((()=>{vt.remove(P(),E)}),[E]);e.useEffect((()=>()=>{M()}),[M]),e.useEffect((()=>{d?R():w&&i||M()}),[d,M,w,i,R]);const j=e=>t=>{e.onKeyDown?.(t),"Escape"===t.key&&229!==t.which&&k()&&(o||(t.stopPropagation(),c&&c(t,"escapeKeyDown")))},A=e=>t=>{e.onClick?.(t),t.target===t.currentTarget&&c&&c(t,"backdropClick")};return{getRootProps:(e={})=>{const n=S(t);delete n.onTransitionEnter,delete n.onTransitionExited;const o={...n,...e};return{role:"presentation",...o,onKeyDown:j(o),ref:y}},getBackdropProps:(e={})=>{const t=e;return{"aria-hidden":!0,...t,onClick:A(t),open:d}},getTransitionProps:()=>({onEnter:W((()=>{x(!1),a&&a()}),l?.props.onEnter??ht),onExited:W((()=>{x(!0),s&&s(),i&&M()}),l?.props.onExited??ht)}),rootRef:y,portalRef:T,isTopModal:k,exited:b,hasTransition:w}}({...z,rootRef:i}),X={...z,exited:U},_=(e=>{const{open:t,exited:n,classes:o}=e;return p({root:["root",!t&&n&&"hidden"],backdrop:["backdrop"]},gt,o)})(X),G={};if(void 0===h.props.tabIndex&&(G.tabIndex="-1"),Y){const{onEnter:e,onExited:t}=V();G.onEnter=e,G.onExited=t}const J={slots:{root:b.Root,backdrop:b.Backdrop,...I},slotProps:{...x,...F}},[Q,Z]=E("root",{ref:i,elementType:yt,externalForwardedProps:{...J,...L,component:y},getSlotProps:H,ownerState:X,className:r(d,_?.root,!X.open&&X.exited&&_?.hidden)}),[ee,te]=E("backdrop",{ref:l?.ref,elementType:s,externalForwardedProps:J,shouldForwardComponentProp:!0,additionalProps:l,getSlotProps:e=>$({...e,onClick:t=>{e?.onClick&&e.onClick(t)}}),className:r(l?.className,_?.backdrop),ownerState:X});return j||B||Y&&!U?o.jsx(Ze,{ref:q,container:v,disablePortal:R,children:o.jsxs(Q,{...Z,children:[!M&&s?o.jsx(ee,{...te}):null,o.jsx(mt,{disableEnforceFocus:P,disableAutoFocus:w,disableRestoreFocus:k,isEnabled:K,open:B,children:e.cloneElement(h,G)})]})}):null}));function wt(e){return l("MuiDialog",e)}const St=c("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]),Et=e.createContext({}),Pt=d(k,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),Ot=d(xt,{name:"MuiDialog",slot:"Root"})({"@media print":{position:"absolute !important"}}),Rt=d("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.container,t[`scroll${i(n.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),kt=d(O,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.paper,t[`scrollPaper${i(n.scroll)}`],t[`paperWidth${i(String(n.maxWidth))}`],n.fullWidth&&t.paperFullWidth,n.fullScreen&&t.paperFullScreen]}})(u((({theme:e})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:e})=>!e.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:"px"===e.breakpoints.unit?Math.max(e.breakpoints.values.xs,444):`max(${e.breakpoints.values.xs}${e.breakpoints.unit}, 444px)`,[`&.${St.paperScrollBody}`]:{[e.breakpoints.down(Math.max(e.breakpoints.values.xs,444)+64)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(e.breakpoints.values).filter((e=>"xs"!==e)).map((t=>({props:{maxWidth:t},style:{maxWidth:`${e.breakpoints.values[t]}${e.breakpoints.unit}`,[`&.${St.paperScrollBody}`]:{[e.breakpoints.down(e.breakpoints.values[t]+64)]:{maxWidth:"calc(100% - 64px)"}}}}))),{props:({ownerState:e})=>e.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:e})=>e.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${St.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]})))),Tt=e.forwardRef((function(t,a){const s=n({props:t,name:"MuiDialog"}),l=P(),c={enter:l.transitions.duration.enteringScreen,exit:l.transitions.duration.leavingScreen},{"aria-describedby":d,"aria-labelledby":u,"aria-modal":f=!0,BackdropComponent:m,BackdropProps:v,children:g,className:y,disableEscapeKeyDown:b=!1,fullScreen:x=!1,fullWidth:w=!1,maxWidth:S="sm",onClick:R,onClose:k,open:M,PaperComponent:j=O,PaperProps:A={},scroll:D="paper",slots:W={},slotProps:C={},TransitionComponent:B=T,transitionDuration:F=c,TransitionProps:I,...N}=s,L={...s,disableEscapeKeyDown:b,fullScreen:x,fullWidth:w,maxWidth:S,scroll:D},z=(e=>{const{classes:t,scroll:n,maxWidth:o,fullWidth:r,fullScreen:a}=e,s={root:["root"],container:["container",`scroll${i(n)}`],paper:["paper",`paperScroll${i(n)}`,`paperWidth${i(String(o))}`,r&&"paperFullWidth",a&&"paperFullScreen"]};return p(s,wt,t)})(L),H=e.useRef(),$=h(u),V=e.useMemo((()=>({titleId:$})),[$]),q={slots:{transition:B,...W},slotProps:{transition:I,paper:A,backdrop:v,...C}},[K,U]=E("root",{elementType:Ot,shouldForwardComponentProp:!0,externalForwardedProps:q,ownerState:L,className:r(z.root,y),ref:a}),[Y,X]=E("backdrop",{elementType:Pt,shouldForwardComponentProp:!0,externalForwardedProps:q,ownerState:L}),[_,G]=E("paper",{elementType:kt,shouldForwardComponentProp:!0,externalForwardedProps:q,ownerState:L,className:r(z.paper,A.className)}),[J,Q]=E("container",{elementType:Rt,externalForwardedProps:q,ownerState:L,className:z.container}),[Z,ee]=E("transition",{elementType:T,externalForwardedProps:q,ownerState:L,additionalProps:{appear:!0,in:M,timeout:F,role:"presentation"}});return o.jsx(K,{closeAfterTransition:!0,slots:{backdrop:Y},slotProps:{backdrop:{transitionDuration:F,as:m,...X}},disableEscapeKeyDown:b,onClose:k,open:M,onClick:e=>{R&&R(e),H.current&&(H.current=null,k&&k(e,"backdropClick"))},...U,...N,children:o.jsx(Z,{...ee,children:o.jsx(J,{onMouseDown:e=>{H.current=e.target===e.currentTarget},...Q,children:o.jsx(_,{as:j,elevation:24,role:"dialog","aria-describedby":d,"aria-labelledby":$,"aria-modal":f,...G,children:o.jsx(Et.Provider,{value:V,children:g})})})})})}));function Mt(e){return l("MuiDialogActions",e)}c("MuiDialogActions",["root","spacing"]);const jt=d("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),At=e.forwardRef((function(e,t){const i=n({props:e,name:"MuiDialogActions"}),{className:a,disableSpacing:s=!1,...l}=i,c={...i,disableSpacing:s},d=(e=>{const{classes:t,disableSpacing:n}=e;return p({root:["root",!n&&"spacing"]},Mt,t)})(c);return o.jsx(jt,{className:r(d.root,a),ownerState:c,ref:t,...l})}));function Dt(e){return l("MuiDialogContent",e)}function Wt(e){return l("MuiDialogTitle",e)}c("MuiDialogContent",["root","dividers"]);const Ct=c("MuiDialogTitle",["root"]),Bt=d("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dividers&&t.dividers]}})(u((({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:e})=>e.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:e})=>!e.dividers,style:{[`.${Ct.root} + &`]:{paddingTop:0}}}]})))),Ft=e.forwardRef((function(e,t){const i=n({props:e,name:"MuiDialogContent"}),{className:a,dividers:s=!1,...l}=i,c={...i,dividers:s},d=(e=>{const{classes:t,dividers:n}=e;return p({root:["root",n&&"dividers"]},Dt,t)})(c);return o.jsx(Bt,{className:r(d.root,a),ownerState:c,ref:t,...l})})),It=d(M,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),Nt=e.forwardRef((function(t,i){const a=n({props:t,name:"MuiDialogTitle"}),{className:s,id:l,...c}=a,d=a,u=(e=>{const{classes:t}=e;return p({root:["root"]},Wt,t)})(d),{titleId:f=l}=e.useContext(Et);return o.jsx(It,{component:"h2",className:r(u.root,s),ownerState:d,ref:i,variant:"h6",id:l??f,...c})}));function Lt(e){return`scale(${e}, ${e**2})`}const zt={entering:{opacity:1,transform:Lt(1)},entered:{opacity:1,transform:"none"}},Ht="undefined"!=typeof navigator&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),$t=e.forwardRef((function(t,n){const{addEndListener:r,appear:i=!0,children:a,easing:s,in:l,onEnter:c,onEntered:d,onEntering:p,onExit:u,onExited:m,onExiting:h,style:g,timeout:y="auto",TransitionComponent:b=j,...x}=t,w=v(),S=e.useRef(),E=P(),O=e.useRef(null),k=f(O,R(a),n),T=e=>t=>{if(e){const n=O.current;void 0===t?e(n):e(n,t)}},M=T(p),W=T(((e,t)=>{D(e);const{duration:n,delay:o,easing:r}=A({style:g,timeout:y,easing:s},{mode:"enter"});let i;"auto"===y?(i=E.transitions.getAutoHeightDuration(e.clientHeight),S.current=i):i=n,e.style.transition=[E.transitions.create("opacity",{duration:i,delay:o}),E.transitions.create("transform",{duration:Ht?i:.666*i,delay:o,easing:r})].join(","),c&&c(e,t)})),C=T(d),B=T(h),F=T((e=>{const{duration:t,delay:n,easing:o}=A({style:g,timeout:y,easing:s},{mode:"exit"});let r;"auto"===y?(r=E.transitions.getAutoHeightDuration(e.clientHeight),S.current=r):r=t,e.style.transition=[E.transitions.create("opacity",{duration:r,delay:n}),E.transitions.create("transform",{duration:Ht?r:.666*r,delay:Ht?n:n||.333*r,easing:o})].join(","),e.style.opacity=0,e.style.transform=Lt(.75),u&&u(e)})),I=T(m);return o.jsx(b,{appear:i,in:l,nodeRef:O,onEnter:W,onEntered:C,onEntering:M,onExit:F,onExited:I,onExiting:B,addEndListener:e=>{"auto"===y&&w.start(S.current||0,e),r&&r(O.current,e)},timeout:"auto"===y?null:y,...x,children:(t,{ownerState:n,...o})=>e.cloneElement(a,{style:{opacity:0,transform:Lt(.75),visibility:"exited"!==t||l?void 0:"hidden",...zt[t],...g,...a.props.style},ref:k,...o})})}));$t&&($t.muiSupportAuto=!0);export{Tt as D,mt as F,$t as G,xt as M,it as P,Nt as a,Ft as b,I as c,At as d,Qe as e,St as f,at as g,N as s,L as u};
