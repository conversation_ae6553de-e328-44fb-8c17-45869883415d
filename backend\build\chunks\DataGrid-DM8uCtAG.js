import{i as e,m as t,j as r,k as n,l as o,am as l,an as i,ao as a,ap as s,aq as c,ar as u,as as d,at as p,au as f}from"../entries/index-CEzJO5Xy.js";import{r as g,e as m}from"./router-BtYqujaw.js";import{g as h,P as b}from"./getThemeProps-gt86ccpv.js";import{r as w}from"./vendor-dblfw9z9.js";import{d as C,u as v,e as y,g as x,a as S,s as R,c as I,m as M,b as k,f as P,h as E,_ as F,i as H,j as T,B as D,C as O}from"./Button-DGZYUY3P.js";import{o as $}from"./ownerWindow-ChLfdzZL.js";import{d as j}from"./isHostComponent-DR4iSCFs.js";import{c as L,P as z,F as A,G as B}from"./Grow-CjOKj0i1.js";import{S as V,F as N,f as G}from"./Switch-BWPUOSX1.js";import{M as W}from"./MenuItem-suKfXYI2.js";import{b as U,i as K,F as _,I as q}from"./InputLabel-BbcIE26O.js";import{S as X,T as Q,I as Y,i as J}from"./TextField-BAse--ht.js";import{T as Z}from"./Tooltip-BkJF6Mu0.js";import{I as ee,i as te}from"./IconButton-CnBvmeAK.js";import{T as re,L as ne}from"./Toolbar-CNUITE_K.js";import{a as oe}from"./Menu-ZU0DMgjT.js";import{K as le,a as ie,D as ae}from"./KeyboardArrowRight-BV-h2cWM.js";import{C as se}from"./Chip-CAtDqtgp.js";import{B as ce}from"./Badge-B3LKl4T2.js";import{A as ue}from"./Autocomplete-CviOU_ku.js";import{u as de}from"./useSlot-CtA82Ni6.js";import{L as pe}from"./ListItemText-DBn_RuMq.js";import{l as fe}from"./listItemTextClasses-DFwCkkgK.js";import{C as ge}from"./Checkbox-CDqupZJG.js";import{g as me}from"./Backdrop-Bzn12VyM.js";import{u as he,P as be}from"./Paper-CcwAvfvc.js";const we=C();function Ce(e){return e.substring(2).toLowerCase()}function ve(e){const{children:t,disableReactTree:r=!1,mouseEvent:n="onClick",onClickAway:o,touchEvent:l="onTouchEnd"}=e,i=g.useRef(!1),a=g.useRef(null),s=g.useRef(!1),c=g.useRef(!1);g.useEffect((()=>(setTimeout((()=>{s.current=!0}),0),()=>{s.current=!1})),[]);const u=v(me(t),a),d=y((e=>{const t=c.current;c.current=!1;const n=$(a.current);if(!s.current||!a.current||"clientX"in e&&function(e,t){return t.documentElement.clientWidth<e.clientX||t.documentElement.clientHeight<e.clientY}(e,n))return;if(i.current)return void(i.current=!1);let l;l=e.composedPath?e.composedPath().includes(a.current):!n.documentElement.contains(e.target)||a.current.contains(e.target),l||!r&&t||o(e)})),p=e=>r=>{c.current=!0;const n=t.props[e];n&&n(r)},f={ref:u};return!1!==l&&(f[l]=p(l)),g.useEffect((()=>{if(!1!==l){const e=Ce(l),t=$(a.current),r=()=>{i.current=!0};return t.addEventListener(e,d),t.addEventListener("touchmove",r),()=>{t.removeEventListener(e,d),t.removeEventListener("touchmove",r)}}}),[d,l]),!1!==n&&(f[n]=p(n)),g.useEffect((()=>{if(!1!==n){const e=Ce(n),t=$(a.current);return t.addEventListener(e,d),()=>{t.removeEventListener(e,d)}}}),[d,n]),g.cloneElement(t,f)}function ye(e){return x("MuiLinearProgress",e)}S("MuiLinearProgress",["root","colorPrimary","colorSecondary","determinate","indeterminate","buffer","query","dashed","dashedColorPrimary","dashedColorSecondary","bar","bar1","bar2","barColorPrimary","barColorSecondary","bar1Indeterminate","bar1Determinate","bar1Buffer","bar2Indeterminate","bar2Buffer"]);const xe=s`
  0% {
    left: -35%;
    right: 100%;
  }

  60% {
    left: 100%;
    right: -90%;
  }

  100% {
    left: 100%;
    right: -90%;
  }
`,Se="string"!=typeof xe?a`
        animation: ${xe} 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
      `:null,Re=s`
  0% {
    left: -200%;
    right: 100%;
  }

  60% {
    left: 107%;
    right: -8%;
  }

  100% {
    left: 107%;
    right: -8%;
  }
`,Ie="string"!=typeof Re?a`
        animation: ${Re} 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;
      `:null,Me=s`
  0% {
    opacity: 1;
    background-position: 0 -23px;
  }

  60% {
    opacity: 0;
    background-position: 0 -23px;
  }

  100% {
    opacity: 1;
    background-position: -200px -23px;
  }
`,ke="string"!=typeof Me?a`
        animation: ${Me} 3s infinite linear;
      `:null,Pe=(e,t)=>e.vars?e.vars.palette.LinearProgress[`${t}Bg`]:"light"===e.palette.mode?l(e.palette[t].main,.62):i(e.palette[t].main,.5),Ee=R("span",{name:"MuiLinearProgress",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[`color${o(r.color)}`],t[r.variant]]}})(M((({theme:e})=>({position:"relative",overflow:"hidden",display:"block",height:4,zIndex:0,"@media print":{colorAdjust:"exact"},variants:[...Object.entries(e.palette).filter(k()).map((([t])=>({props:{color:t},style:{backgroundColor:Pe(e,t)}}))),{props:({ownerState:e})=>"inherit"===e.color&&"buffer"!==e.variant,style:{"&::before":{content:'""',position:"absolute",left:0,top:0,right:0,bottom:0,backgroundColor:"currentColor",opacity:.3}}},{props:{variant:"buffer"},style:{backgroundColor:"transparent"}},{props:{variant:"query"},style:{transform:"rotate(180deg)"}}]})))),Fe=R("span",{name:"MuiLinearProgress",slot:"Dashed",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.dashed,t[`dashedColor${o(r.color)}`]]}})(M((({theme:e})=>({position:"absolute",marginTop:0,height:"100%",width:"100%",backgroundSize:"10px 10px",backgroundPosition:"0 -23px",variants:[{props:{color:"inherit"},style:{opacity:.3,backgroundImage:"radial-gradient(currentColor 0%, currentColor 16%, transparent 42%)"}},...Object.entries(e.palette).filter(k()).map((([t])=>{const r=Pe(e,t);return{props:{color:t},style:{backgroundImage:`radial-gradient(${r} 0%, ${r} 16%, transparent 42%)`}}}))]}))),ke||{animation:`${Me} 3s infinite linear`}),He=R("span",{name:"MuiLinearProgress",slot:"Bar1",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.bar,t.bar1,t[`barColor${o(r.color)}`],("indeterminate"===r.variant||"query"===r.variant)&&t.bar1Indeterminate,"determinate"===r.variant&&t.bar1Determinate,"buffer"===r.variant&&t.bar1Buffer]}})(M((({theme:e})=>({width:"100%",position:"absolute",left:0,bottom:0,top:0,transition:"transform 0.2s linear",transformOrigin:"left",variants:[{props:{color:"inherit"},style:{backgroundColor:"currentColor"}},...Object.entries(e.palette).filter(k()).map((([t])=>({props:{color:t},style:{backgroundColor:(e.vars||e).palette[t].main}}))),{props:{variant:"determinate"},style:{transition:"transform .4s linear"}},{props:{variant:"buffer"},style:{zIndex:1,transition:"transform .4s linear"}},{props:({ownerState:e})=>"indeterminate"===e.variant||"query"===e.variant,style:{width:"auto"}},{props:({ownerState:e})=>"indeterminate"===e.variant||"query"===e.variant,style:Se||{animation:`${xe} 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite`}}]})))),Te=R("span",{name:"MuiLinearProgress",slot:"Bar2",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.bar,t.bar2,t[`barColor${o(r.color)}`],("indeterminate"===r.variant||"query"===r.variant)&&t.bar2Indeterminate,"buffer"===r.variant&&t.bar2Buffer]}})(M((({theme:e})=>({width:"100%",position:"absolute",left:0,bottom:0,top:0,transition:"transform 0.2s linear",transformOrigin:"left",variants:[...Object.entries(e.palette).filter(k()).map((([t])=>({props:{color:t},style:{"--LinearProgressBar2-barColor":(e.vars||e).palette[t].main}}))),{props:({ownerState:e})=>"buffer"!==e.variant&&"inherit"!==e.color,style:{backgroundColor:"var(--LinearProgressBar2-barColor, currentColor)"}},{props:({ownerState:e})=>"buffer"!==e.variant&&"inherit"===e.color,style:{backgroundColor:"currentColor"}},{props:{color:"inherit"},style:{opacity:.3}},...Object.entries(e.palette).filter(k()).map((([t])=>({props:{color:t,variant:"buffer"},style:{backgroundColor:Pe(e,t),transition:"transform .4s linear"}}))),{props:({ownerState:e})=>"indeterminate"===e.variant||"query"===e.variant,style:{width:"auto"}},{props:({ownerState:e})=>"indeterminate"===e.variant||"query"===e.variant,style:Ie||{animation:`${Re} 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite`}}]})))),De=g.forwardRef((function(l,i){const a=e({props:l,name:"MuiLinearProgress"}),{className:s,color:c="primary",value:u,valueBuffer:d,variant:p="indeterminate",...f}=a,g={...a,color:c,variant:p},m=(e=>{const{classes:t,variant:r,color:n}=e,l={root:["root",`color${o(n)}`,r],dashed:["dashed",`dashedColor${o(n)}`],bar1:["bar","bar1",`barColor${o(n)}`,("indeterminate"===r||"query"===r)&&"bar1Indeterminate","determinate"===r&&"bar1Determinate","buffer"===r&&"bar1Buffer"],bar2:["bar","bar2","buffer"!==r&&`barColor${o(n)}`,"buffer"===r&&`color${o(n)}`,("indeterminate"===r||"query"===r)&&"bar2Indeterminate","buffer"===r&&"bar2Buffer"]};return I(l,ye,t)})(g),h=t(),b={},w={bar1:{},bar2:{}};if(("determinate"===p||"buffer"===p)&&void 0!==u){b["aria-valuenow"]=Math.round(u),b["aria-valuemin"]=0,b["aria-valuemax"]=100;let e=u-100;h&&(e=-e),w.bar1.transform=`translateX(${e}%)`}if("buffer"===p&&void 0!==d){let e=(d||0)-100;h&&(e=-e),w.bar2.transform=`translateX(${e}%)`}return r.jsxs(Ee,{className:n(m.root,s),ownerState:g,role:"progressbar",...b,ref:i,...f,children:["buffer"===p?r.jsx(Fe,{className:m.dashed,ownerState:g}):null,r.jsx(He,{className:m.bar1,ownerState:g,style:w.bar1}),"determinate"===p?null:r.jsx(Te,{className:m.bar2,ownerState:g,style:w.bar2})]})})),Oe=L(r.jsx("path",{d:"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"})),$e=L(r.jsx("path",{d:"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"}));function je(e){return x("MuiSkeleton",e)}S("MuiSkeleton",["root","text","rectangular","rounded","circular","pulse","wave","withChildren","fitContent","heightAuto"]);const Le=s`
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
`,ze=s`
  0% {
    transform: translateX(-100%);
  }

  50% {
    /* +0.5s of delay between each loop */
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
`,Ae="string"!=typeof Le?a`
        animation: ${Le} 2s ease-in-out 0.5s infinite;
      `:null,Be="string"!=typeof ze?a`
        &::after {
          animation: ${ze} 2s linear 0.5s infinite;
        }
      `:null,Ve=R("span",{name:"MuiSkeleton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],!1!==r.animation&&t[r.animation],r.hasChildren&&t.withChildren,r.hasChildren&&!r.width&&t.fitContent,r.hasChildren&&!r.height&&t.heightAuto]}})(M((({theme:e})=>{const t=(r=e.shape.borderRadius,String(r).match(/[\d.\-+]*\s*(.*)/)[1]||"px");var r;const n=(o=e.shape.borderRadius,parseFloat(o));var o;return{display:"block",backgroundColor:e.vars?e.vars.palette.Skeleton.bg:c(e.palette.text.primary,"light"===e.palette.mode?.11:.13),height:"1.2em",variants:[{props:{variant:"text"},style:{marginTop:0,marginBottom:0,height:"auto",transformOrigin:"0 55%",transform:"scale(1, 0.60)",borderRadius:`${n}${t}/${Math.round(n/.6*10)/10}${t}`,"&:empty:before":{content:'"\\00a0"'}}},{props:{variant:"circular"},style:{borderRadius:"50%"}},{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:({ownerState:e})=>e.hasChildren,style:{"& > *":{visibility:"hidden"}}},{props:({ownerState:e})=>e.hasChildren&&!e.width,style:{maxWidth:"fit-content"}},{props:({ownerState:e})=>e.hasChildren&&!e.height,style:{height:"auto"}},{props:{animation:"pulse"},style:Ae||{animation:`${Le} 2s ease-in-out 0.5s infinite`}},{props:{animation:"wave"},style:{position:"relative",overflow:"hidden",WebkitMaskImage:"-webkit-radial-gradient(white, black)","&::after":{background:`linear-gradient(\n                90deg,\n                transparent,\n                ${(e.vars||e).palette.action.hover},\n                transparent\n              )`,content:'""',position:"absolute",transform:"translateX(-100%)",bottom:0,left:0,right:0,top:0}}},{props:{animation:"wave"},style:Be||{"&::after":{animation:`${ze} 2s linear 0.5s infinite`}}}]}}))),Ne=g.forwardRef((function(t,o){const l=e({props:t,name:"MuiSkeleton"}),{animation:i="pulse",className:a,component:s="span",height:c,style:u,variant:d="text",width:p,...f}=l,g={...l,animation:i,component:s,variant:d,hasChildren:Boolean(f.children)},m=(e=>{const{classes:t,variant:r,animation:n,hasChildren:o,width:l,height:i}=e;return I({root:["root",r,n,o&&"withChildren",o&&!l&&"fitContent",o&&!i&&"heightAuto"]},je,t)})(g);return r.jsx(Ve,{as:s,ref:o,className:n(m.root,a),ownerState:g,...f,style:{width:p,height:c,...u}})})),Ge=g.createContext(),We=g.createContext();function Ue(e){return x("MuiTableCell",e)}const Ke=S("MuiTableCell",["root","head","body","footer","sizeSmall","sizeMedium","paddingCheckbox","paddingNone","alignLeft","alignCenter","alignRight","alignJustify","stickyHeader"]),_e=R("td",{name:"MuiTableCell",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],t[`size${o(r.size)}`],"normal"!==r.padding&&t[`padding${o(r.padding)}`],"inherit"!==r.align&&t[`align${o(r.align)}`],r.stickyHeader&&t.stickyHeader]}})(M((({theme:e})=>({...e.typography.body2,display:"table-cell",verticalAlign:"inherit",borderBottom:e.vars?`1px solid ${e.vars.palette.TableCell.border}`:`1px solid\n    ${"light"===e.palette.mode?l(c(e.palette.divider,1),.88):i(c(e.palette.divider,1),.68)}`,textAlign:"left",padding:16,variants:[{props:{variant:"head"},style:{color:(e.vars||e).palette.text.primary,lineHeight:e.typography.pxToRem(24),fontWeight:e.typography.fontWeightMedium}},{props:{variant:"body"},style:{color:(e.vars||e).palette.text.primary}},{props:{variant:"footer"},style:{color:(e.vars||e).palette.text.secondary,lineHeight:e.typography.pxToRem(21),fontSize:e.typography.pxToRem(12)}},{props:{size:"small"},style:{padding:"6px 16px",[`&.${Ke.paddingCheckbox}`]:{width:24,padding:"0 12px 0 16px","& > *":{padding:0}}}},{props:{padding:"checkbox"},style:{width:48,padding:"0 0 0 4px"}},{props:{padding:"none"},style:{padding:0}},{props:{align:"left"},style:{textAlign:"left"}},{props:{align:"center"},style:{textAlign:"center"}},{props:{align:"right"},style:{textAlign:"right",flexDirection:"row-reverse"}},{props:{align:"justify"},style:{textAlign:"justify"}},{props:({ownerState:e})=>e.stickyHeader,style:{position:"sticky",top:0,zIndex:2,backgroundColor:(e.vars||e).palette.background.default}}]})))),qe=g.forwardRef((function(t,l){const i=e({props:t,name:"MuiTableCell"}),{align:a="inherit",className:s,component:c,padding:u,scope:d,size:p,sortDirection:f,variant:m,...h}=i,b=g.useContext(Ge),w=g.useContext(We),C=w&&"head"===w.variant;let v;v=c||(C?"th":"td");let y=d;"td"===v?y=void 0:!y&&C&&(y="col");const x=m||w&&w.variant,S={...i,align:a,component:v,padding:u||(b&&b.padding?b.padding:"normal"),size:p||(b&&b.size?b.size:"medium"),sortDirection:f,stickyHeader:"head"===x&&b&&b.stickyHeader,variant:x},R=(e=>{const{classes:t,variant:r,align:n,padding:l,size:i,stickyHeader:a}=e,s={root:["root",r,a&&"stickyHeader","inherit"!==n&&`align${o(n)}`,"normal"!==l&&`padding${o(l)}`,`size${o(i)}`]};return I(s,Ue,t)})(S);let M=null;return f&&(M="asc"===f?"ascending":"descending"),r.jsx(_e,{as:v,ref:l,className:n(R.root,s),"aria-sort":M,scope:y,ownerState:S,...h})})),Xe=g.forwardRef((function(e,n){const{backIconButtonProps:o,count:l,disabled:i=!1,getItemAriaLabel:a,nextIconButtonProps:s,onPageChange:c,page:u,rowsPerPage:d,showFirstButton:p,showLastButton:f,slots:g={},slotProps:m={},...h}=e,b=t(),w=g.firstButton??ee,C=g.lastButton??ee,v=g.nextButton??ee,y=g.previousButton??ee,x=g.firstButtonIcon??Oe,S=g.lastButtonIcon??$e,R=g.nextButtonIcon??le,I=g.previousButtonIcon??ie,M=b?C:w,k=b?v:y,P=b?y:v,E=b?w:C,F=b?m.lastButton:m.firstButton,H=b?m.nextButton:m.previousButton,T=b?m.previousButton:m.nextButton,D=b?m.firstButton:m.lastButton;return r.jsxs("div",{ref:n,...h,children:[p&&r.jsx(M,{onClick:e=>{c(e,0)},disabled:i||0===u,"aria-label":a("first",u),title:a("first",u),...F,children:b?r.jsx(S,{...m.lastButtonIcon}):r.jsx(x,{...m.firstButtonIcon})}),r.jsx(k,{onClick:e=>{c(e,u-1)},disabled:i||0===u,color:"inherit","aria-label":a("previous",u),title:a("previous",u),...H??o,children:b?r.jsx(R,{...m.nextButtonIcon}):r.jsx(I,{...m.previousButtonIcon})}),r.jsx(P,{onClick:e=>{c(e,u+1)},disabled:i||-1!==l&&u>=Math.ceil(l/d)-1,color:"inherit","aria-label":a("next",u),title:a("next",u),...T??s,children:b?r.jsx(I,{...m.previousButtonIcon}):r.jsx(R,{...m.nextButtonIcon})}),f&&r.jsx(E,{onClick:e=>{c(e,Math.max(0,Math.ceil(l/d)-1))},disabled:i||u>=Math.ceil(l/d)-1,"aria-label":a("last",u),title:a("last",u),...D,children:b?r.jsx(x,{...m.firstButtonIcon}):r.jsx(S,{...m.lastButtonIcon})})]})}));function Qe(e){return x("MuiTablePagination",e)}const Ye=S("MuiTablePagination",["root","toolbar","spacer","selectLabel","selectRoot","select","selectIcon","input","menuItem","displayedRows","actions"]);var Je;const Ze=R(qe,{name:"MuiTablePagination",slot:"Root"})(M((({theme:e})=>({overflow:"auto",color:(e.vars||e).palette.text.primary,fontSize:e.typography.pxToRem(14),"&:last-child":{padding:0}})))),et=R(re,{name:"MuiTablePagination",slot:"Toolbar",overridesResolver:(e,t)=>({[`& .${Ye.actions}`]:t.actions,...t.toolbar})})(M((({theme:e})=>({minHeight:52,paddingRight:2,[`${e.breakpoints.up("xs")} and (orientation: landscape)`]:{minHeight:52},[e.breakpoints.up("sm")]:{minHeight:52,paddingRight:2},[`& .${Ye.actions}`]:{flexShrink:0,marginLeft:20}})))),tt=R("div",{name:"MuiTablePagination",slot:"Spacer"})({flex:"1 1 100%"}),rt=R("p",{name:"MuiTablePagination",slot:"SelectLabel"})(M((({theme:e})=>({...e.typography.body2,flexShrink:0})))),nt=R(X,{name:"MuiTablePagination",slot:"Select",overridesResolver:(e,t)=>({[`& .${Ye.selectIcon}`]:t.selectIcon,[`& .${Ye.select}`]:t.select,...t.input,...t.selectRoot})})({color:"inherit",fontSize:"inherit",flexShrink:0,marginRight:32,marginLeft:8,[`& .${Ye.select}`]:{paddingLeft:8,paddingRight:24,textAlign:"right",textAlignLast:"right"}}),ot=R(W,{name:"MuiTablePagination",slot:"MenuItem"})({}),lt=R("p",{name:"MuiTablePagination",slot:"DisplayedRows"})(M((({theme:e})=>({...e.typography.body2,flexShrink:0}))));function it({from:e,to:t,count:r}){return`${e}–${t} of ${-1!==r?r:`more than ${t}`}`}function at(e){return`Go to ${e} page`}const st=g.forwardRef((function(t,o){const l=e({props:t,name:"MuiTablePagination"}),{ActionsComponent:i=Xe,backIconButtonProps:a,colSpan:s,component:c=qe,count:u,disabled:d=!1,getItemAriaLabel:p=at,labelDisplayedRows:f=it,labelRowsPerPage:m="Rows per page:",nextIconButtonProps:h,onPageChange:b,onRowsPerPageChange:w,page:C,rowsPerPage:v,rowsPerPageOptions:y=[10,25,50,100],SelectProps:x={},showFirstButton:S=!1,showLastButton:R=!1,slotProps:M={},slots:k={},...E}=l,F=l,H=(e=>{const{classes:t}=e;return I({root:["root"],toolbar:["toolbar"],spacer:["spacer"],selectLabel:["selectLabel"],select:["select"],input:["input"],selectIcon:["selectIcon"],menuItem:["menuItem"],displayedRows:["displayedRows"],actions:["actions"]},Qe,t)})(F),T=M?.select??x,D=T.native?"option":ot;let O;c!==qe&&"td"!==c||(O=s||1e3);const $=P(T.id),j=P(T.labelId),L={slots:k,slotProps:M},[z,A]=de("root",{ref:o,className:H.root,elementType:Ze,externalForwardedProps:{...L,component:c,...E},ownerState:F,additionalProps:{colSpan:O}}),[B,V]=de("toolbar",{className:H.toolbar,elementType:et,externalForwardedProps:L,ownerState:F}),[N,G]=de("spacer",{className:H.spacer,elementType:tt,externalForwardedProps:L,ownerState:F}),[W,K]=de("selectLabel",{className:H.selectLabel,elementType:rt,externalForwardedProps:L,ownerState:F,additionalProps:{id:j}}),[_,q]=de("select",{className:H.select,elementType:nt,externalForwardedProps:L,ownerState:F}),[X,Q]=de("menuItem",{className:H.menuItem,elementType:D,externalForwardedProps:L,ownerState:F}),[Y,J]=de("displayedRows",{className:H.displayedRows,elementType:lt,externalForwardedProps:L,ownerState:F});return r.jsx(z,{...A,children:r.jsxs(B,{...V,children:[r.jsx(N,{...G}),y.length>1&&r.jsx(W,{...K,children:m}),y.length>1&&r.jsx(_,{variant:"standard",...!T.variant&&{input:Je||(Je=r.jsx(U,{}))},value:v,onChange:w,id:$,labelId:j,...T,classes:{...T.classes,root:n(H.input,H.selectRoot,(T.classes||{}).root),select:n(H.select,(T.classes||{}).select),icon:n(H.selectIcon,(T.classes||{}).icon)},disabled:d,...q,children:y.map((e=>g.createElement(X,{...Q,key:e.label?e.label:e,value:e.value?e.value:e},e.label?e.label:e)))}),r.jsx(Y,{...J,children:f({from:0===u?0:C*v+1,to:-1===u?(C+1)*v:-1===v?u:Math.min(u,(C+1)*v),count:-1===u?-1:u,page:C})}),r.jsx(i,{className:H.actions,backIconButtonProps:a,count:u,nextIconButtonProps:h,onPageChange:b,page:C,rowsPerPage:v,showFirstButton:S,showLastButton:R,slotProps:M.actions,slots:k.actions,getItemAriaLabel:p,disabled:d})]})})})),ct=g.createContext(void 0);function ut(){const e=g.useContext(ct);if(void 0===e)throw new Error(["MUI X: Could not find the Data Grid context.","It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.","This can also happen if you are bundling multiple versions of the Data Grid."].join("\n"));return e}const dt=g.createContext(void 0),pt=()=>{const e=g.useContext(dt);if(!e)throw new Error("MUI X: useGridRootProps should only be used inside the DataGrid, DataGridPro or DataGridPremium component.");return e},ft=parseInt(g.version,10),gt=e=>{if(ft>=19){const t=t=>e(t,t.ref??null);return t.displayName=e.displayName??e.name,t}return g.forwardRef(e)};var mt=Symbol("NOT_FOUND"),ht=e=>Array.isArray(e)?e:[e],bt=(e,t)=>e===t;function wt(e,t){const r="object"==typeof t?t:{equalityCheck:t},{equalityCheck:n=bt,maxSize:o=1,resultEqualityCheck:l}=r,i=function(e){return function(t,r){if(null===t||null===r||t.length!==r.length)return!1;const{length:n}=t;for(let o=0;o<n;o++)if(!e(t[o],r[o]))return!1;return!0}}(n);let a=0;const s=o<=1?function(e){let t;return{get:r=>t&&e(t.key,r)?t.value:mt,put(e,r){t={key:e,value:r}},getEntries:()=>t?[t]:[],clear(){t=void 0}}}(i):function(e,t){let r=[];function n(e){const n=r.findIndex((r=>t(e,r.key)));if(n>-1){const e=r[n];return n>0&&(r.splice(n,1),r.unshift(e)),e.value}return mt}return{get:n,put:function(t,o){n(t)===mt&&(r.unshift({key:t,value:o}),r.length>e&&r.pop())},getEntries:function(){return r},clear:function(){r=[]}}}(o,i);function c(){let t=s.get(arguments);if(t===mt){if(t=e.apply(null,arguments),a++,l){const e=s.getEntries().find((e=>l(e.value,t)));e&&(t=e.value,0!==a&&a--)}s.put(arguments,t)}return t}return c.clearCache=()=>{s.clear(),c.resetResultsCount()},c.resultsCount=()=>a,c.resetResultsCount=()=>{a=0},c}var Ct="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function vt(e,t={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let o,l=0;function i(){let t=r;const{length:i}=arguments;for(let e=0,r=i;e<r;e++){const r=arguments[e];if("function"==typeof r||"object"==typeof r&&null!==r){let e=t.o;null===e&&(t.o=e=new WeakMap);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}else{let e=t.p;null===e&&(t.p=e=new Map);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}}const a=t;let s;if(1===t.s)s=t.v;else if(s=e.apply(null,arguments),l++,n){const e=o?.deref?.()??o;null!=e&&n(e,s)&&(s=e,0!==l&&l--),o="object"==typeof s&&null!==s||"function"==typeof s?new Ct(s):s}return a.s=1,a.v=s,s}return i.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},i.resetResultsCount()},i.resultsCount=()=>l,i.resetResultsCount=()=>{l=0},i}function yt(e,...t){const r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,o=0,l={},i=e.pop();"object"==typeof i&&(l=i,i=e.pop()),function(e,t="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(t)}(i,`createSelector expects an output function after the inputs, but received: [${typeof i}]`);const a={...r,...l},{memoize:s,memoizeOptions:c=[],argsMemoize:u=vt,argsMemoizeOptions:d=[]}=a,p=ht(c),f=ht(d),g=function(e){const t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every((e=>"function"==typeof e))){const r=e.map((e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e)).join(", ");throw new TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),m=s((function(){return n++,i.apply(null,arguments)}),...p),h=u((function(){o++;const e=function(e,t){const r=[],{length:n}=e;for(let o=0;o<n;o++)r.push(e[o].apply(null,t));return r}(g,arguments);return t=m.apply(null,e),t}),...f);return Object.assign(h,{resultFunc:i,memoizedResultFunc:m,dependencies:g,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:s,argsMemoize:u})};return Object.assign(n,{withTypes:()=>n}),n}var xt=yt(vt),St=Object.assign(((e,t=xt)=>{!function(e,t="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const r=Object.keys(e);return t(r.map((t=>e[t])),((...e)=>e.reduce(((e,t,n)=>(e[r[n]]=t,e)),{})))}),{withTypes:()=>St});const Rt=Object.is;function It(e,t){if(e===t)return!0;if(!(e instanceof Object&&t instanceof Object))return!1;let r=0,n=0;for(const o in e){if(r+=1,!Rt(e[o],t[o]))return!1;if(!(o in t))return!1}for(const o in t)n+=1;return r===n}var Mt,kt,Pt={exports:{}},Et={},Ft=(kt||(kt=1,Pt.exports=function(){if(Mt)return Et;Mt=1;var e=w(),t="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=e.useState,n=e.useEffect,o=e.useLayoutEffect,l=e.useDebugValue;function i(e){var r=e.getSnapshot;e=e.value;try{var n=r();return!t(e,n)}catch(o){return!0}}var a="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var a=t(),s=r({inst:{value:a,getSnapshot:t}}),c=s[0].inst,u=s[1];return o((function(){c.value=a,c.getSnapshot=t,i(c)&&u({inst:c})}),[e,a,t]),n((function(){return i(c)&&u({inst:c}),e((function(){i(c)&&u({inst:c})}))}),[e]),l(a),a};return Et.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:a,Et}()),Pt.exports);const Ht=Object.is,Tt=It,Dt=(e,t)=>e===t||e.length===t.length&&e.every(((e,r)=>e===t[r])),Ot=(e,t)=>{let r=Object.is;return t instanceof Array?r=Dt:t instanceof Object&&(r=Tt),r(e,t)},$t=()=>({state:null,equals:null,selector:null,args:void 0}),jt=[],Lt=()=>null,zt=(e,t,r=void 0,n=Ht)=>{const o=E($t),l=null!==o.current.selector,[i,a]=g.useState(l?null:t(e,r));o.current.state=i,o.current.equals=n,o.current.selector=t;const s=o.current.args;if(o.current.args=r,l&&!Ot(s,r)){const t=o.current.selector(e,o.current.args);o.current.equals(o.current.state,t)||(o.current.state=t,a(t))}const c=g.useCallback((()=>(o.current.subscription||(o.current.subscription=e.current.store.subscribe((()=>{const t=o.current.selector(e,o.current.args);o.current.equals(o.current.state,t)||(o.current.state=t,a(t))}))),null)),jt),u=g.useCallback((()=>()=>{o.current.subscription&&(o.current.subscription(),o.current.subscription=void 0)}),jt);return Ft.useSyncExternalStore(u,c,Lt),i};class At{constructor(e){this.value=e}deref(){return this.value}}const Bt=(()=>"undefined"==typeof WeakRef?At:WeakRef)(),Vt=yt({memoize:wt,memoizeOptions:{maxSize:1,equalityCheck:Object.is},argsMemoize:function(e,t={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let o,l=0;function i(){let t=r;const{length:i}=arguments;for(let e=0,r=i;e<r;e+=1){let r=arguments[e];if("function"==typeof r||"object"==typeof r&&null!==r){"current"in r&&"instanceId"in r.current&&(r=r.current.state);let e=t.o;null===e&&(e=new WeakMap,t.o=e);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}else{let e=t.p;null===e&&(e=new Map,t.p=e);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}}const a=t;let s;if(1===t.s)s=t.v;else if(s=e.apply(null,arguments),l+=1,n){const e=(c=o)instanceof Bt?c.deref():c;null!=e&&n(e,s)&&(s=e,0!==l&&(l-=1)),o="object"==typeof s&&null!==s||"function"==typeof s?new Bt(s):s}var c;return a.s=1,a.v=s,s}return i.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},i.resetResultsCount()},i.resultsCount=()=>l,i.resetResultsCount=()=>{l=0},i}}),Nt=new WeakMap,Gt=(e,t,r,n,o,l,...i)=>{if(i.length>0)throw new Error("Unsupported number of selectors");let a;if(e&&t&&r&&n&&o&&l)a=(i,a)=>{const s=e(i,a),c=t(i,a),u=r(i,a),d=n(i,a),p=o(i,a);return l(s,c,u,d,p,a)};else if(e&&t&&r&&n&&o)a=(l,i)=>{const a=e(l,i),s=t(l,i),c=r(l,i),u=n(l,i);return o(a,s,c,u,i)};else if(e&&t&&r&&n)a=(o,l)=>{const i=e(o,l),a=t(o,l),s=r(o,l);return n(i,a,s,l)};else if(e&&t&&r)a=(n,o)=>{const l=e(n,o),i=t(n,o);return r(l,i,o)};else{if(!e||!t)throw new Error("Missing arguments");a=(r,n)=>{const o=e(r,n);return t(o,n)}}return a},Wt=e=>(t,r)=>e(t.current.state,r),Ut=(...e)=>(t,r)=>{const n=t.current.instanceId,o=Nt.get(n),l=o??new Map,i=l.get(e);if(l&&i){if(!Ot(i.selectorArgs,r)){const n=void 0!==r?[...e.slice(0,e.length-1),()=>r,e[e.length-1]]:e,o=Vt(...n);return o.selectorArgs=r,l.set(e,o),o(t,r)}return i(t,r)}const a=void 0!==r?[...e.slice(0,e.length-1),()=>r,e[e.length-1]]:e,s=Vt(...a);return s.selectorArgs=r,o||Nt.set(n,l),l.set(e,s),s(t,r)},Kt=Wt((e=>e.dimensions)),_t=Gt(Kt,(e=>e.columnsTotalWidth)),qt=Gt(Kt,(e=>e.rowHeight)),Xt=Gt(Kt,(e=>e.contentSize.height)),Qt=Gt(Kt,(e=>e.hasScrollX)),Yt=Gt(Kt,(e=>e.hasScrollY)),Jt=Gt(Kt,(e=>e.columnsTotalWidth<e.viewportOuterSize.width)),Zt=Gt(Kt,(e=>e.headerHeight)),er=Gt(Kt,(e=>e.groupHeaderHeight));Gt(Kt,(e=>e.headerFilterHeight));const tr=Gt(Kt,(e=>e.hasScrollX?e.scrollbarSize:0)),rr=Gt(Kt,(e=>e.hasScrollY?e.scrollbarSize:0)),nr=Gt(Kt,tr,((e,t)=>{const r=e.viewportOuterSize.height-e.minimumSize.height>0;return!(0===t&&!r)}));function or(e){return g.memo(e,It)}function lr(e){return x("MuiDataGrid",e)}const ir=S("MuiDataGrid",["aiAssistantPanel","aiAssistantPanelHeader","aiAssistantPanelTitleContainer","aiAssistantPanelTitle","aiAssistantPanelBody","aiAssistantPanelEmptyText","aiAssistantPanelFooter","aiAssistantPanelConversation","aiAssistantPanelConversationList","aiAssistantPanelConversationTitle","aiAssistantPanelSuggestions","aiAssistantPanelSuggestionsList","aiAssistantPanelSuggestionsItem","aiAssistantPanelSuggestionsLabel","actionsCell","aggregationColumnHeader","aggregationColumnHeader--alignLeft","aggregationColumnHeader--alignCenter","aggregationColumnHeader--alignRight","aggregationColumnHeaderLabel","aggregationRowOverlayWrapper","autoHeight","autosizing","mainContent","withSidePanel","booleanCell","cell--editable","cell--editing","cell--flex","cell--textCenter","cell--textLeft","cell--textRight","cell--rangeTop","cell--rangeBottom","cell--rangeLeft","cell--rangeRight","cell--pinnedLeft","cell--pinnedRight","cell--selectionMode","cell","cellCheckbox","cellEmpty","cellSkeleton","cellOffsetLeft","checkboxInput","collapsible","collapsibleTrigger","collapsibleIcon","collapsiblePanel","columnHeader","columnHeader--alignCenter","columnHeader--alignLeft","columnHeader--alignRight","columnHeader--dragging","columnHeader--moving","columnHeader--numeric","columnHeader--sortable","columnHeader--sorted","columnHeader--filtered","columnHeader--pinnedLeft","columnHeader--pinnedRight","columnHeader--last","columnHeader--lastUnpinned","columnHeader--siblingFocused","columnHeader--filter","columnHeaderFilterInput","columnHeaderFilterOperatorLabel","columnHeaderCheckbox","columnHeaderDraggableContainer","columnHeaderTitle","columnHeaderTitleContainer","columnHeaderTitleContainerContent","columnHeader--filledGroup","columnHeader--emptyGroup","columnHeaders","columnSeparator--resizable","columnSeparator--resizing","columnSeparator--sideLeft","columnSeparator--sideRight","columnSeparator","columnsManagement","columnsManagementRow","columnsManagementHeader","columnsManagementSearchInput","columnsManagementFooter","columnsManagementScrollArea","columnsManagementEmptyText","container--top","container--bottom","detailPanel","detailPanelToggleCell","detailPanelToggleCell--expanded","footerCell","panel","panelHeader","panelWrapper","panelContent","panelFooter","paper","editBooleanCell","editInputCell","filler","filler--borderBottom","filler--pinnedLeft","filler--pinnedRight","filterForm","filterFormDeleteIcon","filterFormLogicOperatorInput","filterFormColumnInput","filterFormOperatorInput","filterFormValueInput","filterIcon","footerContainer","headerFilterRow","iconButtonContainer","iconSeparator","main","main--hasPinnedRight","main--hiddenContent","menu","menuIcon","menuIconButton","menuOpen","menuList","overlay","overlayWrapper","overlayWrapperInner","root","root--densityStandard","root--densityComfortable","root--densityCompact","root--disableUserSelection","root--noToolbar","row","row--editable","row--editing","row--firstVisible","row--lastVisible","row--dragging","row--dynamicHeight","row--detailPanelExpanded","row--borderBottom","rowReorderCellPlaceholder","rowCount","rowReorderCellContainer","rowReorderCell","rowReorderCell--draggable","rowSkeleton","scrollArea--left","scrollArea--right","scrollArea","scrollbar","scrollbar--vertical","scrollbar--horizontal","scrollbarFiller","scrollbarFiller--header","scrollbarFiller--borderTop","scrollbarFiller--borderBottom","scrollbarFiller--pinnedRight","selectedRowCount","sortButton","sortIcon","shadowScrollArea","sidebar","sidebarHeader","toolbarContainer","toolbar","toolbarLabel","toolbarDivider","toolbarFilterList","toolbarQuickFilter","toolbarQuickFilterTrigger","toolbarQuickFilterControl","virtualScroller","virtualScroller--hasScrollX","virtualScrollerContent","virtualScrollerContent--overflowed","virtualScrollerRenderZone","withVerticalBorder","withBorderColor","cell--withRightBorder","cell--withLeftBorder","columnHeader--withRightBorder","columnHeader--withLeftBorder","treeDataGroupingCell","treeDataGroupingCellToggle","treeDataGroupingCellLoadingContainer","groupingCriteriaCell","groupingCriteriaCellToggle","groupingCriteriaCellLoadingContainer","pinnedRows","pinnedRows--top","pinnedRows--bottom","pivotPanelAvailableFields","pivotPanelField","pivotPanelField--sorted","pivotPanelFieldActionContainer","pivotPanelFieldCheckbox","pivotPanelFieldDragIcon","pivotPanelFieldList","pivotPanelFieldName","pivotPanelHeader","pivotPanelPlaceholder","pivotPanelScrollArea","pivotPanelSearchContainer","pivotPanelSection","pivotPanelSectionTitle","pivotPanelSections","pivotPanelSwitch","pivotPanelSwitchLabel","prompt","promptContent","promptText","promptFeedback","promptChangeList","promptChangesToggle","promptChangesToggleIcon","promptIcon","promptIconContainer","promptError","promptAction"]);let ar=function(e){return e.DataGrid="DataGrid",e.DataGridPro="DataGridPro",e.DataGridPremium="DataGridPremium",e}({});class sr{static create(){return new sr}}const cr={current:"undefined"!=typeof FinalizationRegistry?new class{constructor(){this.registry=new FinalizationRegistry((e=>{"function"==typeof e&&e()}))}register(e,t,r){this.registry.register(e,t,r)}unregister(e){this.registry.unregister(e)}reset(){}}:new class{constructor(e=1e3){this.timeouts=new Map,this.cleanupTimeout=1e3,this.cleanupTimeout=e}register(e,t,r){this.timeouts||(this.timeouts=new Map);const n=setTimeout((()=>{"function"==typeof t&&t(),this.timeouts.delete(r.cleanupToken)}),this.cleanupTimeout);this.timeouts.set(r.cleanupToken,n)}unregister(e){const t=this.timeouts.get(e.cleanupToken);t&&(this.timeouts.delete(e.cleanupToken),clearTimeout(t))}reset(){this.timeouts&&(this.timeouts.forEach(((e,t)=>{this.unregister({cleanupToken:t})})),this.timeouts=void 0)}}};let ur=0;function dr(e,t,r,n){const o=g.useState(sr.create)[0],l=g.useRef(null),i=g.useRef(null);i.current=r;const a=g.useRef(null);if(!l.current&&i.current){const r=(e,t,r)=>{t.defaultMuiPrevented||i.current?.(e,t,r)};l.current=e.current.subscribeEvent(t,r,n),ur+=1,a.current={cleanupToken:ur},cr.current.register(o,(()=>{l.current?.(),l.current=null,a.current=null}),a.current)}else!i.current&&l.current&&(l.current(),l.current=null,a.current&&(cr.current.unregister(a.current),a.current=null));g.useEffect((()=>{if(!l.current&&i.current){const r=(e,t,r)=>{t.defaultMuiPrevented||i.current?.(e,t,r)};l.current=e.current.subscribeEvent(t,r,n)}return a.current&&cr.current&&(cr.current.unregister(a.current),a.current=null),()=>{l.current?.(),l.current=null}}),[e,t,n])}const pr={isFirst:!0};function fr(e,t,r){dr(e,t,r,pr)}const gr={compact:.7,comfortable:1.3,standard:1},mr=Wt((e=>e.density)),hr=Gt(mr,(e=>gr[e]));function br(e){throw new Error("Failed assertion: should not be rendered")}const wr={values:{xs:0,sm:600,md:900,lg:1200,xl:1536},up:e=>{const t=wr.values;return`@media (min-width:${"number"==typeof t[e]?t[e]:e}px)`}},Cr={spacingUnit:"--DataGrid-t-spacing-unit",colors:{border:{base:"--DataGrid-t-color-border-base"},foreground:{base:"--DataGrid-t-color-foreground-base",muted:"--DataGrid-t-color-foreground-muted",accent:"--DataGrid-t-color-foreground-accent",disabled:"--DataGrid-t-color-foreground-disabled",error:"--DataGrid-t-color-foreground-error"},background:{base:"--DataGrid-t-color-background-base",overlay:"--DataGrid-t-color-background-overlay",backdrop:"--DataGrid-t-color-background-backdrop"},interactive:{hover:"--DataGrid-t-color-interactive-hover",hoverOpacity:"--DataGrid-t-color-interactive-hover-opacity",focus:"--DataGrid-t-color-interactive-focus",focusOpacity:"--DataGrid-t-color-interactive-focus-opacity",disabled:"--DataGrid-t-color-interactive-disabled",disabledOpacity:"--DataGrid-t-color-interactive-disabled-opacity",selected:"--DataGrid-t-color-interactive-selected",selectedOpacity:"--DataGrid-t-color-interactive-selected-opacity"}},header:{background:{base:"--DataGrid-t-header-background-base"}},cell:{background:{pinned:"--DataGrid-t-cell-background-pinned"}},radius:{base:"--DataGrid-t-radius-base"},typography:{font:{body:"--DataGrid-t-typography-font-body",small:"--DataGrid-t-typography-font-small",large:"--DataGrid-t-typography-font-large"},fontFamily:{base:"--DataGrid-t-typography-font-family-base"},fontWeight:{light:"--DataGrid-t-typography-font-weight-light",regular:"--DataGrid-t-typography-font-weight-regular",medium:"--DataGrid-t-typography-font-weight-medium",bold:"--DataGrid-t-typography-font-weight-bold"}},transitions:{easing:{easeIn:"--DataGrid-t-transition-easing-ease-in",easeOut:"--DataGrid-t-transition-easing-ease-out",easeInOut:"--DataGrid-t-transition-easing-ease-in-out"},duration:{short:"--DataGrid-t-transition-duration-short",base:"--DataGrid-t-transition-duration-base",long:"--DataGrid-t-transition-duration-long"}},shadows:{base:"--DataGrid-t-shadow-base",overlay:"--DataGrid-t-shadow-overlay"},zIndex:{panel:"--DataGrid-t-z-index-panel",menu:"--DataGrid-t-z-index-menu"}},vr=function e(t){if("string"==typeof t)return`var(${t})`;const r={};for(const n in t)Object.hasOwn(t,n)&&(r[n]=e(t[n]));return r}(Cr),yr=u({breakpoints:wr,spacing:function(e,t,r,n){return void 0===e?xr(1):void 0===t?xr(e):void 0===r?xr(e)+" "+xr(t):void 0===n?xr(e)+" "+xr(t)+" "+xr(r):xr(e)+" "+xr(t)+" "+xr(r)+" "+xr(n)},transition:function(e,t){const{duration:r=yr.transitions.duration.base,easing:n=yr.transitions.easing.easeInOut,delay:o=0}=t??{};return e.map((e=>`${e} ${r} ${n} ${o}ms`)).join(", ")},keys:Cr},vr);function xr(e){return 0===e?"0":`calc(var(--DataGrid-t-spacing-unit) * ${e})`}const Sr=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","hasFocus","isValidating","debounceMs","isProcessingProps","onValueChange","slotProps"],Rr=R(br,{name:"MuiDataGrid",slot:"EditInputCell"})({font:yr.typography.font.body,padding:"1px 0","& input":{padding:"0 16px",height:"100%"}}),Ir=gt(((e,t)=>{const n=pt(),{id:o,value:l,field:i,colDef:a,hasFocus:s,debounceMs:c=200,isProcessingProps:p,onValueChange:f,slotProps:m}=e,h=F(e,Sr),b=ut(),w=g.useRef(null),[C,v]=g.useState(l),y=(e=>{const{classes:t}=e;return I({root:["editInputCell"]},lr,t)})(n),x=g.useCallback((async e=>{const t=e.target.value,r=b.current.getColumn(i);let n=t;r.valueParser&&(n=r.valueParser(t,b.current.getRow(o),r,b)),v(n),b.current.setEditCellValue({id:o,field:i,value:n,debounceMs:c,unstable_skipValueParser:!0},e),f&&await f(e,t)}),[b,c,i,o,f]),S=b.current.unstable_getEditCellMeta(o,i);return g.useEffect((()=>{"debouncedSetEditCellValue"!==S?.changeReason&&v(l)}),[S,l]),d((()=>{s&&w.current.focus()}),[s]),r.jsx(Rr,u({as:n.slots.baseInput,inputRef:w,className:y.root,ownerState:n,fullWidth:!0,type:"number"===a.type?a.type:"text",value:C??"",onChange:x,endAdornment:p?r.jsx(n.slots.loadIcon,{fontSize:"small",color:"action"}):void 0},h,m?.root,{ref:t}))})),Mr=Wt((e=>e.rows)),kr=Gt(Mr,(e=>e.totalRowCount)),Pr=Gt(Mr,(e=>e.loading)),Er=Gt(Mr,(e=>e.totalTopLevelRowCount)),Fr=Gt(Mr,(e=>e.dataRowIdToModelLookup));Gt(Fr,((e,t)=>e[t]));const Hr=Gt(Mr,(e=>e.tree)),Tr=Gt(Hr,((e,t)=>e[t])),Dr=Gt(Mr,(e=>e.groupsToFetch)),Or=Gt(Mr,(e=>e.groupingName)),$r=Gt(Mr,(e=>e.treeDepths)),jr=Ut(Mr,(e=>{const t=Object.entries(e.treeDepths);return 0===t.length?1:(t.filter((([,e])=>e>0)).map((([e])=>Number(e))).sort(((e,t)=>t-e))[0]??0)+1})),Lr=Gt(Mr,(e=>e.dataRowIds)),zr=Ut(Lr,Fr,((e,t)=>e.reduce(((e,r)=>t[r]?(e.push(t[r]),e):e),[]))),Ar=Ut(Gt(Mr,(e=>e?.additionalRowGroups)),(e=>{const t=e?.pinnedRows;return{bottom:t?.bottom?.map((e=>({id:e.id,model:e.model??{}})))??[],top:t?.top?.map((e=>({id:e.id,model:e.model??{}})))??[]}})),Br=Gt(Ar,(e=>(e?.top?.length||0)+(e?.bottom?.length||0))),Vr=(e,t)=>t&&e.length>1?[e[0]]:e,Nr=(e,t)=>r=>u({},r,{sorting:u({},r.sorting,{sortModel:Vr(e,t)})}),Gr=(e,t)=>{const r=e.indexOf(t);return t&&-1!==r&&r+1!==e.length?e[r+1]:e[0]},Wr=(e,t)=>null==e&&null!=t?-1:null==t&&null!=e?1:null==e&&null==t?0:null,Ur=new Intl.Collator,Kr=(e,t)=>{const r=Wr(e,t);return null!==r?r:Number(e)-Number(t)},_r=(e,t)=>{const r=Wr(e,t);return null!==r?r:e>t?1:e<t?-1:0},qr=["item","applyValue","type","apiRef","focusElementRef","tabIndex","disabled","isFilterActive","slotProps","clearButton","headerFilterMenu"];function Xr(e){const{item:t,applyValue:n,type:o,apiRef:l,focusElementRef:i,tabIndex:a,disabled:s,slotProps:c,clearButton:d,headerFilterMenu:p}=e,f=F(e,qr),m=c?.root,h=H(),[b,w]=g.useState(Qr(t.value)),[C,v]=g.useState(!1),y=P(),x=pt(),S=g.useCallback((e=>{const r=Qr(e.target.value);w(r),v(!0),h.start(x.filterDebounceMs,(()=>{const e=u({},t,{value:"number"!==o||Number.isNaN(Number(r))?r:Number(r),fromInput:y});n(e),v(!1)}))}),[h,x.filterDebounceMs,t,o,y,n]);return g.useEffect((()=>{t.fromInput===y&&null!=t.value||w(Qr(t.value))}),[y,t]),r.jsxs(g.Fragment,{children:[r.jsx(x.slots.baseTextField,u({id:y,label:l.current.getLocaleText("filterPanelInputLabel"),placeholder:l.current.getLocaleText("filterPanelInputPlaceholder"),value:b??"",onChange:S,type:o||"text",disabled:s,slotProps:u({},m?.slotProps,{input:u({endAdornment:C?r.jsx(x.slots.loadIcon,{fontSize:"small",color:"action"}):null},m?.slotProps?.input),htmlInput:u({tabIndex:a},m?.slotProps?.htmlInput)}),inputRef:i},x.slotProps?.baseTextField,f,m)),p,d]})}function Qr(e){if(null!=e&&""!==e)return String(e)}function Yr(e){return"object"==typeof e&&null!==e}function Jr(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}const Zr=(e,t,r)=>Math.max(t,Math.min(r,e));function en(e,t){return Array.from({length:t-e}).map(((t,r)=>e+r))}function tn(e){return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}const rn=(e,t)=>r=>{e&&t(r)};function nn(e){const{item:t,applyValue:n,type:o,apiRef:l,focusElementRef:i,slotProps:a}=e,s=P(),[c,d]=g.useState([]),[p,f]=g.useState(t.value||[]),m=pt();g.useEffect((()=>{const e=t.value??[];f(e.map(String))}),[t.value]);const h=g.useCallback(((e,r)=>{f(r.map(String)),n(u({},t,{value:[...r.map((e=>"number"===o?Number(e):e))]}))}),[n,t,o]),b=g.useCallback(((e,t)=>{d(""===t?[]:[t])}),[d]),w=m.slots.baseAutocomplete;return r.jsx(w,u({multiple:!0,freeSolo:!0,options:c,id:s,value:p,onChange:h,onInputChange:b,label:l.current.getLocaleText("filterPanelInputLabel"),placeholder:l.current.getLocaleText("filterPanelInputPlaceholder"),slotProps:{textField:{type:o||"text",inputRef:i}}},a?.root))}var on=function(e){return e.Cell="cell",e.Row="row",e}(on||{}),ln=function(e){return e.Edit="edit",e.View="view",e}(ln||{}),an=function(e){return e.Edit="edit",e.View="view",e}(an||{}),sn=function(e){return e.And="and",e.Or="or",e}(sn||{});class cn{constructor(e){this.data=void 0,this.data=e.ids}has(e){return this.data.has(e)}select(e){this.data.add(e)}unselect(e){this.data.delete(e)}}class un{constructor(e){this.data=void 0,this.data=e.ids}has(e){return!this.data.has(e)}select(e){this.data.delete(e)}unselect(e){this.data.add(e)}}const dn=e=>"include"===e.type?new cn(e):new un(e);var pn=function(e){return e.enterKeyDown="enterKeyDown",e.cellDoubleClick="cellDoubleClick",e.printableKeyDown="printableKeyDown",e.deleteKeyDown="deleteKeyDown",e.pasteKeyDown="pasteKeyDown",e}(pn||{}),fn=function(e){return e.cellFocusOut="cellFocusOut",e.escapeKeyDown="escapeKeyDown",e.enterKeyDown="enterKeyDown",e.tabKeyDown="tabKeyDown",e.shiftTabKeyDown="shiftTabKeyDown",e}(fn||{}),gn=function(e){return e.enterKeyDown="enterKeyDown",e.cellDoubleClick="cellDoubleClick",e.printableKeyDown="printableKeyDown",e.deleteKeyDown="deleteKeyDown",e}(gn||{}),mn=function(e){return e.rowFocusOut="rowFocusOut",e.escapeKeyDown="escapeKeyDown",e.enterKeyDown="enterKeyDown",e.tabKeyDown="tabKeyDown",e.shiftTabKeyDown="shiftTabKeyDown",e}(mn||{});function hn(e){return void 0!==e.field}const bn={filteredRowsLookup:{},filteredChildrenCountLookup:{},filteredDescendantCountLookup:{}},wn=()=>({items:[],logicOperator:sn.And,quickFilterValues:[],quickFilterLogicOperator:sn.And});function Cn(e){return{current:e.current.getPublicApi()}}let vn=function(e){return e.LEFT="left",e.RIGHT="right",e}({});const yn={left:[],right:[]},xn=Wt((e=>e.isRtl)),Sn=Wt((e=>e.columns)),Rn=Gt(Sn,(e=>e.orderedFields)),In=Gt(Sn,(e=>e.lookup)),Mn=Ut(Rn,In,((e,t)=>e.map((e=>t[e])))),kn=Gt(Sn,(e=>e.columnVisibilityModel)),Pn=Gt(Sn,(e=>e.initialColumnVisibilityModel)),En=Ut(Mn,kn,((e,t)=>e.filter((e=>!1!==t[e.field])))),Fn=Ut(En,(e=>e.map((e=>e.field)))),Hn=Wt((e=>e.pinnedColumns)),Tn=Ut(Sn,Hn,Fn,xn,((e,t,r,n)=>{const o=function(e,t,r){if(!Array.isArray(e.left)&&!Array.isArray(e.right))return yn;if(0===e.left?.length&&0===e.right?.length)return yn;const n=(e,t)=>Array.isArray(e)?e.filter((e=>t.includes(e))):[],o=n(e.left,t),l=t.filter((e=>!o.includes(e))),i=n(e.right,l);return r?{left:i,right:o}:{left:o,right:i}}(t,r,n);return{left:o.left.map((t=>e.lookup[t])),right:o.right.map((t=>e.lookup[t]))}})),Dn=Ut(En,(e=>{const t=[];let r=0;for(let n=0;n<e.length;n+=1)t.push(r),r+=e[n].computedWidth;return t})),On=Ut(Mn,(e=>e.filter((e=>e.filterable)))),$n=Ut(Mn,(e=>e.reduce(((e,t)=>(t.filterable&&(e[t.field]=t),e)),{}))),jn=Ut(Mn,(e=>e.some((e=>void 0!==e.colSpan))));let Ln;const zn=(e,t)=>{const r=u({},e);if(null==r.id&&(r.id=Math.round(1e5*Math.random())),null==r.operator){const e=In(t)[r.field];r.operator=e&&e.filterOperators[0].value}return r},An=(e,t,r)=>{const n=e.items.length>1;let o;o=n&&t?[e.items[0]]:e.items;const l=n&&o.some((e=>null==e.id));return o.some((e=>null==e.operator))||l?u({},e,{items:o.map((e=>zn(e,r)))}):e.items!==o?u({},e,{items:o}):e},Bn=(e,t,r)=>n=>u({},n,{filterModel:An(e,t,r)}),Vn=e=>"string"==typeof e?e.normalize("NFD").replace(/[\u0300-\u036f]/g,""):e,Nn=(e,t)=>{if(!e.field||!e.operator)return null;const r=t.current.getColumn(e.field);if(!r)return null;let n;if(r.valueParser){const o=r.valueParser;n=Array.isArray(e.value)?e.value?.map((e=>o(e,void 0,r,t))):o(e.value,void 0,r,t)}else n=e.value;const{ignoreDiacritics:o}=t.current.rootProps;o&&(n=Vn(n));const l=u({},e,{value:n}),i=r.filterOperators;if(!i?.length)throw new Error(`MUI X: No filter operators found for column '${r.field}'.`);const a=i.find((e=>e.value===l.operator));if(!a)throw new Error(`MUI X: No filter operator found for column '${r.field}' and operator value '${l.operator}'.`);const s=Cn(t),c=a.getApplyFilterFn(l,r);return"function"!=typeof c?null:{item:l,fn:e=>{let n=t.current.getRowValue(e,r);return o&&(n=Vn(n)),c(n,e,r,s)}}};let Gn=1;const Wn=e=>e.quickFilterExcludeHiddenColumns??!0,Un=e=>null!=e,Kn=(e,t,r,n,o)=>{const l=((e,t,r)=>(e.cleanedFilterItems||(e.cleanedFilterItems=r.filter((e=>null!==Nn(e,t)))),e.cleanedFilterItems))(o,n,r.items),i=e.filter(Un),a=t.filter(Un);if(i.length>0){const e=e=>i.some((t=>t[e.id]));if((r.logicOperator??wn().logicOperator)===sn.And){if(!l.every(e))return!1}else if(!l.some(e))return!1}if(a.length>0&&null!=r.quickFilterValues){const e=e=>a.some((t=>t[e]));if((r.quickFilterLogicOperator??wn().quickFilterLogicOperator)===sn.And){if(!r.quickFilterValues.every(e))return!1}else if(!r.quickFilterValues.some(e))return!1}return!0},_n=(e,t)=>r=>{if(!r.value)return null;const n=e?r.value:r.value.trim(),o=new RegExp(Jr(n),"i");return e=>{if(null==e)return t;const r=o.test(String(e));return t?!r:r}},qn=(e,t)=>r=>{if(!r.value)return null;const n=e?r.value:r.value.trim(),o=new Intl.Collator(void 0,{sensitivity:"base",usage:"search"});return e=>{if(null==e)return t;const r=0===o.compare(n,e.toString());return t?!r:r}},Xn=e=>()=>t=>{const r=""===t||null==t;return e?!r:r},Qn={width:100,minWidth:50,maxWidth:1/0,hideable:!0,sortable:!0,resizable:!0,filterable:!0,groupable:!0,pinnable:!0,aggregable:!0,editable:!1,sortComparator:(e,t)=>{const r=Wr(e,t);return null!==r?r:"string"==typeof e?Ur.compare(e.toString(),t.toString()):e-t},type:"string",align:"left",filterOperators:((e=!1)=>[{value:"contains",getApplyFilterFn:_n(e,!1),InputComponent:Xr},{value:"doesNotContain",getApplyFilterFn:_n(e,!0),InputComponent:Xr},{value:"equals",getApplyFilterFn:qn(e,!1),InputComponent:Xr},{value:"doesNotEqual",getApplyFilterFn:qn(e,!0),InputComponent:Xr},{value:"startsWith",getApplyFilterFn:t=>{if(!t.value)return null;const r=e?t.value:t.value.trim(),n=new RegExp(`^${Jr(r)}.*$`,"i");return e=>null!=e&&n.test(e.toString())},InputComponent:Xr},{value:"endsWith",getApplyFilterFn:t=>{if(!t.value)return null;const r=e?t.value:t.value.trim(),n=new RegExp(`.*${Jr(r)}$`,"i");return e=>null!=e&&n.test(e.toString())},InputComponent:Xr},{value:"isEmpty",getApplyFilterFn:Xn(!1),requiresFilterValue:!1},{value:"isNotEmpty",getApplyFilterFn:Xn(!0),requiresFilterValue:!1},{value:"isAnyOf",getApplyFilterFn:t=>{if(!Array.isArray(t.value)||0===t.value.length)return null;const r=e?t.value:t.value.map((e=>e.trim())),n=new Intl.Collator(void 0,{sensitivity:"base",usage:"search"});return e=>null!=e&&r.some((t=>0===n.compare(t,e.toString()||"")))},InputComponent:nn}])(),renderEditCell:e=>r.jsx(Ir,u({},e)),getApplyQuickFilterFn:e=>{if(!e)return null;const t=new RegExp(Jr(e),"i");return(e,r,n,o)=>{let l=o.current.getRowFormattedValue(r,n);return o.current.ignoreDiacritics&&(l=Vn(l)),null!=l&&t.test(l.toString())}}},Yn=g.createContext(void 0),Jn=()=>{const e=g.useContext(Yn);if(void 0===e)throw new Error(["MUI X: Could not find the Data Grid configuration context.","It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.","This can also happen if you are bundling multiple versions of the Data Grid."].join("\n"));return e},Zn=g.createContext({className:"unset",tag:r.jsx("style",{href:"/unset"})});function eo(){return g.useContext(Zn).className}function to(e){const t=Jn(),n=pt(),o=t.hooks.useCSSVariables(),l=g.useMemo((()=>{const e=`MuiDataGridVariables-${o.id}`,t=`.${e}{${function(e){let t="";for(const r in e)Object.hasOwn(e,r)&&(t+=`${r}:${e[r]};`);return t}(o.variables)}}`;return{className:e,tag:r.jsx("style",{href:`/${e}`,nonce:n.nonce,children:t})}}),[n.nonce,o]);return r.jsx(Zn.Provider,{value:l,children:e.children})}const ro=["open","target","onClose","children","position","className","onExited"],no=R(br,{name:"MuiDataGrid",slot:"Menu"})({zIndex:yr.zIndex.menu,[`& .${ir.menuList}`]:{outline:0}});function oo(e){const{open:t,target:o,onClose:l,children:i,position:a,className:s,onExited:c}=e,p=F(e,ro),f=ut(),m=pt(),h=(e=>{const{classes:t}=e;return I({root:["menu"]},lr,t)})(m),b=eo(),w=g.useRef(null);return d((()=>{t?w.current=document.activeElement instanceof HTMLElement?document.activeElement:null:(w.current?.focus?.(),w.current=null)}),[t]),g.useEffect((()=>{const e=t?"menuOpen":"menuClose";f.current.publishEvent(e,{target:o})}),[f,t,o]),r.jsx(no,u({as:m.slots.basePopper,className:n(h.root,s,b),ownerState:m,open:t,target:o,transition:!0,placement:a,onClickAway:e=>{e.target&&(o===e.target||o?.contains(e.target))||l(e)},onExited:c,clickAwayMouseEvent:"onMouseDown"},p,m.slotProps?.basePopper,{children:i}))}const lo=["api","colDef","id","hasFocus","isEditable","field","value","formattedValue","row","rowNode","cellMode","tabIndex","position","focusElementRef"];function io(e){const{colDef:n,id:o,hasFocus:l,tabIndex:i,position:a="bottom-end",focusElementRef:s}=e,c=F(e,lo),[d,p]=g.useState(-1),[f,m]=g.useState(!1),h=ut(),b=g.useRef(null),w=g.useRef(null),C=g.useRef(!1),v=g.useRef({}),y=t(),x=P(),S=P(),R=pt();if(!(e=>"function"==typeof e.getActions)(n))throw new Error("MUI X: Missing the `getActions` property in the `GridColDef`.");const I=n.getActions(h.current.getRowParams(o)),M=I.filter((e=>!e.props.showInMenu)),k=I.filter((e=>e.props.showInMenu)),E=M.length+(k.length?1:0);g.useLayoutEffect((()=>{l||Object.entries(v.current).forEach((([e,t])=>{t?.stop({},(()=>{delete v.current[e]}))}))}),[l]),g.useEffect((()=>{d<0||!b.current||d>=b.current.children.length||b.current.children[d].focus({preventScroll:!0})}),[d]),g.useEffect((()=>{l||(p(-1),C.current=!1)}),[l]),g.useImperativeHandle(s,(()=>({focus(){if(!C.current){const e=I.findIndex((e=>!e.props.disabled));p(e)}}})),[I]),g.useEffect((()=>{d>=E&&p(E-1)}),[d,E]);const H=()=>{m(!1)},T=e=>t=>{v.current[e]=t},D=(e,t)=>r=>{p(e),C.current=!0,t&&t(r)};return r.jsxs("div",u({role:"menu",ref:b,tabIndex:-1,className:ir.actionsCell,onKeyDown:e=>{if(E<=1)return;const t=(e,r)=>{if(e<0||e>I.length)return e;const n=("left"===r?-1:1)*(y?-1:1);return I[e+n]?.props.disabled?t(e+n,r):e+n};let r=d;"ArrowRight"===e.key?r=t(d,"right"):"ArrowLeft"===e.key&&(r=t(d,"left")),r<0||r>=E||r!==d&&(e.preventDefault(),e.stopPropagation(),p(r))}},c,{children:[M.map(((e,t)=>g.cloneElement(e,{key:t,touchRippleRef:T(t),onClick:D(t,e.props.onClick),tabIndex:d===t?i:-1}))),k.length>0&&S&&r.jsx(R.slots.baseIconButton,u({ref:w,id:S,"aria-label":h.current.getLocaleText("actionsCellMore"),"aria-haspopup":"menu","aria-expanded":f,"aria-controls":f?x:void 0,role:"menuitem",size:"small",onClick:e=>{e.stopPropagation(),e.preventDefault(),f?H():(m(!0),p(E-1),C.current=!0)},touchRippleRef:T(S),tabIndex:d===M.length?i:-1},R.slotProps?.baseIconButton,{children:r.jsx(R.slots.moreActionsIcon,{fontSize:"small"})})),k.length>0&&r.jsx(oo,{open:f,target:w.current,position:a,onClose:H,children:r.jsx(R.slots.baseMenuList,{id:x,className:ir.menuList,onKeyDown:e=>{"Tab"===e.key&&e.preventDefault(),["Tab","Escape"].includes(e.key)&&H()},"aria-labelledby":S,autoFocusItem:!0,children:k.map(((e,t)=>g.cloneElement(e,{key:t,closeMenu:H})))})})]}))}const ao="actions",so=u({},Qn,{sortable:!1,filterable:!1,aggregable:!1,width:100,display:"flex",align:"center",headerAlign:"center",headerName:"",disableColumnMenu:!0,disableExport:!0,renderCell:e=>r.jsx(io,u({},e)),getApplyQuickFilterFn:()=>null}),co="auto-generated-group-node-root",uo=Symbol("mui.id_autogenerated"),po=(e,t,r)=>{const n=t?t(e):e.id;return function(e,t,r="A row was provided without id in the rows prop:"){if(null==e)throw new Error(["MUI X: The Data Grid component requires all rows to have a unique `id` property.","Alternatively, you can use the `getRowId` prop to specify a custom id for each row.",r,JSON.stringify(t)].join("\n"))}(n,e,r),n},fo=({rows:e,getRowId:t,loading:r,rowCount:n})=>{const o={type:"full",rows:[]},l={};for(let i=0;i<e.length;i+=1){const r=e[i],n=po(r,t);l[n]=r,o.rows.push(n)}return{rowsBeforePartialUpdates:e,loadingPropBeforePartialUpdates:r,rowCountPropBeforePartialUpdates:n,updates:o,dataRowIdToModelLookup:l}},go=({tree:e,rowCountProp:t=0})=>{const r=e[co];return Math.max(t,r.children.length+(null==r.footerId?0:1))},mo=({apiRef:e,rowCountProp:t=0,loadingProp:r,previousTree:n,previousTreeDepths:o,previousGroupsToFetch:l})=>{const i=e.current.caches.rows,{tree:a,treeDepths:s,dataRowIds:c,groupingName:d,groupsToFetch:p=[]}=e.current.applyStrategyProcessor("rowTreeCreation",{previousTree:n,previousTreeDepths:o,updates:i.updates,dataRowIdToModelLookup:i.dataRowIdToModelLookup,previousGroupsToFetch:l}),f=e.current.unstable_applyPipeProcessors("hydrateRows",{tree:a,treeDepths:s,dataRowIds:c,dataRowIdToModelLookup:i.dataRowIdToModelLookup});return e.current.caches.rows.updates={type:"partial",actions:{insert:[],modify:[],remove:[]},idToActionLookup:{}},u({},f,{totalRowCount:Math.max(t,f.dataRowIds.length),totalTopLevelRowCount:go({tree:f.tree,rowCountProp:t}),groupingName:d,loading:r,groupsToFetch:p})},ho=e=>"skeletonRow"===e.type||"footer"===e.type||"group"===e.type&&e.isAutoGenerated||"pinnedRow"===e.type&&e.isAutoGenerated,bo=(e,t,r)=>{const n=e[t];if("group"!==n.type)return[];const o=[];for(let l=0;l<n.children.length;l+=1){const t=n.children[l];r&&ho(e[t])||o.push(t);const i=bo(e,t,r);for(let e=0;e<i.length;e+=1)o.push(i[e])}return r||null==n.footerId||o.push(n.footerId),o},wo=({previousCache:e,getRowId:t,updates:r,groupKeys:n})=>{if("full"===e.updates.type)throw new Error("MUI X: Unable to prepare a partial update if a full update is not applied yet.");const o=new Map;r.forEach((e=>{const r=po(e,t,"A row was provided without id when calling updateRows():");o.has(r)?o.set(r,u({},o.get(r),e)):o.set(r,e)}));const l={type:"partial",actions:{insert:[...e.updates.actions.insert??[]],modify:[...e.updates.actions.modify??[]],remove:[...e.updates.actions.remove??[]]},idToActionLookup:u({},e.updates.idToActionLookup),groupKeys:n},i=u({},e.dataRowIdToModelLookup),a={insert:{},modify:{},remove:{}};o.forEach(((e,t)=>{const r=l.idToActionLookup[t];if("delete"===e._action){if("remove"===r||!i[t])return;return null!=r&&(a[r][t]=!0),l.actions.remove.push(t),void delete i[t]}const n=i[t];if(n)return"remove"===r?(a.remove[t]=!0,l.actions.modify.push(t)):null==r&&l.actions.modify.push(t),void(i[t]=u({},n,e));"remove"===r?(a.remove[t]=!0,l.actions.insert.push(t)):null==r&&l.actions.insert.push(t),i[t]=e}));const s=Object.keys(a);for(let c=0;c<s.length;c+=1){const e=s[c],t=a[e];Object.keys(t).length>0&&(l.actions[e]=l.actions[e].filter((e=>!t[e])))}return{dataRowIdToModelLookup:i,updates:l,rowsBeforePartialUpdates:e.rowsBeforePartialUpdates,loadingPropBeforePartialUpdates:e.loadingPropBeforePartialUpdates,rowCountPropBeforePartialUpdates:e.rowCountPropBeforePartialUpdates}},Co="var(--DataGrid-overlayHeight, calc(var(--height) * 2))";function vo(e,t,r){const n=[];return t.forEach((t=>{const o=po(t,r,"A row was provided without id when calling updateRows():"),l=Tr(e,o);if("pinnedRow"===l?.type){const r=e.current.caches.pinnedRows,n=r.idLookup[o];n&&(r.idLookup[o]=u({},n,t))}else n.push(t)})),n}const yo=(e,t,r)=>"number"==typeof e&&e>0?e:t,xo="__row_group_by_columns_group__",So="__detail_panel_toggle__";let Ro=function(e){return e[e.NONE=0]="NONE",e[e.LEFT=1]="LEFT",e[e.RIGHT=2]="RIGHT",e[e.VIRTUAL=3]="VIRTUAL",e}({});const Io=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","hasFocus","tabIndex","hideDescendantCount"];function Mo(e){const{value:t,rowNode:n}=e,o=F(e,Io),l=ut(),i=pt(),a=(e=>{const{classes:t}=e;return I({root:["booleanCell"]},lr,t)})({classes:i.classes}),s=zt(l,jr)>0&&"group"===n.type&&!1===i.treeData,c=g.useMemo((()=>t?i.slots.booleanCellTrueIcon:i.slots.booleanCellFalseIcon),[i.slots.booleanCellFalseIcon,i.slots.booleanCellTrueIcon,t]);return s&&void 0===t?null:r.jsx(c,u({fontSize:"small",className:a.root,titleAccess:l.current.getLocaleText(t?"booleanCellTrueLabel":"booleanCellFalseLabel"),"data-value":Boolean(t)},o))}const ko=g.memo(Mo),Po=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","className","hasFocus","isValidating","isProcessingProps","error","onValueChange"];function Eo(e){const{id:t,value:o,field:l,className:i,hasFocus:a,onValueChange:s}=e,c=F(e,Po),p=ut(),f=g.useRef(null),m=P(),[h,b]=g.useState(o),w=pt(),C=(e=>{const{classes:t}=e;return I({root:["editBooleanCell"]},lr,t)})({classes:w.classes}),v=g.useCallback((async e=>{const r=e.target.checked;s&&await s(e,r),b(r),await p.current.setEditCellValue({id:t,field:l,value:r},e)}),[p,l,t,s]);return g.useEffect((()=>{b(o)}),[o]),d((()=>{a&&f.current.focus()}),[a]),r.jsx("label",u({htmlFor:m,className:n(C.root,i)},c,{children:r.jsx(w.slots.baseCheckbox,u({id:m,inputRef:f,checked:Boolean(h),onChange:v,size:"small"},w.slotProps?.baseCheckbox))}))}const Fo=["item","applyValue","apiRef","focusElementRef","isFilterActive","headerFilterMenu","clearButton","tabIndex","slotProps"];function Ho(e){return"true"===String(e).toLowerCase()||"false"!==String(e).toLowerCase()&&void 0}const To=u({},Qn,{type:"boolean",display:"flex",align:"center",headerAlign:"center",renderCell:e=>e.field!==xo&&ho(e.rowNode)?"":r.jsx(ko,u({},e)),renderEditCell:e=>r.jsx(Eo,u({},e)),sortComparator:Kr,valueFormatter:(e,t,r,n)=>e?n.current.getLocaleText("booleanCellTrueLabel"):n.current.getLocaleText("booleanCellFalseLabel"),filterOperators:[{value:"is",getApplyFilterFn:e=>{const t=Ho(e.value);return void 0===t?null:e=>Boolean(e)===t},InputComponent:function(e){const{item:t,applyValue:n,apiRef:o,focusElementRef:l,headerFilterMenu:i,clearButton:a,tabIndex:s,slotProps:c}=e,d=F(e,Fo),[p,f]=g.useState(Ho(t.value)),m=pt(),h=P(),b=P(),w=m.slotProps?.baseSelect||{},C=w.native??!1,v=m.slotProps?.baseSelectOption||{},y=g.useCallback((e=>{const r=Ho(e.target.value);f(r),n(u({},t,{value:r}))}),[n,t]);g.useEffect((()=>{f(Ho(t.value))}),[t.value]);const x=c?.root.label??o.current.getLocaleText("filterPanelInputLabel"),S=c?.root.slotProps;return r.jsxs(g.Fragment,{children:[r.jsxs(m.slots.baseSelect,u({fullWidth:!0,labelId:h,id:b,label:x,value:void 0===p?"":String(p),onChange:y,native:C,slotProps:{htmlInput:u({ref:l,tabIndex:s},S?.htmlInput)}},w,d,c?.root,{children:[r.jsx(m.slots.baseSelectOption,u({},v,{native:C,value:"",children:o.current.getLocaleText("filterValueAny")})),r.jsx(m.slots.baseSelectOption,u({},v,{native:C,value:"true",children:o.current.getLocaleText("filterValueTrue")})),r.jsx(m.slots.baseSelectOption,u({},v,{native:C,value:"false",children:o.current.getLocaleText("filterValueFalse")}))]})),i,a]})}}],getApplyQuickFilterFn:()=>null,aggregable:!1,pastedValueParser:e=>(e=>{switch(e.toLowerCase().trim()){case"true":case"yes":case"1":return!0;case"false":case"no":case"0":case"null":case"undefined":return!1;default:return}})(e)});function Do(e){for(const t in e)return!1;return!0}const Oo=Wt((e=>e.sorting)),$o=Gt(Oo,(e=>e.sortedRows)),jo=Ut($o,Fr,Hr,((e,t,r)=>e.reduce(((e,n)=>{const o=t[n];if(o)e.push({id:n,model:o});else{const t=r[n];t&&ho(t)&&e.push({id:n,model:{[uo]:n}})}return e}),[]))),Lo=Gt(Oo,(e=>e.sortModel)),zo=Ut(Lo,(e=>e.reduce(((t,r,n)=>(t[r.field]={sortDirection:r.sort,sortIndex:e.length>1?n+1:void 0},t)),{})));Ut($o,(e=>e.reduce(((e,t,r)=>(e[t]=r,e)),Object.create(null))));const Ao=Wt((e=>e.filter)),Bo=Gt(Ao,(e=>e.filterModel)),Vo=Gt(Bo,(e=>e.quickFilterValues)),No=Wt((e=>e.visibleRowsLookup)),Go=Gt(Ao,(e=>e.filteredRowsLookup));Gt(Ao,(e=>e.filteredChildrenCountLookup)),Gt(Ao,(e=>e.filteredDescendantCountLookup));const Wo=Ut(No,jo,((e,t)=>Do(e)?t:t.filter((t=>!1!==e[t.id])))),Uo=Ut(Wo,(e=>e.map((e=>e.id)))),Ko=Ut(Go,jo,((e,t)=>Do(e)?t:t.filter((t=>!1!==e[t.id])))),_o=Ut(Ko,(e=>e.map((e=>e.id))));Ut(Uo,Hr,((e,t)=>{const r={};let n=0;return e.reduce(((e,o)=>{const l=t[o];return r[l.depth]||(r[l.depth]=0),l.depth>n&&(r[l.depth]=0),n=l.depth,r[l.depth]+=1,e[o]=r[l.depth],e}),{})}));const qo=Ut(Wo,Hr,jr,((e,t,r)=>r<2?e:e.filter((e=>0===t[e.id]?.depth)))),Xo=Gt(Wo,(e=>e.length)),Qo=Gt(qo,(e=>e.length)),Yo=Gt(Ko,(e=>e.length));Gt(Yo,Qo,((e,t)=>e-t));const Jo=Ut(Bo,In,((e,t)=>e.items?.filter((e=>{if(!e.field)return!1;const r=t[e.field];if(!r?.filterOperators||0===r?.filterOperators?.length)return!1;const n=r.filterOperators.find((t=>t.value===e.operator));return!!n&&(!n.InputComponent||null!=e.value&&""!==e.value?.toString())})))),Zo=Ut(Jo,(e=>e.reduce(((e,t)=>(e[t.field]?e[t.field].push(t):e[t.field]=[t],e)),{}))),el=Wt((e=>e.rowSelection)),tl=Ut(el,dn),rl=Gt(el,Yo,((e,t)=>"include"===e.type?e.ids.size:t-e.ids.size)),nl=Ut(el,Fr,Lr,((e,t,r)=>{const n=new Map;if("include"===e.type)for(const o of e.ids)n.set(o,t[o]);else for(let o=0;o<r.length;o+=1){const l=r[o];e.ids.has(l)||n.set(l,t[l])}return n}));function ol(e,t){const r=Hr(e),n=$o(e),o=Go(e),l=r[t];if(!l||"group"!==l.type)return[];const i=[];for(let a=n.findIndex((e=>e===t))+1;a<n.length&&r[n[a]]?.depth>l.depth;a+=1){const t=n[a];!1!==o[t]&&e.current.isRowSelectable(t)&&i.push(t)}return i}function ll(e){return e.signature===ar.DataGrid?e.checkboxSelection&&!0!==e.disableMultipleRowSelection:!e.disableMultipleRowSelection}const il=(e,t,r,n,o,l,i=tl(e))=>{const a=Go(e),s=new Set([]);if((n||o)&&!1!==a[r]){if(n){const n=t[r];"group"===n?.type&&ol(e,r).forEach((e=>{l(e),s.add(e)}))}if(o){const n=e=>{if(!i.has(e)&&!s.has(e))return!1;const r=t[e];return!!r&&("group"!==r.type||r.children.every(n))},o=r=>{const i=((e,t,r)=>{const n=e[r];if(!n)return[];const o=n.parent;return null==o?[]:e[o].children.filter((e=>e!==r&&!1!==t[e]))})(t,a,r);if(0===i.length||i.every(n)){const n=t[r],i=n?.parent;null!=i&&i!==co&&e.current.isRowSelectable(i)&&(l(i),s.add(i),o(i))}};o(r)}}},al=(e,t,r,n,o,l)=>{const i=tl(e);if(o||n){if(o){const e=((e,t)=>{const r=[];let n=t;for(;null!=n&&n!==co;){const t=e[n];if(!t)return r;r.push(n),n=t.parent}return r})(t,r);e.forEach((e=>{i.has(e)&&l(e)}))}if(n){const n=t[r];"group"===n?.type&&ol(e,r).forEach((e=>{l(e)}))}}},sl=["field","id","formattedValue","row","rowNode","colDef","isEditable","cellMode","hasFocus","tabIndex","api"],cl=gt((function(e,t){const{field:n,id:o,rowNode:l,tabIndex:i}=e,a=F(e,sl),s=ut(),c=pt(),d=(e=>{const{classes:t}=e;return I({root:["checkboxInput"]},lr,t)})({classes:c.classes});g.useLayoutEffect((()=>{if(0===i){const e=s.current.getCellElement(o,n);e&&(e.tabIndex=-1)}}),[s,i,o,n]);const p=g.useCallback((e=>{" "===e.key&&e.stopPropagation()}),[]),f=s.current.isRowSelectable(o),m=(h=o,b=c.rowSelectionPropagation?.parents??!1,Gt(Hr,$o,Go,tl,((e,t,r,n)=>{const o=e[h];if(!o||"group"!==o.type)return{isIndeterminate:!1,isChecked:n.has(h)};if(n.has(h))return{isIndeterminate:!1,isChecked:!0};let l=0,i=0;for(let a=t.findIndex((e=>e===h))+1;a<t.length&&e[t[a]]?.depth>o.depth;a+=1){const e=t[a];!1!==r[e]&&(l+=1,n.has(e)&&(i+=1))}return{isIndeterminate:i>0&&(i<l||!n.has(h)),isChecked:b?i>0:n.has(h)}})));var h,b;const{isIndeterminate:w,isChecked:C}=zt(s,m,void 0,Tt);if("footer"===l.type||"pinnedRow"===l.type)return null;const v=s.current.getLocaleText(C&&!w?"checkboxSelectionUnselectRow":"checkboxSelectionSelectRow");return r.jsx(c.slots.baseCheckbox,u({tabIndex:i,checked:C&&!w,onChange:e=>{const t={value:e.target.checked,id:o};s.current.publishEvent("rowSelectionCheckboxChange",t,e)},className:d.root,slotProps:{htmlInput:{"aria-label":v,name:"select_row"}},onKeyDown:p,indeterminate:w,disabled:!f},c.slotProps?.baseCheckbox,a,{ref:t}))})),ul=cl,dl=Wt((e=>e.focus)),pl=Gt(dl,(e=>e.cell)),fl=Gt(dl,(e=>e.columnHeader));Gt(dl,(e=>e.columnHeaderFilter));const gl=Gt(dl,(e=>e.columnGroupHeader)),ml=Wt((e=>e.tabIndex)),hl=Gt(ml,(e=>e.cell)),bl=Gt(ml,(e=>e.columnHeader));Gt(ml,(e=>e.columnHeaderFilter));const wl=Gt(ml,(e=>e.columnGroupHeader)),Cl=(e,t,r)=>t>0&&e>0?Math.ceil(e/t):-1===e?r+2:0,vl=e=>({page:0,pageSize:e?0:100}),yl=(e,t)=>{if(t===ar.DataGrid&&e>100)throw new Error(["MUI X: `pageSize` cannot exceed 100 in the MIT version of the DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature."].join("\n"))},xl=Wt((e=>e.pagination)),Sl=Gt(xl,(e=>e.enabled&&"client"===e.paginationMode)),Rl=Gt(xl,(e=>e.paginationModel)),Il=Gt(xl,(e=>e.rowCount)),Ml=Gt(xl,(e=>e.meta)),kl=Gt(Rl,(e=>e.page)),Pl=Gt(Rl,(e=>e.pageSize)),El=Gt(Rl,Il,((e,t)=>Cl(t,e.pageSize,e.page))),Fl=Ut(Sl,Rl,Hr,jr,Wo,qo,((e,t,r,n,o,l)=>{if(!e)return null;const i=l.length,a=Math.min(t.pageSize*t.page,i-1),s=-1===t.pageSize?i-1:Math.min(a+t.pageSize-1,i-1);if(-1===a||-1===s)return null;if(n<2)return{firstRowIndex:a,lastRowIndex:s};const c=l[a],u=s-a+1,d=o.findIndex((e=>e.id===c.id));let p=d,f=0;for(;p<o.length&&f<=u;){const e=o[p],t=r[e.id]?.depth;void 0===t?p+=1:((f<u||t>0)&&(p+=1),0===t&&(f+=1))}return{firstRowIndex:d,lastRowIndex:p-1}})),Hl=Ut(Wo,Fl,((e,t)=>t?e.slice(t.firstRowIndex,t.lastRowIndex+1):[])),Tl=Ut(Uo,Fl,((e,t)=>t?e.slice(t.firstRowIndex,t.lastRowIndex+1):[])),Dl=Ut(Sl,Fl,Hl,Wo,((e,t,r,n)=>e?{rows:r,range:t,rowIdToIndexMap:r.reduce(((e,t,r)=>(e.set(t.id,r),e)),new Map)}:{rows:n,range:0===n.length?null:{firstRowIndex:0,lastRowIndex:n.length-1},rowIdToIndexMap:n.reduce(((e,t,r)=>(e.set(t.id,r),e)),new Map)})),Ol=["field","colDef"],$l=gt((function(e,t){const n=F(e,Ol),[,o]=g.useState(!1),l=ut(),i=pt(),a=(e=>{const{classes:t}=e;return I({root:["checkboxInput"]},lr,t)})({classes:i.classes}),s=zt(l,bl),c=zt(l,el),d=zt(l,Uo),p=zt(l,Tl),f=g.useMemo((()=>{const e=i.isRowSelectable;if("function"!=typeof e)return c;if("exclude"===c.type)return c;const t={type:"include",ids:new Set};for(const r of c.ids)i.keepNonExistentRowsSelected&&t.ids.add(r),l.current.getRow(r)&&e(l.current.getRowParams(r))&&t.ids.add(r);return t}),[l,i.isRowSelectable,i.keepNonExistentRowsSelected,c]),m=g.useMemo((()=>{const e=i.pagination&&i.checkboxSelectionVisibleOnly&&"server"!==i.paginationMode?p:d,t=new Set;for(let r=0;r<e.length;r+=1){const n=e[r];l.current.getRow(n)&&l.current.isRowSelectable(n)&&t.add(n)}return t}),[l,i.pagination,i.paginationMode,i.checkboxSelectionVisibleOnly,p,d]),h=g.useMemo((()=>{const e=dn(f);let t=0;for(const r of m)e.has(r)&&(t+=1);return t}),[f,m]),b=g.useMemo((()=>{if(0===f.ids.size)return!1;const e=dn(f);for(const t of m)if(!e.has(t))return!0;return!1}),[f,m]),w=h>0,C=null!==s&&s.field===e.field?0:-1;g.useLayoutEffect((()=>{const t=l.current.getColumnHeaderElement(e.field);0===C&&t&&(t.tabIndex=-1)}),[C,l,e.field]);const v=g.useCallback((e=>{" "===e.key&&l.current.publishEvent("headerSelectionCheckboxChange",{value:!w})}),[l,w]),y=g.useCallback((()=>{o((e=>!e))}),[]);g.useEffect((()=>l.current.subscribeEvent("rowSelectionChange",y)),[l,y]);const x=l.current.getLocaleText(w&&!b?"checkboxSelectionUnselectAllRows":"checkboxSelectionSelectAllRows");return r.jsx(i.slots.baseCheckbox,u({indeterminate:b,checked:w&&!b,onChange:e=>{const t={value:e.target.checked};l.current.publishEvent("headerSelectionCheckboxChange",t)},className:a.root,slotProps:{htmlInput:{"aria-label":x,name:"select_all_rows"}},tabIndex:C,onKeyDown:v,disabled:!ll(i)},i.slotProps?.baseCheckbox,n,{ref:t}))})),jl=Wt(((e,t)=>uo in t?t[uo]:e.props.getRowId?e.props.getRowId(t):t.id)),Ll="__check__",zl=u({},To,{type:"custom",field:Ll,width:50,resizable:!1,sortable:!1,filterable:!1,aggregable:!1,disableColumnMenu:!0,disableReorder:!0,disableExport:!0,getApplyQuickFilterFn:()=>null,display:"flex",valueGetter:(e,t,r,n)=>{const o=jl(n,t);return n.current.isRowSelected(o)},renderHeader:e=>r.jsx($l,u({},e)),renderCell:e=>r.jsx(ul,u({},e))}),Al=["item","applyValue","type","apiRef","focusElementRef","slotProps","isFilterActive","headerFilterMenu","clearButton","tabIndex","disabled"];function Bl(e,t){if(null==e)return"";const r=new Date(e);return Number.isNaN(r.getTime())?"":"date"===t?r.toISOString().substring(0,10):"datetime-local"===t?(r.setMinutes(r.getMinutes()-r.getTimezoneOffset()),r.toISOString().substring(0,19)):r.toISOString().substring(0,10)}function Vl(e){const{item:t,applyValue:n,type:o,apiRef:l,focusElementRef:i,slotProps:a,headerFilterMenu:s,clearButton:c,tabIndex:d,disabled:p}=e,f=F(e,Al),m=a?.root.slotProps,h=H(),[b,w]=g.useState((()=>Bl(t.value,o))),[C,v]=g.useState(!1),y=P(),x=pt(),S=g.useCallback((e=>{h.clear();const r=e.target.value;w(r),v(!0),h.start(x.filterDebounceMs,(()=>{const e=new Date(r);n(u({},t,{value:Number.isNaN(e.getTime())?void 0:e})),v(!1)}))}),[n,t,x.filterDebounceMs,h]);return g.useEffect((()=>{const e=Bl(t.value,o);w(e)}),[t.value,o]),r.jsxs(g.Fragment,{children:[r.jsx(x.slots.baseTextField,u({fullWidth:!0,id:y,label:l.current.getLocaleText("filterPanelInputLabel"),placeholder:l.current.getLocaleText("filterPanelInputPlaceholder"),value:b,onChange:S,type:o||"text",disabled:p,inputRef:i,slotProps:u({},m,{input:u({endAdornment:C?r.jsx(x.slots.loadIcon,{fontSize:"small",color:"action"}):null},m?.input),htmlInput:u({max:"datetime-local"===o?"9999-12-31T23:59":"9999-12-31",tabIndex:d},m?.htmlInput)})},x.slotProps?.baseTextField,f,a?.root)),s,c]})}function Nl(e,t,r,n){if(!e.value)return null;const o=new Date(e.value);r?o.setSeconds(0,0):(o.setMinutes(o.getMinutes()+o.getTimezoneOffset()),o.setHours(0,0,0,0));const l=o.getTime();return e=>{if(!e)return!1;if(n)return t(e.getTime(),l);const o=new Date(e);return r?o.setSeconds(0,0):o.setHours(0,0,0,0),t(o.getTime(),l)}}const Gl=e=>[{value:"is",getApplyFilterFn:t=>Nl(t,((e,t)=>e===t),e),InputComponent:Vl,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"not",getApplyFilterFn:t=>Nl(t,((e,t)=>e!==t),e),InputComponent:Vl,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"after",getApplyFilterFn:t=>Nl(t,((e,t)=>e>t),e),InputComponent:Vl,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"onOrAfter",getApplyFilterFn:t=>Nl(t,((e,t)=>e>=t),e),InputComponent:Vl,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"before",getApplyFilterFn:t=>Nl(t,((e,t)=>e<t),e,!e),InputComponent:Vl,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"onOrBefore",getApplyFilterFn:t=>Nl(t,((e,t)=>e<=t),e),InputComponent:Vl,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"isEmpty",getApplyFilterFn:()=>e=>null==e,requiresFilterValue:!1},{value:"isNotEmpty",getApplyFilterFn:()=>e=>null!=e,requiresFilterValue:!1}],Wl=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","hasFocus","inputProps","isValidating","isProcessingProps","onValueChange","slotProps"],Ul=R(br)({fontSize:"inherit"});function Kl(e){const{id:t,value:n,field:o,colDef:l,hasFocus:i,onValueChange:a,slotProps:s}=e,c=F(e,Wl),p="dateTime"===l.type,f=ut(),m=g.useRef(null),h=g.useMemo((()=>{let e,t;return e=null==n?null:n instanceof Date?n:new Date((n??"").toString()),t=null==e||Number.isNaN(e.getTime())?"":new Date(e.getTime()-6e4*e.getTimezoneOffset()).toISOString().substr(0,p?16:10),{parsed:e,formatted:t}}),[n,p]),[b,w]=g.useState(h),C=pt(),v=(e=>{const{classes:t}=e;return I({root:["editInputCell"]},lr,t)})({classes:C.classes}),y=g.useCallback((e=>{if(""===e)return null;const[t,r]=e.split("T"),[n,o,l]=t.split("-"),i=new Date;if(i.setFullYear(Number(n),Number(o)-1,Number(l)),i.setHours(0,0,0,0),r){const[e,t]=r.split(":");i.setHours(Number(e),Number(t),0,0)}return i}),[]),x=g.useCallback((async e=>{const r=e.target.value,n=y(r);a&&await a(e,n),w({parsed:n,formatted:r}),f.current.setEditCellValue({id:t,field:o,value:n},e)}),[f,o,t,a,y]);return g.useEffect((()=>{w((e=>h.parsed!==e.parsed&&h.parsed?.getTime()!==e.parsed?.getTime()?h:e))}),[h]),d((()=>{i&&m.current.focus()}),[i]),r.jsx(Ul,u({as:C.slots.baseInput,inputRef:m,fullWidth:!0,className:v.root,type:p?"datetime-local":"date",value:b.formatted,onChange:x},c,s?.root,{slotProps:{htmlInput:u({max:p?"9999-12-31T23:59":"9999-12-31"},s?.root?.slotProps?.htmlInput)}}))}const _l=e=>r.jsx(Kl,u({},e));function ql({value:e,columnType:t,rowId:r,field:n}){if(!(e instanceof Date))throw new Error([`MUI X: \`${t}\` column type only accepts \`Date\` objects as values.`,"Use `valueGetter` to transform the value into a `Date` object.",`Row ID: ${r}, field: "${n}".`].join("\n"))}const Xl=u({},Qn,{type:"date",sortComparator:_r,valueFormatter:(e,t,r,n)=>e?(ql({value:e,columnType:"date",rowId:jl(n,t),field:r.field}),e.toLocaleDateString()):"",filterOperators:Gl(),renderEditCell:_l,pastedValueParser:e=>new Date(e)}),Ql=u({},Qn,{type:"dateTime",sortComparator:_r,valueFormatter:(e,t,r,n)=>e?(ql({value:e,columnType:"dateTime",rowId:jl(n,t),field:r.field}),e.toLocaleString()):"",filterOperators:Gl(!0),renderEditCell:_l,pastedValueParser:e=>new Date(e)}),Yl=e=>null==e?null:Number(e),Jl=u({},Qn,{type:"number",align:"right",headerAlign:"right",sortComparator:Kr,valueParser:e=>""===e?null:Number(e),valueFormatter:e=>function(e){return"number"==typeof e&&!Number.isNaN(e)}(e)?e.toLocaleString():e||"",filterOperators:[{value:"=",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>Yl(t)===e.value,InputComponent:Xr,InputComponentProps:{type:"number"}},{value:"!=",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>Yl(t)!==e.value,InputComponent:Xr,InputComponentProps:{type:"number"}},{value:">",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>null!=t&&Yl(t)>e.value,InputComponent:Xr,InputComponentProps:{type:"number"}},{value:">=",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>null!=t&&Yl(t)>=e.value,InputComponent:Xr,InputComponentProps:{type:"number"}},{value:"<",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>null!=t&&Yl(t)<e.value,InputComponent:Xr,InputComponentProps:{type:"number"}},{value:"<=",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>null!=t&&Yl(t)<=e.value,InputComponent:Xr,InputComponentProps:{type:"number"}},{value:"isEmpty",getApplyFilterFn:()=>e=>null==e,requiresFilterValue:!1},{value:"isNotEmpty",getApplyFilterFn:()=>e=>null!=e,requiresFilterValue:!1},{value:"isAnyOf",getApplyFilterFn:e=>Array.isArray(e.value)&&0!==e.value.length?t=>null!=t&&e.value.includes(Number(t)):null,InputComponent:nn,InputComponentProps:{type:"number"}}],getApplyQuickFilterFn:e=>null==e||Number.isNaN(e)||""===e?null:t=>Yl(t)===Yl(e)});function Zl(e){return"singleSelect"===e?.type}function ei(e,t){if(e)return"function"==typeof e.valueOptions?e.valueOptions(u({field:e.field},t)):e.valueOptions}function ti(e,t,r){if(void 0===t)return;const n=t.find((t=>{const n=r(t);return String(n)===String(e)}));return r(n)}const ri=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","className","hasFocus","isValidating","isProcessingProps","error","onValueChange","initialOpen","slotProps"];function ni(e){const t=pt(),{id:n,value:o,field:l,row:i,colDef:a,hasFocus:s,error:c,onValueChange:p,initialOpen:f=t.editMode===on.Cell,slotProps:m}=e,h=F(e,ri),b=ut(),w=g.useRef(null),C=g.useRef(null),[v,y]=g.useState(f),x=(t.slotProps?.baseSelect||{}).native??!1;if(d((()=>{s&&C.current?.focus()}),[s]),!Zl(a))return null;const S=ei(a,{id:n,row:i});if(!S)return null;const R=a.getOptionValue,I=a.getOptionLabel;return S&&a?r.jsx(t.slots.baseSelect,u({ref:w,value:o,onChange:async e=>{if(!Zl(a)||!S)return;y(!1);const t=ti(e.target.value,S,R);p&&await p(e,t),await b.current.setEditCellValue({id:n,field:l,value:t},e)},open:v,onOpen:e=>{(function(e){return!!e.key})(e)&&"Enter"===e.key||y(!0)},onClose:(e,r)=>{if(t.editMode!==on.Row){if("backdropClick"===r||"Escape"===e.key){const t=b.current.getCellParams(n,l);b.current.publishEvent("cellEditStop",u({},t,{reason:"Escape"===e.key?fn.escapeKeyDown:fn.cellFocusOut}))}}else y(!1)},error:c,native:x,fullWidth:!0,slotProps:{htmlInput:{ref:C}}},h,m?.root,t.slotProps?.baseSelect,{children:S.map((e=>{const r=R(e);return g.createElement(t.slots.baseSelectOption,u({},t.slotProps?.baseSelectOption||{},{native:x,key:r,value:r}),I(e))}))})):null}const oi=["item","applyValue","type","apiRef","focusElementRef","tabIndex","isFilterActive","clearButton","headerFilterMenu","slotProps"],li=({column:e,OptionComponent:t,getOptionLabel:r,getOptionValue:n,isSelectNative:o,baseSelectOptionProps:l})=>["",...ei(e)||[]].map((e=>{const i=n(e);let a=r(e);return""===a&&(a=" "),g.createElement(t,u({},l,{native:o,key:i,value:i}),a)}));function ii(e){const{item:t,applyValue:n,type:o,apiRef:l,focusElementRef:i,tabIndex:a,clearButton:s,headerFilterMenu:c,slotProps:d}=e,p=F(e,oi),f=t.value??"",m=P(),h=P(),b=pt(),w=b.slotProps?.baseSelect?.native??!1;let C=null;if(t.field){const e=l.current.getColumn(t.field);Zl(e)&&(C=e)}const v=C?.getOptionValue,y=C?.getOptionLabel,x=g.useMemo((()=>ei(C)),[C]),S=g.useCallback((e=>{let r=e.target.value;r=ti(r,x,v),n(u({},t,{value:r}))}),[x,v,n,t]);if(!Zl(C))return null;const R=d?.root.label??l.current.getLocaleText("filterPanelInputLabel");return r.jsxs(g.Fragment,{children:[r.jsx(b.slots.baseSelect,u({fullWidth:!0,id:m,label:R,labelId:h,value:f,onChange:S,slotProps:{htmlInput:u({tabIndex:a,ref:i,type:o||"text",placeholder:d?.root.placeholder??l.current.getLocaleText("filterPanelInputPlaceholder")},d?.root.slotProps?.htmlInput)},native:w},b.slotProps?.baseSelect,p,d?.root,{children:li({column:C,OptionComponent:b.slots.baseSelectOption,getOptionLabel:y,getOptionValue:v,isSelectNative:w,baseSelectOptionProps:b.slotProps?.baseSelectOption})})),c,s]})}const ai=["item","applyValue","type","apiRef","focusElementRef","slotProps"],si=e=>null!=e&&Yr(e)?e.value:e,ci=u({},Qn,{type:"singleSelect",getOptionLabel:e=>Yr(e)?e.label:String(e),getOptionValue:e=>Yr(e)?e.value:e,valueFormatter(e,t,r,n){const o=jl(n,t);if(!Zl(r))return"";const l=ei(r,{id:o,row:t});if(null==e)return"";if(!l)return e;if("object"!=typeof l[0])return r.getOptionLabel(e);const i=l.find((t=>r.getOptionValue(t)===e));return i?r.getOptionLabel(i):""},renderEditCell:e=>r.jsx(ni,u({},e)),filterOperators:[{value:"is",getApplyFilterFn:e=>null==e.value||""===e.value?null:t=>si(t)===si(e.value),InputComponent:ii},{value:"not",getApplyFilterFn:e=>null==e.value||""===e.value?null:t=>si(t)!==si(e.value),InputComponent:ii},{value:"isAnyOf",getApplyFilterFn:e=>{if(!Array.isArray(e.value)||0===e.value.length)return null;const t=e.value.map(si);return e=>t.includes(si(e))},InputComponent:function(e){const{item:t,applyValue:n,type:o,apiRef:l,focusElementRef:i,slotProps:a}=e,s=F(e,ai),c=P(),d=pt();let p=null;if(t.field){const e=l.current.getColumn(t.field);Zl(e)&&(p=e)}const f=p?.getOptionValue,m=p?.getOptionLabel,h=g.useCallback(((e,t)=>f(e)===f(t)),[f]),b=g.useMemo((()=>ei(p)||[]),[p]),w=g.useMemo((()=>Array.isArray(t.value)?t.value.reduce(((e,t)=>{const r=b.find((e=>f(e)===t));return null!=r&&e.push(r),e}),[]):[]),[f,t.value,b]),C=g.useCallback(((e,r)=>{n(u({},t,{value:r.map(f)}))}),[n,t,f]),v=d.slots.baseAutocomplete;return r.jsx(v,u({multiple:!0,options:b,isOptionEqualToValue:h,id:c,value:w,onChange:C,getOptionLabel:m,label:l.current.getLocaleText("filterPanelInputLabel"),placeholder:l.current.getLocaleText("filterPanelInputPlaceholder"),slotProps:{textField:{type:o||"text",inputRef:i}}},s,a?.root))}}],pastedValueParser:(e,t,r)=>{const n=r,o=ei(n)||[],l=n.getOptionValue;if(o.find((t=>l(t)===e)))return e}}),ui=Wt((e=>e.headerFiltering)),di=Gt(ui,(e=>e?.enabled??!1)),pi=Gt(ui,(e=>e.editing)),fi=Gt(ui,(e=>e.menuOpen)),gi=Wt((e=>e.columnGrouping)),mi=Ut(gi,(e=>e?.unwrappedGroupingModel??{})),hi=Ut(gi,(e=>e?.lookup??{})),bi=Ut(gi,(e=>e?.headerStructure??[])),wi=Gt(gi,(e=>e?.maxDepth??0)),Ci=["maxWidth","minWidth","width","flex"],vi={string:Qn,number:Jl,date:Xl,dateTime:Ql,boolean:To,singleSelect:ci,[ao]:so,custom:Qn},yi=(e,t)=>{const r={};let n=0,o=0;const l=[];e.orderedFields.forEach((t=>{let i=e.lookup[t],a=0,s=!1;!1!==e.columnVisibilityModel[t]&&(i.flex&&i.flex>0?(n+=i.flex,s=!0):a=Zr(i.width||Qn.width,i.minWidth||Qn.minWidth,i.maxWidth||Qn.maxWidth),o+=a),i.computedWidth!==a&&(i=u({},i,{computedWidth:a})),s&&l.push(i),r[t]=i}));const i=void 0===t?0:t.viewportOuterSize.width-(t.hasScrollY?t.scrollbarSize:0),a=Math.max(i-o,0);if(n>0&&i>0){const e=function({initialFreeSpace:e,totalFlexUnits:t,flexColumns:r}){const n=new Set(r.map((e=>e.field))),o={all:{},frozenFields:[],freeze:e=>{const t=o.all[e];t&&!0!==t.frozen&&(o.all[e].frozen=!0,o.frozenFields.push(e))}};return function l(){if(o.frozenFields.length===n.size)return;const i={min:{},max:{}};let a=e,s=t,c=0;o.frozenFields.forEach((e=>{a-=o.all[e].computedWidth,s-=o.all[e].flex}));for(let e=0;e<r.length;e+=1){const t=r[e];if(o.all[t.field]&&!0===o.all[t.field].frozen)continue;let n=a/s*t.flex;n<t.minWidth?(c+=t.minWidth-n,n=t.minWidth,i.min[t.field]=!0):n>t.maxWidth&&(c+=t.maxWidth-n,n=t.maxWidth,i.max[t.field]=!0),o.all[t.field]={frozen:!1,computedWidth:n,flex:t.flex}}c<0?Object.keys(i.max).forEach((e=>{o.freeze(e)})):c>0?Object.keys(i.min).forEach((e=>{o.freeze(e)})):r.forEach((({field:e})=>{o.freeze(e)})),l()}(),o.all}({initialFreeSpace:a,totalFlexUnits:n,flexColumns:l});Object.keys(e).forEach((t=>{r[t].computedWidth=e[t].computedWidth}))}return u({},e,{lookup:r})};function xi(e){let t=vi.string;return e&&vi[e]&&(t=vi[e]),t}const Si=({apiRef:e,columnsToUpsert:t,initialState:r,columnVisibilityModel:n=kn(e),keepOnlyColumnsToUpsert:o=!1,updateInitialVisibilityModel:l=!1})=>{const i=!e.current.state.columns;let a;if(i)a={orderedFields:[],lookup:{},columnVisibilityModel:n,initialColumnVisibilityModel:n};else{const t=Sn(e);a={orderedFields:o?[]:[...t.orderedFields],lookup:u({},t.lookup),columnVisibilityModel:n,initialColumnVisibilityModel:l?n:t.initialColumnVisibilityModel}}let s={};o&&!i&&(s=Object.keys(a.lookup).reduce(((e,t)=>u({},e,{[t]:!1})),{})),t.forEach((e=>{const{field:t}=e;s[t]=!0;let r=a.lookup[t];null==r?(r=u({},xi(e.type),{field:t,hasBeenResized:!1}),a.orderedFields.push(t)):o&&a.orderedFields.push(t),r&&r.type!==e.type&&(r=u({},xi(e.type),{field:t}));let n=r.hasBeenResized;Ci.forEach((t=>{void 0!==e[t]&&(n=!0,-1===e[t]&&(e[t]=1/0))})),a.lookup[t]=p(r,u({},xi(e.type),e,{hasBeenResized:n}))})),o&&!i&&Object.keys(a.lookup).forEach((e=>{s[e]||delete a.lookup[e]}));const c=((e,t)=>{if(!t)return e;const{orderedFields:r=[],dimensions:n={}}=t,o=Object.keys(n);if(0===o.length&&0===r.length)return e;const l={},i=[];for(let c=0;c<r.length;c+=1){const t=r[c];e.lookup[t]&&(l[t]=!0,i.push(t))}const a=0===i.length?e.orderedFields:[...i,...e.orderedFields.filter((e=>!l[e]))],s=u({},e.lookup);for(let c=0;c<o.length;c+=1){const e=o[c],t=u({},s[e],{hasBeenResized:!0});Object.entries(n[e]).forEach((([e,r])=>{t[e]=-1===r?1/0:r})),s[e]=t}return u({},e,{orderedFields:a,lookup:s})})(e.current.unstable_applyPipeProcessors("hydrateColumns",a),r);return yi(c,e.current.getRootDimensions?.()??void 0)};function Ri(e,t){if(t.listView)return 0;const r=hr(e),n=wi(e),o=di(e);return Math.floor(t.columnHeaderHeight*r)+Math.floor((t.columnGroupHeaderHeight??t.columnHeaderHeight)*r)*n+(o?Math.floor((t.headerFilterHeight??t.columnHeaderHeight)*r):0)}const Ii=we("div",{name:"MuiDataGrid",slot:"ScrollArea",overridesResolver:(e,t)=>[{[`&.${ir["scrollArea--left"]}`]:t["scrollArea--left"]},{[`&.${ir["scrollArea--right"]}`]:t["scrollArea--right"]},t.scrollArea]})((()=>({position:"absolute",top:0,zIndex:101,width:20,bottom:0,[`&.${ir["scrollArea--left"]}`]:{left:0},[`&.${ir["scrollArea--right"]}`]:{right:0}}))),Mi=Gt(Kt,((e,t)=>"left"===t?e.leftPinnedWidth:"right"===t?e.rightPinnedWidth+(e.hasScrollX?e.scrollbarSize:0):0));function ki(e){const{scrollDirection:t,scrollPosition:n}=e,o=g.useRef(null),l=ut(),i=H(),a=zt(l,hr),s=zt(l,_t),c=zt(l,Mi,t),d=()=>{const e=Kt(l);if("left"===t)return n.current.left>0;if("right"===t){const t=s-e.viewportInnerSize.width;return n.current.left<t}return!1},[p,f]=g.useState(d),m=pt(),h=u({},m,{scrollDirection:t}),b=(e=>{const{scrollDirection:t,classes:r}=e;return I({root:["scrollArea",`scrollArea--${t}`]},lr,r)})(h),w=Ri(l,m),C=Math.floor(m.columnHeaderHeight*a),v={height:C,top:w-C};"left"===t?v.left=c:"right"===t&&(v.right=c);const x=y((e=>{let r;if(e.preventDefault(),"left"===t)r=e.clientX-o.current.getBoundingClientRect().right;else{if("right"!==t)throw new Error("MUI X: Wrong drag direction");r=Math.max(1,e.clientX-o.current.getBoundingClientRect().left)}r=1.5*(r-1)+1,i.start(0,(()=>{l.current.scroll({left:n.current.left+r,top:n.current.top})}))}));return dr(l,"scrollPositionChange",(()=>{f(d)})),p?r.jsx(Ii,{ref:o,className:b.root,ownerState:h,onDragOver:x,style:v}):null}const Pi=or((function(e){const t=ut(),[n,o]=g.useState(!1);return dr(t,"columnHeaderDragStart",(()=>o(!0))),dr(t,"columnHeaderDragEnd",(()=>o(!1))),n?r.jsx(ki,u({},e)):null})),Ei=g.createContext(void 0);function Fi(){const e=g.useContext(Ei);if(void 0===e)throw new Error(["MUI X: Could not find the Data Grid private context.","It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.","This can also happen if you are bundling multiple versions of the Data Grid."].join("\n"));return e}const Hi=()=>{};function Ti(e,t,r){const n=g.useRef(!0);d((()=>{n.current=!1,e.current.register(r,t)}),[e,r,t]),n.current&&e.current.register(r,t)}function Di(e,t){const r=g.useRef(null);if(r.current)return r.current;const n=e.current.getLogger(t);return r.current=n,n}const Oi=(e,t,r,n,o)=>{const l=Di(e,"useNativeEventListener");fr(e,"rootMount",(()=>{const e=t();if(e&&r)return l.debug(`Binding native ${r} event`),e.addEventListener(r,n,o),()=>{l.debug(`Clearing native ${r} event`),e.removeEventListener(r,n,o)}}))},$i=e=>{const t=g.useRef(!0);t.current&&(t.current=!1,e())},ji=(e,t)=>Dl(e),Li=(e,t)=>zt(e,Dl),zi=("undefined"!=typeof navigator?navigator.userAgent.toLowerCase():"empty").includes("firefox"),Ai=Wt((e=>e.rowsMeta)),Bi=Wt((e=>e.virtualization));Gt(Bi,(e=>e.enabled));const Vi=Gt(Bi,(e=>e.enabledForColumns)),Ni=Gt(Bi,(e=>e.enabledForRows)),Gi=Gt(Bi,(e=>e.renderContext)),Wi=Ut((e=>e.current.state.virtualization.renderContext.firstColumnIndex),(e=>e.current.state.virtualization.renderContext.lastColumnIndex),((e,t)=>({firstColumnIndex:e,lastColumnIndex:t}))),Ui={firstRowIndex:0,lastRowIndex:0,firstColumnIndex:0,lastColumnIndex:0},Ki=(e,t)=>{const{disableVirtualization:r,autoHeight:n}=t;return u({},e,{virtualization:{enabled:!r,enabledForColumns:!r,enabledForRows:!r&&!n,renderContext:Ui}})},_i=Wt((e=>e.rowSpanning)),qi=Gt(_i,(e=>e.hiddenCells)),Xi=Gt(_i,(e=>e.spannedCells)),Qi=Gt(_i,(e=>e.hiddenCellOriginMap)),Yi=Wt((e=>e.listViewColumn)),Ji=Ut(Gt(pl,Gi,Dl,En,((e,t,r,n)=>{if(!e)return!1;const o=r.rowIdToIndexMap.get(e.id),l=n.slice(t.firstColumnIndex,t.lastColumnIndex).findIndex((t=>t.field===e.field));return!(void 0!==o&&-1!==l&&o>=t.firstRowIndex&&o<=t.lastRowIndex)})),En,Dl,pl,((e,t,r,n)=>{if(!e)return null;const o=r.rowIdToIndexMap.get(n.id);if(void 0===o)return null;const l=t.findIndex((e=>e.field===n.field));return-1===l?null:u({},n,{rowIndex:o,columnIndex:l})}));function Zi(e,t){return Math.round(e*10**t)/10**t}const ea="undefined"!=typeof window&&/jsdom|HappyDOM/.test(window.navigator.userAgent);var ta=function(e){return e[e.NONE=0]="NONE",e[e.UP=1]="UP",e[e.DOWN=2]="DOWN",e[e.LEFT=3]="LEFT",e[e.RIGHT=4]="RIGHT",e}(ta||{});const ra={top:0,left:0},na=Object.freeze(new Map);function oa(e){return e.current.state.dimensions.viewportOuterSize.width>0&&e.current.state.dimensions.columnsTotalWidth>e.current.state.dimensions.viewportOuterSize.width}function la(e,t,r,n){const o=Kt(e),l=ji(e),i=t.listView?[Yi(e)]:En(e),a=Qi(e),s=e.current.state.rows.dataRowIds.at(-1),c=i.at(-1);return{enabledForRows:r,enabledForColumns:n,apiRef:e,autoHeight:t.autoHeight,rowBufferPx:t.rowBufferPx,columnBufferPx:t.columnBufferPx,leftPinnedWidth:o.leftPinnedWidth,columnsTotalWidth:o.columnsTotalWidth,viewportInnerWidth:o.viewportInnerSize.width,viewportInnerHeight:o.viewportInnerSize.height,lastRowHeight:void 0!==s?e.current.unstable_getRowHeight(s):0,lastColumnWidth:c?.computedWidth??0,rowsMeta:Ai(e),columnPositions:Dn(e),rows:l.rows,range:l.range,pinnedColumns:Tn(e),visibleColumns:i,hiddenCellsOriginMap:a,listView:t.listView??!1,virtualizeColumnsWithAutoRowHeight:t.virtualizeColumnsWithAutoRowHeight}}function ia(e,t,r){const n={firstRowIndex:0,lastRowIndex:e.rows.length,firstColumnIndex:0,lastColumnIndex:e.visibleColumns.length},{top:o,left:l}=t,i=Math.abs(l)+e.leftPinnedWidth;if(e.enabledForRows){let t=Math.min(aa(e,o,{atStart:!0,lastPosition:e.rowsMeta.positions[e.rowsMeta.positions.length-1]+e.lastRowHeight}),e.rowsMeta.positions.length-1);const r=e.hiddenCellsOriginMap[t];if(r){const e=Math.min(...Object.values(r));t=Math.min(t,e)}const l=e.autoHeight?t+e.rows.length:aa(e,o+e.viewportInnerHeight);n.firstRowIndex=t,n.lastRowIndex=l}if(e.listView)return u({},n,{lastColumnIndex:1});if(e.enabledForColumns){let t=0,o=e.columnPositions.length,l=!1;const[a,s]=ca({firstIndex:n.firstRowIndex,lastIndex:n.lastRowIndex,minFirstIndex:0,maxLastIndex:e.rows.length,bufferBefore:r.buffer.rowBefore,bufferAfter:r.buffer.rowAfter,positions:e.rowsMeta.positions,lastSize:e.lastRowHeight});if(!e.virtualizeColumnsWithAutoRowHeight)for(let r=a;r<s&&!l;r+=1){const t=e.rows[r];l=e.apiRef.current.rowHasAutoHeight(t.id)}l&&!e.virtualizeColumnsWithAutoRowHeight||(t=sa(i,e.columnPositions,{atStart:!0,lastPosition:e.columnsTotalWidth}),o=sa(i+e.viewportInnerWidth,e.columnPositions)),n.firstColumnIndex=t,n.lastColumnIndex=o}const a=function(e,t,r){const[n,o]=ca({firstIndex:t.firstRowIndex,lastIndex:t.lastRowIndex,minFirstIndex:0,maxLastIndex:e.rows.length,bufferBefore:r.buffer.rowBefore,bufferAfter:r.buffer.rowAfter,positions:e.rowsMeta.positions,lastSize:e.lastRowHeight}),[l,i]=ca({firstIndex:t.firstColumnIndex,lastIndex:t.lastColumnIndex,minFirstIndex:e.pinnedColumns.left.length,maxLastIndex:e.visibleColumns.length-e.pinnedColumns.right.length,bufferBefore:r.buffer.columnBefore,bufferAfter:r.buffer.columnAfter,positions:e.columnPositions,lastSize:e.lastColumnWidth}),a=function({firstColumnToRender:e,apiRef:t,firstRowToRender:r,lastRowToRender:n,visibleRows:o}){let l=e;for(let i=r;i<n;i+=1)if(o[i]){const r=o[i].id,n=t.current.unstable_getCellColSpanInfo(r,e);n&&n.spannedByColSpan&&(l=n.leftVisibleCellIndex)}return l}({firstColumnToRender:l,apiRef:e.apiRef,firstRowToRender:n,lastRowToRender:o,visibleRows:e.rows});return{firstRowIndex:n,lastRowIndex:o,firstColumnIndex:a,lastColumnIndex:i}}(e,n,r);return a}function aa(e,t,r){const n=e.apiRef.current.getLastMeasuredRowIndex();let o=n===1/0;e.range?.lastRowIndex&&!o&&(o=n>=e.range.lastRowIndex);const l=Zr(n-(e.range?.firstRowIndex||0),0,e.rowsMeta.positions.length);return o||e.rowsMeta.positions[l]>=t?sa(t,e.rowsMeta.positions,r):function(e,t,r,n){let o=1;for(;r<t.length&&Math.abs(t[r])<e;)r+=o,o*=2;return sa(e,t,n,Math.floor(r/2),Math.min(r,t.length))}(t,e.rowsMeta.positions,l,r)}function sa(e,t,r=void 0,n=0,o=t.length){if(t.length<=0)return-1;if(n>=o)return n;const l=n+Math.floor((o-n)/2),i=t[l];let a;return a=r?.atStart?e-((l===t.length-1?r.lastPosition:t[l+1])-i)<i:e<=i,a?sa(e,t,r,n,l):sa(e,t,r,l+1,o)}function ca({firstIndex:e,lastIndex:t,bufferBefore:r,bufferAfter:n,minFirstIndex:o,maxLastIndex:l,positions:i,lastSize:a}){const s=i[e]-r,c=i[t]+n,u=sa(s,i,{atStart:!0,lastPosition:i[i.length-1]+a}),d=sa(c,i);return[Zr(u,o,l),Zr(d,o,l)]}function ua(e,t,r){const n=(e[t.firstColumnIndex]??0)-(e[r]??0);return Math.abs(n)}function da(e,t,r,n,o,l){if(e)switch(t){case ta.LEFT:t=ta.RIGHT;break;case ta.RIGHT:t=ta.LEFT}switch(t){case ta.NONE:return{rowAfter:r,rowBefore:r,columnAfter:n,columnBefore:n};case ta.LEFT:return{rowAfter:0,rowBefore:0,columnAfter:0,columnBefore:l};case ta.RIGHT:return{rowAfter:0,rowBefore:0,columnAfter:l,columnBefore:0};case ta.UP:return{rowAfter:0,rowBefore:o,columnAfter:0,columnBefore:0};case ta.DOWN:return{rowAfter:o,rowBefore:0,columnAfter:0,columnBefore:0};default:throw new Error("unreachable")}}const pa=we("div",{name:"MuiDataGrid",slot:"OverlayWrapper",shouldForwardProp:e=>"overlayType"!==e&&"loadingOverlayVariant"!==e&&"right"!==e})((({overlayType:e,loadingOverlayVariant:t,right:r})=>"skeleton"!==t?{position:"sticky",top:"var(--DataGrid-headersTotalHeight)",left:0,right:`${r}px`,width:0,height:0,zIndex:"loadingOverlay"===e?5:4}:{})),fa=we("div",{name:"MuiDataGrid",slot:"OverlayWrapperInner",shouldForwardProp:e=>"overlayType"!==e&&"loadingOverlayVariant"!==e})({});function ga(e){const t=ut(),n=pt(),o=zt(t,Kt);let l=Math.max(o.viewportOuterSize.height-o.topContainerHeight-o.bottomContainerHeight-(o.hasScrollX?o.scrollbarSize:0),0);0===l&&(l=Co);const i=(e=>{const{classes:t}=e;return I({root:["overlayWrapper"],inner:["overlayWrapperInner"]},lr,t)})(u({},e,{classes:n.classes}));return r.jsx(pa,u({className:i.root},e,{right:o.columnsTotalWidth-o.viewportOuterSize.width,children:r.jsx(fa,u({className:i.inner,style:{height:l,width:o.viewportOuterSize.width}},e))}))}const ma=Wt((e=>e.pivoting)),ha=Gt(ma,(e=>e?.active)),ba=new Map,wa=Gt(ma,(e=>e?.initialColumns||ba));Gt(ma,(e=>e?.panelOpen));const Ca=Wt((e=>e.columnMenu)),va=or((function(){const e=Fi(),t=pt(),n=zt(e,En),o=zt(e,Zo),l=zt(e,zo),i=zt(e,bl),a=zt(e,(()=>null===hl(e))),s=zt(e,wl),c=zt(e,fl),d=zt(e,gl),p=zt(e,wi),f=zt(e,Ca),g=zt(e,kn),m=zt(e,bi),h=!(null===s&&null===i&&a),b=e.current.columnHeadersContainerRef;return r.jsx(t.slots.columnHeaders,u({ref:b,visibleColumns:n,filterColumnLookup:o,sortColumnLookup:l,columnHeaderTabIndexState:i,columnGroupHeaderTabIndexState:s,columnHeaderFocus:c,columnGroupHeaderFocus:d,headerGroupingMaxDepth:p,columnMenuState:f,columnVisibility:g,columnGroupsHeaderStructure:m,hasOtherElementInTabSequence:h},t.slotProps?.columnHeaders))})),ya=we("div")({position:"absolute",top:"var(--DataGrid-headersTotalHeight)",left:0,width:"calc(100% - (var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize)))"}),xa=we("div",{name:"MuiDataGrid",slot:"Main",overridesResolver:(e,t)=>{const{ownerState:r,loadingOverlayVariant:n,overlayType:o}=e,l="skeleton"===n||"noColumnsOverlay"===o;return[t.main,r.hasPinnedRight&&t["main--hasPinnedRight"],l&&t["main--hiddenContent"]]}})({flexGrow:1,position:"relative",overflow:"hidden",display:"flex",flexDirection:"column"}),Sa=gt(((e,t)=>{const{ownerState:n}=e,o=pt(),l=Jn().hooks.useGridAriaAttributes();return r.jsxs(xa,u({ownerState:n,className:e.className,tabIndex:-1},l,o.slotProps?.main,{ref:t,children:[r.jsx(ya,{role:"presentation","data-id":"gridPanelAnchor"}),e.children]}))})),Ra=we("div")({position:"sticky",zIndex:40,top:0});function Ia(e){const t=I({root:["topContainer"]},lr,{});return r.jsx(Ra,u({},e,{className:n(t.root,ir["container--top"]),role:"presentation"}))}const Ma=we("div",{name:"MuiDataGrid",slot:"VirtualScrollerContent",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.virtualScrollerContent,r.overflowedContent&&t["virtualScrollerContent--overflowed"]]}})({}),ka=gt((function(e,t){const o=pt(),l=!o.autoHeight&&"auto"===e.style?.minHeight,i=((e,t)=>{const{classes:r}=e;return I({root:["virtualScrollerContent",t&&"virtualScrollerContent--overflowed"]},lr,r)})(o,l),a={classes:o.classes,overflowedContent:l};return r.jsx(Ma,u({},e,{ownerState:a,className:n(i.root,e.className),ref:t}))})),Pa=we("div")({display:"flex",flexDirection:"row",width:"var(--DataGrid-rowWidth)",boxSizing:"border-box"}),Ea=we("div")({position:"sticky",height:"100%",boxSizing:"border-box",borderTop:"1px solid var(--rowBorderColor)",backgroundColor:yr.cell.background.pinned}),Fa=we(Ea)({left:0,borderRight:"1px solid var(--rowBorderColor)"}),Ha=we(Ea)({right:0,borderLeft:"1px solid var(--rowBorderColor)"}),Ta=we("div")({flexGrow:1,borderTop:"1px solid var(--rowBorderColor)"}),Da=or((function({rowsLength:e}){const t=ut(),{viewportOuterSize:n,minimumSize:o,hasScrollX:l,hasScrollY:i,scrollbarSize:a,leftPinnedWidth:s,rightPinnedWidth:c}=zt(t,Kt),u=l?a:0,d=n.height-o.height>0;return 0!==u||d?r.jsxs(Pa,{className:ir.filler,role:"presentation",style:{height:u,"--rowBorderColor":0===e?"transparent":"var(--DataGrid-rowBorderColor)"},children:[s>0&&r.jsx(Fa,{className:ir["filler--pinnedLeft"],style:{width:s}}),r.jsx(Ta,{}),c>0&&r.jsx(Ha,{className:ir["filler--pinnedRight"],style:{width:c+(i?a:0)}})]}):null})),Oa=["className"],$a=we("div",{name:"MuiDataGrid",slot:"VirtualScrollerRenderZone"})({position:"absolute",display:"flex",flexDirection:"column"}),ja=gt((function(e,t){const{className:o}=e,l=F(e,Oa),i=ut(),a=pt(),s=(e=>{const{classes:t}=e;return I({root:["virtualScrollerRenderZone"]},lr,t)})(a),c=zt(i,(()=>{const e=Gi(i);return Ai(i).positions[e.firstRowIndex]??0}));return r.jsx($a,u({className:n(s.root,o),ownerState:a,style:{transform:`translate3d(0, ${c}px, 0)`}},l,{ref:t}))})),La={includeHeaders:!0,includeOutliers:!1,outliersFactor:1.5,expand:!1,disableColumnVirtualization:!0},za=Wt((e=>e.editRows)),Aa=Gt(za,((e,{rowId:t,editMode:r})=>r===on.Row&&Boolean(e[t]))),Ba=Gt(za,((e,{rowId:t,field:r})=>e[t]?.[r]??null)),Va=Wt((e=>e.preferencePanel)),Na=Gt(Va,((e,t)=>!(!e.open||e.labelId!==t)));var Ga=function(e){return e.filters="filters",e.columns="columns",e.aiAssistant="aiAssistant",e}(Ga||{});function Wa(e){return JSON.stringify([e.filterModel,e.sortModel,e.start,e.end])}class Ua{constructor({ttl:e=3e5,getKey:t=Wa}){this.cache=void 0,this.ttl=void 0,this.getKey=void 0,this.cache={},this.ttl=e,this.getKey=t}set(e,t){const r=this.getKey(e),n=Date.now()+this.ttl;this.cache[r]={value:t,expiry:n}}get(e){const t=this.getKey(e),r=this.cache[t];if(r){if(!(Date.now()>r.expiry))return r.value;delete this.cache[t]}}clear(){this.cache={}}}class Ka extends Error{constructor(e){super(e.message),this.params=void 0,this.cause=void 0,this.name="GridGetRowsError",this.params=e.params,this.cause=e.cause}}class _a extends Error{constructor(e){super(e.message),this.params=void 0,this.cause=void 0,this.name="GridUpdateRowError",this.params=e.params,this.cause=e.cause}}const qa=we("div")({position:"absolute",display:"inline-block",zIndex:60,"&:hover":{zIndex:70},"--size":"calc(max(var(--DataGrid-scrollbarSize), 14px))"}),Xa=we(qa)({width:"var(--size)",height:"calc(var(--DataGrid-hasScrollY) * (100% - var(--DataGrid-topContainerHeight) - var(--DataGrid-bottomContainerHeight) - var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize)))",overflowY:"auto",overflowX:"hidden",outline:0,"& > div":{width:"var(--size)"},top:"var(--DataGrid-topContainerHeight)",right:"0px"}),Qa=we(qa)({width:"100%",height:"var(--size)",overflowY:"hidden",overflowX:"auto",outline:0,"& > div":{height:"var(--size)"},bottom:"0px"}),Ya=gt((function(e,t){const n=Fi(),o=pt(),l=g.useRef(!1),i=g.useRef(0),a=g.useRef(null),s=g.useRef(null),c=((e,t)=>{const{classes:r}=e;return I({root:["scrollbar",`scrollbar--${t}`],content:["scrollbarContent"]},lr,r)})(o,e.position),u=zt(n,Kt),d="vertical"===e.position?"height":"width",p="vertical"===e.position?"scrollTop":"scrollLeft",f="vertical"===e.position?"top":"left",m="vertical"===e.position?u.hasScrollX:u.hasScrollY,h=u.minimumSize[d]+(m?u.scrollbarSize:0),b=("vertical"===e.position?u.viewportInnerSize.height:u.viewportOuterSize.width)*(h/u.viewportOuterSize[d]),w=y((()=>{const t=a.current,r=e.scrollPosition.current;if(!t)return;if(r[f]===i.current)return;if(i.current=r[f],l.current)return void(l.current=!1);l.current=!0;const n=r[f]/h;t[p]=n*b})),C=y((()=>{const e=n.current.virtualScrollerRef.current,t=a.current;if(!t)return;if(l.current)return void(l.current=!1);l.current=!0;const r=t[p]/b;e[p]=r*h}));T((()=>{const e=n.current.virtualScrollerRef.current,t=a.current,r={passive:!0};return e.addEventListener("scroll",w,r),t.addEventListener("scroll",C,r),()=>{e.removeEventListener("scroll",w,r),t.removeEventListener("scroll",C,r)}})),g.useEffect((()=>{s.current.style.setProperty(d,`${b}px`)}),[b,d]);const x="vertical"===e.position?Xa:Qa;return r.jsx(x,{ref:v(t,a),className:c.root,style:"vertical"===e.position&&o.listView?{height:"100%",top:0}:void 0,tabIndex:-1,"aria-hidden":"true",onFocus:e=>{e.target.blur()},children:r.jsx("div",{ref:s,className:c.content})})})),Ja=we("div",{name:"MuiDataGrid",slot:"VirtualScroller",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.virtualScroller,r.hasScrollX&&t["virtualScroller--hasScrollX"]]}})({position:"relative",height:"100%",flexGrow:1,overflow:"scroll",scrollbarWidth:"none",display:"flex",flexDirection:"column","&::-webkit-scrollbar":{display:"none"},"@media print":{overflow:"hidden"},zIndex:0}),Za=e=>e.current.state.dimensions.rightPinnedWidth>0;function es(e){const n=ut(),o=pt(),l=zt(n,Yt),i=zt(n,Qt),a=zt(n,Za),s=zt(n,nr),{getOverlay:c,overlaysProps:p}=(()=>{const e=ut(),t=pt(),n=zt(e,kr),o=zt(e,Xo),l=zt(e,Br),i=zt(e,En),a=0===n&&0===l,s=zt(e,Pr),c=zt(e,ha),d=!s&&a,p=!s&&n>0&&0===o,f=!s&&0===i.length;let g=null,m=null;d&&(g="noRowsOverlay"),f&&(g="noColumnsOverlay"),d&&c&&(g="emptyPivotOverlay"),p&&(g="noResultsOverlay"),s&&(g="loadingOverlay",m=t.slotProps?.loadingOverlay?.[a?"noRowsVariant":"variant"]??(a?"skeleton":"linear-progress"));const h={overlayType:g,loadingOverlayVariant:m};return{getOverlay:()=>{if(!g)return null;const e=t.slots?.[g],n=t.slotProps?.[g];return r.jsx(ga,u({},h,{children:r.jsx(e,u({},n))}))},overlaysProps:h}})(),f=u({classes:o.classes,hasScrollX:i,hasPinnedRight:a},p),h=(e=>{const{classes:t,hasScrollX:r,hasPinnedRight:n,loadingOverlayVariant:o,overlayType:l}=e;return I({root:["main",n&&"main--hasPinnedRight",("skeleton"===o||"noColumnsOverlay"===l)&&"main--hiddenContent"],scroller:["virtualScroller",r&&"virtualScroller--hasScrollX"]},lr,t)})(f),b=(()=>{const e=Fi(),n=pt(),{listView:o}=n,l=zt(e,(()=>o?[Yi(e)]:En(e))),i=zt(e,Ni)&&!ea,a=zt(e,Vi)&&!ea,s=zt(e,Ar),c=Tn(e),p=o?yn:c,f=s.bottom.length>0,[h,b]=g.useState(na),w=t(),C=zt(e,tl),v=Li(e),x=e.current.mainElementRef,S=e.current.virtualScrollerRef,R=e.current.virtualScrollbarVerticalRef,I=e.current.virtualScrollbarHorizontalRef,M=zt(e,jn),k=g.useRef(!1),P=zt(e,qt),F=zt(e,Xt),T=zt(e,_t),D=zt(e,oa),O=zt(e,rr),$=zt(e,Jt),j=g.useRef(null),L=g.useCallback((t=>{if(x.current=t,!t)return;const r=t.getBoundingClientRect();let n={width:Zi(r.width,1),height:Zi(r.height,1)};if((!j.current||n.width!==j.current.width&&n.height!==j.current.height)&&(j.current=n,e.current.publishEvent("resize",n)),"undefined"==typeof ResizeObserver)return;const o=new ResizeObserver((t=>{const r=t[0];if(!r)return;const o={width:Zi(r.contentRect.width,1),height:Zi(r.contentRect.height,1)};o.width===n.width&&o.height===n.height||(e.current.publishEvent("resize",o),n=o)}));return o.observe(t),ft>=19?()=>{x.current=null,o.disconnect()}:void 0}),[e,x]),z=g.useRef(n.initialState?.scroll??ra),A=g.useRef(!1),B=g.useRef(ra),V=g.useRef(Ui),N=zt(e,Gi),G=zt(e,Ji),W=H(),U=g.useRef(void 0),K=E((()=>((e,t,r,n)=>({direction:ta.NONE,buffer:da(e,ta.NONE,t,r,n,300)}))(w,n.rowBufferPx,n.columnBufferPx,15*P))).current,_=g.useCallback((t=>{if((r=t)===(n=e.current.state.virtualization.renderContext)||r.firstRowIndex===n.firstRowIndex&&r.lastRowIndex===n.lastRowIndex&&r.firstColumnIndex===n.firstColumnIndex&&r.lastColumnIndex===n.lastColumnIndex)return;var r,n;const o=t.firstRowIndex!==V.current.firstRowIndex||t.lastRowIndex!==V.current.lastRowIndex;e.current.setState((e=>u({},e,{virtualization:u({},e.virtualization,{renderContext:t})}))),Kt(e).isReady&&o&&(V.current=t,e.current.publishEvent("renderedRowsIntervalChange",t)),B.current=z.current}),[e]),q=y((()=>{const t=S.current;if(!t)return;const r=Kt(e),o=Math.ceil(r.minimumSize.height-r.viewportOuterSize.height),l=Math.ceil(r.minimumSize.width-r.viewportInnerSize.width),s={top:Zr(t.scrollTop,0,o),left:w?Zr(t.scrollLeft,-l,0):Zr(t.scrollLeft,0,l)},c=s.left-z.current.left,u=s.top-z.current.top,d=0!==c||0!==u;z.current=s;const p=d?function(e,t){return 0===e&&0===t?ta.NONE:Math.abs(t)>=Math.abs(e)?t>0?ta.DOWN:ta.UP:e>0?ta.RIGHT:ta.LEFT}(c,u):ta.NONE,f=Math.abs(z.current.top-B.current.top),g=Math.abs(z.current.left-B.current.left),h=f>=P||g>=50,b=K.direction!==p;if(!h&&!b)return N;if(b)switch(p){case ta.NONE:case ta.LEFT:case ta.RIGHT:U.current=void 0;break;default:U.current=N}K.direction=p,K.buffer=da(w,p,n.rowBufferPx,n.columnBufferPx,15*P,300);const C=ia(la(e,n,i,a),z.current,K);return m.flushSync((()=>{_(C)})),W.start(1e3,q),C})),X=()=>{if(!Kt(e).isReady&&(i||a))return;const t=ia(la(e,n,i,a),z.current,K);U.current=void 0,_(t)},Q=y((()=>{if(A.current)return void(A.current=!1);const t=q();e.current.publishEvent("scrollPositionChange",{top:z.current.top,left:z.current.left,renderContext:t})})),Y=y((t=>{e.current.publishEvent("virtualScrollerWheel",{},t)})),J=y((t=>{e.current.publishEvent("virtualScrollerTouchMove",{},t)})),Z=g.useMemo((()=>({overflowX:!D||o?"hidden":void 0,overflowY:n.autoHeight?"hidden":void 0})),[D,n.autoHeight,o]),ee=g.useMemo((()=>{const e={width:D?T:"auto",flexBasis:F,flexShrink:0};return 0===e.flexBasis&&(e.flexBasis=Co),e}),[T,F,D]),te=g.useCallback((t=>{t&&e.current.publishEvent("virtualScrollerContentSizeChange",{columnsTotalWidth:T,contentHeight:F})}),[e,T,F]);return d((()=>{k.current&&e.current.updateRenderContext?.()}),[e,a,i]),d((()=>{o&&(S.current.scrollLeft=0)}),[o,S]),(t=>{const r=g.useRef(!1);d((()=>r.current||!t?Hi:(r.current=!0,(()=>{if(e.current.publishEvent("scrollPositionChange",{top:z.current.top,left:z.current.left,renderContext:N}),k.current=!0,n.initialState?.scroll&&S.current){const t=S.current,{top:r,left:o}=n.initialState.scroll,l={top:!(r>0),left:!(o>0)};if(!l.left&&T&&(t.scrollLeft=o,A.current=!0,l.left=!0),!l.top&&F&&(t.scrollTop=r,A.current=!0,l.top=!0),!l.top||!l.left){const n=e.current.subscribeEvent("virtualScrollerContentSizeChange",(e=>{!l.left&&e.columnsTotalWidth&&(t.scrollLeft=o,A.current=!0,l.left=!0),!l.top&&e.contentHeight&&(t.scrollTop=r,A.current=!0,l.top=!0),l.left&&l.top&&n()}));return n}}})())),[r.current||t])})(N!==Ui),e.current.register("private",{updateRenderContext:X}),fr(e,"sortedRowsSet",X),fr(e,"paginationModelChange",X),fr(e,"columnsChange",X),{renderContext:N,setPanels:b,getRows:(t={})=>{if(!t.rows&&!v.range)return[];const o=Hr(e);let i=N;t.renderContext&&(i=t.renderContext,i.firstColumnIndex=N.firstColumnIndex,i.lastColumnIndex=N.lastColumnIndex);const a=!f&&void 0===t.position||f&&"bottom"===t.position,c=void 0!==t.position;let d;switch(t.position){case"top":d=0;break;case"bottom":d=s.top.length+v.rows.length;break;case void 0:d=s.top.length}const g=t.rows??v.rows,m=i.firstRowIndex,b=Math.min(i.lastRowIndex,g.length),w=t.rows?en(0,t.rows.length):en(m,b);let y=-1;!c&&G&&(G.rowIndex<m&&(w.unshift(G.rowIndex),y=G.rowIndex),G.rowIndex>b&&(w.push(G.rowIndex),y=G.rowIndex));const x=[],S=n.slotProps?.row,R=Dn(e);return w.forEach((s=>{const{id:f,model:m}=g[s];if(!o[f])return;const b=(v?.range?.firstRowIndex||0)+d+s;if(M){const t=p.left.length,r=l.length-p.right.length;e.current.calculateColSpan({rowId:f,minFirstColumn:t,maxLastColumn:r,columns:l}),p.left.length>0&&e.current.calculateColSpan({rowId:f,minFirstColumn:0,maxLastColumn:p.left.length,columns:l}),p.right.length>0&&e.current.calculateColSpan({rowId:f,minFirstColumn:l.length-p.right.length,maxLastColumn:l.length,columns:l})}const w=e.current.rowHasAutoHeight(f)?"auto":e.current.unstable_getRowHeight(f),I=C.has(f)&&e.current.isRowSelectable(f);let k=!1;void 0===t.position&&(k=0===s);let P=!1;const E=s===g.length-1;a&&(c?P=E:s===v.rows.length-1&&(P=!0));let F=i;U.current&&s>=U.current.firstRowIndex&&s<U.current.lastRowIndex&&(F=U.current);const H=s===y,D=G?.rowIndex===b,j=ua(R,F,p.left.length),L=E&&"top"===t.position,z=F.firstColumnIndex,A=F.lastColumnIndex;if(x.push(r.jsx(n.slots.row,u({row:m,rowId:f,index:b,selected:I,offsetLeft:j,columnsTotalWidth:T,rowHeight:w,pinnedColumns:p,visibleColumns:l,firstColumnIndex:z,lastColumnIndex:A,focusedColumnIndex:D?G.columnIndex:void 0,isFirstVisible:k,isLastVisible:P,isNotVisible:H,showBottomBorder:L,scrollbarWidth:O,gridHasFiller:$},S),f)),H)return;const B=h.get(f);B&&x.push(B),void 0===t.position&&E&&x.push(e.current.getInfiniteLoadingTriggerElement?.({lastRowId:f}))})),x},getContainerProps:()=>({ref:L}),getScrollerProps:()=>({ref:S,onScroll:Q,onWheel:Y,onTouchMove:J,style:Z,role:"presentation",tabIndex:zi?-1:void 0}),getContentProps:()=>({style:ee,role:"presentation",ref:te}),getRenderZoneProps:()=>({role:"rowgroup"}),getScrollbarVerticalProps:()=>({ref:R,scrollPosition:z}),getScrollbarHorizontalProps:()=>({ref:I,scrollPosition:z}),getScrollAreaProps:()=>({scrollPosition:z})}})(),{getContainerProps:w,getScrollerProps:C,getContentProps:v,getRenderZoneProps:x,getScrollbarVerticalProps:S,getScrollbarHorizontalProps:R,getRows:M,getScrollAreaProps:k}=b,P=M();return r.jsxs(Sa,u({className:h.root},w(),{ownerState:f,children:[r.jsx(Pi,u({scrollDirection:"left"},k())),r.jsx(Pi,u({scrollDirection:"right"},k())),r.jsxs(Ja,u({className:h.scroller},C(),{ownerState:f,children:[r.jsxs(Ia,{children:[!o.listView&&r.jsx(va,{}),r.jsx(o.slots.pinnedRows,{position:"top",virtualScroller:b})]}),c(),r.jsx(ka,u({},v(),{children:r.jsxs(ja,u({},x(),{children:[P,r.jsx(o.slots.detailPanels,{virtualScroller:b})]}))})),s&&r.jsx(Da,{rowsLength:P.length}),r.jsx(o.slots.bottomContainer,{children:r.jsx(o.slots.pinnedRows,{position:"bottom",virtualScroller:b})})]})),i&&!o.listView&&r.jsx(Ya,u({position:"horizontal"},R())),l&&r.jsx(Ya,u({position:"vertical"},S())),e.children]}))}function ts(){const e=pt();return e.hideFooter?null:r.jsx(e.slots.footer,u({},e.slotProps?.footer))}let rs;const ns=(e,t)=>{if(e)if(t){if(e===Ro.LEFT)return"right";if(e===Ro.RIGHT)return"left"}else{if(e===Ro.LEFT)return"left";if(e===Ro.RIGHT)return"right"}};function os(e,t,r,n){const o=ns(r,t);return o&&void 0!==n?(e[o]=n,e):e}const ls=["column","row","rowId","rowNode","align","children","colIndex","width","className","style","colSpan","disableDragEvents","isNotVisible","pinnedOffset","pinnedPosition","showRightBorder","showLeftBorder","onClick","onDoubleClick","onMouseDown","onMouseUp","onMouseOver","onKeyDown","onKeyUp","onDragEnter","onDragOver"],is=["changeReason","unstable_updateValueOnRender"];Ro.LEFT,vn.LEFT,Ro.RIGHT,vn.RIGHT,Ro.NONE,Ro.VIRTUAL;const as=gt((function(e,l){const{column:i,row:a,rowId:s,rowNode:c,align:d,colIndex:p,width:f,className:m,style:h,colSpan:b,disableDragEvents:w,isNotVisible:C,pinnedOffset:y,pinnedPosition:x,showRightBorder:S,showLeftBorder:R,onClick:M,onDoubleClick:k,onMouseDown:P,onMouseUp:E,onMouseOver:H,onKeyDown:T,onKeyUp:D,onDragEnter:O,onDragOver:j}=e,L=F(e,ls),z=Fi(),A=pt(),B=t(),V=i.field,N=zt(z,Ba,{rowId:s,field:V}),G=Jn().hooks.useCellAggregationResult(s,V),W=N?ln.Edit:ln.View,U=z.current.getCellParamsForRow(s,V,a,{colDef:i,cellMode:W,rowNode:c,tabIndex:zt(z,(()=>{const e=hl(z);return e&&e.field===V&&e.id===s?0:-1})),hasFocus:zt(z,(()=>{const e=pl(z);return e?.id===s&&e.field===V}))});U.api=z.current,G&&(U.value=G.value,U.formattedValue=i.valueFormatter?i.valueFormatter(U.value,a,i,z):U.value);const K=zt(z,(()=>z.current.unstable_applyPipeProcessors("isCellSelected",!1,{id:s,field:V}))),_=zt(z,qi),q=zt(z,Xi),{hasFocus:X,isEditable:Q=!1,value:Y}=U,J="actions"===i.type&&i.getActions?.(z.current.getRowParams(s)).some((e=>!e.props.disabled)),Z="view"!==W&&Q||J?-1:U.tabIndex,{classes:ee,getCellClassName:te}=A,re=[zt(z,(()=>z.current.unstable_applyPipeProcessors("cellClassName",[],{id:s,field:V}).filter(Boolean).join(" ")))];i.cellClassName&&re.push("function"==typeof i.cellClassName?i.cellClassName(U):i.cellClassName),"flex"===i.display&&re.push(ir["cell--flex"]),te&&re.push(te(U));const ne=U.formattedValue??Y,oe=g.useRef(null),le=v(l,oe),ie=g.useRef(null),ae=A.cellSelection??!1,se=(e=>{const{align:t,showLeftBorder:r,showRightBorder:n,pinnedPosition:l,isEditable:i,isSelected:a,isSelectionMode:s,classes:c}=e,u={root:["cell",`cell--text${o(t)}`,a&&"selected",i&&"cell--editable",r&&"cell--withLeftBorder",n&&"cell--withRightBorder",l===Ro.LEFT&&"cell--pinnedLeft",l===Ro.RIGHT&&"cell--pinnedRight",s&&!i&&"cell--selectionMode"]};return I(u,lr,c)})({align:d,showLeftBorder:R,showRightBorder:S,isEditable:Q,classes:A.classes,pinnedPosition:x,isSelected:K,isSelectionMode:ae}),ce=g.useCallback((e=>t=>{const r=z.current.getCellParams(s,V||"");z.current.publishEvent(e,r,t),E&&E(t)}),[z,V,E,s]),ue=g.useCallback((e=>t=>{const r=z.current.getCellParams(s,V||"");z.current.publishEvent(e,r,t),P&&P(t)}),[z,V,P,s]),de=g.useCallback(((e,t)=>r=>{if(!z.current.getRow(s))return;const n=z.current.getCellParams(s,V||"");z.current.publishEvent(e,n,r),t&&t(r)}),[z,V,s]),pe=_[s]?.[V]??!1,fe=q[s]?.[V]??1,ge=g.useMemo((()=>{if(C)return{padding:0,opacity:0,width:0,height:0,border:0};const e=os(u({"--width":`${f}px`},h),B,x,y),t=x===Ro.LEFT,r=x===Ro.RIGHT;return fe>1&&(e.height=`calc(var(--height) * ${fe})`,e.zIndex=10,(t||r)&&(e.zIndex=40)),e}),[f,C,h,y,x,B,fe]);if(g.useEffect((()=>{if(!X||W===ln.Edit)return;const e=$(z.current.rootElementRef.current);if(oe.current&&!oe.current.contains(e.activeElement)){const e=oe.current.querySelector('[tabindex="0"]'),t=ie.current||e||oe.current;if(void 0===rs&&document.createElement("div").focus({get preventScroll(){return rs=!0,!1}}),rs)t.focus({preventScroll:!0});else{const e=z.current.getScrollPosition();t.focus(),z.current.scroll(e)}}}),[X,W,z]),pe)return r.jsx("div",{"data-colindex":p,role:"presentation",style:u({width:"var(--width)"},ge)});let me,he,be=L.onFocus;if(null===N&&i.renderCell&&(me=i.renderCell(U)),null!==N&&i.renderEditCell){const e=z.current.getRowWithUpdatedValues(s,i.field),t=F(N,is),r=i.valueFormatter?i.valueFormatter(N.value,e,i,z):U.formattedValue,n=u({},U,{row:e,formattedValue:r},t);me=i.renderEditCell(n),re.push(ir["cell--editing"]),re.push(ee?.["cell--editing"])}if(void 0===me){const e=ne?.toString();me=e,he=e}g.isValidElement(me)&&J&&(me=g.cloneElement(me,{focusElementRef:ie}));const we=w?null:{onDragEnter:de("cellDragEnter",O),onDragOver:de("cellDragOver",j)};return r.jsx("div",u({className:n(se.root,re,m),role:"gridcell","data-field":V,"data-colindex":p,"aria-colindex":p+1,"aria-colspan":b,"aria-rowspan":fe,style:ge,title:he,tabIndex:Z,onClick:de("cellClick",M),onDoubleClick:de("cellDoubleClick",k),onMouseOver:de("cellMouseOver",H),onMouseDown:ue("cellMouseDown"),onMouseUp:ce("cellMouseUp"),onKeyDown:de("cellKeyDown",T),onKeyUp:de("cellKeyUp",D)},we,L,{onFocus:be,ref:le,children:me}))})),ss=or(as),cs=["field","type","align","width","height","empty","style","className"],us="1.3em",ds=[40,80],ps={number:[40,60],string:[40,80],date:[40,60],dateTime:[60,80],singleSelect:[40,80]},fs=function(){const e=(t=12345,()=>{let e=t+=1831565813;return e=Math.imul(e^e>>>15,1|e),e^=e+Math.imul(e^e>>>7,61|e),((e^e>>>14)>>>0)/4294967296});var t;return(t,r)=>t+(r-t)*e()}(),gs=or((function(e){const{field:t,type:l,align:i,width:a,height:s,empty:c=!1,style:d,className:p}=e,f=F(e,cs),m=pt(),h=(e=>{const{align:t,classes:r,empty:n}=e,l={root:["cell","cellSkeleton",`cell--text${t?o(t):"Left"}`,n&&"cellEmpty"]};return I(l,lr,r)})({classes:m.classes,align:i,empty:c}),b=g.useMemo((()=>{if("boolean"===l||"actions"===l)return{variant:"circular",width:us,height:us};const[e,t]=l?ps[l]??ds:ds;return{variant:"text",width:`${Math.round(fs(e,t))}%`,height:"1.2em"}}),[l]);return r.jsx("div",u({"data-field":t,className:n(h.root,p),style:u({height:s,maxWidth:a,minWidth:a},d)},f,{children:!c&&r.jsx(m.slots.baseSkeleton,u({},b))}))})),ms={width:3,rx:1.5,x:10.5},hs=e=>e.current.state.dimensions.hasScrollX&&(!e.current.state.dimensions.hasScrollY||0===e.current.state.dimensions.scrollbarSize),bs=R("div",{name:"MuiDataGrid",slot:"Root",overridesResolver:(e,t)=>[t.root,{[`&.${ir.autoHeight}`]:t.autoHeight},{[`&.${ir.autosizing}`]:t.autosizing},{[`&.${ir["root--densityStandard"]}`]:t["root--densityStandard"]},{[`&.${ir["root--densityComfortable"]}`]:t["root--densityComfortable"]},{[`&.${ir["root--densityCompact"]}`]:t["root--densityCompact"]},{[`&.${ir["root--disableUserSelection"]}`]:t["root--disableUserSelection"]},{[`&.${ir["root--noToolbar"]}`]:t["root--noToolbar"]},{[`&.${ir.withVerticalBorder}`]:t.withVerticalBorder},{[`& .${ir.actionsCell}`]:t.actionsCell},{[`& .${ir.booleanCell}`]:t.booleanCell},{[`& .${ir.cell}`]:t.cell},{[`& .${ir["cell--editable"]}`]:t["cell--editable"]},{[`& .${ir["cell--editing"]}`]:t["cell--editing"]},{[`& .${ir["cell--flex"]}`]:t["cell--flex"]},{[`& .${ir["cell--pinnedLeft"]}`]:t["cell--pinnedLeft"]},{[`& .${ir["cell--pinnedRight"]}`]:t["cell--pinnedRight"]},{[`& .${ir["cell--rangeBottom"]}`]:t["cell--rangeBottom"]},{[`& .${ir["cell--rangeLeft"]}`]:t["cell--rangeLeft"]},{[`& .${ir["cell--rangeRight"]}`]:t["cell--rangeRight"]},{[`& .${ir["cell--rangeTop"]}`]:t["cell--rangeTop"]},{[`& .${ir["cell--selectionMode"]}`]:t["cell--selectionMode"]},{[`& .${ir["cell--textCenter"]}`]:t["cell--textCenter"]},{[`& .${ir["cell--textLeft"]}`]:t["cell--textLeft"]},{[`& .${ir["cell--textRight"]}`]:t["cell--textRight"]},{[`& .${ir["cell--withLeftBorder"]}`]:t["cell--withLeftBorder"]},{[`& .${ir["cell--withRightBorder"]}`]:t["cell--withRightBorder"]},{[`& .${ir.cellCheckbox}`]:t.cellCheckbox},{[`& .${ir.cellEmpty}`]:t.cellEmpty},{[`& .${ir.cellOffsetLeft}`]:t.cellOffsetLeft},{[`& .${ir.cellSkeleton}`]:t.cellSkeleton},{[`& .${ir.checkboxInput}`]:t.checkboxInput},{[`& .${ir.columnHeader}`]:t.columnHeader},{[`& .${ir["columnHeader--alignCenter"]}`]:t["columnHeader--alignCenter"]},{[`& .${ir["columnHeader--alignLeft"]}`]:t["columnHeader--alignLeft"]},{[`& .${ir["columnHeader--alignRight"]}`]:t["columnHeader--alignRight"]},{[`& .${ir["columnHeader--dragging"]}`]:t["columnHeader--dragging"]},{[`& .${ir["columnHeader--emptyGroup"]}`]:t["columnHeader--emptyGroup"]},{[`& .${ir["columnHeader--filledGroup"]}`]:t["columnHeader--filledGroup"]},{[`& .${ir["columnHeader--filtered"]}`]:t["columnHeader--filtered"]},{[`& .${ir["columnHeader--last"]}`]:t["columnHeader--last"]},{[`& .${ir["columnHeader--lastUnpinned"]}`]:t["columnHeader--lastUnpinned"]},{[`& .${ir["columnHeader--moving"]}`]:t["columnHeader--moving"]},{[`& .${ir["columnHeader--numeric"]}`]:t["columnHeader--numeric"]},{[`& .${ir["columnHeader--pinnedLeft"]}`]:t["columnHeader--pinnedLeft"]},{[`& .${ir["columnHeader--pinnedRight"]}`]:t["columnHeader--pinnedRight"]},{[`& .${ir["columnHeader--siblingFocused"]}`]:t["columnHeader--siblingFocused"]},{[`& .${ir["columnHeader--sortable"]}`]:t["columnHeader--sortable"]},{[`& .${ir["columnHeader--sorted"]}`]:t["columnHeader--sorted"]},{[`& .${ir["columnHeader--withLeftBorder"]}`]:t["columnHeader--withLeftBorder"]},{[`& .${ir["columnHeader--withRightBorder"]}`]:t["columnHeader--withRightBorder"]},{[`& .${ir.columnHeaderCheckbox}`]:t.columnHeaderCheckbox},{[`& .${ir.columnHeaderDraggableContainer}`]:t.columnHeaderDraggableContainer},{[`& .${ir.columnHeaderTitleContainer}`]:t.columnHeaderTitleContainer},{[`& .${ir.columnHeaderTitleContainerContent}`]:t.columnHeaderTitleContainerContent},{[`& .${ir.columnSeparator}`]:t.columnSeparator},{[`& .${ir["columnSeparator--resizable"]}`]:t["columnSeparator--resizable"]},{[`& .${ir["columnSeparator--resizing"]}`]:t["columnSeparator--resizing"]},{[`& .${ir["columnSeparator--sideLeft"]}`]:t["columnSeparator--sideLeft"]},{[`& .${ir["columnSeparator--sideRight"]}`]:t["columnSeparator--sideRight"]},{[`& .${ir["container--bottom"]}`]:t["container--bottom"]},{[`& .${ir["container--top"]}`]:t["container--top"]},{[`& .${ir.detailPanelToggleCell}`]:t.detailPanelToggleCell},{[`& .${ir["detailPanelToggleCell--expanded"]}`]:t["detailPanelToggleCell--expanded"]},{[`& .${ir.editBooleanCell}`]:t.editBooleanCell},{[`& .${ir.filterIcon}`]:t.filterIcon},{[`& .${ir["filler--borderBottom"]}`]:t["filler--borderBottom"]},{[`& .${ir["filler--pinnedLeft"]}`]:t["filler--pinnedLeft"]},{[`& .${ir["filler--pinnedRight"]}`]:t["filler--pinnedRight"]},{[`& .${ir.groupingCriteriaCell}`]:t.groupingCriteriaCell},{[`& .${ir.groupingCriteriaCellLoadingContainer}`]:t.groupingCriteriaCellLoadingContainer},{[`& .${ir.groupingCriteriaCellToggle}`]:t.groupingCriteriaCellToggle},{[`& .${ir.headerFilterRow}`]:t.headerFilterRow},{[`& .${ir.iconSeparator}`]:t.iconSeparator},{[`& .${ir.menuIcon}`]:t.menuIcon},{[`& .${ir.menuIconButton}`]:t.menuIconButton},{[`& .${ir.menuList}`]:t.menuList},{[`& .${ir.menuOpen}`]:t.menuOpen},{[`& .${ir.overlayWrapperInner}`]:t.overlayWrapperInner},{[`& .${ir.pinnedRows}`]:t.pinnedRows},{[`& .${ir["pinnedRows--bottom"]}`]:t["pinnedRows--bottom"]},{[`& .${ir["pinnedRows--top"]}`]:t["pinnedRows--top"]},{[`& .${ir.row}`]:t.row},{[`& .${ir["row--borderBottom"]}`]:t["row--borderBottom"]},{[`& .${ir["row--detailPanelExpanded"]}`]:t["row--detailPanelExpanded"]},{[`& .${ir["row--dragging"]}`]:t["row--dragging"]},{[`& .${ir["row--dynamicHeight"]}`]:t["row--dynamicHeight"]},{[`& .${ir["row--editable"]}`]:t["row--editable"]},{[`& .${ir["row--editing"]}`]:t["row--editing"]},{[`& .${ir["row--firstVisible"]}`]:t["row--firstVisible"]},{[`& .${ir["row--lastVisible"]}`]:t["row--lastVisible"]},{[`& .${ir.rowReorderCell}`]:t.rowReorderCell},{[`& .${ir["rowReorderCell--draggable"]}`]:t["rowReorderCell--draggable"]},{[`& .${ir.rowReorderCellContainer}`]:t.rowReorderCellContainer},{[`& .${ir.rowReorderCellPlaceholder}`]:t.rowReorderCellPlaceholder},{[`& .${ir.rowSkeleton}`]:t.rowSkeleton},{[`& .${ir.scrollbar}`]:t.scrollbar},{[`& .${ir["scrollbar--horizontal"]}`]:t["scrollbar--horizontal"]},{[`& .${ir["scrollbar--vertical"]}`]:t["scrollbar--vertical"]},{[`& .${ir.scrollbarFiller}`]:t.scrollbarFiller},{[`& .${ir["scrollbarFiller--borderBottom"]}`]:t["scrollbarFiller--borderBottom"]},{[`& .${ir["scrollbarFiller--borderTop"]}`]:t["scrollbarFiller--borderTop"]},{[`& .${ir["scrollbarFiller--header"]}`]:t["scrollbarFiller--header"]},{[`& .${ir["scrollbarFiller--pinnedRight"]}`]:t["scrollbarFiller--pinnedRight"]},{[`& .${ir.sortIcon}`]:t.sortIcon},{[`& .${ir.treeDataGroupingCell}`]:t.treeDataGroupingCell},{[`& .${ir.treeDataGroupingCellLoadingContainer}`]:t.treeDataGroupingCellLoadingContainer},{[`& .${ir.treeDataGroupingCellToggle}`]:t.treeDataGroupingCellToggle},{[`& .${ir.withBorderColor}`]:t.withBorderColor}]})((()=>{const e=Fi(),t=zt(e,hs),r=yr.colors.background.base,n=yr.header.background.base,o=yr.cell.background.pinned,l=ws(yr.colors.interactive.hover,1),i=yr.colors.interactive.hoverOpacity,a=yr.colors.interactive.selected,s=yr.colors.interactive.selectedOpacity,c=a,u=`calc(${s} + ${i})`,d=Cs(r,l,i),p=Cs(r,a,s),f=Cs(r,c,u),g=Cs(o,l,i),m=Cs(o,a,s),h=Cs(o,c,u),b=e=>({[`& .${ir["cell--pinnedLeft"]}, & .${ir["cell--pinnedRight"]}`]:{backgroundColor:e,"&.Mui-selected":{backgroundColor:Cs(e,p,s),"&:hover":{backgroundColor:Cs(e,f,u)}}}}),w=b(g),C=b(m),v=b(h),y={backgroundColor:p,"&:hover":{backgroundColor:f,"@media (hover: none)":{backgroundColor:p}}};return{"--unstable_DataGrid-radius":yr.radius.base,"--unstable_DataGrid-headWeight":yr.typography.fontWeight.medium,"--DataGrid-rowBorderColor":yr.colors.border.base,"--DataGrid-cellOffsetMultiplier":2,"--DataGrid-width":"0px","--DataGrid-hasScrollX":"0","--DataGrid-hasScrollY":"0","--DataGrid-scrollbarSize":"10px","--DataGrid-rowWidth":"0px","--DataGrid-columnsTotalWidth":"0px","--DataGrid-leftPinnedWidth":"0px","--DataGrid-rightPinnedWidth":"0px","--DataGrid-headerHeight":"0px","--DataGrid-headersTotalHeight":"0px","--DataGrid-topContainerHeight":"0px","--DataGrid-bottomContainerHeight":"0px",flex:1,boxSizing:"border-box",position:"relative",borderWidth:"1px",borderStyle:"solid",borderColor:yr.colors.border.base,borderRadius:"var(--unstable_DataGrid-radius)",backgroundColor:yr.colors.background.base,color:yr.colors.foreground.base,font:yr.typography.font.body,outline:"none",height:"100%",display:"flex",minWidth:0,minHeight:0,flexDirection:"column",overflow:"hidden",overflowAnchor:"none",transform:"translate(0, 0)",[`.${ir.main} > *:first-child/* emotion-disable-server-rendering-unsafe-selector-warning-please-do-not-use-this-the-warning-exists-for-a-reason */`]:{borderTopLeftRadius:"var(--unstable_DataGrid-radius)",borderTopRightRadius:"var(--unstable_DataGrid-radius)"},[`&.${ir.autoHeight}`]:{height:"auto"},[`&.${ir.autosizing}`]:{[`& .${ir.columnHeaderTitleContainerContent} > *`]:{overflow:"visible !important"},"@media (hover: hover)":{[`& .${ir.menuIcon}`]:{width:"0 !important",visibility:"hidden !important"}},[`& .${ir.cell}`]:{overflow:"visible !important",whiteSpace:"nowrap",minWidth:"max-content !important",maxWidth:"max-content !important"},[`& .${ir.groupingCriteriaCell}`]:{width:"unset"},[`& .${ir.treeDataGroupingCell}`]:{width:"unset"}},[`&.${ir.withSidePanel}`]:{flexDirection:"row"},[`& .${ir.mainContent}`]:{display:"flex",flexDirection:"column",overflow:"hidden",flex:1},[`& .${ir.columnHeader}, & .${ir.cell}`]:{WebkitTapHighlightColor:"transparent",padding:"0 10px",boxSizing:"border-box"},[`& .${ir.columnHeader}:focus-within, & .${ir.cell}:focus-within`]:{outline:`solid ${ws(yr.colors.interactive.focus,.5)} 1px`,outlineOffset:-1},[`& .${ir.columnHeader}:focus, & .${ir.cell}:focus`]:{outline:`solid ${yr.colors.interactive.focus} 1px`,outlineOffset:-1},[`& .${ir.columnHeader}:focus,\n      & .${ir["columnHeader--withLeftBorder"]},\n      & .${ir["columnHeader--withRightBorder"]},\n      & .${ir["columnHeader--siblingFocused"]},\n      & .${ir["virtualScroller--hasScrollX"]} .${ir["columnHeader--lastUnpinned"]},\n      & .${ir["virtualScroller--hasScrollX"]} .${ir["columnHeader--last"]}\n      `]:{[`& .${ir.columnSeparator}`]:{opacity:0},"@media (hover: none)":{[`& .${ir["columnSeparator--resizable"]}`]:{opacity:1}},[`& .${ir["columnSeparator--resizable"]}:hover`]:{opacity:1}},[`&.${ir["root--noToolbar"]} [aria-rowindex="1"] [aria-colindex="1"]`]:{borderTopLeftRadius:"calc(var(--unstable_DataGrid-radius) - 1px)"},[`&.${ir["root--noToolbar"]} [aria-rowindex="1"] .${ir["columnHeader--last"]}`]:{borderTopRightRadius:t?"calc(var(--unstable_DataGrid-radius) - 1px)":void 0},[`& .${ir.columnHeaderCheckbox}, & .${ir.cellCheckbox}`]:{padding:0,justifyContent:"center",alignItems:"center"},[`& .${ir.columnHeader}`]:{position:"relative",display:"flex",alignItems:"center",backgroundColor:n},[`& .${ir["columnHeader--filter"]}`]:{paddingTop:8,paddingBottom:8,paddingRight:5,minHeight:"min-content",overflow:"hidden"},[`& .${ir["virtualScroller--hasScrollX"]} .${ir["columnHeader--last"]}`]:{overflow:"hidden"},[`& .${ir["pivotPanelField--sorted"]} .${ir.iconButtonContainer},\n      & .${ir["columnHeader--sorted"]} .${ir.iconButtonContainer},\n      & .${ir["columnHeader--filtered"]} .${ir.iconButtonContainer}`]:{visibility:"visible",width:"auto"},[`& .${ir.pivotPanelField}:not(.${ir["pivotPanelField--sorted"]}) .${ir.sortButton},\n      & .${ir.columnHeader}:not(.${ir["columnHeader--sorted"]}) .${ir.sortButton}`]:{opacity:0,transition:yr.transition(["opacity"],{duration:yr.transitions.duration.short})},[`& .${ir.columnHeaderTitleContainer}`]:{display:"flex",alignItems:"center",gap:yr.spacing(.25),minWidth:0,flex:1,whiteSpace:"nowrap",overflow:"hidden"},[`& .${ir.columnHeaderTitleContainerContent}`]:{overflow:"hidden",display:"flex",alignItems:"center"},[`& .${ir["columnHeader--filledGroup"]} .${ir.columnHeaderTitleContainer}`]:{borderBottomWidth:"1px",borderBottomStyle:"solid",boxSizing:"border-box"},[`& .${ir.sortIcon}, & .${ir.filterIcon}`]:{fontSize:"inherit"},[`& .${ir["columnHeader--sortable"]}`]:{cursor:"pointer"},[`& .${ir["columnHeader--alignCenter"]} .${ir.columnHeaderTitleContainer}`]:{justifyContent:"center"},[`& .${ir["columnHeader--alignRight"]} .${ir.columnHeaderDraggableContainer}, & .${ir["columnHeader--alignRight"]} .${ir.columnHeaderTitleContainer}`]:{flexDirection:"row-reverse"},[`& .${ir["columnHeader--alignCenter"]} .${ir.menuIcon}`]:{marginLeft:"auto"},[`& .${ir["columnHeader--alignRight"]} .${ir.menuIcon}`]:{marginRight:"auto",marginLeft:-5},[`& .${ir["columnHeader--moving"]}`]:{backgroundColor:d},[`& .${ir["columnHeader--pinnedLeft"]}, & .${ir["columnHeader--pinnedRight"]}`]:{position:"sticky",zIndex:40,background:yr.header.background.base},[`& .${ir.columnSeparator}`]:{position:"absolute",overflow:"hidden",zIndex:30,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",maxWidth:10,color:yr.colors.border.base},[`& .${ir.columnHeaders}`]:{width:"var(--DataGrid-rowWidth)",backgroundColor:n},"@media (hover: hover)":{[`& .${ir.columnHeader}:hover`]:{[`& .${ir.menuIcon}`]:{width:"auto",visibility:"visible"},[`& .${ir.iconButtonContainer}`]:{visibility:"visible",width:"auto"}},[`& .${ir.columnHeader}:not(.${ir["columnHeader--sorted"]}):hover .${ir.sortButton},\n        & .${ir.pivotPanelField}:not(.${ir["pivotPanelField--sorted"]}):hover .${ir.sortButton},\n        & .${ir.pivotPanelField}:not(.${ir["pivotPanelField--sorted"]}) .${ir.sortButton}:focus-visible`]:{opacity:.5}},"@media (hover: none)":{[`& .${ir.columnHeader} .${ir.menuIcon}`]:{width:"auto",visibility:"visible"},[`& .${ir.columnHeader}:focus,\n        & .${ir["columnHeader--siblingFocused"]}`]:{[`.${ir["columnSeparator--resizable"]}`]:{color:yr.colors.foreground.accent}},[`& .${ir.pivotPanelField}:not(.${ir["pivotPanelField--sorted"]}) .${ir.sortButton}`]:{opacity:.5}},[`& .${ir["columnSeparator--sideLeft"]}`]:{left:-5},[`& .${ir["columnSeparator--sideRight"]}`]:{right:-5},[`& .${ir["columnHeader--withRightBorder"]} .${ir["columnSeparator--sideLeft"]}`]:{left:-5.5},[`& .${ir["columnHeader--withRightBorder"]} .${ir["columnSeparator--sideRight"]}`]:{right:-5.5},[`& .${ir["columnSeparator--resizable"]}`]:{cursor:"col-resize",touchAction:"none",[`&.${ir["columnSeparator--resizing"]}`]:{color:yr.colors.foreground.accent},"@media (hover: none)":{[`& .${ir.iconSeparator} rect`]:ms},"@media (hover: hover)":{"&:hover":{color:yr.colors.foreground.accent,[`& .${ir.iconSeparator} rect`]:ms}},"& svg":{pointerEvents:"none"}},[`& .${ir.iconSeparator}`]:{color:"inherit",transition:yr.transition(["color","width"],{duration:yr.transitions.duration.short})},[`& .${ir.menuIcon}`]:{width:0,visibility:"hidden",fontSize:20,marginRight:-5,display:"flex",alignItems:"center"},[`.${ir.menuOpen}`]:{visibility:"visible",width:"auto"},[`& .${ir.headerFilterRow}`]:{[`& .${ir.columnHeader}`]:{boxSizing:"border-box",borderBottom:"1px solid var(--DataGrid-rowBorderColor)"}},[`& .${ir["row--borderBottom"]} .${ir.columnHeader},\n      & .${ir["row--borderBottom"]} .${ir.filler},\n      & .${ir["row--borderBottom"]} .${ir.scrollbarFiller}`]:{borderBottom:"1px solid var(--DataGrid-rowBorderColor)"},[`& .${ir["row--borderBottom"]} .${ir.cell}`]:{borderBottom:"1px solid var(--rowBorderColor)"},[`.${ir.row}`]:{display:"flex",width:"var(--DataGrid-rowWidth)",breakInside:"avoid","--rowBorderColor":"var(--DataGrid-rowBorderColor)",[`&.${ir["row--firstVisible"]}`]:{"--rowBorderColor":"transparent"},"&:hover":{backgroundColor:d,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${ir.rowSkeleton}:hover`]:{backgroundColor:"transparent"},"&.Mui-selected":y},[`& .${ir["container--top"]}, & .${ir["container--bottom"]}`]:{"[role=row]":{background:yr.colors.background.base}},[`& .${ir.cell}`]:{flex:"0 0 auto",height:"var(--height)",width:"var(--width)",lineHeight:"calc(var(--height) - 1px)",boxSizing:"border-box",borderTop:"1px solid var(--rowBorderColor)",overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis","&.Mui-selected":y},[`& .${ir["virtualScrollerContent--overflowed"]} .${ir["row--lastVisible"]} .${ir.cell}`]:{borderTopColor:"transparent"},[`& .${ir.pinnedRows} .${ir.row}, .${ir.aggregationRowOverlayWrapper} .${ir.row}`]:{backgroundColor:o,"&:hover":{backgroundColor:g}},[`& .${ir["pinnedRows--top"]} :first-of-type`]:{[`& .${ir.cell}, .${ir.scrollbarFiller}`]:{borderTop:"none"}},[`&.${ir["root--disableUserSelection"]} .${ir.cell}`]:{userSelect:"none"},[`& .${ir["row--dynamicHeight"]} > .${ir.cell}`]:{whiteSpace:"initial",lineHeight:"inherit"},[`& .${ir.cellEmpty}`]:{flex:1,padding:0,height:"unset"},[`& .${ir.cell}.${ir["cell--selectionMode"]}`]:{cursor:"default"},[`& .${ir.cell}.${ir["cell--editing"]}`]:{padding:1,display:"flex",boxShadow:yr.shadows.base,backgroundColor:yr.colors.background.overlay,"&:focus-within":{outline:`1px solid ${yr.colors.interactive.focus}`,outlineOffset:-1}},[`& .${ir["row--editing"]}`]:{boxShadow:yr.shadows.base},[`& .${ir["row--editing"]} .${ir.cell}`]:{boxShadow:"none",backgroundColor:yr.colors.background.overlay},[`& .${ir.editBooleanCell}`]:{display:"flex",height:"100%",width:"100%",alignItems:"center",justifyContent:"center"},[`& .${ir.booleanCell}[data-value="true"]`]:{color:yr.colors.foreground.muted},[`& .${ir.booleanCell}[data-value="false"]`]:{color:yr.colors.foreground.disabled},[`& .${ir.actionsCell}`]:{display:"inline-flex",alignItems:"center",gridGap:yr.spacing(1)},[`& .${ir.rowReorderCell}`]:{display:"inline-flex",flex:1,alignItems:"center",justifyContent:"center",opacity:yr.colors.interactive.disabledOpacity},[`& .${ir["rowReorderCell--draggable"]}`]:{cursor:"move",opacity:1},[`& .${ir.rowReorderCellContainer}`]:{padding:0,display:"flex",alignItems:"stretch"},[`.${ir.withBorderColor}`]:{borderColor:yr.colors.border.base},[`& .${ir["cell--withLeftBorder"]}, & .${ir["columnHeader--withLeftBorder"]}`]:{borderLeftColor:"var(--DataGrid-rowBorderColor)",borderLeftWidth:"1px",borderLeftStyle:"solid"},[`& .${ir["cell--withRightBorder"]}, & .${ir["columnHeader--withRightBorder"]}`]:{borderRightColor:"var(--DataGrid-rowBorderColor)",borderRightWidth:"1px",borderRightStyle:"solid"},[`& .${ir["cell--flex"]}`]:{display:"flex",alignItems:"center",lineHeight:"inherit"},[`& .${ir["cell--textLeft"]}`]:{textAlign:"left",justifyContent:"flex-start"},[`& .${ir["cell--textRight"]}`]:{textAlign:"right",justifyContent:"flex-end"},[`& .${ir["cell--textCenter"]}`]:{textAlign:"center",justifyContent:"center"},[`& .${ir["cell--pinnedLeft"]}, & .${ir["cell--pinnedRight"]}`]:{position:"sticky",zIndex:30,background:yr.cell.background.pinned,"&.Mui-selected":{backgroundColor:m}},[`& .${ir.row}`]:{"&:hover":w,"&.Mui-selected":C,"&.Mui-selected:hover":v},[`& .${ir.cellOffsetLeft}`]:{flex:"0 0 auto",display:"inline-block"},[`& .${ir.cellSkeleton}`]:{flex:"0 0 auto",height:"100%",display:"inline-flex",alignItems:"center"},[`& .${ir.columnHeaderDraggableContainer}`]:{display:"flex",width:"100%",height:"100%"},[`& .${ir.rowReorderCellPlaceholder}`]:{display:"none"},[`& .${ir["columnHeader--dragging"]}, & .${ir["row--dragging"]}`]:{background:yr.colors.background.overlay,padding:"0 12px",borderRadius:"var(--unstable_DataGrid-radius)",opacity:yr.colors.interactive.disabledOpacity},[`& .${ir["row--dragging"]}`]:{background:yr.colors.background.overlay,padding:"0 12px",borderRadius:"var(--unstable_DataGrid-radius)",opacity:yr.colors.interactive.disabledOpacity,[`& .${ir.rowReorderCellPlaceholder}`]:{display:"flex"}},[`& .${ir.treeDataGroupingCell}`]:{display:"flex",alignItems:"center",width:"100%"},[`& .${ir.treeDataGroupingCellToggle}`]:{flex:"0 0 28px",alignSelf:"stretch",marginRight:yr.spacing(2)},[`& .${ir.treeDataGroupingCellLoadingContainer}, .${ir.groupingCriteriaCellLoadingContainer}`]:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},[`& .${ir.groupingCriteriaCell}`]:{display:"flex",alignItems:"center",width:"100%"},[`& .${ir.groupingCriteriaCellToggle}`]:{flex:"0 0 28px",alignSelf:"stretch",marginRight:yr.spacing(2)},[`.${ir.scrollbarFiller}`]:{minWidth:"calc(var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize))",alignSelf:"stretch",[`&.${ir["scrollbarFiller--borderTop"]}`]:{borderTop:"1px solid var(--DataGrid-rowBorderColor)"},[`&.${ir["scrollbarFiller--borderBottom"]}`]:{borderBottom:"1px solid var(--DataGrid-rowBorderColor)"},[`&.${ir["scrollbarFiller--pinnedRight"]}`]:{backgroundColor:yr.cell.background.pinned,position:"sticky",right:0}},[`& .${ir.filler}`]:{flex:"1 0 auto"},[`& .${ir["filler--borderBottom"]}`]:{borderBottom:"1px solid var(--DataGrid-rowBorderColor)"},[`& .${ir["main--hiddenContent"]}`]:{[`& .${ir.virtualScrollerContent}`]:{position:"fixed",visibility:"hidden"},[`& .${ir["scrollbar--vertical"]}, & .${ir.pinnedRows}, & .${ir.virtualScroller} > .${ir.filler}`]:{display:"none"}}}}));function ws(e,t){return`rgba(from ${e} r g b / ${t})`}function Cs(e,t,r){return`color-mix(in srgb,${e}, ${t} calc(${r} * 100%))`}const vs=()=>()=>{},ys=()=>!1,xs=()=>!0,Ss=g.createContext(void 0);function Rs(){const e=g.useContext(Ss);if(void 0===e)throw new Error("MUI X: Missing context.");return e}function Is({children:e}){const t=g.useRef(null),n=g.useRef(null),o=g.useRef(null),l=g.useMemo((()=>({columnsPanelTriggerRef:t,filterPanelTriggerRef:n,aiAssistantPanelTriggerRef:o})),[]);return r.jsx(Ss.Provider,{value:l,children:e})}function Ms(){const e=ut(),t=zt(e,Mn),n=pt(),o=zt(e,Va),{columnsPanelTriggerRef:l,filterPanelTriggerRef:i,aiAssistantPanelTriggerRef:a}=Rs(),s=e.current.unstable_applyPipeProcessors("preferencePanel",null,o.openedPanelValue??Ga.filters);let c=null;switch(o.openedPanelValue){case Ga.filters:c=i.current;break;case Ga.columns:c=l.current;break;case Ga.aiAssistant:c=a.current}return r.jsx(n.slots.panel,u({id:o.panelId,open:t.length>0&&o.open,"aria-labelledby":o.labelId,target:c,onClose:()=>e.current.hidePreferences()},n.slotProps?.panel,{children:s}))}function ks(){const e=pt();return r.jsxs(g.Fragment,{children:[r.jsx(Ms,{}),e.showToolbar&&r.jsx(e.slots.toolbar,u({},e.slotProps?.toolbar))]})}const Ps=["className","children","sidePanel"],Es=gt((function(e,t){const l=pt(),{className:i,children:a,sidePanel:s}=e,c=F(e,Ps),d=Fi(),p=zt(d,mr),f=d.current.rootElementRef,m=g.useCallback((e=>{null!==e&&d.current.publishEvent("rootMount",e)}),[d]),h=v(f,t,m),b=l,w=((e,t)=>{const{autoHeight:r,classes:n,showCellVerticalBorder:l}=e,i={root:["root",r&&"autoHeight",`root--density${o(t)}`,null===e.slots.toolbar&&"root--noToolbar","withBorderColor",l&&"withVerticalBorder"]};return I(i,lr,n)})(b,p),C=g.useContext(Zn);return Ft.useSyncExternalStore(vs,ys,xs)?null:r.jsxs(bs,u({className:n(w.root,i,C.className,s&&ir.withSidePanel),ownerState:b},c,{ref:h,children:[r.jsxs("div",{className:ir.mainContent,role:"presentation",children:[r.jsx(ks,{}),r.jsx(es,{children:a}),r.jsx(ts,{})]}),s,C.tag]}))})),Fs=or(Es),Hs=["className"],Ts=we("div",{name:"MuiDataGrid",slot:"FooterContainer"})({display:"flex",justifyContent:"space-between",alignItems:"center",minHeight:52,borderTop:"1px solid"}),Ds=gt((function(e,t){const{className:o}=e,l=F(e,Hs),i=pt(),a=(e=>{const{classes:t}=e;return I({root:["footerContainer","withBorderColor"]},lr,t)})(i);return r.jsx(Ts,u({className:n(a.root,o),ownerState:i},l,{ref:t}))})),Os=["className"],$s=we("div",{name:"MuiDataGrid",slot:"Overlay"})({width:"100%",height:"100%",display:"flex",gap:yr.spacing(1),flexDirection:"column",alignSelf:"center",alignItems:"center",justifyContent:"center",textAlign:"center",textWrap:"balance",backgroundColor:yr.colors.background.backdrop}),js=gt((function(e,t){const{className:o}=e,l=F(e,Os),i=pt(),a=(e=>{const{classes:t}=e;return I({root:["overlay"]},lr,t)})(i);return r.jsx($s,u({className:n(a.root,o),ownerState:i},l,{ref:t}))}));function Ls(e,t,r,o={}){return"function"==typeof t?t(r,o):t?(t.props.className&&(r.className=n(t.props.className,r.className)),(t.props.style||r.style)&&(r.style=u({},r.style,t.props.style)),(t.props.sx||r.sx)&&(r.sx=(l=r.sx,i=t.props.sx,l&&i?(Array.isArray(l)?l:[l]).concat(Array.isArray(i)?i:[i]):l||i)),g.cloneElement(t,r)):g.createElement(e,r);var l,i}const zs=g.createContext(void 0),As=["render","className"],Bs=we("div",{name:"MuiDataGrid",slot:"Toolbar"})({flex:0,display:"flex",alignItems:"center",justifyContent:"end",gap:yr.spacing(.25),padding:yr.spacing(.75),minHeight:52,boxSizing:"border-box",borderBottom:`1px solid ${yr.colors.border.base}`}),Vs=gt((function(e,t){const{render:o,className:l}=e,i=F(e,As),a=pt(),s=(e=>{const{classes:t}=e;return I({root:["toolbar"]},lr,t)})(a),[c,d]=g.useState(null),[p,f]=g.useState([]),m=g.useCallback(((e,t)=>{f((r=>[...r,{id:e,ref:t}]))}),[]),h=g.useCallback((e=>{f((t=>t.filter((t=>t.id!==e)))),c===e&&d(null)}),[c]),b=g.useCallback(((e,t,r=!0)=>{let n=e;const o=p.length;for(let l=0;l<o;l+=1){if(n+=t,n>=o){if(!r)return-1;n=0}else if(n<0){if(!r)return-1;n=o-1}if(!p[n].ref.current?.disabled&&"true"!==p[n].ref.current?.ariaDisabled)return n}return-1}),[p]),w=g.useCallback((e=>{if(!c)return;const t=p.findIndex((e=>e.id===c));let r=-1;if("ArrowRight"===e.key?(e.preventDefault(),r=b(t,1)):"ArrowLeft"===e.key?(e.preventDefault(),r=b(t,-1)):"Home"===e.key?(e.preventDefault(),r=b(-1,1,!1)):"End"===e.key&&(e.preventDefault(),r=b(p.length,-1,!1)),r>=0&&r<p.length){const e=p[r];d(e.id),e.ref.current?.focus()}}),[p,c,b]),C=g.useCallback((e=>{c!==e&&d(e)}),[c]),v=g.useCallback((e=>{const t=p.findIndex((t=>t.id===e)),r=b(t,1);if(r>=0&&r<p.length){const e=p[r];d(e.id),e.ref.current?.focus()}}),[p,b]),y=g.useMemo((()=>({focusableItemId:c,registerItem:m,unregisterItem:h,onItemKeyDown:w,onItemFocus:C,onItemDisabled:v})),[c,m,h,w,C,v]),x=Ls(Bs,o,u({role:"toolbar","aria-orientation":"horizontal","aria-label":a.label||void 0,className:n(s.root,l)},i,{ref:t}));return g.useEffect((()=>{p.length>0&&d(p[0].id)}),[p]),r.jsx(zs.Provider,{value:y,children:x})})),Ns=["render","onKeyDown","onFocus","disabled","aria-disabled"],Gs=gt((function(e,t){const{render:n,onKeyDown:o,onFocus:l,disabled:i,"aria-disabled":a}=e,s=F(e,Ns),c=P(),d=pt(),p=g.useRef(null),f=v(p,t),{focusableItemId:m,registerItem:h,unregisterItem:b,onItemKeyDown:w,onItemFocus:C,onItemDisabled:y}=function(){const e=g.useContext(zs);if(void 0===e)throw new Error("MUI X: Missing context. Toolbar subcomponents must be placed within a <Toolbar /> component.");return e}();g.useEffect((()=>(h(c,p),()=>b(c))),[]);const x=g.useRef(i);g.useEffect((()=>{x.current!==i&&!0===i&&y(c,i),x.current=i}),[i,c,y]);const S=g.useRef(a);g.useEffect((()=>{S.current!==a&&!0===a&&y(c,!0),S.current=a}),[a,c,y]);const R=Ls(d.slots.baseIconButton,n,u({},d.slotProps?.baseIconButton,{tabIndex:m===c?0:-1},s,{disabled:i,"aria-disabled":a,onKeyDown:e=>{w(e),o?.(e)},onFocus:e=>{C(c),l?.(e)},ref:f}));return r.jsx(g.Fragment,{children:R})})),Ws=g.memo((e=>{const{colDef:t,open:n,columnMenuId:o,columnMenuButtonId:l,iconButtonRef:i}=e,a=ut(),s=pt(),c=(e=>{const{classes:t,open:r}=e;return I({root:["menuIcon",r&&"menuOpen"],button:["menuIconButton"]},lr,t)})(u({},e,{classes:s.classes})),d=g.useCallback((e=>{e.preventDefault(),e.stopPropagation(),a.current.toggleColumnMenu(t.field)}),[a,t.field]),p=t.headerName??t.field;return r.jsx("div",{className:c.root,children:r.jsx(s.slots.baseTooltip,u({title:a.current.getLocaleText("columnMenuLabel"),enterDelay:1e3},s.slotProps?.baseTooltip,{children:r.jsx(s.slots.baseIconButton,u({ref:i,tabIndex:-1,className:c.button,"aria-label":a.current.getLocaleText("columnMenuAriaLabel")(p),size:"small",onClick:d,"aria-haspopup":"menu","aria-expanded":n,"aria-controls":n?o:void 0,id:l},s.slotProps?.baseIconButton,{children:r.jsx(s.slots.columnMenuIcon,{fontSize:"inherit"})}))}))})}));function Us({columnMenuId:e,columnMenuButtonId:t,ContentComponent:n,contentComponentProps:o,field:l,open:i,target:a,onExited:s}){const c=ut(),d=c.current.getColumn(l),p=y((e=>{e&&(e.stopPropagation(),a?.contains(e.target))||c.current.hideColumnMenu()}));return a&&d?r.jsx(oo,{position:"bottom-"+("right"===d.align?"start":"end"),open:i,target:a,onClose:p,onExited:s,children:r.jsx(n,u({colDef:d,hideMenu:p,open:i,id:e,labelledby:t},o))}):null}function Ks(e,t){return e.closest(`.${t}`)}function _s(e){return e.replace(/["\\]/g,"\\$&")}function qs(e){return`.${ir.row}[data-id="${_s(String(e))}"]`}function Xs(e){return 1===e.target.nodeType&&!e.currentTarget.contains(e.target)}function Qs(e,t){return e.rootElementRef.current.querySelector(`.${ir[t]}`)}const Ys=({api:e,colIndex:t,position:r,filterFn:n})=>{if(null===t)return[];const o=[];return Zs(e).forEach((e=>{e.getAttribute("data-id")&&e.querySelectorAll(`.${ir["left"===r?"cell--pinnedLeft":"cell--pinnedRight"]}`).forEach((e=>{const t=ec(e);null!==t&&n(t)&&o.push(e)}))})),o},Js=({api:e,colIndex:t,position:r,filterFn:n})=>{if(!e.columnHeadersContainerRef?.current)return[];if(null===t)return[];const o=[];return e.columnHeadersContainerRef.current.querySelectorAll(`.${ir["left"===r?"columnHeader--pinnedLeft":"columnHeader--pinnedRight"]}`).forEach((e=>{const t=ec(e);null!==t&&n(t,e)&&o.push(e)})),o};function Zs(e){return e.virtualScrollerRef.current.querySelectorAll(`:scope > div > div > .${ir.row}`)}function ec(e){const t=e.getAttribute("aria-colindex");return t?Number(t)-1:null}const tc=["className","aria-label"],rc=we("div",{name:"MuiDataGrid",slot:"ColumnHeaderTitle"})({textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",fontWeight:"var(--unstable_DataGrid-headWeight)",lineHeight:"normal"}),nc=gt((function(e,t){const{className:o}=e,l=F(e,tc),i=pt(),a=(e=>{const{classes:t}=e;return I({root:["columnHeaderTitle"]},lr,t)})(i);return r.jsx(rc,u({className:n(a.root,o),ownerState:i},l,{ref:t}))}));function oc(e){const{label:t,description:n}=e,o=pt(),l=g.useRef(null),[i,a]=g.useState(""),s=g.useCallback((()=>{if(!n&&l?.current){const r=(e=l.current).scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth;a(r?t:"")}var e}),[n,t]);return r.jsx(o.slots.baseTooltip,u({title:n||i},o.slotProps?.baseTooltip,{children:r.jsx(nc,{onMouseOver:s,ref:l,children:t})}))}const lc=["resizable","resizing","height","side"];var ic=function(e){return e.Left="left",e.Right="right",e}(ic||{});function ac(e){const{height:t,side:n=ic.Right}=e,l=F(e,lc),i=pt(),a=(e=>{const{resizable:t,resizing:r,classes:n,side:l}=e,i={root:["columnSeparator",t&&"columnSeparator--resizable",r&&"columnSeparator--resizing",l&&`columnSeparator--side${o(l)}`],icon:["iconSeparator"]};return I(i,lr,n)})(u({},e,{side:n,classes:i.classes})),s=g.useCallback((e=>{e.preventDefault(),e.stopPropagation()}),[]);return r.jsx("div",u({className:a.root,style:{minHeight:t}},l,{onClick:s,children:r.jsx(i.slots.columnResizeIcon,{className:a.icon})}))}const sc=g.memo(ac),cc=["classes","columnMenuOpen","colIndex","height","isResizing","sortDirection","hasFocus","tabIndex","separatorSide","isDraggable","headerComponent","description","elementId","width","columnMenuIconButton","columnMenu","columnTitleIconButtons","headerClassName","label","resizable","draggableContainerProps","columnHeaderSeparatorProps","style"],uc=gt((function(e,t){const{classes:o,colIndex:l,height:i,isResizing:a,sortDirection:s,hasFocus:c,tabIndex:d,separatorSide:p,isDraggable:f,headerComponent:m,description:h,width:b,columnMenuIconButton:w=null,columnMenu:C=null,columnTitleIconButtons:y=null,headerClassName:x,label:S,resizable:R,draggableContainerProps:I,columnHeaderSeparatorProps:M,style:k}=e,P=F(e,cc),E=Fi(),H=pt(),T=g.useRef(null),D=v(T,t);let O="none";return null!=s&&(O="asc"===s?"ascending":"descending"),g.useLayoutEffect((()=>{const e=E.current.state.columnMenu;if(c&&!e.open){const e=T.current.querySelector('[tabindex="0"]')||T.current;e?.focus(),E.current.columnHeadersContainerRef?.current&&(E.current.columnHeadersContainerRef.current.scrollLeft=0)}}),[E,c]),r.jsxs("div",u({className:n(o.root,x),style:u({},k,{width:b}),role:"columnheader",tabIndex:d,"aria-colindex":l+1,"aria-sort":O},P,{ref:D,children:[r.jsxs("div",u({className:o.draggableContainer,draggable:f,role:"presentation"},I,{children:[r.jsxs("div",{className:o.titleContainer,role:"presentation",children:[r.jsx("div",{className:o.titleContainerContent,children:void 0!==m?m:r.jsx(oc,{label:S,description:h,columnWidth:b})}),y]}),w]})),r.jsx(sc,u({resizable:!H.disableColumnResize&&!!R,resizing:a,height:i,side:p},M)),C]}))})),dc=or((function(e){const{colDef:o,columnMenuOpen:l,colIndex:i,headerHeight:a,isResizing:s,isLast:c,sortDirection:d,sortIndex:p,filterItemsCounter:f,hasFocus:m,tabIndex:h,disableReorder:b,separatorSide:w,showLeftBorder:C,showRightBorder:v,pinnedPosition:y,pinnedOffset:x}=e,S=Fi(),R=pt(),M=t(),k=g.useRef(null),E=P(),F=P(),H=g.useRef(null),[T,D]=g.useState(l),O=g.useMemo((()=>!R.disableColumnReorder&&!b&&!o.disableReorder),[R.disableColumnReorder,b,o.disableReorder]);let $;o.renderHeader&&($=o.renderHeader(S.current.getColumnHeaderParams(o.field)));const j=(e=>{const{colDef:t,classes:r,isDragging:n,sortDirection:o,showRightBorder:l,showLeftBorder:i,filterItemsCounter:a,pinnedPosition:s,isLastUnpinned:c,isSiblingFocused:u}=e,d=null!=o,p=null!=a&&a>0,f="number"===t.type,g={root:["columnHeader","left"===t.headerAlign&&"columnHeader--alignLeft","center"===t.headerAlign&&"columnHeader--alignCenter","right"===t.headerAlign&&"columnHeader--alignRight",t.sortable&&"columnHeader--sortable",n&&"columnHeader--moving",d&&"columnHeader--sorted",p&&"columnHeader--filtered",f&&"columnHeader--numeric","withBorderColor",l&&"columnHeader--withRightBorder",i&&"columnHeader--withLeftBorder",s===Ro.LEFT&&"columnHeader--pinnedLeft",s===Ro.RIGHT&&"columnHeader--pinnedRight",c&&"columnHeader--lastUnpinned",u&&"columnHeader--siblingFocused"],draggableContainer:["columnHeaderDraggableContainer"],titleContainer:["columnHeaderTitleContainer"],titleContainerContent:["columnHeaderTitleContainerContent"]};return I(g,lr,r)})(u({},e,{classes:R.classes,showRightBorder:v,showLeftBorder:C})),L=g.useCallback((e=>t=>{Xs(t)||S.current.publishEvent(e,S.current.getColumnHeaderParams(o.field),t)}),[S,o.field]),z=g.useMemo((()=>({onClick:L("columnHeaderClick"),onContextMenu:L("columnHeaderContextMenu"),onDoubleClick:L("columnHeaderDoubleClick"),onMouseOver:L("columnHeaderOver"),onMouseOut:L("columnHeaderOut"),onMouseEnter:L("columnHeaderEnter"),onMouseLeave:L("columnHeaderLeave"),onKeyDown:L("columnHeaderKeyDown"),onFocus:L("columnHeaderFocus"),onBlur:L("columnHeaderBlur")})),[L]),A=g.useMemo((()=>O?{onDragStart:L("columnHeaderDragStart"),onDragEnter:L("columnHeaderDragEnter"),onDragOver:L("columnHeaderDragOver"),onDragEnd:L("columnHeaderDragEnd")}:{}),[O,L]),B=g.useMemo((()=>({onMouseDown:L("columnSeparatorMouseDown"),onDoubleClick:L("columnSeparatorDoubleClick")})),[L]);g.useEffect((()=>{T||D(l)}),[T,l]);const V=g.useCallback((()=>{D(!1)}),[]),N=!R.disableColumnMenu&&!o.disableColumnMenu&&r.jsx(Ws,{colDef:o,columnMenuId:E,columnMenuButtonId:F,open:T,iconButtonRef:H}),G=r.jsx(Us,{columnMenuId:E,columnMenuButtonId:F,field:o.field,open:l,target:H.current,ContentComponent:R.slots.columnMenu,contentComponentProps:R.slotProps?.columnMenu,onExited:V}),W=o.sortingOrder??R.sortingOrder,U=(o.sortable||null!=d)&&!o.hideSortIcons&&!R.disableColumnSorting,K=r.jsxs(g.Fragment,{children:[!R.disableColumnFilter&&r.jsx(R.slots.columnHeaderFilterIconButton,u({field:o.field,counter:f},R.slotProps?.columnHeaderFilterIconButton)),U&&r.jsx(R.slots.columnHeaderSortIcon,u({field:o.field,direction:d,index:p,sortingOrder:W,disabled:!o.sortable},R.slotProps?.columnHeaderSortIcon))]});g.useLayoutEffect((()=>{const e=S.current.state.columnMenu;if(m&&!e.open){const e=k.current.querySelector('[tabindex="0"]')||k.current;e?.focus(),S.current.columnHeadersContainerRef?.current&&(S.current.columnHeadersContainerRef.current.scrollLeft=0)}}),[S,m]);const _="function"==typeof o.headerClassName?o.headerClassName({field:o.field,colDef:o}):o.headerClassName,q=o.headerName??o.field,X=g.useMemo((()=>os(u({},e.style),M,y,x)),[y,x,e.style,M]);return r.jsx(uc,u({ref:k,classes:j,columnMenuOpen:l,colIndex:i,height:a,isResizing:s,sortDirection:d,hasFocus:m,tabIndex:h,separatorSide:w,isDraggable:O,headerComponent:$,description:o.description,elementId:o.field,width:o.computedWidth,columnMenuIconButton:N,columnTitleIconButtons:K,headerClassName:n(_,c&&ir["columnHeader--last"]),label:q,resizable:!R.disableColumnResize&&!!o.resizable,"data-field":o.field,columnMenu:G,draggableContainerProps:A,columnHeaderSeparatorProps:B,style:X},z))})),pc=["className"],fc=we("div",{name:"MuiDataGrid",slot:"IconButtonContainer"})((()=>({display:"flex",visibility:"hidden",width:0}))),gc=gt((function(e,t){const{className:o}=e,l=F(e,pc),i=pt(),a=(e=>{const{classes:t}=e;return I({root:["iconButtonContainer"]},lr,t)})(i);return r.jsx(fc,u({className:n(a.root,o),ownerState:i},l,{ref:t}))})),mc=["sortingOrder"],hc=g.memo((function(e){const{sortingOrder:t}=e,n=F(e,mc),o=pt(),[l]=t,i="asc"===l?o.slots.columnSortedAscendingIcon:o.slots.columnSortedDescendingIcon;return i?r.jsx(i,u({},n)):null})),bc=["direction","index","sortingOrder","disabled","className"],wc=we(br,{name:"MuiDataGrid",slot:"SortButton"})({transition:yr.transition(["opacity"],{duration:yr.transitions.duration.short,easing:yr.transitions.easing.easeInOut})});function Cc(e){const{direction:t,index:o,sortingOrder:l,disabled:i,className:a}=e,s=F(e,bc),c=ut(),d=pt(),p=u({},e,{classes:d.classes}),f=(e=>{const{classes:t}=e;return I({root:["sortButton"],icon:["sortIcon"]},lr,t)})(p),m=function(e,t,n,o){let l;const i={};return"asc"===t?l=e.columnSortedAscendingIcon:"desc"===t?l=e.columnSortedDescendingIcon:(l=hc,i.sortingOrder=o),l?r.jsx(l,u({fontSize:"small",className:n},i)):null}(d.slots,t,f.icon,l);if(!m)return null;const h=r.jsx(wc,u({as:d.slots.baseIconButton,ownerState:p,"aria-label":c.current.getLocaleText("columnHeaderSortIconLabel"),title:c.current.getLocaleText("columnHeaderSortIconLabel"),size:"small",disabled:i,className:n(f.root,a)},d.slotProps?.baseIconButton,s,{children:m}));return r.jsxs(g.Fragment,{children:[null!=o&&r.jsx(d.slots.baseBadge,{badgeContent:o,color:"default",overlap:"circular",children:h}),null==o&&h]})}function vc(e){return r.jsx(gc,{children:r.jsx(Cc,u({},e,{tabIndex:-1}))})}const yc=g.memo(vc);function xc(e){const{counter:t,field:n,onClick:o}=e,l=ut(),i=pt(),a=(e=>{const{classes:t}=e;return I({icon:["filterIcon"]},lr,t)})(u({},e,{classes:i.classes})),s=P(),c=zt(l,Na,s),d=P(),p=g.useCallback((e=>{e.preventDefault(),e.stopPropagation();const{open:t,openedPanelValue:r}=Va(l);t&&r===Ga.filters?l.current.hideFilterPanel():l.current.showFilterPanel(void 0,d,s),o&&o(l.current.getColumnHeaderParams(n),e)}),[l,n,o,d,s]);if(!t)return null;const f=r.jsx(i.slots.baseIconButton,u({id:s,onClick:p,"aria-label":l.current.getLocaleText("columnHeaderFiltersLabel"),size:"small",tabIndex:-1,"aria-haspopup":"menu","aria-expanded":c,"aria-controls":c?d:void 0},i.slotProps?.baseIconButton,{children:r.jsx(i.slots.columnFilteredIcon,{className:a.icon,fontSize:"small"})}));return r.jsx(i.slots.baseTooltip,u({title:l.current.getLocaleText("columnHeaderFiltersTooltipActive")(t),enterDelay:1e3},i.slotProps?.baseTooltip,{children:r.jsxs(gc,{children:[t>1&&r.jsx(i.slots.baseBadge,{badgeContent:t,color:"default",children:f}),1===t&&f]})}))}const Sc=L,Rc=Sc(r.jsx("path",{d:"M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"})),Ic=Sc(r.jsx("path",{d:"M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"})),Mc=Sc(r.jsx("path",{d:"M8.59 16.59 13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"})),kc=Sc(r.jsx("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"})),Pc=Sc(r.jsx("path",{d:"M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"})),Ec=Sc(r.jsx("path",{d:"M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.72-4.8 5.74-7.39c.51-.66.04-1.61-.79-1.61H5.04c-.83 0-1.3.95-.79 1.61z"})),Fc=Sc(r.jsx("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}));Sc(r.jsx("path",{d:"M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"})),Sc(r.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}));const Hc=Sc(r.jsx("path",{d:"M14.67 5v14H9.33V5zm1 14H21V5h-5.33zm-7.34 0V5H3v14z"})),Tc=Sc(r.jsx("rect",{width:"1",height:"24",x:"11.5",rx:"0.5"})),Dc=Sc(r.jsx("path",{d:"M4 15h16v-2H4v2zm0 4h16v-2H4v2zm0-8h16V9H4v2zm0-6v2h16V5H4z"})),Oc=Sc(r.jsx("path",{d:"M21,8H3V4h18V8z M21,10H3v4h18V10z M21,16H3v4h18V16z"})),$c=Sc(r.jsx("path",{d:"M4 18h17v-6H4v6zM4 5v6h17V5H4z"})),jc=Sc(r.jsx("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),Lc=Sc(r.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),zc=Sc(r.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"})),Ac=Sc(r.jsx("path",{d:"M19 13H5v-2h14v2z"})),Bc=Sc(r.jsx("path",{d:"M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"})),Vc=Sc(r.jsx("path",{d:"M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),Nc=Sc(r.jsx("path",{d:"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"})),Gc=Sc(r.jsx("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),Wc=Sc(r.jsx("path",{d:"M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"})),Uc=Sc(r.jsx("g",{children:r.jsx("path",{d:"M14.67,5v14H9.33V5H14.67z M15.67,19H21V5h-5.33V19z M8.33,19V5H3v14H8.33z"})})),Kc=Sc(r.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z"}));Sc(r.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"}));const _c=Sc(r.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zm2.46-7.12l1.41-1.41L12 12.59l2.12-2.12 1.41 1.41L13.41 14l2.12 2.12-1.41 1.41L12 15.41l-2.12 2.12-1.41-1.41L10.59 14l-2.13-2.12zM15.5 4l-1-1h-5l-1 1H5v2h14V4z"})),qc=Sc(r.jsx("path",{d:"M5 20h14v-2H5zM19 9h-4V3H9v6H5l7 7z"}));function Xc(e){return 1===e.key.length&&!e.ctrlKey&&!e.metaKey}const Qc=e=>0===e.indexOf("Arrow")||0===e.indexOf("Page")||" "===e||"Home"===e||"End"===e,Yc=e=>"Tab"===e||"Escape"===e;function Jc(e){return(e.ctrlKey||e.metaKey)&&"V"===String.fromCharCode(e.keyCode)&&!e.shiftKey&&!e.altKey}const Zc=["hideMenu","colDef","id","labelledby","className","children","open"],eu=R(br)((()=>({minWidth:248}))),tu=gt((function(e,t){const{hideMenu:o,id:l,labelledby:i,className:a,children:s,open:c}=e,d=F(e,Zc),p=pt(),f=g.useCallback((e=>{"Tab"===e.key&&e.preventDefault(),Yc(e.key)&&o(e)}),[o]);return r.jsx(eu,u({as:p.slots.baseMenuList,id:l,className:n(ir.menuList,a),"aria-labelledby":i,onKeyDown:f,autoFocus:c},d,{ref:t,children:s}))})),ru=["displayOrder"];function nu(e){const{colDef:t,onClick:n}=e,o=ut(),l=pt(),i=1===En(o).filter((e=>!0!==e.disableColumnMenu)).length,a=g.useCallback((e=>{i||(o.current.setColumnVisibility(t.field,!1),n(e))}),[o,t.field,n,i]);return l.disableColumnSelector||!1===t.hideable?null:r.jsx(l.slots.baseMenuItem,{onClick:a,disabled:i,iconStart:r.jsx(l.slots.columnMenuHideIcon,{fontSize:"small"}),children:o.current.getLocaleText("columnMenuHideColumn")})}function ou(e){const{onClick:t}=e,n=ut(),o=pt(),l=g.useCallback((e=>{t(e),n.current.showPreferences(Ga.columns)}),[n,t]);return o.disableColumnSelector?null:r.jsx(o.slots.baseMenuItem,{onClick:l,iconStart:r.jsx(o.slots.columnMenuManageColumnsIcon,{fontSize:"small"}),children:n.current.getLocaleText("columnMenuManageColumns")})}const lu=["defaultSlots","defaultSlotProps","slots","slotProps"],iu={columnMenuSortItem:function(e){const{colDef:t,onClick:n}=e,o=ut(),l=zt(o,Lo),i=pt(),a=g.useMemo((()=>{if(!t)return null;const e=l.find((e=>e.field===t.field));return e?.sort}),[t,l]),s=t.sortingOrder??i.sortingOrder,c=g.useCallback((e=>{n(e);const r=e.currentTarget.getAttribute("data-value")||null;o.current.sortColumn(t.field,r===a?null:r)}),[o,t,n,a]);if(i.disableColumnSorting||!t||!t.sortable||!s.some((e=>!!e)))return null;const u=e=>{const r=o.current.getLocaleText(e);return"function"==typeof r?r(t):r};return r.jsxs(g.Fragment,{children:[s.includes("asc")&&"asc"!==a?r.jsx(i.slots.baseMenuItem,{onClick:c,"data-value":"asc",iconStart:r.jsx(i.slots.columnMenuSortAscendingIcon,{fontSize:"small"}),children:u("columnMenuSortAsc")}):null,s.includes("desc")&&"desc"!==a?r.jsx(i.slots.baseMenuItem,{onClick:c,"data-value":"desc",iconStart:r.jsx(i.slots.columnMenuSortDescendingIcon,{fontSize:"small"}),children:u("columnMenuSortDesc")}):null,s.includes(null)&&null!=a?r.jsx(i.slots.baseMenuItem,{onClick:c,iconStart:i.slots.columnMenuUnsortIcon?r.jsx(i.slots.columnMenuUnsortIcon,{fontSize:"small"}):r.jsx("span",{}),children:o.current.getLocaleText("columnMenuUnsort")}):null]})},columnMenuFilterItem:function(e){const{colDef:t,onClick:n}=e,o=ut(),l=pt(),i=g.useCallback((e=>{n(e),o.current.showFilterPanel(t.field)}),[o,t.field,n]);return l.disableColumnFilter||!t.filterable?null:r.jsx(l.slots.baseMenuItem,{onClick:i,iconStart:r.jsx(l.slots.columnMenuFilterIcon,{fontSize:"small"}),children:o.current.getLocaleText("columnMenuFilter")})},columnMenuColumnsItem:function(e){return r.jsxs(g.Fragment,{children:[r.jsx(nu,u({},e)),r.jsx(ou,u({},e))]})}},au={columnMenuSortItem:{displayOrder:10},columnMenuFilterItem:{displayOrder:20},columnMenuColumnsItem:{displayOrder:30}},su=gt((function(e,t){const{defaultSlots:n,defaultSlotProps:o,slots:l,slotProps:i}=e,a=F(e,lu),s=(e=>{const t=Fi(),r=pt(),{defaultSlots:n,defaultSlotProps:o,slots:l={},slotProps:i={},hideMenu:a,colDef:s,addDividers:c=!0}=e,d=g.useMemo((()=>u({},n,l)),[n,l]),p=g.useMemo((()=>{if(!i||0===Object.keys(i).length)return o;const e=u({},i);return Object.entries(o).forEach((([t,r])=>{e[t]=u({},r,i[t]||{})})),e}),[o,i]),f=t.current.unstable_applyPipeProcessors("columnMenu",[],e.colDef),m=g.useMemo((()=>{const e=Object.keys(n);return Object.keys(l).filter((t=>!e.includes(t)))}),[l,n]);return g.useMemo((()=>{const e=Array.from(new Set([...f,...m])).filter((e=>null!=d[e])).sort(((e,t)=>{const r=p[e],n=p[t];return(Number.isFinite(r?.displayOrder)?r.displayOrder:100)-(Number.isFinite(n?.displayOrder)?n.displayOrder:100)}));return e.reduce(((t,n,o)=>{let l={colDef:s,onClick:a};const i=p[n];if(i){const e=F(i,ru);l=u({},l,e)}return c&&o!==e.length-1?[...t,[d[n],l],[r.slots.baseDivider,{}]]:[...t,[d[n],l]]}),[])}),[c,s,f,a,d,p,m,r.slots.baseDivider])})(u({},a,{defaultSlots:n,defaultSlotProps:o,slots:l,slotProps:i}));return r.jsx(tu,u({},a,{ref:t,children:s.map((([e,t],n)=>r.jsx(e,u({},t),n)))}))})),cu=gt((function(e,t){return r.jsx(su,u({},e,{ref:t,defaultSlots:iu,defaultSlotProps:au}))})),uu=["className"],du=R("div",{name:"MuiDataGrid",slot:"PanelWrapper"})({display:"flex",flexDirection:"column",flex:1,"&:focus":{outline:0}}),pu=gt((function(e,t){const{className:o}=e,l=F(e,uu),i=pt(),a=(e=>{const{classes:t}=e;return I({root:["panelWrapper"]},lr,t)})(i);return r.jsx(du,u({tabIndex:-1,className:n(a.root,o),ownerState:i},l,{ref:t}))})),fu=["children","className","classes","onClose"],gu=S("MuiDataGrid",["panel","paper"]),mu=R(br,{name:"MuiDataGrid",slot:"panel"})({zIndex:yr.zIndex.panel}),hu=R("div",{name:"MuiDataGrid",slot:"panelContent"})({backgroundColor:yr.colors.background.overlay,borderRadius:yr.radius.base,boxShadow:yr.shadows.overlay,display:"flex",maxWidth:`calc(100vw - ${yr.spacing(2)})`,overflow:"auto"}),bu=gt(((e,t)=>{const{children:o,className:l,onClose:i}=e,a=F(e,fu),s=ut(),c=pt(),d=gu,[p,f]=g.useState(!1),m=eo(),h=y((()=>f(!0))),b=y((()=>f(!1))),w=y((()=>{i?.()})),C=y((e=>{"Escape"===e.key&&i?.()})),[v,x]=g.useState(null);return g.useEffect((()=>{const e=s.current.rootElementRef?.current?.querySelector('[data-id="gridPanelAnchor"]');e&&x(e)}),[s]),v?r.jsx(mu,u({as:c.slots.basePopper,ownerState:c,placement:"bottom-end",className:n(d.panel,l,m),flip:!0,onDidShow:h,onDidHide:b,onClickAway:w,clickAwayMouseEvent:"onPointerUp",clickAwayTouchEvent:!1,focusTrap:!0},a,c.slotProps?.basePopper,{target:e.target??v,ref:t,children:r.jsx(hu,{className:d.paper,ownerState:c,onKeyDown:C,children:p&&o})})):null})),wu=["className"],Cu=we("div",{name:"MuiDataGrid",slot:"PanelContent"})({display:"flex",flexDirection:"column",overflow:"auto",flex:"1 1",maxHeight:400,padding:yr.spacing(2.5,1.5,2,1),gap:yr.spacing(2.5)});function vu(e){const{className:t}=e,o=F(e,wu),l=pt(),i=(e=>{const{classes:t}=e;return I({root:["panelContent"]},lr,t)})(l);return r.jsx(Cu,u({className:n(i.root,t),ownerState:l},o))}const yu=["className"],xu=R("div",{name:"MuiDataGrid",slot:"PanelFooter"})({padding:yr.spacing(1),display:"flex",justifyContent:"space-between",borderTop:`1px solid ${yr.colors.border.base}`});function Su(e){const{className:t}=e,o=F(e,yu),l=pt(),i=(e=>{const{classes:t}=e;return I({root:["panelFooter"]},lr,t)})(l);return r.jsx(xu,u({className:n(i.root,t),ownerState:l},o))}const Ru=["item","hasMultipleFilters","deleteFilter","applyFilterChanges","showMultiFilterOperators","disableMultiFilterOperator","applyMultiFilterOperatorChanges","focusElementRef","logicOperators","columnsSort","filterColumns","deleteIconProps","logicOperatorInputProps","operatorInputProps","columnInputProps","valueInputProps","readOnly","children"],Iu=["InputComponentProps"],Mu=R("div",{name:"MuiDataGrid",slot:"FilterForm"})({display:"flex",gap:yr.spacing(1.5)}),ku=R("div",{name:"MuiDataGrid",slot:"FilterFormDeleteIcon"})({flexShrink:0,display:"flex",justifyContent:"center",alignItems:"center"}),Pu=R("div",{name:"MuiDataGrid",slot:"FilterFormLogicOperatorInput"})({minWidth:75,justifyContent:"end"}),Eu=R("div",{name:"MuiDataGrid",slot:"FilterFormColumnInput"})({width:150}),Fu=R("div",{name:"MuiDataGrid",slot:"FilterFormOperatorInput"})({width:150}),Hu=R("div",{name:"MuiDataGrid",slot:"FilterFormValueInput"})({width:190}),Tu=e=>e.headerName||e.field,Du=new Intl.Collator,Ou=gt((function(e,t){const{item:l,hasMultipleFilters:i,deleteFilter:a,applyFilterChanges:s,showMultiFilterOperators:c,disableMultiFilterOperator:d,applyMultiFilterOperatorChanges:p,focusElementRef:f,logicOperators:m=[sn.And,sn.Or],columnsSort:h,filterColumns:b,deleteIconProps:w={},logicOperatorInputProps:C={},operatorInputProps:v={},columnInputProps:y={},valueInputProps:x={},readOnly:S}=e,R=F(e,Ru),M=ut(),k=zt(M,In),E=zt(M,On),H=zt(M,Bo),T=P(),D=P(),O=P(),$=P(),j=pt(),L=(e=>{const{classes:t}=e;return I({root:["filterForm"],deleteIcon:["filterFormDeleteIcon"],logicOperatorInput:["filterFormLogicOperatorInput"],columnInput:["filterFormColumnInput"],operatorInput:["filterFormOperatorInput"],valueInput:["filterFormValueInput"]},lr,t)})(j),z=g.useRef(null),A=g.useRef(null),B=H.logicOperator??sn.And,V=i&&m.length>0,N=(j.slotProps?.baseSelect||{}).native??!1,G=j.slotProps?.baseSelectOption||{},{InputComponentProps:W}=x,U=F(x,Iu),K=zt(M,ha),_=zt(M,wa),{filteredColumns:q,selectedField:X}=g.useMemo((()=>{let e=l.field;const t=!1===k[l.field].filterable?k[l.field]:null;if(t)return{filteredColumns:[t],selectedField:e};if(K)return{filteredColumns:E.filter((e=>void 0!==_.get(e.field))),selectedField:e};if(void 0===b||"function"!=typeof b)return{filteredColumns:E,selectedField:e};const r=b({field:l.field,columns:E,currentFilters:H?.items||[]});return{filteredColumns:E.filter((t=>{const n=r.includes(t.field);return t.field!==l.field||n||(e=void 0),n})),selectedField:e}}),[l.field,k,K,b,E,H?.items,_]),Q=g.useMemo((()=>{switch(h){case"asc":return q.sort(((e,t)=>Du.compare(Tu(e),Tu(t))));case"desc":return q.sort(((e,t)=>-Du.compare(Tu(e),Tu(t))));default:return q}}),[q,h]),Y=l.field?M.current.getColumn(l.field):null,J=g.useMemo((()=>l.operator&&Y?Y.filterOperators?.find((e=>e.value===l.operator)):null),[l,Y]),Z=g.useCallback((e=>{const t=e.target.value,r=M.current.getColumn(t);if(r.field===Y.field)return;const n=r.filterOperators.find((e=>e.value===l.operator))||r.filterOperators[0];let o=n.InputComponent&&n.InputComponent===J?.InputComponent&&r.type===Y.type?l.value:void 0;if("singleSelect"===r.type&&void 0!==o){const e=r,t=ei(e);Array.isArray(o)?o=o.filter((r=>void 0!==ti(r,t,e?.getOptionValue))):void 0===ti(l.value,t,e?.getOptionValue)&&(o=void 0)}s(u({},l,{field:t,operator:n.value,value:o}))}),[M,s,l,Y,J]),ee=g.useCallback((e=>{const t=e.target.value,r=Y?.filterOperators.find((e=>e.value===t));s(u({},l,{operator:t,value:r?.InputComponent&&r?.InputComponent===J?.InputComponent?l.value:void 0}))}),[s,l,Y,J]),te=g.useCallback((e=>{const t=e.target.value===sn.And.toString()?sn.And:sn.Or;p(t)}),[p]);return g.useImperativeHandle(f,(()=>({focus:()=>{J?.InputComponent?z?.current?.focus():A.current.focus()}})),[J]),r.jsxs(Mu,u({className:L.root,"data-id":l.id,ownerState:j},R,{ref:t,children:[r.jsx(ku,u({},w,{className:n(L.deleteIcon,w.className),ownerState:j,children:r.jsx(j.slots.baseIconButton,u({"aria-label":M.current.getLocaleText("filterPanelDeleteIconLabel"),title:M.current.getLocaleText("filterPanelDeleteIconLabel"),onClick:()=>{a(l)},size:"small",disabled:S},j.slotProps?.baseIconButton,{children:r.jsx(j.slots.filterPanelDeleteIcon,{fontSize:"small"})}))})),r.jsx(Pu,u({as:j.slots.baseSelect,sx:[V?{display:"flex"}:{display:"none"},c?{visibility:"visible"}:{visibility:"hidden"},C.sx],className:n(L.logicOperatorInput,C.className),ownerState:j},C,{size:"small",slotProps:{htmlInput:{"aria-label":M.current.getLocaleText("filterPanelLogicOperator")}},value:B??"",onChange:te,disabled:!!d||1===m.length,native:N},j.slotProps?.baseSelect,{children:m.map((e=>g.createElement(j.slots.baseSelectOption,u({},G,{native:N,key:e.toString(),value:e.toString()}),M.current.getLocaleText((e=>{switch(e){case sn.And:return"filterPanelOperatorAnd";case sn.Or:return"filterPanelOperatorOr";default:throw new Error("MUI X: Invalid `logicOperator` property in the `GridFilterPanel`.")}})(e)))))})),r.jsx(Eu,u({as:j.slots.baseSelect},y,{className:n(L.columnInput,y.className),ownerState:j,size:"small",labelId:D,id:T,label:M.current.getLocaleText("filterPanelColumns"),value:X??"",onChange:Z,native:N,disabled:S},j.slotProps?.baseSelect,{children:Q.map((e=>g.createElement(j.slots.baseSelectOption,u({},G,{native:N,key:e.field,value:e.field}),Tu(e))))})),r.jsx(Fu,u({as:j.slots.baseSelect,size:"small"},v,{className:n(L.operatorInput,v.className),ownerState:j,labelId:$,label:M.current.getLocaleText("filterPanelOperator"),id:O,value:l.operator,onChange:ee,native:N,inputRef:A,disabled:S},j.slotProps?.baseSelect,{children:Y?.filterOperators?.map((e=>g.createElement(j.slots.baseSelectOption,u({},G,{native:N,key:e.value,value:e.value}),e.label||M.current.getLocaleText(`filterOperator${o(e.value)}`))))})),r.jsx(Hu,u({},U,{className:n(L.valueInput,U.className),ownerState:j,children:J?.InputComponent?r.jsx(J.InputComponent,u({apiRef:M,item:l,applyValue:s,focusElementRef:z,disabled:S,slotProps:{root:{size:"small"}}},J.InputComponentProps,W),l.field):null}))]}))})),$u=["logicOperators","columnsSort","filterFormProps","getColumnForNewFilter","children","disableAddFilterButton","disableRemoveAllButton"],ju=e=>({field:e.field,operator:e.filterOperators[0].value,id:Math.round(1e5*Math.random())}),Lu=gt((function(e,t){const n=ut(),o=pt(),l=zt(n,Bo),i=zt(n,On),a=zt(n,$n),s=g.useRef(null),c=g.useRef(null),{logicOperators:d=[sn.And,sn.Or],columnsSort:p,filterFormProps:f,getColumnForNewFilter:m,disableAddFilterButton:h=!1,disableRemoveAllButton:b=!1}=e,w=F(e,$u),C=n.current.upsertFilterItem,v=g.useCallback((e=>{n.current.setFilterLogicOperator(e)}),[n]),y=g.useCallback((()=>{let e;if(m&&"function"==typeof m){const t=m({currentFilters:l?.items||[],columns:i});if(null===t)return null;e=i.find((({field:e})=>e===t))}else e=i.find((e=>e.filterOperators?.length));return e?ju(e):null}),[l?.items,i,m]),x=g.useCallback((()=>{if(void 0===m||"function"!=typeof m)return y();const e=l.items.length?l.items:[y()].filter(Boolean),t=m({currentFilters:e,columns:i});if(null===t)return null;const r=i.find((({field:e})=>e===t));return r?ju(r):null}),[l.items,i,m,y]),S=g.useMemo((()=>l.items.length?l.items:(c.current||(c.current=y()),c.current?[c.current]:[])),[l.items,y]),R=S.length>1,{readOnlyFilters:I,validFilters:M}=g.useMemo((()=>S.reduce(((e,t)=>(a[t.field]?e.validFilters.push(t):e.readOnlyFilters.push(t),e)),{readOnlyFilters:[],validFilters:[]})),[S,a]),k=g.useCallback((()=>{const e=x();e&&n.current.upsertFilterItems([...S,e])}),[n,x,S]),P=g.useCallback((e=>{const t=1===M.length;n.current.deleteFilterItem(e),t&&n.current.hideFilterPanel()}),[n,M.length]),E=g.useCallback((()=>1===M.length&&void 0===M[0].value?(n.current.deleteFilterItem(M[0]),n.current.hideFilterPanel()):n.current.setFilterModel(u({},l,{items:I}),"removeAllFilterItems")),[n,I,l,M]);return g.useEffect((()=>{d.length>0&&l.logicOperator&&!d.includes(l.logicOperator)&&v(d[0])}),[d,v,l.logicOperator]),g.useEffect((()=>{M.length>0&&s.current.focus()}),[M.length]),r.jsxs(pu,u({},w,{ref:t,children:[r.jsxs(vu,{children:[I.map(((e,t)=>r.jsx(Ou,u({item:e,applyFilterChanges:C,deleteFilter:P,hasMultipleFilters:R,showMultiFilterOperators:t>0,disableMultiFilterOperator:1!==t,applyMultiFilterOperatorChanges:v,focusElementRef:null,readOnly:!0,logicOperators:d,columnsSort:p},f),null==e.id?t:e.id))),M.map(((e,t)=>r.jsx(Ou,u({item:e,applyFilterChanges:C,deleteFilter:P,hasMultipleFilters:R,showMultiFilterOperators:I.length+t>0,disableMultiFilterOperator:I.length+t!==1,applyMultiFilterOperatorChanges:v,focusElementRef:t===M.length-1?s:null,logicOperators:d,columnsSort:p},f),null==e.id?t+I.length:e.id)))]}),o.disableMultipleColumnsFiltering||h&&b?null:r.jsxs(Su,{children:[h?r.jsx("span",{}):r.jsx(o.slots.baseButton,u({onClick:k,startIcon:r.jsx(o.slots.filterPanelAddIcon,{})},o.slotProps?.baseButton,{children:n.current.getLocaleText("filterPanelAddFilter")})),!b&&M.length>0?r.jsx(o.slots.baseButton,u({onClick:E,startIcon:r.jsx(o.slots.filterPanelRemoveAllIcon,{})},o.slotProps?.baseButton,{children:n.current.getLocaleText("filterPanelRemoveAll")})):null]})]}))})),zu=(e,t)=>(e.headerName||e.field).toLowerCase().indexOf(t)>-1,Au=["children"],Bu=s({from:{opacity:0},to:{opacity:1}}),Vu=s({"from, to":{"--scrollable":'" "'}}),Nu=we("div",{name:"MuiDataGrid",slot:"ShadowScrollArea"})`
  flex: 1;
  display: flex;
  flex-direction: column;
  animation: ${Vu};
  animation-timeline: --scroll-timeline;
  animation-fill-mode: none;
  box-sizing: border-box;
  overflow: auto;
  scrollbar-width: thin;
  scroll-timeline: --scroll-timeline block;

  &::before,
  &::after {
    content: '';
    flex-shrink: 0;
    display: block;
    position: sticky;
    left: 0;
    width: 100%;
    height: 4px;
    animation: ${Bu} linear both;
    animation-timeline: --scroll-timeline;

    // Custom property toggle trick:
    // - Detects if the element is scrollable
    // - https://css-tricks.com/the-css-custom-property-toggle-trick/
    --visibility-scrollable: var(--scrollable) visible;
    --visibility-not-scrollable: hidden;
    visibility: var(--visibility-scrollable, var(--visibility-not-scrollable));
  }

  &::before {
    top: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 0, transparent 100%);
    animation-range: 0 4px;
  }

  &::after {
    bottom: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.05) 0, transparent 100%);
    animation-direction: reverse;
    animation-range: calc(100% - 4px) 100%;
  }
`,Gu=gt((function(e,t){const{children:n}=e,o=F(e,Au);return r.jsx(Nu,u({},o,{ref:t,children:n}))})),Wu=new Intl.Collator,Uu=R("div",{name:"MuiDataGrid",slot:"ColumnsManagement"})({display:"flex",flexDirection:"column",padding:yr.spacing(.5,1.5)}),Ku=R(Gu,{name:"MuiDataGrid",slot:"ColumnsManagementScrollArea"})({maxHeight:300}),_u=R("div",{name:"MuiDataGrid",slot:"ColumnsManagementHeader"})({padding:yr.spacing(1.5,2),borderBottom:`1px solid ${yr.colors.border.base}`}),qu=R(br,{name:"MuiDataGrid",slot:"ColumnsManagementSearchInput"})({[`& .${K.input}::-webkit-search-decoration,\n      & .${K.input}::-webkit-search-cancel-button,\n      & .${K.input}::-webkit-search-results-button,\n      & .${K.input}::-webkit-search-results-decoration`]:{display:"none"}}),Xu=R("div",{name:"MuiDataGrid",slot:"ColumnsManagementFooter"})({padding:yr.spacing(1,1,1,1.5),display:"flex",justifyContent:"space-between",borderTop:`1px solid ${yr.colors.border.base}`}),Qu=R("div",{name:"MuiDataGrid",slot:"ColumnsManagementEmptyText"})({padding:yr.spacing(1,0),alignSelf:"center",font:yr.typography.font.body}),Yu=gt((function(e,t){const{children:n,slotProps:o={}}=e,l=o.button||{},i=o.tooltip||{},a=ut(),s=pt(),c=P(),d=P(),[p,f]=g.useState(!1),m=g.useRef(null),h=v(t,m),b=()=>f(!1);return null==n?null:r.jsxs(g.Fragment,{children:[r.jsx(s.slots.baseTooltip,u({title:a.current.getLocaleText("toolbarExportLabel"),enterDelay:1e3},s.slotProps?.baseTooltip,i,{children:r.jsx(s.slots.baseButton,u({size:"small",startIcon:r.jsx(s.slots.exportIcon,{}),"aria-expanded":p,"aria-label":a.current.getLocaleText("toolbarExportLabel"),"aria-haspopup":"menu","aria-controls":p?d:void 0,id:c},s.slotProps?.baseButton,l,{onClick:e=>{f((e=>!e)),l.onClick?.(e)},ref:h,children:a.current.getLocaleText("toolbarExport")}))})),r.jsx(oo,{open:p,target:m.current,onClose:b,position:"bottom-end",children:r.jsx(s.slots.baseMenuList,{id:d,className:ir.menuList,"aria-labelledby":c,onKeyDown:e=>{"Tab"===e.key&&e.preventDefault(),Yc(e.key)&&b()},autoFocusItem:p,children:g.Children.map(n,(e=>g.isValidElement(e)?g.cloneElement(e,{hideMenu:b}):e))})})]})})),Ju=["hideMenu","options"],Zu=["hideMenu","options"],ed=["csvOptions","printOptions","excelOptions"];function td(e){const t=ut(),n=pt(),{hideMenu:o,options:l}=e,i=F(e,Ju);return r.jsx(n.slots.baseMenuItem,u({onClick:()=>{t.current.exportDataAsCsv(l),o?.()}},i,{children:t.current.getLocaleText("toolbarExportCSV")}))}function rd(e){const t=ut(),n=pt(),{hideMenu:o,options:l}=e,i=F(e,Zu);return r.jsx(n.slots.baseMenuItem,u({onClick:()=>{t.current.exportDataAsPrint(l),o?.()}},i,{children:t.current.getLocaleText("toolbarExportPrint")}))}function nd(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)){const r=e.length;if(r!==t.length)return!1;for(let n=0;n<r;n+=1)if(!nd(e[n],t[n]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;const r=Array.from(e.entries());for(let e=0;e<r.length;e+=1)if(!t.has(r[e][0]))return!1;for(let e=0;e<r.length;e+=1){const n=r[e];if(!nd(n[1],t.get(n[0])))return!1}return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;const r=Array.from(e.entries());for(let e=0;e<r.length;e+=1)if(!t.has(r[e][0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){const r=e.length;if(r!==t.length)return!1;for(let n=0;n<r;n+=1)if(e[n]!==t[n])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const r=Object.keys(e),n=r.length;if(n!==Object.keys(t).length)return!1;for(let e=0;e<n;e+=1)if(!Object.prototype.hasOwnProperty.call(t,r[e]))return!1;for(let o=0;o<n;o+=1){const n=r[o];if(!nd(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}gt((function(e,t){const n=e,{csvOptions:o={},printOptions:l={},excelOptions:i}=n,a=F(n,ed),s=ut().current.unstable_applyPipeProcessors("exportMenu",[],{excelOptions:i,csvOptions:o,printOptions:l}).sort(((e,t)=>e.componentName>t.componentName?1:-1));return 0===s.length?null:r.jsx(Yu,u({},a,{ref:t,children:s.map(((e,t)=>g.cloneElement(e.component,{key:t})))}))}));const od=g.createContext(void 0);function ld(){const e=g.useContext(od);if(void 0===e)throw new Error("MUI X: Missing context. Quick Filter subcomponents must be placed within a <QuickFilter /> component.");return e}const id=["render","className","parser","formatter","debounceMs","defaultExpanded","expanded","onExpandedChange"],ad=e=>e.split(" ").filter((e=>""!==e)),sd=e=>e.join(" ");function cd(e){const t=pt(),{render:n,className:o,parser:l=ad,formatter:i=sd,debounceMs:a=t.filterDebounceMs,defaultExpanded:s,expanded:c,onExpandedChange:p}=e,f=F(e,id),m=ut(),h=g.useRef(null),b=g.useRef(null),w=zt(m,Vo),[C,v]=g.useState(i(w??[])),[y,x]=g.useState(s??C.length>0),S=c??y,R=g.useMemo((()=>({value:C,expanded:S})),[C,S]),I="function"==typeof o?o(R):o,M=g.useRef(null),k=P(),E=g.useCallback((e=>{p&&p(e),void 0===c&&x(e)}),[p,c]),H=g.useRef(w);g.useEffect((()=>{nd(H.current,w)||(H.current=w,v((e=>nd(l(e),w)?e:i(w??[]))))}),[w,i,l]);const T=g.useRef(!0),D=g.useRef(S);g.useEffect((()=>{T.current?T.current=!1:D.current!==S&&(S?requestAnimationFrame((()=>{h.current?.focus({preventScroll:!0})})):b.current?.focus({preventScroll:!0}),D.current=S)}),[S]);const O=g.useMemo((()=>j((e=>{const t=l(e);H.current=t,m.current.setQuickFilterValues(t)}),a)),[m,a,l]);g.useEffect((()=>O.clear),[O]);const $=g.useCallback((e=>{const t=e.target.value;v(t),O(t)}),[O]),L=g.useCallback((()=>{v(""),m.current.setQuickFilterValues([]),h.current?.focus()}),[m,h]),z=g.useMemo((()=>({controlRef:h,triggerRef:b,state:R,controlId:k,clearValue:L,onValueChange:$,onExpandedChange:E})),[k,R,$,L,E]);d((()=>{M.current&&b.current&&M.current.style.setProperty("--trigger-width",`${b.current?.offsetWidth}px`)}),[]);const A=Ls("div",n,u({className:I},f,{ref:M}),R);return r.jsx(od.Provider,{value:z,children:A})}const ud=["render","className","slotProps","onKeyDown","onChange"],dd=gt((function(e,t){const{render:n,className:o,slotProps:l,onKeyDown:i,onChange:a}=e,s=F(e,ud),c=pt(),{state:d,controlId:p,controlRef:f,onValueChange:m,onExpandedChange:h,clearValue:b}=ld(),w="function"==typeof o?o(d):o,C=v(f,t),y=Ls(c.slots.baseTextField,n,u({},c.slotProps?.baseTextField,{slotProps:u({htmlInput:u({role:"searchbox",id:p,tabIndex:d.expanded?void 0:-1},l?.htmlInput,{onBlur:e=>{""===d.value&&h(!1),l?.htmlInput?.onBlur?.(e)}})},l),value:d.value,className:w},s,{onChange:e=>{d.expanded||h(!0),m(e),a?.(e)},onKeyDown:e=>{"Escape"===e.key&&(""===d.value?h(!1):b()),i?.(e)},ref:C}),d);return r.jsx(g.Fragment,{children:y})})),pd=["render","className","onClick"],fd=gt((function(e,t){const{render:n,className:o,onClick:l}=e,i=F(e,pd),a=pt(),{state:s,clearValue:c}=ld(),d="function"==typeof o?o(s):o,p=Ls(a.slots.baseIconButton,n,u({},a.slotProps?.baseIconButton,{className:d,tabIndex:-1},i,{onClick:e=>{c(),l?.(e)},ref:t}),s);return r.jsx(g.Fragment,{children:p})})),gd=["render","className","onClick"],md=gt((function(e,t){const{render:n,className:o,onClick:l}=e,i=F(e,gd),a=pt(),{state:s,controlId:c,onExpandedChange:d,triggerRef:p}=ld(),f="function"==typeof o?o(s):o,m=v(p,t),h=Ls(a.slots.baseButton,n,u({},a.slotProps?.baseButton,{className:f,"aria-controls":c,"aria-expanded":s.expanded},i,{onClick:e=>{d(!s.expanded),l?.(e)},ref:m}),s);return r.jsx(g.Fragment,{children:h})})),hd=["quickFilterParser","quickFilterFormatter","debounceMs","className","slotProps"],bd=["ref","slotProps"],wd=we("div",{name:"MuiDataGrid",slot:"ToolbarQuickFilter"})({display:"grid",alignItems:"center"}),Cd=we(Gs,{name:"MuiDataGrid",slot:"ToolbarQuickFilterTrigger"})((({ownerState:e})=>({gridArea:"1 / 1",width:"min-content",height:"min-content",zIndex:1,opacity:e.expanded?0:1,pointerEvents:e.expanded?"none":"auto",transition:yr.transition(["opacity"])}))),vd=we((e=>{throw new Error("Failed assertion: should not be rendered")}),{name:"MuiDataGrid",slot:"ToolbarQuickFilterControl"})((({ownerState:e})=>({gridArea:"1 / 1",overflowX:"clip",width:e.expanded?260:"var(--trigger-width)",opacity:e.expanded?1:0,transition:yr.transition(["width","opacity"])})));function yd(e){const t=ut(),o=pt(),l={classes:o.classes,expanded:!1},i=(e=>{const{classes:t}=e;return I({root:["toolbarQuickFilter"],trigger:["toolbarQuickFilterTrigger"],control:["toolbarQuickFilterControl"]},lr,t)})(l),{quickFilterParser:a,quickFilterFormatter:s,debounceMs:c,className:d,slotProps:p}=e,f=F(e,hd);return r.jsx(cd,{parser:a,formatter:s,debounceMs:c,render:(e,a)=>{const s=u({},l,{expanded:a.expanded});return r.jsxs(wd,u({},e,{className:n(i.root,d),children:[r.jsx(md,{render:e=>r.jsx(o.slots.baseTooltip,{title:t.current.getLocaleText("toolbarQuickFilterLabel"),enterDelay:0,children:r.jsx(Cd,u({className:i.trigger},e,{ownerState:s,color:"default","aria-disabled":a.expanded,children:r.jsx(o.slots.quickFilterIcon,{fontSize:"small"})}))})}),r.jsx(dd,{render:e=>{let{ref:n,slotProps:l}=e,a=F(e,bd);return r.jsx(vd,u({as:o.slots.baseTextField,className:i.control,ownerState:s,inputRef:n,"aria-label":t.current.getLocaleText("toolbarQuickFilterLabel"),placeholder:t.current.getLocaleText("toolbarQuickFilterPlaceholder"),size:"small",slotProps:u({input:u({startAdornment:r.jsx(o.slots.quickFilterIcon,{fontSize:"small"}),endAdornment:a.value?r.jsx(fd,{render:r.jsx(o.slots.baseIconButton,{size:"small",edge:"end","aria-label":t.current.getLocaleText("toolbarQuickFilterDeleteIconLabel"),children:r.jsx(o.slots.quickFilterClearIcon,{fontSize:"small"})})}):null},l?.input)},l)},o.slotProps?.baseTextField,a,p?.root,f))}})]}))}})}const xd=["render","className","onClick","onPointerUp"],Sd=gt((function(e,t){const{render:n,className:o,onClick:l,onPointerUp:i}=e,a=F(e,xd),s=pt(),c=P(),d=P(),p=ut(),f=zt(p,Va),m=f.open&&f.openedPanelValue===Ga.filters,h=zt(p,Jo).length,b={open:m,filterCount:h},w="function"==typeof o?o(b):o,{filterPanelTriggerRef:C}=Rs(),y=v(t,C),x=Ls(s.slots.baseButton,n,u({},s.slotProps?.baseButton,{id:c,"aria-haspopup":"true","aria-expanded":m?"true":void 0,"aria-controls":m?d:void 0,onClick:e=>{m?p.current.hidePreferences():p.current.showPreferences(Ga.filters,d,c),l?.(e)},onPointerUp:e=>{m&&e.stopPropagation(),i?.(e)},className:w},a,{ref:y}),b);return r.jsx(g.Fragment,{children:x})})),Rd=["render","className","onClick","onPointerUp"],Id=gt((function(e,t){const{render:n,className:o,onClick:l,onPointerUp:i}=e,a=F(e,Rd),s=pt(),c=P(),d=P(),p=ut(),f=zt(p,Va),m=f.open&&f.openedPanelValue===Ga.columns,h={open:m},b="function"==typeof o?o(h):o,{columnsPanelTriggerRef:w}=Rs(),C=v(t,w),y=Ls(s.slots.baseButton,n,u({},s.slotProps?.baseButton,{id:c,"aria-haspopup":"true","aria-expanded":m?"true":void 0,"aria-controls":m?d:void 0,className:b},a,{onPointerUp:e=>{m&&e.stopPropagation(),i?.(e)},onClick:e=>{m?p.current.hidePreferences():p.current.showPreferences(Ga.columns,d,c),l?.(e)},ref:C}),h);return r.jsx(g.Fragment,{children:y})})),Md=["render","options","onClick"],kd=gt((function(e,t){const{render:n,options:o,onClick:l}=e,i=F(e,Md),a=pt(),s=ut(),c=Ls(a.slots.baseButton,n,u({},a.slotProps?.baseButton,{onClick:e=>{s.current.exportDataAsCsv(o),l?.(e)}},i,{ref:t}));return r.jsx(g.Fragment,{children:c})})),Pd=["render","options","onClick"],Ed=gt((function(e,t){const{render:n,options:o,onClick:l}=e,i=F(e,Pd),a=pt(),s=ut(),c=Ls(a.slots.baseButton,n,u({},a.slotProps?.baseButton,{onClick:e=>{s.current.exportDataAsPrint(o),l?.(e)}},i,{ref:t}));return r.jsx(g.Fragment,{children:c})})),Fd=["className"],Hd=["className"],Td=e=>{const{classes:t}=e;return I({divider:["toolbarDivider"],label:["toolbarLabel"]},lr,t)},Dd=we(br,{name:"MuiDataGrid",slot:"ToolbarDivider"})({height:"50%",margin:yr.spacing(0,.5)}),Od=we("span",{name:"MuiDataGrid",slot:"ToolbarLabel"})({flex:1,font:yr.typography.font.large,fontWeight:yr.typography.fontWeight.medium,margin:yr.spacing(0,.5),textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap"});function $d(e){const t=F(e,Fd),n=pt(),o=Td(n);return r.jsx(Dd,u({as:n.slots.baseDivider,orientation:"vertical",className:o.divider},t))}function jd(e){const t=F(e,Hd),n=pt(),o=Td(n);return r.jsx(Od,u({className:o.label},t))}const Ld=["className","selectedRowCount"],zd=we("div",{name:"MuiDataGrid",slot:"SelectedRowCount"})({alignItems:"center",display:"flex",margin:yr.spacing(0,2),visibility:"hidden",width:0,height:0,[yr.breakpoints.up("sm")]:{visibility:"visible",width:"auto",height:"auto"}}),Ad=gt((function(e,t){const{className:o,selectedRowCount:l}=e,i=F(e,Ld),a=ut(),s=pt(),c=(e=>{const{classes:t}=e;return I({root:["selectedRowCount"]},lr,t)})(s),d=a.current.getLocaleText("footerRowSelected")(l);return r.jsx(zd,u({className:n(c.root,o),ownerState:s},i,{ref:t,children:d}))})),Bd=gt((function(e,t){const n=ut(),o=pt(),l=zt(n,Er),i=zt(n,rl),a=zt(n,Qo),s=!o.hideFooterSelectedRowCount&&i>0?r.jsx(Ad,{selectedRowCount:i}):r.jsx("div",{}),c=o.hideFooterRowCount||o.pagination?null:r.jsx(o.slots.footerRowCount,u({},o.slotProps?.footerRowCount,{rowCount:l,visibleRowCount:a})),d=o.pagination&&!o.hideFooterPagination&&o.slots.pagination&&r.jsx(o.slots.pagination,{});return r.jsxs(Ds,u({},e,{ref:t,children:[s,c,d]}))})),Vd=(e,t,r,n,o,l)=>{let i;switch(e){case Ro.LEFT:i=n[r];break;case Ro.RIGHT:i=o-n[r]-t+l;break;default:i=void 0}return i},Nd=(e,t,r,n,o)=>{const l=t===r-1;return!(e!==Ro.LEFT||!l)||!!n&&(e===Ro.LEFT||(e===Ro.RIGHT?!l:!l||o))},Gd=(e,t)=>e===Ro.RIGHT&&0===t,Wd={root:ir.scrollbarFiller,header:ir["scrollbarFiller--header"],borderTop:ir["scrollbarFiller--borderTop"],borderBottom:ir["scrollbarFiller--borderBottom"],pinnedRight:ir["scrollbarFiller--pinnedRight"]};function Ud({header:e,borderTop:t=!0,borderBottom:o,pinnedRight:l}){return r.jsx("div",{role:"presentation",className:n(Wd.root,e&&Wd.header,t&&Wd.borderTop,o&&Wd.borderBottom,l&&Wd.pinnedRight)})}const Kd=["skeletonRowsCount","visibleColumns","showFirstRowBorder"],_d=we("div",{name:"MuiDataGrid",slot:"SkeletonLoadingOverlay"})({minWidth:"100%",width:"max-content",height:"100%",overflow:"clip"}),qd=e=>parseInt(e.getAttribute("data-colindex"),10),Xd=gt((function(e,o){const l=pt(),{slots:i}=l,a=t(),s=(e=>{const{classes:t}=e;return I({root:["skeletonLoadingOverlay"]},lr,t)})({classes:l.classes}),c=g.useRef(null),d=v(c,o),p=ut(),f=zt(p,Kt),m=zt(p,_t),h=zt(p,Dn),b=g.useMemo((()=>h.filter((e=>e<=m)).length),[m,h]),{skeletonRowsCount:w,visibleColumns:C,showFirstRowBorder:y}=e,x=F(e,Kd),S=zt(p,En),R=g.useMemo((()=>S.slice(0,b)),[S,b]),M=zt(p,Tn),k=g.useCallback((e=>-1!==M.left.findIndex((t=>t.field===e))?Ro.LEFT:-1!==M.right.findIndex((t=>t.field===e))?Ro.RIGHT:void 0),[M.left,M.right]),P=g.useMemo((()=>{const e=[];for(let t=0;t<w;t+=1){const o=[];for(let e=0;e<R.length;e+=1){const s=R[e],c=k(s.field),d=c===Ro.LEFT,p=c===Ro.RIGHT,g=ns(c,a),m=g?M[g].length:R.length-M.left.length-M.right.length,b=g?M[g].findIndex((e=>e.field===s.field)):e-M.left.length,w=f.hasScrollY?f.scrollbarSize:0,v=os({},a,c,Vd(c,s.computedWidth,e,h,f.columnsTotalWidth,w)),y=f.columnsTotalWidth<f.viewportOuterSize.width,x=Nd(c,b,m,l.showCellVerticalBorder,y),S=Gd(c,b),I=e===R.length-1,P=p&&0===b,E=P&&y,F=I&&!P&&y,H=f.viewportOuterSize.width-f.columnsTotalWidth,T=Math.max(0,H),D=r.jsx(i.skeletonCell,{width:T,empty:!0},`skeleton-filler-column-${t}`),O=I&&0!==w;E&&o.push(D),o.push(r.jsx(i.skeletonCell,{field:s.field,type:s.type,align:s.align,width:"var(--width)",height:f.rowHeight,"data-colindex":e,empty:C&&!C.has(s.field),className:n(d&&ir["cell--pinnedLeft"],p&&ir["cell--pinnedRight"],x&&ir["cell--withRightBorder"],S&&ir["cell--withLeftBorder"]),style:u({"--width":`${s.computedWidth}px`},v)},`skeleton-column-${t}-${s.field}`)),F&&o.push(D),O&&o.push(r.jsx(Ud,{pinnedRight:M.right.length>0},`skeleton-scrollbar-filler-${t}`))}e.push(r.jsx("div",{className:n(ir.row,ir.rowSkeleton,0===t&&!y&&ir["row--firstVisible"]),children:o},`skeleton-row-${t}`))}return e}),[w,R,k,a,M,f.hasScrollY,f.scrollbarSize,f.columnsTotalWidth,f.viewportOuterSize.width,f.rowHeight,h,l.showCellVerticalBorder,i,C,y]);return dr(p,"columnResize",(e=>{const{colDef:t,width:r}=e,n=c.current?.querySelectorAll(`[data-field="${_s(t.field)}"]`);if(!n)throw new Error("MUI X: Expected skeleton cells to be defined with `data-field` attribute.");const o=R.findIndex((e=>e.field===t.field)),l=k(t.field),i=l===Ro.LEFT,a=l===Ro.RIGHT,s=getComputedStyle(n[0]).getPropertyValue("--width"),u=parseInt(s,10)-r;if(n&&n.forEach((e=>{e.style.setProperty("--width",`${r}px`)})),i){const e=c.current?.querySelectorAll(`.${ir["cell--pinnedLeft"]}`);e?.forEach((e=>{qd(e)>o&&(e.style.left=parseInt(getComputedStyle(e).left,10)-u+"px")}))}if(a){const e=c.current?.querySelectorAll(`.${ir["cell--pinnedRight"]}`);e?.forEach((e=>{qd(e)<o&&(e.style.right=`${parseInt(getComputedStyle(e).right,10)+u}px`)}))}})),r.jsx(_d,u({className:s.root},x,{ref:d,children:P}))})),Qd=gt((function(e,t){const n=ut(),o=zt(n,Kt),l=o?.viewportInnerSize.height??0,i=Math.ceil(l/o.rowHeight);return r.jsx(Xd,u({},e,{skeletonRowsCount:i,ref:t}))})),Yd=["variant","noRowsVariant","style"],Jd={"circular-progress":{component:e=>e.slots.baseCircularProgress,style:{}},"linear-progress":{component:e=>e.slots.baseLinearProgress,style:{display:"block"}},skeleton:{component:()=>Qd,style:{display:"block"}}},Zd=gt((function(e,t){const{variant:n="linear-progress",noRowsVariant:o="skeleton",style:l}=e,i=F(e,Yd),a=ut(),s=pt(),c=zt(a,kr),d=Jd[0===c?o:n],p=d.component(s);return r.jsx(js,u({style:u({},d.style,l)},i,{ref:t,children:r.jsx(p,{})}))})),ep=gt((function(e,t){const n=ut().current.getLocaleText("noRowsLabel");return r.jsx(js,u({},e,{ref:t,children:n}))})),tp=gt((function(e,t){const n=pt(),o=ut(),l=zt(o,Rn),i=!n.disableColumnSelector&&l.length>0;return r.jsxs(js,u({},e,{ref:t,children:[o.current.getLocaleText("noColumnsOverlayLabel"),i&&r.jsx(n.slots.baseButton,u({size:"small"},n.slotProps?.baseButton,{onClick:()=>{o.current.showPreferences(Ga.columns)},children:o.current.getLocaleText("noColumnsOverlayManageColumns")}))]}))})),rp=R(br)({maxHeight:"calc(100% + 1px)",flexGrow:1}),np=["className","rowCount","visibleRowCount"],op=we("div",{name:"MuiDataGrid",slot:"RowCount"})({alignItems:"center",display:"flex",margin:yr.spacing(0,2)}),lp=gt((function(e,t){const{className:o,rowCount:l,visibleRowCount:i}=e,a=F(e,np),s=ut(),c=pt(),d=(e=>{const{classes:t}=e;return I({root:["rowCount"]},lr,t)})(c);if(0===l)return null;const p=i<l?s.current.getLocaleText("footerTotalVisibleRows")(i,l):l.toLocaleString();return r.jsxs(op,u({className:n(d.root,o),ownerState:c},a,{ref:t,children:[s.current.getLocaleText("footerTotalRows")," ",p]}))})),ip=["selected","rowId","row","index","style","rowHeight","className","visibleColumns","pinnedColumns","offsetLeft","columnsTotalWidth","firstColumnIndex","lastColumnIndex","focusedColumnIndex","isFirstVisible","isLastVisible","isNotVisible","showBottomBorder","scrollbarWidth","gridHasFiller","onClick","onDoubleClick","onMouseEnter","onMouseLeave","onMouseOut","onMouseOver"],ap=Gt(za,((e,t)=>!!t&&!!Do(e))),sp=gt((function(e,t){const{selected:o,rowId:l,row:i,index:a,style:s,rowHeight:c,className:d,visibleColumns:p,pinnedColumns:f,offsetLeft:m,columnsTotalWidth:h,firstColumnIndex:b,lastColumnIndex:w,focusedColumnIndex:C,isFirstVisible:y,isLastVisible:x,isNotVisible:S,showBottomBorder:R,scrollbarWidth:M,gridHasFiller:k,onClick:P,onDoubleClick:E,onMouseEnter:H,onMouseLeave:T,onMouseOut:D,onMouseOver:O}=e,$=F(e,ip),j=Fi(),L=Jn(),z=g.useRef(null),A=pt(),B=Li(j),V=zt(j,Lo),N=zt(j,jr),G=zt(j,Dn),W=A.rowReordering,U=zt(j,ap,W),K=v(z,t),_=Tr(j,l),q=zt(j,Aa,{rowId:l,editMode:A.editMode}),X=A.editMode===on.Row,Q=void 0!==C,Y=Q&&C>=f.left.length&&C<b,J=Q&&C<p.length-f.right.length&&C>=w,Z=function(e,t){return I(t,lr,e)}(A.classes,{root:["row",o&&"selected",X&&"row--editable",q&&"row--editing",y&&"row--firstVisible",x&&"row--lastVisible",R&&"row--borderBottom","auto"===c&&"row--dynamicHeight"]}),ee=L.hooks.useGridRowAriaAttributes();g.useLayoutEffect((()=>{if(B.range){const e=j.current.getRowIndexRelativeToVisibleRows(l);void 0!==e&&j.current.unstable_setLastMeasuredRowIndex(e)}if(z.current&&"auto"===c)return j.current.observeRowHeight(z.current,l)}),[j,B.range,c,l]);const te=g.useCallback(((e,t)=>r=>{Xs(r)||j.current.getRow(l)&&(j.current.publishEvent(e,j.current.getRowParams(l),r),t&&t(r))}),[j,l]),re=g.useCallback((e=>{const t=Ks(e.target,ir.cell),r=t?.getAttribute("data-field");if(r){if(r===zl.field)return;if(r===So)return;if("__reorder__"===r)return;if(j.current.getCellMode(l,r)===ln.Edit)return;const e=j.current.getColumn(r);if(e?.type===ao)return}te("rowClick",P)(e)}),[j,P,te,l]),{slots:ne,slotProps:oe,disableColumnReorder:le}=A,ie=zt(j,(()=>u({},j.current.getRowHeightEntry(l))),void 0,Tt),ae=g.useMemo((()=>{if(S)return{opacity:0,width:0,height:0};const e=u({},s,{maxHeight:"auto"===c?"none":c,minHeight:c,"--height":"number"==typeof c?`${c}px`:c});if(ie.spacingTop&&(e["border"===A.rowSpacingType?"borderTopWidth":"marginTop"]=ie.spacingTop),ie.spacingBottom){const t="border"===A.rowSpacingType?"borderBottomWidth":"marginBottom";let r=e[t];"number"!=typeof r&&(r=parseInt(r||"0",10)),r+=ie.spacingBottom,e[t]=r}return e}),[S,c,s,ie,A.rowSpacingType]),se=j.current.unstable_applyPipeProcessors("rowClassName",[],l),ce=ee(_,a);if("function"==typeof A.getRowClassName){const e=a-(B.range?.firstRowIndex||0),t=u({},j.current.getRowParams(l),{isFirstVisible:0===e,isLastVisible:e===B.rows.length-1,indexRelativeToCurrentPage:e});se.push(A.getRowClassName(t))}const ue=(e,t,n,o,a=Ro.NONE)=>{const s=j.current.unstable_getCellColSpanInfo(l,n);if(s?.spannedByColSpan)return null;const d=s?.cellProps.width??e.computedWidth,p=s?.cellProps.colSpan??1,f=Vd(a,e.computedWidth,n,G,h,M);if("skeletonRow"===_.type)return r.jsx(ne.skeletonCell,{type:e.type,width:d,height:c,field:e.field,align:e.align},e.field);const g="__reorder__"===e.field,m=!(le||e.disableReorder),b=U&&!V.length&&N<=1,w=!(m||g&&b),C=a===Ro.VIRTUAL,v=Gd(a,t),y=Nd(a,t,o,A.showCellVerticalBorder,k);return r.jsx(ne.cell,u({column:e,width:d,rowId:l,align:e.align||"left",colIndex:n,colSpan:p,disableDragEvents:w,isNotVisible:C,pinnedOffset:f,pinnedPosition:a,showLeftBorder:v,showRightBorder:y,row:i,rowNode:_},oe?.cell),e.field)},de=f.left.map(((e,t)=>ue(e,t,t,f.left.length,Ro.LEFT))),pe=f.right.map(((e,t)=>{const r=p.length-f.right.length+t;return ue(e,t,r,f.right.length,Ro.RIGHT)})),fe=p.length-f.left.length-f.right.length,ge=[];Y&&ge.push(ue(p[C],C-f.left.length,C,fe,Ro.VIRTUAL));for(let r=b;r<w;r+=1){const e=p[r],t=r-f.left.length;e&&ge.push(ue(e,t,r,fe))}J&&ge.push(ue(p[C],C-f.left.length,C,fe,Ro.VIRTUAL));const me=i?{onClick:re,onDoubleClick:te("rowDoubleClick",E),onMouseEnter:te("rowMouseEnter",H),onMouseLeave:te("rowMouseLeave",T),onMouseOut:te("rowMouseOut",D),onMouseOver:te("rowMouseOver",O)}:null;return r.jsxs("div",u({"data-id":l,"data-rowindex":a,role:"row",className:n(...se,Z.root,d),style:ae},ce,me,$,{ref:K,children:[de,r.jsx("div",{role:"presentation",className:ir.cellOffsetLeft,style:{width:m}}),ge,r.jsx("div",{role:"presentation",className:n(ir.cell,ir.cellEmpty)}),pe,0!==M&&r.jsx(Ud,{pinnedRight:f.right.length>0,borderTop:!y})]}))})),cp=or(sp);function up({privateApiRef:e,configuration:t,props:n,children:o}){const l=g.useRef(e.current.getPublicApi());return r.jsx(Yn.Provider,{value:t,children:r.jsx(dt.Provider,{value:n,children:r.jsx(Ei.Provider,{value:e,children:r.jsx(ct.Provider,{value:l,children:r.jsx(Is,{children:r.jsx(to,{children:o})})})})})})}const dp=function(){try{const e="__some_random_key_you_are_not_going_to_use__";return window.localStorage.setItem(e,e),window.localStorage.removeItem(e),!0}catch(e){return!1}}()&&null!=window.localStorage.getItem("DEBUG"),pp=()=>{},fp={debug:pp,info:pp,warn:pp,error:pp},gp=["debug","info","warn","error"];function mp(e,t,r=console){const n=gp.indexOf(t);if(-1===n)throw new Error(`MUI X: Log level ${t} not recognized.`);return gp.reduce(((t,o,l)=>(t[o]=l>=n?(...t)=>{const[n,...l]=t;r[o](`MUI X: ${e} - ${n}`,...l)}:pp,t)),{})}function hp(e){return hp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},hp(e)}function bp(e){var t=function(e){if("object"!=hp(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=hp(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==hp(t)?t:t+""}const wp=(e,t,r,n=!0)=>{const o=g.useRef(null),l=g.useRef(`mui-${Math.round(1e9*Math.random())}`),i=g.useCallback((()=>{o.current=e.current.registerPipeProcessor(t,l.current,r)}),[e,r,t]);$i((()=>{n&&i()}));const a=g.useRef(!0);g.useEffect((()=>(a.current?a.current=!1:n&&i(),()=>{o.current&&(o.current(),o.current=null)})),[i,n])},Cp=(e,t,r)=>{const n=g.useRef(null),o=g.useRef(`mui-${Math.round(1e9*Math.random())}`),l=g.useCallback((()=>{n.current=e.current.registerPipeApplier(t,o.current,r)}),[e,r,t]);$i((()=>{l()}));const i=g.useRef(!0);g.useEffect((()=>(i.current?i.current=!1:l(),()=>{n.current&&(n.current(),n.current=null)})),[l])};let vp=function(e){return e.DataSource="dataSource",e.RowTree="rowTree",e}({});const yp=(e,t,r,n)=>{const o=g.useCallback((()=>{e.current.registerStrategyProcessor(t,r,n)}),[e,n,r,t]);$i((()=>{o()}));const l=g.useRef(!0);g.useEffect((()=>{l.current?l.current=!1:o()}),[o])},xp="none",Sp={dataSourceRowsUpdate:vp.DataSource,rowTreeCreation:vp.RowTree,filtering:vp.RowTree,sorting:vp.RowTree,visibleRowsLookupCreation:vp.RowTree},Rp=(e,t)=>u({},e,{props:{getRowId:t.getRowId}}),Ip=(e,t,r)=>{const n=g.useRef(!1);n.current||(t.current.state=e(t.current.state,r,t),n.current=!0)};function Mp(e,t){if(null==e)return"";const r="string"==typeof e?e:`${e}`;if(t.shouldAppendQuotes||t.escapeFormulas){const e=r.replace(/"/g,'""');return t.escapeFormulas&&["=","+","-","@","\t","\r"].includes(e[0])?`"'${e}"`:[t.delimiter,"\n","\r",'"'].some((e=>r.includes(e)))?`"${e}"`:e}return r}const kp=(e,t)=>{const{csvOptions:r,ignoreValueFormatter:n}=t;let o;if(n){const t=e.colDef.type;o="number"===t?String(e.value):"date"===t||"dateTime"===t?e.value?.toISOString():"function"==typeof e.value?.toString?e.value.toString():e.value}else o=e.formattedValue;return Mp(o,r)};class Pp{constructor(e){this.options=void 0,this.rowString="",this.isEmpty=!0,this.options=e}addValue(e){this.isEmpty||(this.rowString+=this.options.csvOptions.delimiter),"function"==typeof this.options.sanitizeCellValue?this.rowString+=this.options.sanitizeCellValue(e,this.options.csvOptions):this.rowString+=e,this.isEmpty=!1}getRowString(){return this.rowString}}function Ep(e){const t=document.createElement("span");t.style.whiteSpace="pre",t.style.userSelect="all",t.style.opacity="0px",t.textContent=e,document.body.appendChild(t);const r=document.createRange();r.selectNode(t);const n=window.getSelection();n.removeAllRanges(),n.addRange(r);try{document.execCommand("copy")}finally{document.body.removeChild(t)}}const Fp=e=>u({},e,{columnMenu:{open:!1}}),Hp=(e,t,r)=>{const n=Si({apiRef:r,columnsToUpsert:t.columns,initialState:t.initialState?.columns,columnVisibilityModel:t.columnVisibilityModel??t.initialState?.columns?.columnVisibilityModel??{},keepOnlyColumnsToUpsert:!0});return u({},e,{columns:n,pinnedColumns:e.pinnedColumns??yn})};function Tp(e){return t=>u({},t,{columns:e})}const Dp=(e,t)=>u({},e,{density:t.initialState?.density??t.density??"standard"}),Op=({apiRef:e,options:t})=>{const r=Mn(e);return t.fields?t.fields.reduce(((e,t)=>{const n=r.find((e=>e.field===t));return n&&e.push(n),e}),[]):(t.allColumns?r:En(e)).filter((e=>!e.disableExport))},$p=({apiRef:e})=>{const t=_o(e),r=Hr(e),n=rl(e),o=t.filter((e=>"footer"!==r[e].type)),l=Ar(e),i=l?.top?.map((e=>e.id))||[],a=l?.bottom?.map((e=>e.id))||[];if(o.unshift(...i),o.push(...a),n>0){const t=nl(e);return o.filter((e=>t.has(e)))}return o},jp=(e,t,r)=>{let n=e.paginationModel;const o=e.rowCount,l=r?.pageSize??n.pageSize,i=r?.page??n.page,a=Cl(o,l,i);!r||r?.page===n.page&&r?.pageSize===n.pageSize||(n=r);const s=-1===l?0:((e,t=0)=>0===t?e:Math.max(Math.min(e,t-1),0))(n.page,a);return s!==n.page&&(n=u({},n,{page:s})),yl(n.pageSize,t),n},Lp=(e,t,r)=>{const n=t.filterModel??t.initialState?.filter?.filterModel??wn();return u({},e,{filter:u({filterModel:An(n,t.disableMultipleColumnsFiltering,r)},bn),visibleRowsLookup:{}})},zp=e=>e.filteredRowsLookup;function Ap(e,t){return e.current.applyStrategyProcessor("visibleRowsLookupCreation",{tree:t.rows.tree,filteredRowsLookup:t.filter.filteredRowsLookup})}function Bp(){return wt(Object.values)}const Vp=e=>u({},e,{focus:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null},tabIndex:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}}),Np=({currentColIndex:e,firstColIndex:t,lastColIndex:r,isRtl:n})=>{if(n){if(e<r)return e+1}else if(!n&&e>t)return e-1;return null},Gp=({currentColIndex:e,firstColIndex:t,lastColIndex:r,isRtl:n})=>{if(n){if(e>t)return e-1}else if(!n&&e<r)return e+1;return null},Wp=Ut(Dl,Ar,((e,t)=>(t.top||[]).concat(e.rows,t.bottom||[]))),Up=(e,t)=>{const r=u({},vl(t.autoPageSize),t.paginationModel??t.initialState?.pagination?.paginationModel);yl(r.pageSize,t.signature);const n=t.rowCount??t.initialState?.pagination?.rowCount??("client"===t.paginationMode?e.rows?.totalRowCount:void 0),o=t.paginationMeta??t.initialState?.pagination?.meta??{};return u({},e,{pagination:u({},e.pagination,{paginationModel:r,rowCount:n,meta:o,enabled:!0===t.pagination,paginationMode:t.paginationMode})})},Kp=(e,t)=>u({},e,{preferencePanel:t.initialState?.preferencePanel??{open:!1}}),_p=e=>{switch(e.type){case"boolean":return!1;case"date":case"dateTime":case"number":return;case"singleSelect":return null;default:return""}},qp=["id","field"],Xp=["id","field"],Qp=["id"],Yp=["id"],Jp=e=>u({},e,{editRows:{}}),Zp=(e,t,r)=>{const n=!!t.dataSource;return r.current.caches.rows=fo({rows:n?[]:t.rows,getRowId:t.getRowId,loading:t.loading,rowCount:t.rowCount}),u({},e,{rows:mo({apiRef:r,rowCountProp:t.rowCount,loadingProp:!!n||t.loading,previousTree:null,previousTreeDepths:null})})},ef=e=>"full"===e.updates.type?(e=>{const t={[co]:u({},{type:"group",id:co,depth:-1,groupingField:null,groupingKey:null,isAutoGenerated:!0,children:[],childrenFromPath:{},childrenExpanded:!0,parent:null},{children:e})};for(let r=0;r<e.length;r+=1){const n=e[r];t[n]={id:n,depth:0,parent:co,type:"leaf",groupingKey:null}}return{groupingName:xp,tree:t,treeDepths:{0:e.length},dataRowIds:e}})(e.updates.rows):(({previousTree:e,actions:t})=>{const r=u({},e),n={};for(let i=0;i<t.remove.length;i+=1){const e=t.remove[i];n[e]=!0,delete r[e]}for(let i=0;i<t.insert.length;i+=1){const e=t.insert[i];r[e]={id:e,depth:0,parent:co,type:"leaf",groupingKey:null}}const o=r[co];let l=[...o.children,...t.insert];return Object.values(n).length&&(l=l.filter((e=>!n[e]))),r[co]=u({},o,{children:l}),{groupingName:xp,tree:r,treeDepths:{0:l.length},dataRowIds:l}})({previousTree:e.previousTree,actions:e.updates.actions});class tf extends Error{}const rf={type:"include",ids:new Set},nf=(e,t)=>u({},e,{rowSelection:t.rowSelection?t.rowSelectionModel??rf:rf}),of=(e,t)=>{const r=t.sortModel??t.initialState?.sorting?.sortModel??[];return u({},e,{sorting:{sortModel:Vr(r,t.disableMultipleColumnsSorting),sortedRows:[]}})};function lf(e){const{containerSize:t,scrollPosition:r,elementSize:n,elementOffset:o}=e,l=o+n;return n>t?o:l-t>r?l-t:o<r?o:void 0}const af={autoHeight:!1,autoPageSize:!1,autosizeOnMount:!1,checkboxSelection:!1,checkboxSelectionVisibleOnly:!1,clipboardCopyCellDelimiter:"\t",columnBufferPx:150,columnHeaderHeight:56,disableAutosize:!1,disableColumnFilter:!1,disableColumnMenu:!1,disableColumnReorder:!1,disableColumnResize:!1,disableColumnSelector:!1,disableColumnSorting:!1,disableDensitySelector:!1,disableEval:!1,disableMultipleColumnsFiltering:!1,disableMultipleColumnsSorting:!1,disableMultipleRowSelection:!1,disableRowSelectionOnClick:!1,disableVirtualization:!1,editMode:on.Cell,filterDebounceMs:150,filterMode:"client",hideFooter:!1,hideFooterPagination:!1,hideFooterRowCount:!1,hideFooterSelectedRowCount:!1,ignoreDiacritics:!1,ignoreValueFormatterDuringExport:!1,keepColumnPositionIfDraggedOutside:!1,keepNonExistentRowsSelected:!1,loading:!1,logger:console,logLevel:"error",pageSizeOptions:[25,50,100],pagination:!1,paginationMode:"client",resizeThrottleMs:60,rowBufferPx:150,rowHeight:52,rows:[],rowSelection:!0,rowSpacingType:"margin",rowSpanning:!1,showCellVerticalBorder:!1,showColumnVerticalBorder:!1,showToolbar:!1,sortingMode:"client",sortingOrder:["asc","desc",null],throttleRowsMs:0,virtualizeColumnsWithAutoRowHeight:!1},sf={width:0,height:0},cf={isReady:!1,root:sf,viewportOuterSize:sf,viewportInnerSize:sf,contentSize:sf,minimumSize:sf,hasScrollX:!1,hasScrollY:!1,scrollbarSize:0,headerHeight:0,groupHeaderHeight:0,headerFilterHeight:0,rowWidth:0,rowHeight:0,columnsTotalWidth:0,leftPinnedWidth:0,rightPinnedWidth:0,headersTotalHeight:0,topContainerHeight:0,bottomContainerHeight:0},uf=(e,t,r)=>{const n=cf,o=hr(r);return u({},e,{dimensions:u({},n,ff(t,r,o,Tn(r)))})},df=Gt(En,Dn,((e,t)=>{const r=e.length;return 0===r?0:Zi(t[r-1]+e[r-1].computedWidth,1)}));function pf(e,t){const r=(t,r)=>e.style.setProperty(t,r);r("--DataGrid-hasScrollX",`${Number(t.hasScrollX)}`),r("--DataGrid-hasScrollY",`${Number(t.hasScrollY)}`),r("--DataGrid-scrollbarSize",`${t.scrollbarSize}px`),r("--DataGrid-rowWidth",`${t.rowWidth}px`),r("--DataGrid-columnsTotalWidth",`${t.columnsTotalWidth}px`),r("--DataGrid-leftPinnedWidth",`${t.leftPinnedWidth}px`),r("--DataGrid-rightPinnedWidth",`${t.rightPinnedWidth}px`),r("--DataGrid-headerHeight",`${t.headerHeight}px`),r("--DataGrid-headersTotalHeight",`${t.headersTotalHeight}px`),r("--DataGrid-topContainerHeight",`${t.topContainerHeight}px`),r("--DataGrid-bottomContainerHeight",`${t.bottomContainerHeight}px`),r("--height",`${t.rowHeight}px`)}function ff(e,t,r,n){const o=yo(e.rowHeight,af.rowHeight);return{rowHeight:Math.floor(o*r),headerHeight:Math.floor(e.columnHeaderHeight*r),groupHeaderHeight:Math.floor((e.columnGroupHeaderHeight??e.columnHeaderHeight)*r),headerFilterHeight:Math.floor((e.headerFilterHeight??e.columnHeaderHeight)*r),columnsTotalWidth:df(t),headersTotalHeight:Ri(t,e),leftPinnedWidth:n.left.reduce(((e,t)=>e+t.computedWidth),0),rightPinnedWidth:n.right.reduce(((e,t)=>e+t.computedWidth),0)}}const gf=new WeakMap,mf=void 0!==globalThis.ResizeObserver?globalThis.ResizeObserver:class{observe(){}unobserve(){}disconnect(){}},hf=(e,t,r)=>{r.current.caches.rowsMeta={heights:new Map};const n=qt(r),o=kr(r),l=xl(r),i=Math.min(l.enabled?l.paginationModel.pageSize:o,o);return u({},e,{rowsMeta:{currentPageTotalHeight:i*n,positions:Array.from({length:i},((e,t)=>t*n)),pinnedTopRowsTotalHeight:0,pinnedBottomRowsTotalHeight:0}})};function bf(e){const{apiRef:t,lookup:r,columnIndex:n,rowId:o,minFirstColumnIndex:l,maxLastColumnIndex:i,columns:a}=e,s=a.length,c=a[n],u=t.current.getRow(o),d=t.current.getRowValue(u,c),p="function"==typeof c.colSpan?c.colSpan(d,u,c,t):c.colSpan;if(!p||1===p)return wf(r,o,n,{spannedByColSpan:!1,cellProps:{colSpan:1,width:c.computedWidth}}),{colSpan:1};let f=c.computedWidth;for(let g=1;g<p;g+=1){const e=n+g;e>=l&&e<i&&(f+=a[e].computedWidth,wf(r,o,n+g,{spannedByColSpan:!0,rightVisibleCellIndex:Math.min(n+p,s-1),leftVisibleCellIndex:n})),wf(r,o,n,{spannedByColSpan:!1,cellProps:{colSpan:p,width:f}})}return{colSpan:p}}function wf(e,t,r,n){e[t]||(e[t]={}),e[t][r]=n}const Cf=(e,t,r)=>{if(hn(e)){if(void 0!==r[e.field])throw new Error(["MUI X: columnGroupingModel contains duplicated field",`column field ${e.field} occurs two times in the grouping model:`,`- ${r[e.field].join(" > ")}`,`- ${t.join(" > ")}`].join("\n"));return void(r[e.field]=t)}const{groupId:n,children:o}=e;o.forEach((e=>{Cf(e,[...t,n],r)}))},vf=e=>{if(!e)return{};const t={};return e.forEach((e=>{Cf(e,[],t)})),t},yf=(e,t,r)=>{const n=e=>t[e]??[],o=[],l=Math.max(...e.map((e=>n(e).length))),i=(e,t,r)=>nd(n(e).slice(0,r+1),n(t).slice(0,r+1));for(let a=0;a<l;a+=1){const t=e.reduce(((e,t)=>{const o=n(t)[a]??null;if(0===e.length)return[{columnFields:[t],groupId:o}];const l=e[e.length-1],s=l.columnFields[l.columnFields.length-1];return l.groupId!==o||!i(s,t,a)||(c=s,u=t,r?.left&&r.left.includes(c)&&!r.left.includes(u)||r?.right&&!r.right.includes(c)&&r.right.includes(u))?[...e,{columnFields:[t],groupId:o}]:[...e.slice(0,e.length-1),{columnFields:[...l.columnFields,t],groupId:o}];var c,u}),[]);o.push(t)}return o},xf=["groupId","children"],Sf=e=>{let t={};return e.forEach((e=>{if(hn(e))return;const{groupId:r,children:n}=e,o=F(e,xf);if(!r)throw new Error("MUI X: An element of the columnGroupingModel does not have either `field` or `groupId`.");const l=u({},o,{groupId:r}),i=Sf(n);if(void 0!==i[r]||void 0!==t[r])throw new Error(`MUI X: The groupId ${r} is used multiple times in the columnGroupingModel.`);t=u({},t,i,{[r]:l})})),u({},t)},Rf=(e,t,r)=>{if(!t.columnGroupingModel)return e;const n=Rn(r),o=Fn(r),l=Sf(t.columnGroupingModel??[]),i=vf(t.columnGroupingModel??[]),a=yf(n,i,r.current.state.pinnedColumns??{}),s=0===o.length?0:Math.max(...o.map((e=>i[e]?.length??0)));return u({},e,{columnGrouping:{lookup:l,unwrappedGroupingModel:i,headerStructure:a,maxDepth:s}})};function If(e,t){if(void 0!==t&&e.changedTouches){for(let r=0;r<e.changedTouches.length;r+=1){const n=e.changedTouches[r];if(n.identifier===t)return{x:n.clientX,y:n.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function Mf(e,t,r,n){let o=e;return o+="Right"===n?t-r.left:r.right-t,o}function kf(e){e.preventDefault(),e.stopImmediatePropagation()}const Pf=e=>u({},e,{columnResize:{resizingColumnField:""}});function Ef(){return{colDef:void 0,initialColWidth:0,initialTotalWidth:0,previousMouseClickEvent:void 0,columnHeaderElement:void 0,headerFilterElement:void 0,groupHeaderElements:[],cellElements:[],leftPinnedCellsAfter:[],rightPinnedCellsBefore:[],fillerLeft:void 0,fillerRight:void 0,leftPinnedHeadersAfter:[],rightPinnedHeadersBefore:[]}}function Ff(e,t,r){e&&(e.style[t]=`${parseInt(e.style[t],10)+r}px`)}function Hf(e){return 0!==e.firstRowIndex||0!==e.lastRowIndex}const Tf=(e,t,r)=>{if(!e)return null;let n=e[t.field];const o=t.rowSpanValueGetter??t.valueGetter;return o&&(n=o(n,e,t,r)),n},Df={spannedCells:{},hiddenCells:{},hiddenCellOriginMap:{}},Of={firstRowIndex:0,lastRowIndex:0},$f=new Set([Ll,"__reorder__",So]),jf=(e,t,r,n,o,l,i)=>{const a=l?{}:u({},e.current.state.rowSpanning.spannedCells),s=l?{}:u({},e.current.state.rowSpanning.hiddenCells),c=l?{}:u({},e.current.state.rowSpanning.hiddenCellOriginMap);return l&&(i=Of),t.forEach((t=>{if(!$f.has(t.field)){for(let l=o.firstRowIndex;l<o.lastRowIndex;l+=1){const i=r[l];if(s[i.id]?.[t.field])continue;const u=Tf(i.model,t,e);if(null==u)continue;let d=i.id,p=l,f=0;const g=[];if(l===o.firstRowIndex){let o=l-1,i=r[o];for(;o>=n.firstRowIndex&&i&&Tf(i.model,t,e)===u;){const e=r[o+1];s[e.id]?s[e.id][t.field]=!0:s[e.id]={[t.field]:!0},g.push(l),f+=1,d=i.id,p=o,o-=1,i=r[o]}}g.forEach((e=>{c[e]?c[e][t.field]=p:c[e]={[t.field]:p}}));let m=l+1;for(;m<=n.lastRowIndex&&r[m]&&Tf(r[m].model,t,e)===u;){const e=r[m];s[e.id]?s[e.id][t.field]=!0:s[e.id]={[t.field]:!0},c[m]?c[m][t.field]=p:c[m]={[t.field]:p},m+=1,f+=1}f>0&&(a[d]?a[d][t.field]=f+1:a[d]={[t.field]:f+1})}i={firstRowIndex:Math.min(i.firstRowIndex,o.firstRowIndex),lastRowIndex:Math.max(i.lastRowIndex,o.lastRowIndex)}}})),{spannedCells:a,hiddenCells:s,hiddenCellOriginMap:c,processedRange:i}},Lf=(e,t)=>{const r=Lr(t).length;if(e.pagination){const e=Pl(t);let n=20;return e>0&&(n=e-1),{firstRowIndex:0,lastRowIndex:Math.min(n,r)}}return{firstRowIndex:0,lastRowIndex:Math.min(20,r)}},zf=(e,t,r)=>{if(!t.rowSpanning)return u({},e,{rowSpanning:Df});const n=e.rows.dataRowIds||[],o=e.columns.orderedFields||[],l=e.rows.dataRowIdToModelLookup,i=e.columns.lookup,a=Boolean(e.filter.filterModel.items.length)||Boolean(e.filter.filterModel.quickFilterValues?.length);if(!n.length||!o.length||!l||!i||a)return u({},e,{rowSpanning:Df});const s=Lf(t,r),c=n.map((e=>({id:e,model:l[e]}))),d=o.map((e=>i[e])),{spannedCells:p,hiddenCells:f,hiddenCellOriginMap:g}=jf(r,d,c,s,s,!0,Of);return u({},e,{rowSpanning:{spannedCells:p,hiddenCells:f,hiddenCellOriginMap:g}})},Af=(e,t,r)=>u({},e,{listViewColumn:t.listViewColumn?u({},t.listViewColumn,{computedWidth:Bf(r)}):void 0});function Bf(e){return Kt(e).viewportInnerSize.width}const Vf=Gt(Bo,Lo,Rl,((e,t,r)=>({groupKeys:[],paginationModel:r,sortModel:t,filterModel:e,start:r.page*r.pageSize,end:r.page*r.pageSize+r.pageSize-1})));let Nf=function(e){return e.Default="set-new-rows",e.LazyLoading="replace-row-range",e}({});class Gf{constructor(e){this.chunkSize=void 0,this.getCacheKeys=e=>{if(this.chunkSize<1||"number"!=typeof e.start)return[e];const t=[];for(let r=e.start;r<e.end;r+=this.chunkSize){const n=Math.min(r+this.chunkSize-1,e.end);t.push(u({},e,{start:r,end:n}))}return t},this.splitResponse=(e,t)=>{const r=this.getCacheKeys(e),n=new Map;return r.forEach((r=>{const o=r.end===e.end,l=u({},t,{pageInfo:u({},t.pageInfo,{hasNextPage:void 0!==t.pageInfo?.hasNextPage&&!o||t.pageInfo?.hasNextPage,nextCursor:void 0===t.pageInfo?.nextCursor||o?t.pageInfo?.nextCursor:t.rows[r.end+1].id}),rows:"number"!=typeof r.start||"number"!=typeof e.start?t.rows:t.rows.slice(r.start-e.start,r.end-e.start+1)});n.set(r,l)})),n},this.chunkSize=e}}Gf.mergeResponses=e=>1===e.length?e[0]:e.reduce(((e,t)=>({rows:[...e.rows,...t.rows],rowCount:t.rowCount,pageInfo:t.pageInfo})),{rows:[],rowCount:0,pageInfo:{}});const Wf={clear:()=>{},get:()=>{},set:()=>{}};function Uf(e,t={}){return null===e?Wf:e??new Ua(t)}const Kf=(e,n)=>{((e,r)=>{(e=>{const t=g.useRef(null),r=g.useRef(null),n=g.useRef(null),o=g.useRef(null),l=g.useRef(null),i=g.useRef(null);e.current.register("public",{rootElementRef:t}),e.current.register("private",{mainElementRef:r,virtualScrollerRef:n,virtualScrollbarVerticalRef:o,virtualScrollbarHorizontalRef:l,columnHeadersContainerRef:i})})(e),((e,t)=>{g.useEffect((()=>{e.current.setState((e=>u({},e,{props:{getRowId:t.getRowId}})))}),[e,t.getRowId])})(e,r),(e=>{const r=t();void 0===e.current.state.isRtl&&(e.current.state.isRtl=r);const n=g.useRef(!0);g.useEffect((()=>{n.current?n.current=!1:e.current.setState((e=>u({},e,{isRtl:r})))}),[e,r])})(e),((e,t)=>{Ti(e,{getLogger:g.useCallback((e=>dp?mp(e,"debug",t.logger):t.logLevel?mp(e,t.logLevel.toString(),t.logger):fp),[t.logLevel,t.logger])},"private")})(e,r),(e=>{const t=g.useRef({}),r=g.useCallback((e=>{t.current[e.stateId]=e}),[]),n=g.useCallback(((r,n)=>{let o;if(o="function"==typeof r?r(e.current.state):r,e.current.state===o)return!1;const l={current:{state:o}};let i=!1;const a=[];if(Object.keys(t.current).forEach((r=>{const n=t.current[r],o=n.stateSelector(e),s=n.stateSelector(l);s!==o&&(a.push({stateId:n.stateId,hasPropChanged:s!==n.propModel}),void 0!==n.propModel&&s!==n.propModel&&(i=!0))})),a.length>1)throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${a[0].stateId}, therefore, you're not allowed to update ${a.map((e=>e.stateId)).join(", ")} in the same transaction.`);if(i||(e.current.state=o,e.current.publishEvent("stateChange",o),e.current.store.update(o)),1===a.length){const{stateId:r,hasPropChanged:o}=a[0],s=t.current[r],c=s.stateSelector(l);s.propOnChange&&o&&s.propOnChange(c,{reason:n,api:e.current}),i||e.current.publishEvent(s.changeEvent,c,{reason:n})}return!i}),[e]),o={updateControlState:g.useCallback(((t,r,n)=>e.current.setState((e=>u({},e,{[t]:r(e[t])})),n)),[e]),registerControlState:r};Ti(e,{setState:n},"public"),Ti(e,o,"private")})(e),(e=>{const t=g.useRef({}),r=g.useRef(!1),n=g.useCallback((e=>{!r.current&&e&&(r.current=!0,Object.values(e.appliers).forEach((e=>{e()})),r.current=!1)}),[]),o=g.useCallback(((e,r,o)=>{t.current[e]||(t.current[e]={processors:new Map,processorsAsArray:[],appliers:{}});const l=t.current[e];return l.processors.get(r)!==o&&(l.processors.set(r,o),l.processorsAsArray=Array.from(t.current[e].processors.values()),n(l)),()=>{t.current[e].processors.delete(r),t.current[e].processorsAsArray=Array.from(t.current[e].processors.values())}}),[n]),l=g.useCallback(((e,r,n)=>(t.current[e]||(t.current[e]={processors:new Map,processorsAsArray:[],appliers:{}}),t.current[e].appliers[r]=n,()=>{const n=t.current[e].appliers,o=F(n,[r].map(bp));t.current[e].appliers=o})),[]),i=g.useCallback((e=>{n(t.current[e])}),[n]),a={unstable_applyPipeProcessors:g.useCallback(((...e)=>{const[r,n,o]=e;if(!t.current[r])return n;const l=t.current[r].processorsAsArray;let i=n;for(let t=0;t<l.length;t+=1)i=l[t](i,o);return i}),[])};Ti(e,{registerPipeProcessor:o,registerPipeApplier:l,requestPipeProcessorsApplication:i},"private"),Ti(e,a,"public")})(e),(e=>{const t=g.useRef(new Map),r=g.useRef({}),n=g.useCallback(((t,n,o)=>{const l=()=>{const e=r.current[n],o=F(e,[t].map(bp));r.current[n]=o};r.current[n]||(r.current[n]={});const i=r.current[n],a=i[t];return i[t]=o,a&&a!==o?(t===e.current.getActiveStrategy(Sp[n])&&e.current.publishEvent("activeStrategyProcessorChange",n),l):l}),[e]),o=g.useCallback(((t,n)=>{const o=e.current.getActiveStrategy(Sp[t]);if(null==o)throw new Error("Can't apply a strategy processor before defining an active strategy");const l=r.current[t];if(!l||!l[o])throw new Error(`No processor found for processor "${t}" on strategy "${o}"`);return(0,l[o])(n)}),[e]),l=g.useCallback((e=>{const r=Array.from(t.current.entries()).find((([,t])=>t.group===e&&t.isAvailable()));return r?.[0]??xp}),[]),i=g.useCallback(((r,n,o)=>{t.current.set(n,{group:r,isAvailable:o}),e.current.publishEvent("strategyAvailabilityChange")}),[e]);Ti(e,{registerStrategyProcessor:n,applyStrategyProcessor:o,getActiveStrategy:l,setStrategyAvailability:i},"private")})(e),((e,t)=>{const r=g.useCallback((e=>{if(null==t.localeText[e])throw new Error(`Missing translation for key ${e}.`);return t.localeText[e]}),[t.localeText]);e.current.register("public",{getLocaleText:r})})(e,r),e.current.register("private",{rootProps:r})})(e,n),((e,t)=>{const r=(e=>{const{classes:t}=e;return g.useMemo((()=>I({cellCheckbox:["cellCheckbox"],columnHeaderCheckbox:["columnHeaderCheckbox"]},lr,t)),[t])})({classes:t.classes}),n=g.useCallback((n=>{const o=u({},zl,{cellClassName:r.cellCheckbox,headerClassName:r.columnHeaderCheckbox,headerName:e.current.getLocaleText("checkboxSelectionHeaderName")}),l=t.checkboxSelection,i=null!=n.lookup[Ll];return l&&!i?(n.lookup[Ll]=o,n.orderedFields=[Ll,...n.orderedFields]):!l&&i?(delete n.lookup[Ll],n.orderedFields=n.orderedFields.filter((e=>e!==Ll))):l&&i&&(n.lookup[Ll]=u({},o,n.lookup[Ll])),n}),[e,r,t.checkboxSelection]);wp(e,"hydrateColumns",n)})(e,n),(e=>{yp(e,xp,"rowTreeCreation",ef)})(e),Ip(Rp,e,n),Ip(nf,e,n),Ip(Hp,e,n),Ip(Zp,e,n),Ip(Up,e,n),Ip(Jp,e,n),Ip(Vp,e,n),Ip(of,e,n),Ip(Kp,e,n),Ip(Lp,e,n),Ip(zf,e,n),Ip(Dp,e,n),Ip(Pf,e,n),Ip(Fp,e,n),Ip(Rf,e,n),Ip(Ki,e,n),Ip(uf,e,n),Ip(hf,e,n),Ip(Af,e,n),((e,r)=>{const n=Di(e,"useGridKeyboardNavigation"),o=t(),l=r.listView,i=g.useCallback((()=>Wp(e)),[e]),a="DataGrid"!==r.signature&&r.headerFilters,s=g.useCallback(((t,r,o="left",i="up")=>{const a=Wo(e),s=e.current.unstable_getCellColSpanInfo(r,t);s&&s.spannedByColSpan&&("left"===o?t=s.leftVisibleCellIndex:"right"===o&&(t=s.rightVisibleCellIndex));const c=l?Yi(e).field:Fn(e)[t],u=function(e,t,r,n){const o=qi(e);if(!o[t]?.[r])return t;const l=_o(e);let i=l.indexOf(t)+("down"===n?1:-1);for(;i>=0&&i<l.length;){const e=l[i];if(!o[e]?.[r])return e;i+="down"===n?1:-1}return t}(e,r,c,i),d=a.findIndex((e=>e.id===u));n.debug(`Navigating to cell row ${d}, col ${t}`),e.current.scrollToIndexes({colIndex:t,rowIndex:d}),e.current.setCellFocus(u,c)}),[e,n,l]),c=g.useCallback(((t,r)=>{n.debug(`Navigating to header col ${t}`),e.current.scrollToIndexes({colIndex:t});const o=e.current.getVisibleColumns()[t].field;e.current.setColumnHeaderFocus(o,r)}),[e,n]),u=g.useCallback(((t,r)=>{n.debug(`Navigating to header filter col ${t}`),e.current.scrollToIndexes({colIndex:t});const o=e.current.getVisibleColumns()[t].field;e.current.setColumnHeaderFilterFocus(o,r)}),[e,n]),d=g.useCallback(((t,r,o)=>{n.debug(`Navigating to header col ${t}`),e.current.scrollToIndexes({colIndex:t});const{field:l}=e.current.getVisibleColumns()[t];e.current.setColumnGroupHeaderFocus(l,r,o)}),[e,n]),p=g.useCallback((e=>i()[e]?.id),[i]),f=g.useCallback(((t,r)=>{const n=r.currentTarget.querySelector(`.${ir.columnHeaderTitleContainerContent}`);if(n&&n.contains(r.target)&&t.field!==zl.field)return;const l=i(),f=e.current.getViewportPageSize(),g=t.field?e.current.getColumnIndex(t.field):0,m=l.length>0?0:null,h=l.length-1,b=En(e).length-1,w=wi(e);let C=!0;switch(r.key){case"ArrowDown":a?u(g,r):null!==m&&s(g,p(m));break;case"ArrowRight":{const e=Gp({currentColIndex:g,firstColIndex:0,lastColIndex:b,isRtl:o});null!==e&&c(e,r);break}case"ArrowLeft":{const e=Np({currentColIndex:g,firstColIndex:0,lastColIndex:b,isRtl:o});null!==e&&c(e,r);break}case"ArrowUp":w>0&&d(g,w-1,r);break;case"PageDown":null!==m&&null!==h&&s(g,p(Math.min(m+f,h)));break;case"Home":c(0,r);break;case"End":c(b,r);break;case"Enter":(r.ctrlKey||r.metaKey)&&e.current.toggleColumnMenu(t.field);break;case" ":break;default:C=!1}C&&r.preventDefault()}),[e,i,a,u,s,p,o,c,d]),m=g.useCallback(((t,r)=>{const n=pi(e)===t.field,l=fi(e)===t.field;if(n||l||!Qc(r.key))return;const a=i(),d=e.current.getViewportPageSize(),f=t.field?e.current.getColumnIndex(t.field):0,g=a.length-1,m=En(e).length-1;let h=!0;switch(r.key){case"ArrowDown":{const e=p(0);null!=e&&s(f,e);break}case"ArrowRight":{const e=Gp({currentColIndex:f,firstColIndex:0,lastColIndex:m,isRtl:o});null!==e&&u(e,r);break}case"ArrowLeft":{const n=Np({currentColIndex:f,firstColIndex:0,lastColIndex:m,isRtl:o});null!==n?u(n,r):e.current.setColumnHeaderFilterFocus(t.field,r);break}case"ArrowUp":c(f,r);break;case"PageDown":null!==g&&s(f,p(Math.min(0+d,g)));break;case"Home":u(0,r);break;case"End":u(m,r);break;case" ":break;default:h=!1}h&&r.preventDefault()}),[e,i,u,o,c,s,p]),h=g.useCallback(((t,r)=>{const n=gl(e);if(null===n)return;const{field:o,depth:l}=n,{fields:a,depth:u,maxDepth:f}=t,g=i(),m=e.current.getViewportPageSize(),h=e.current.getColumnIndex(o),b=o?e.current.getColumnIndex(o):0,w=g.length-1,C=En(e).length-1;let v=!0;switch(r.key){case"ArrowDown":u===f-1?c(h,r):d(h,l+1,r);break;case"ArrowUp":u>0&&d(h,l-1,r);break;case"ArrowRight":{const e=a.length-a.indexOf(o)-1;h+e+1<=C&&d(h+e+1,l,r);break}case"ArrowLeft":{const e=a.indexOf(o);h-e-1>=0&&d(h-e-1,l,r);break}case"PageDown":null!==w&&s(b,p(Math.min(0+m,w)));break;case"Home":d(0,l,r);break;case"End":d(C,l,r);break;case" ":break;default:v=!1}v&&r.preventDefault()}),[e,i,c,d,s,p]),b=g.useCallback(((t,r)=>{if(Xs(r))return;const n=e.current.getCellParams(t.id,t.field);if(n.cellMode===ln.Edit||!Qc(r.key))return;if(!e.current.unstable_applyPipeProcessors("canUpdateFocus",!0,{event:r,cell:n}))return;const d=i();if(0===d.length)return;const f=e.current.getViewportPageSize(),g=l?()=>0:e.current.getColumnIndex,m=t.field?g(t.field):0,h=d.findIndex((e=>e.id===t.id)),b=d.length-1,w=(l?[Yi(e)]:En(e)).length-1;let C=!0;switch(r.key){case"ArrowDown":h<b&&s(m,p(h+1),o?"right":"left","down");break;case"ArrowUp":h>0?s(m,p(h-1)):a?u(m,r):c(m,r);break;case"ArrowRight":{const e=Gp({currentColIndex:m,firstColIndex:0,lastColIndex:w,isRtl:o});null!==e&&s(e,p(h),o?"left":"right");break}case"ArrowLeft":{const e=Np({currentColIndex:m,firstColIndex:0,lastColIndex:w,isRtl:o});null!==e&&s(e,p(h),o?"right":"left");break}case"Tab":r.shiftKey&&m>0?s(m-1,p(h),"left"):!r.shiftKey&&m<w&&s(m+1,p(h),"right");break;case" ":{if(t.field===So)break;const e=t.colDef;if(e&&("__tree_data_group__"===e.field||(v=e.field)===xo||null!==(e=>{const t=e.match(/^__row_group_by_columns_group_(.*)__$/);return t?t[1]:null})(v)))break;!r.shiftKey&&h<b&&s(m,p(Math.min(h+f,b)));break}case"PageDown":h<b&&s(m,p(Math.min(h+f,b)));break;case"PageUp":{const e=Math.max(h-f,0);e!==h&&e>=0?s(m,p(e)):c(m,r);break}case"Home":r.ctrlKey||r.metaKey||r.shiftKey?s(0,p(0)):s(0,p(h));break;case"End":r.ctrlKey||r.metaKey||r.shiftKey?s(w,p(b)):s(w,p(h));break;default:C=!1}var v;C&&r.preventDefault()}),[e,i,o,s,p,a,u,c,l]),w=g.useCallback(((e,{event:t})=>" "!==t.key&&e),[]);wp(e,"canStartEditing",w),dr(e,"columnHeaderKeyDown",f),dr(e,"headerFilterKeyDown",m),dr(e,"columnGroupHeaderKeyDown",h),dr(e,"cellKeyDown",b)})(e,n),((e,t)=>{const r=Di(e,"useGridSelection"),n=g.useCallback((e=>(...r)=>{t.rowSelection&&e(...r)}),[t.rowSelection]),o=t.signature!==ar.DataGrid&&(t.rowSelectionPropagation?.parents||t.rowSelectionPropagation?.descendants),l=g.useMemo((()=>t.rowSelectionModel),[t.rowSelectionModel]),i=g.useRef(null);e.current.registerControlState({stateId:"rowSelection",propModel:l,propOnChange:t.onRowSelectionModelChange,stateSelector:el,changeEvent:"rowSelectionChange"});const{checkboxSelection:a,disableRowSelectionOnClick:s,isRowSelectable:c}=t,d=ll(t),p=zt(e,Hr),f=zt(e,jr)>1,m=g.useCallback((t=>{let r=t;const n=i.current??t,o=e.current.isRowSelected(t);if(o){const t=Uo(e),o=t.findIndex((e=>e===n)),l=t.findIndex((e=>e===r));if(o===l)return;r=o>l?t[l+1]:t[l-1]}i.current=t,e.current.selectRowRange({startId:n,endId:r},!o)}),[e]),h=y((()=>t.pagination&&t.checkboxSelectionVisibleOnly&&"client"===t.paginationMode?Tl(e):Uo(e))),b=g.useCallback((n=>{if(t.signature===ar.DataGrid&&!d&&("include"!==n.type||n.ids.size>1))throw new Error(["MUI X: `rowSelectionModel` can only contain 1 item in DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection."].join("\n"));el(e)!==n&&(r.debug("Setting selection model"),e.current.setState((e=>u({},e,{rowSelection:t.rowSelection?n:rf}))))}),[e,r,t.rowSelection,t.signature,d]),w=g.useCallback((t=>tl(e).has(t)),[e]),C=g.useCallback((r=>{if(!1===t.rowSelection)return!1;if(c&&!c(e.current.getRowParams(r)))return!1;const n=Tr(e,r);return"footer"!==n?.type&&"pinnedRow"!==n?.type}),[e,t.rowSelection,c]),v=g.useCallback((()=>nl(e)),[e]),x=g.useCallback(((n,l=!0,a=!1)=>{if(e.current.isRowSelectable(n))if(i.current=n,a){r.debug(`Setting selection for row ${n}`);const i={type:"include",ids:new Set},a=e=>{i.ids.add(e)};l&&(a(n),o&&il(e,p,n,t.rowSelectionPropagation?.descendants??!1,t.rowSelectionPropagation?.parents??!1,a)),e.current.setRowSelectionModel(i)}else{r.debug(`Toggling selection for row ${n}`);const i=el(e),a={type:i.type,ids:new Set(i.ids)},s=dn(a);s.unselect(n);const c=e=>{s.select(e)},u=e=>{s.unselect(e)};l?(c(n),o&&il(e,p,n,t.rowSelectionPropagation?.descendants??!1,t.rowSelectionPropagation?.parents??!1,c)):o&&al(e,p,n,t.rowSelectionPropagation?.descendants??!1,t.rowSelectionPropagation?.parents??!1,u),("include"===a.type&&a.ids.size<2||d)&&e.current.setRowSelectionModel(a)}}),[e,r,o,p,t.rowSelectionPropagation?.descendants,t.rowSelectionPropagation?.parents,d]),S=g.useCallback(((n,l=!0,i=!1)=>{if(r.debug("Setting selection for several rows"),!1===t.rowSelection)return;const a=new Set;for(let t=0;t<n.length;t+=1){const r=n[t];e.current.isRowSelectable(r)&&a.add(r)}const s=el(e);let c;if(i){if(c={type:"include",ids:a},l){const r=dn(c);if(o){const n=e=>{r.select(e)};for(const r of a)il(e,p,r,t.rowSelectionPropagation?.descendants??!1,t.rowSelectionPropagation?.parents??!1,n)}}else c.ids=new Set;if(s.type===c.type&&c.ids.size===s.ids.size&&Array.from(c.ids).every((e=>s.ids.has(e))))return}else{c={type:s.type,ids:new Set(s.ids)};const r=dn(c),n=e=>{r.select(e)},i=e=>{r.unselect(e)};for(const s of a)l?(r.select(s),o&&il(e,p,s,t.rowSelectionPropagation?.descendants??!1,t.rowSelectionPropagation?.parents??!1,n)):(i(s),o&&al(e,p,s,t.rowSelectionPropagation?.descendants??!1,t.rowSelectionPropagation?.parents??!1,i))}("include"===c.type&&c.ids.size<2||d)&&e.current.setRowSelectionModel(c)}),[r,o,d,e,p,t.rowSelectionPropagation?.descendants,t.rowSelectionPropagation?.parents,t.rowSelection]),R=g.useCallback((r=>{if(!f||!o||0===r.ids.size&&"include"===r.type)return r;const n={type:r.type,ids:new Set(r.ids)},l=dn(n),i=e=>{l.select(e)};for(const o of r.ids)il(e,p,o,t.rowSelectionPropagation?.descendants??!1,t.rowSelectionPropagation?.parents??!1,i,l);return n}),[e,p,t.rowSelectionPropagation?.descendants,t.rowSelectionPropagation?.parents,f,o]),I={selectRows:S,selectRowRange:g.useCallback((({startId:t,endId:n},o=!0,l=!1)=>{if(!e.current.getRow(t)||!e.current.getRow(n))return;r.debug(`Expanding selection from row ${t} to row ${n}`);const i=Uo(e),a=i.indexOf(t),s=i.indexOf(n),[c,u]=a>s?[s,a]:[a,s],d=i.slice(c,u+1);e.current.selectRows(d,o,l)}),[e,r]),getPropagatedRowSelectionModel:R};Ti(e,{selectRow:x,setRowSelectionModel:b,getSelectedRows:v,isRowSelected:w,isRowSelectable:C},"public"),Ti(e,I,t.signature===ar.DataGrid?"private":"public");const M=g.useRef(!0),k=g.useCallback(((r=!1)=>{if(M.current)return;const n=el(e),o=Fr(e),l=Go(e),i=e=>"server"===t.filterMode?!o[e]:!o[e]||!1===l[e],a={type:n.type,ids:new Set(n.ids)},s=dn(a);let c=!1;for(const e of n.ids){if(i(e)){if(t.keepNonExistentRowsSelected)continue;s.unselect(e),c=!0;continue}if(!t.rowSelectionPropagation?.parents)continue;const r=p[e];if("group"===r?.type){if(r.isAutoGenerated){s.unselect(e),c=!0;continue}r.children.every((e=>!1===l[e]))||(s.unselect(e),c=!0)}}const u=f&&t.rowSelectionPropagation?.parents&&(a.ids.size>0||"exclude"===a.type);if(c||u&&!r)if(u)if("exclude"===a.type){const r=h(),n=[];for(let e=0;e<r.length;e+=1){const o=r[e];!t.keepNonExistentRowsSelected&&i(o)||!s.has(o)||n.push(o)}e.current.selectRows(n,!0,!0)}else e.current.selectRows(Array.from(a.ids),!0,!0);else e.current.setRowSelectionModel(a)}),[e,f,t.rowSelectionPropagation?.parents,t.keepNonExistentRowsSelected,t.filterMode,p,h]),P=g.useCallback(((t,r)=>{const n=r.metaKey||r.ctrlKey,o=!a&&!n&&!(e=>!!e.key)(r),l=!d||o,i=e.current.isRowSelected(t),s=rl(e)>1&&l||!i;e.current.selectRow(t,s,l)}),[e,d,a]),E=g.useCallback(((t,r)=>{if(s)return;const n=r.target.closest(`.${ir.cell}`)?.getAttribute("data-field");if(n!==zl.field&&n!==So){if(n){const t=e.current.getColumn(n);if(t?.type===ao)return}"pinnedRow"!==Tr(e,t.id).type&&(r.shiftKey&&d?m(t.id):P(t.id,r))}}),[s,d,e,m,P]),F=g.useCallback(((e,t)=>{d&&t.shiftKey&&window.getSelection()?.removeAllRanges()}),[d]),H=g.useCallback(((t,r)=>{d&&r.nativeEvent.shiftKey?m(t.id):e.current.selectRow(t.id,t.value,!d)}),[e,m,d]),T=g.useCallback((r=>{const n=Bo(e),l=Vo(e),i=n.items.length>0||(l?.length||0)>0;t.isRowSelectable||t.checkboxSelectionVisibleOnly||!o||i?e.current.selectRows(h(),r):e.current.setRowSelectionModel({type:r?"exclude":"include",ids:new Set})}),[e,o,h,t.checkboxSelectionVisibleOnly,t.isRowSelectable]),D=g.useCallback((e=>{T(e.value)}),[T]),O=g.useCallback(((t,r)=>{if(e.current.getCellMode(t.id,t.field)!==ln.Edit&&!Xs(r)){if(Qc(r.key)&&r.shiftKey){const n=pl(e);if(n&&n.id!==t.id){r.preventDefault();const o=e.current.isRowSelected(n.id);if(!d)return void e.current.selectRow(n.id,!o,!0);const l=e.current.getRowIndexRelativeToVisibleRows(n.id),i=e.current.getRowIndexRelativeToVisibleRows(t.id);let a,s;l>i?o?(a=i,s=l-1):(a=i,s=l):o?(a=l+1,s=i):(a=l,s=i);const c=ji(e),u=[];for(let e=a;e<=s;e+=1)u.push(c.rows[e].id);return void e.current.selectRows(u,!o)}}if(" "===r.key&&r.shiftKey)return r.preventDefault(),void P(t.id,r);"A"===String.fromCharCode(r.keyCode)&&(r.ctrlKey||r.metaKey)&&(r.preventDefault(),T(!0))}}),[e,d,P,T]),$=y((()=>{if(!t.rowSelection)return void e.current.setRowSelectionModel(rf);if(void 0===l)return;if(!o||!f||"include"===l.type&&0===l.ids.size)return void e.current.setRowSelectionModel(l);const r=e.current.getPropagatedRowSelectionModel(l);r.type===l.type&&r.ids.size===l.ids.size&&Array.from(l.ids).every((e=>r.ids.has(e)))?e.current.setRowSelectionModel(l):e.current.setRowSelectionModel(r)}));dr(e,"sortedRowsSet",n((()=>k(!0)))),dr(e,"filteredRowsSet",n((()=>k()))),dr(e,"rowClick",n(E)),dr(e,"rowSelectionCheckboxChange",n(H)),dr(e,"headerSelectionCheckboxChange",D),dr(e,"cellMouseDown",n(F)),dr(e,"cellKeyDown",n(O)),g.useEffect((()=>{$()}),[e,l,t.rowSelection,$]);const j=null!=l;g.useEffect((()=>{if(j||!t.rowSelection||"function"!=typeof C)return;const r=el(e);if("include"!==r.type)return;const n=new Set;for(const e of r.ids)C(e)&&n.add(e);n.size<r.ids.size&&e.current.setRowSelectionModel({type:r.type,ids:n})}),[e,C,j,t.rowSelection]),g.useEffect((()=>{if(!t.rowSelection||j)return;const r=el(e);!d&&("include"===r.type&&r.ids.size>1||"exclude"===r.type)&&e.current.setRowSelectionModel(rf)}),[e,d,a,j,t.rowSelection]),g.useEffect((()=>{n(k)}),[k,n]),g.useEffect((()=>{M.current&&(M.current=!1)}),[])})(e,n),function(e,t){const n=Di(e,"useGridColumns"),o=g.useRef(t.columns);e.current.registerControlState({stateId:"visibleColumns",propModel:t.columnVisibilityModel,propOnChange:t.onColumnVisibilityModelChange,stateSelector:kn,changeEvent:"columnVisibilityModelChange"});const l=g.useCallback((t=>{n.debug("Updating columns state."),e.current.setState(Tp(t)),e.current.publishEvent("columnsChange",t.orderedFields)}),[n,e]),i=g.useCallback((t=>In(e)[t]),[e]),a=g.useCallback((()=>Mn(e)),[e]),s=g.useCallback((()=>En(e)),[e]),c=g.useCallback(((t,r=!0)=>(r?En(e):Mn(e)).findIndex((e=>e.field===t))),[e]),d=g.useCallback((t=>{const r=c(t);return Dn(e)[r]}),[e,c]),p=g.useCallback((t=>{kn(e)!==t&&(e.current.setState((r=>u({},r,{columns:Si({apiRef:e,columnsToUpsert:[],initialState:void 0,columnVisibilityModel:t,keepOnlyColumnsToUpsert:!1})}))),e.current.updateRenderContext?.())}),[e]),f=g.useCallback((t=>{if(ha(e))return void e.current.updateNonPivotColumns(t);const r=Si({apiRef:e,columnsToUpsert:t,initialState:void 0,keepOnlyColumnsToUpsert:!1,updateInitialVisibilityModel:!0});l(r)}),[e,l]),m=g.useCallback(((t,r)=>{const n=kn(e);if(r!==(n[t]??!0)){const o=u({},n,{[t]:r});e.current.setColumnVisibilityModel(o)}}),[e]),h=g.useCallback((t=>Rn(e).findIndex((e=>e===t))),[e]),b=g.useCallback(((t,r)=>{const o=Rn(e),i=h(t);if(i===r)return;n.debug(`Moving column ${t} to index ${r}`);const a=[...o],s=a.splice(i,1)[0];a.splice(r,0,s),l(u({},Sn(e),{orderedFields:a}));const c={column:e.current.getColumn(t),targetIndex:e.current.getColumnIndexRelativeToVisibleColumns(t),oldIndex:i};e.current.publishEvent("columnIndexChange",c)}),[e,n,l,h]),w=g.useCallback(((t,r)=>{n.debug(`Updating column ${t} width to ${r}`);const o=Sn(e),i=o.lookup[t],a=u({},i,{width:r,hasBeenResized:!0});l(yi(u({},o,{lookup:u({},o.lookup,{[t]:a})}),e.current.getRootDimensions())),e.current.publishEvent("columnWidthChange",{element:e.current.getColumnHeaderElement(t),colDef:a,width:r})}),[e,n,l]),C={setColumnIndex:b};Ti(e,{getColumn:i,getAllColumns:a,getColumnIndex:c,getColumnPosition:d,getVisibleColumns:s,getColumnIndexRelativeToVisibleColumns:h,updateColumns:f,setColumnVisibilityModel:p,setColumnVisibility:m,setColumnWidth:w},"public"),Ti(e,C,t.signature===ar.DataGrid?"private":"public");const v=g.useCallback(((r,n)=>{const o={},l=kn(e);(!n.exportOnlyDirtyModels||null!=t.columnVisibilityModel||Object.keys(t.initialState?.columns?.columnVisibilityModel??{}).length>0||Object.keys(l).length>0)&&(o.columnVisibilityModel=l),o.orderedFields=Rn(e);const i=Mn(e),a={};return i.forEach((e=>{if(e.hasBeenResized){const t={};Ci.forEach((r=>{let n=e[r];n===1/0&&(n=-1),t[r]=n})),a[e.field]=t}})),Object.keys(a).length>0&&(o.dimensions=a),u({},r,{columns:o})}),[e,t.columnVisibilityModel,t.initialState?.columns]),y=g.useCallback(((t,r)=>{const n=r.stateToRestore.columns?.columnVisibilityModel,o=r.stateToRestore.columns;if(null==n&&null==o)return t;const l=Si({apiRef:e,columnsToUpsert:[],initialState:o,columnVisibilityModel:n,keepOnlyColumnsToUpsert:!1});return e.current.setState(Tp(l)),null!=o&&e.current.publishEvent("columnsChange",l.orderedFields),t}),[e]),x=g.useCallback(((e,n)=>{if(n===Ga.columns){const e=t.slots.columnsPanel;return r.jsx(e,u({},t.slotProps?.columnsPanel))}return e}),[t.slots.columnsPanel,t.slotProps?.columnsPanel]),S=g.useCallback((r=>{const n=ha(e);return t.disableColumnSelector||n?r:[...r,"columnMenuColumnsItem"]}),[t.disableColumnSelector,e]);wp(e,"columnMenu",S),wp(e,"exportState",v),wp(e,"restoreState",y),wp(e,"preferencePanel",x);const R=g.useRef(null);dr(e,"viewportInnerSizeChange",(t=>{if(R.current!==t.width){if(R.current=t.width,!En(e).some((e=>e.flex&&e.flex>0)))return;l(yi(Sn(e),e.current.getRootDimensions()))}}));const I=g.useCallback((()=>{n.info("Columns pipe processing have changed, regenerating the columns");const t=Si({apiRef:e,columnsToUpsert:[],initialState:void 0,keepOnlyColumnsToUpsert:!1});l(t)}),[e,n,l]);Cp(e,"hydrateColumns",I);const M=g.useRef(!0);g.useEffect((()=>{if(M.current)return void(M.current=!1);if(n.info(`GridColumns have changed, new length ${t.columns.length}`),o.current===t.columns)return;const r=Si({apiRef:e,initialState:void 0,columnsToUpsert:t.columns,keepOnlyColumnsToUpsert:!0,updateInitialVisibilityModel:!0});o.current=t.columns,l(r)}),[n,e,l,t.columns]),g.useEffect((()=>{void 0!==t.columnVisibilityModel&&e.current.setColumnVisibilityModel(t.columnVisibilityModel)}),[e,n,t.columnVisibilityModel])}(e,n),((e,t)=>{const r=Di(e,"useGridRows"),n=g.useRef(Date.now()),o=g.useRef(t.rowCount),l=H(),i=g.useCallback((t=>{const r=Fr(e)[t];if(r)return r;const n=Tr(e,t);return n&&ho(n)?{[uo]:t}:null}),[e]),a=g.useCallback((t=>jl(e,t)),[e]),s=g.useCallback((({cache:r,throttle:o})=>{const i=()=>{n.current=Date.now(),e.current.setState((r=>u({},r,{rows:mo({apiRef:e,rowCountProp:t.rowCount,loadingProp:t.loading,previousTree:Hr(e),previousTreeDepths:$r(e),previousGroupsToFetch:Dr(e)})}))),e.current.publishEvent("rowsSet")};if(l.clear(),e.current.caches.rows=r,!o)return void i();const a=t.throttleRowsMs-(Date.now()-n.current);a>0?l.start(a,i):i()}),[t.throttleRowsMs,t.rowCount,t.loading,e,l]),c=g.useCallback((n=>{if(r.debug(`Updating all rows, new length ${n.length}`),ha(e))return void e.current.updateNonPivotRows(n,!1);const o=fo({rows:n,getRowId:t.getRowId,loading:t.loading,rowCount:t.rowCount}),l=e.current.caches.rows;o.rowsBeforePartialUpdates=l.rowsBeforePartialUpdates,s({cache:o,throttle:!0})}),[r,t.getRowId,t.loading,t.rowCount,s,e]),d=g.useCallback((r=>{if(t.signature===ar.DataGrid&&r.length>1)throw new Error(["MUI X: You cannot update several rows at once in `apiRef.current.updateRows` on the DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature."].join("\n"));if(ha(e))return void e.current.updateNonPivotRows(r);const n=vo(e,r,t.getRowId),o=wo({updates:n,getRowId:t.getRowId,previousCache:e.current.caches.rows});s({cache:o,throttle:!0})}),[t.signature,t.getRowId,s,e]),p=g.useCallback(((r,n)=>{const o=vo(e,r,t.getRowId),l=wo({updates:o,getRowId:t.getRowId,previousCache:e.current.caches.rows,groupKeys:n??[]});s({cache:l,throttle:!1})}),[t.getRowId,s,e]),f=g.useCallback((t=>{r.debug(`Setting loading to ${t}`),e.current.setState((e=>u({},e,{rows:u({},e.rows,{loading:t})}))),e.current.caches.rows.loadingPropBeforePartialUpdates=t}),[e,r]),m=g.useCallback((()=>{const t=Lr(e),r=Fr(e);return new Map(t.map((e=>[e,r[e]??{}])))}),[e]),h=g.useCallback((()=>kr(e)),[e]),b=g.useCallback((()=>Lr(e)),[e]),w=g.useCallback((t=>{const{rowIdToIndexMap:r}=ji(e);return r.get(t)}),[e]),C=g.useCallback(((t,r)=>{const n=Tr(e,t);if(!n)throw new Error(`MUI X: No row with id #${t} found.`);if("group"!==n.type)throw new Error("MUI X: Only group nodes can be expanded or collapsed.");const o=u({},n,{childrenExpanded:r});e.current.setState((e=>u({},e,{rows:u({},e.rows,{tree:u({},e.rows.tree,{[t]:o})})}))),e.current.publishEvent("rowExpansionChange",o)}),[e]),v=g.useCallback((t=>Tr(e,t)??null),[e]),y=g.useCallback((({skipAutoGeneratedRows:t=!0,groupId:r,applySorting:n,applyFiltering:o})=>{const l=Hr(e);let i;if(n){const n=l[r];if(!n)return[];const o=$o(e);i=[];for(let e=o.findIndex((e=>e===r))+1;e<o.length&&l[o[e]].depth>n.depth;e+=1){const r=o[e];t&&ho(l[r])||i.push(r)}}else i=bo(l,r,t);if(o){const t=Go(e);i=Do(t)?i:i.filter((e=>!1!==t[e]))}return i}),[e]),x=g.useCallback(((t,n)=>{const o=Tr(e,t);if(!o)throw new Error(`MUI X: No row with id #${t} found.`);if(o.parent!==co)throw new Error("MUI X: The row reordering do not support reordering of grouped rows yet.");if("leaf"!==o.type)throw new Error("MUI X: The row reordering do not support reordering of footer or grouping rows.");e.current.setState((o=>{const l=Hr(e)[co],i=l.children,a=i.findIndex((e=>e===t));if(-1===a||a===n)return o;r.debug(`Moving row ${t} to index ${n}`);const s=[...i];return s.splice(n,0,s.splice(a,1)[0]),u({},o,{rows:u({},o.rows,{tree:u({},o.rows.tree,{[co]:u({},l,{children:s})})})})})),e.current.publishEvent("rowsSet")}),[e,r]),S={getRow:i,setLoading:f,getRowId:a,getRowModels:m,getRowsCount:h,getAllRowIds:b,setRows:c,updateRows:d,getRowNode:v,getRowIndexRelativeToVisibleRows:w,unstable_replaceRows:g.useCallback(((r,n)=>{if(t.signature===ar.DataGrid&&n.length>1)throw new Error(["MUI X: You cannot replace rows using `apiRef.current.unstable_replaceRows` on the DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature."].join("\n"));if(0===n.length)return;if(jr(e)>1)throw new Error("`apiRef.current.unstable_replaceRows` is not compatible with tree data and row grouping");const o=u({},Hr(e)),l=u({},Fr(e)),i=o[co],a=[...i.children],s=new Set;for(let e=0;e<n.length;e+=1){const i=n[e],c=po(i,t.getRowId,"A row was provided without id when calling replaceRows()."),[u]=a.splice(r+e,1,c);s.has(u)||(delete l[u],delete o[u]);const d={id:c,depth:0,parent:co,type:"leaf",groupingKey:null};l[c]=i,o[c]=d,s.add(c)}o[co]=u({},i,{children:a});const c=a.filter((e=>"leaf"===o[e]?.type));e.current.caches.rows.dataRowIdToModelLookup=l,e.current.setState((e=>u({},e,{rows:u({},e.rows,{loading:t.loading,totalRowCount:Math.max(t.rowCount||0,a.length),dataRowIdToModelLookup:l,dataRowIds:c,tree:o})}))),e.current.publishEvent("rowsSet")}),[e,t.signature,t.getRowId,t.loading,t.rowCount])},R={setRowIndex:x,setRowChildrenExpansion:C,getRowGroupChildren:y},I={updateNestedRows:p},M=g.useCallback((()=>{let n;r.info("Row grouping pre-processing have changed, regenerating the row tree"),n=e.current.caches.rows.rowsBeforePartialUpdates===t.rows?u({},e.current.caches.rows,{updates:{type:"full",rows:Lr(e)}}):fo({rows:t.rows,getRowId:t.getRowId,loading:t.loading,rowCount:t.rowCount}),s({cache:n,throttle:!1})}),[r,e,t.rows,t.getRowId,t.loading,t.rowCount,s]),k=E((()=>t.dataSource)),P=g.useCallback((e=>{t.dataSource&&t.dataSource!==k.current?k.current=t.dataSource:"rowTreeCreation"===e&&M()}),[M,k,t.dataSource]),F=g.useCallback((()=>{e.current.getActiveStrategy(vp.RowTree)!==Or(e)&&M()}),[e,M]);dr(e,"activeStrategyProcessorChange",P),dr(e,"strategyAvailabilityChange",F);const T=g.useCallback((()=>{e.current.setState((r=>{const n=e.current.unstable_applyPipeProcessors("hydrateRows",{tree:Hr(e),treeDepths:$r(e),dataRowIds:Lr(e),dataRowIdToModelLookup:Fr(e)});return u({},r,{rows:u({},r.rows,n,{totalTopLevelRowCount:go({tree:n.tree,rowCountProp:t.rowCount})})})})),e.current.publishEvent("rowsSet")}),[e,t.rowCount]);Cp(e,"hydrateRows",T),Ti(e,S,"public"),Ti(e,R,t.signature===ar.DataGrid?"private":"public"),Ti(e,I,"private");const D=g.useRef(!0);g.useEffect((()=>{if(D.current)return void(D.current=!1);let n=!1;t.rowCount!==o.current&&(n=!0,o.current=t.rowCount);const l=t.dataSource?zr(e):t.rows,i=e.current.caches.rows.rowsBeforePartialUpdates===l,a=e.current.caches.rows.loadingPropBeforePartialUpdates===t.loading,c=e.current.caches.rows.rowCountPropBeforePartialUpdates===t.rowCount;i&&(a||(e.current.setState((e=>u({},e,{rows:u({},e.rows,{loading:t.loading})}))),e.current.caches.rows.loadingPropBeforePartialUpdates=t.loading),c||(e.current.setState((e=>u({},e,{rows:u({},e.rows,{totalRowCount:Math.max(t.rowCount||0,e.rows.totalRowCount),totalTopLevelRowCount:Math.max(t.rowCount||0,e.rows.totalTopLevelRowCount)})}))),e.current.caches.rows.rowCountPropBeforePartialUpdates=t.rowCount),!n)||(r.debug(`Updating all rows, new length ${l?.length}`),s({cache:fo({rows:l,getRowId:t.getRowId,loading:t.loading,rowCount:t.rowCount}),throttle:!1}))}),[t.rows,t.rowCount,t.getRowId,t.loading,t.dataSource,r,s,e])})(e,n),((e,t)=>{const r=E((()=>e.current.state.rowSpanning!==Df?Lf(t,e):Of)),n=g.useCallback(((n,o=!1)=>{const{range:l,rows:i}=ji(e,(t.pagination,t.paginationMode));if(null===l||!Hf(n))return;o&&(r.current=Of);const a=function(e,t){return e.firstRowIndex>=t.firstRowIndex&&e.lastRowIndex<=t.lastRowIndex?null:e.firstRowIndex>=t.firstRowIndex&&e.lastRowIndex>t.lastRowIndex?{firstRowIndex:t.lastRowIndex,lastRowIndex:e.lastRowIndex}:e.firstRowIndex<t.firstRowIndex&&e.lastRowIndex<=t.lastRowIndex?{firstRowIndex:e.firstRowIndex,lastRowIndex:t.firstRowIndex-1}:e}({firstRowIndex:n.firstRowIndex,lastRowIndex:Math.min(n.lastRowIndex,l.lastRowIndex+1)},r.current);if(null===a)return;const s=En(e),{spannedCells:c,hiddenCells:d,hiddenCellOriginMap:p,processedRange:f}=jf(e,s,i,l,a,o,r.current);r.current=f;const g=Object.keys(c).length,m=Object.keys(d).length,h=Object.keys(e.current.state.rowSpanning.spannedCells).length,b=Object.keys(e.current.state.rowSpanning.hiddenCells).length;(o||g!==h||m!==b)&&(0!==g||0!==h)&&e.current.setState((e=>u({},e,{rowSpanning:{spannedCells:c,hiddenCells:d,hiddenCellOriginMap:p}})))}),[e,r,t.pagination,t.paginationMode]),o=g.useCallback((()=>{const t=Gi(e);Hf(t)&&n(t,!0)}),[e,n]);dr(e,"renderedRowsIntervalChange",rn(t.rowSpanning,n)),dr(e,"sortedRowsSet",rn(t.rowSpanning,o)),dr(e,"paginationModelChange",rn(t.rowSpanning,o)),dr(e,"filteredRowsSet",rn(t.rowSpanning,o)),dr(e,"columnsChange",rn(t.rowSpanning,o)),g.useEffect((()=>{t.rowSpanning?e.current.state.rowSpanning===Df&&o():e.current.state.rowSpanning!==Df&&e.current.setState((e=>u({},e,{rowSpanning:Df})))}),[e,o,t.rowSpanning])})(e,n),function(e,t){const r=g.useCallback((t=>({field:t,colDef:e.current.getColumn(t)})),[e]),n=g.useCallback((t=>{const r=e.current.getRow(t);if(!r)throw new tf(`No row with id #${t} found`);return{id:t,columns:e.current.getAllColumns(),row:r}}),[e]),o=g.useCallback(((t,r,n,{cellMode:o,colDef:l,hasFocus:i,rowNode:a,tabIndex:s})=>{const c=n[r],u=l?.valueGetter?l.valueGetter(c,n,l,e):c,d={id:t,field:r,row:n,rowNode:a,colDef:l,cellMode:o,hasFocus:i,tabIndex:s,value:u,formattedValue:u,isEditable:!1,api:e.current};return l&&l.valueFormatter&&(d.formattedValue=l.valueFormatter(u,n,l,e)),d.isEditable=l&&e.current.isCellEditable(d),d}),[e]),l=g.useCallback(((r,n)=>{const o=e.current.getRow(r),l=Tr(e,r);if(!o||!l)throw new tf(`No row with id #${r} found`);const i=pl(e),a=hl(e),s=e.current.getCellMode(r,n);return e.current.getCellParamsForRow(r,n,o,{colDef:t.listView&&t.listViewColumn?.field===n?Yi(e):e.current.getColumn(n),rowNode:l,hasFocus:null!==i&&i.field===n&&i.id===r,tabIndex:a&&a.field===n&&a.id===r?0:-1,cellMode:s})}),[e,t.listView,t.listViewColumn?.field]),i=g.useCallback(((t,r)=>{const n=e.current.getColumn(r),o=e.current.getRow(t);if(!o)throw new tf(`No row with id #${t} found`);return n&&n.valueGetter?n.valueGetter(o[n.field],o,n,e):o[r]}),[e]),a=g.useCallback(((t,r)=>((e,t,r)=>{const n=t.field;if(!t||!t.valueGetter)return e[n];const o=e[t.field];return t.valueGetter(o,e,t,r)})(t,r,e)),[e]),s=g.useCallback(((t,r)=>{const n=a(t,r);return r&&r.valueFormatter?r.valueFormatter(n,t,r,e):n}),[e,a]),c=g.useCallback((t=>e.current.rootElementRef.current?function(e,t){return e.querySelector(`[role="columnheader"][data-field="${_s(t)}"]`)}(e.current.rootElementRef.current,t):null),[e]),u=g.useCallback((t=>e.current.rootElementRef.current?function(e,t){return e.querySelector(qs(t))}(e.current.rootElementRef.current,t):null),[e]),d=g.useCallback(((t,r)=>e.current.rootElementRef.current?function(e,{id:t,field:r}){const n=`${qs(t)} .${ir.cell}[data-field="${_s(r)}"]`;return e.querySelector(n)}(e.current.rootElementRef.current,{id:t,field:r}):null),[e]),p={getCellParamsForRow:o};Ti(e,{getCellValue:i,getCellParams:l,getCellElement:d,getRowValue:a,getRowFormattedValue:s,getRowParams:n,getRowElement:u,getColumnHeaderParams:r,getColumnHeaderElement:c},"public"),Ti(e,p,"private")}(e,n),(e=>{const t=g.useRef({}),r=()=>{t.current={}},n={resetColSpan:r,calculateColSpan:g.useCallback((({rowId:r,minFirstColumn:n,maxLastColumn:o,columns:l})=>{for(let i=n;i<o;i+=1){const a=bf({apiRef:e,lookup:t.current,columnIndex:i,rowId:r,minFirstColumnIndex:n,maxLastColumnIndex:o,columns:l});a.colSpan>1&&(i+=a.colSpan-1)}}),[e])};Ti(e,{unstable_getCellColSpanInfo:(e,r)=>t.current[e]?.[r]},"public"),Ti(e,n,"private"),dr(e,"columnOrderChange",r)})(e),((e,t)=>{const r=g.useCallback((t=>mi(e)[t]??[]),[e]),n=g.useCallback((()=>hi(e)),[e]);Ti(e,{getColumnGroupPath:r,getAllGroupDetails:n},"public");const o=g.useCallback((()=>{const r=vf(t.columnGroupingModel??[]);e.current.setState((e=>{const t=e.columns?.orderedFields??[],n=e.pinnedColumns??{},o=yf(t,r,n);return u({},e,{columnGrouping:u({},e.columnGrouping,{headerStructure:o})})}))}),[e,t.columnGroupingModel]),l=g.useCallback((t=>{const r=e.current.getPinnedColumns?.()??{},n=Rn(e),o=Fn(e),l=Sf(t??[]),i=vf(t??[]),a=yf(n,i,r),s=0===o.length?0:Math.max(...o.map((e=>i[e]?.length??0)));e.current.setState((e=>u({},e,{columnGrouping:{lookup:l,unwrappedGroupingModel:i,headerStructure:a,maxDepth:s}})))}),[e]);dr(e,"columnIndexChange",o),dr(e,"columnsChange",(()=>{l(t.columnGroupingModel)})),dr(e,"columnVisibilityModelChange",(()=>{l(t.columnGroupingModel)})),g.useEffect((()=>{l(t.columnGroupingModel)}),[l,t.columnGroupingModel])})(e,n),((e,t)=>{((e,t)=>{const[r,n]=g.useState({}),o=g.useRef(r),l=g.useRef({}),{processRowUpdate:i,onProcessRowUpdateError:a,cellModesModel:s,onCellModesModelChange:c}=t,p=e=>(...r)=>{t.editMode===on.Cell&&e(...r)},f=g.useCallback(((t,r)=>{const n=e.current.getCellParams(t,r);if(!e.current.isCellEditable(n))throw new Error(`MUI X: The cell with id=${t} and field=${r} is not editable.`)}),[e]),m=g.useCallback(((t,r,n)=>{if(e.current.getCellMode(t,r)!==n)throw new Error(`MUI X: The cell with id=${t} and field=${r} is not in ${n} mode.`)}),[e]),h=g.useCallback(((t,r)=>{if(!t.isEditable)return;if(t.cellMode===ln.Edit)return;const n=u({},t,{reason:pn.cellDoubleClick});e.current.publishEvent("cellEditStart",n,r)}),[e]),b=g.useCallback(((t,r)=>{if(t.cellMode===ln.View)return;if(e.current.getCellMode(t.id,t.field)===ln.View)return;const n=u({},t,{reason:fn.cellFocusOut});e.current.publishEvent("cellEditStop",n,r)}),[e]),w=g.useCallback(((t,r)=>{if(t.cellMode===ln.Edit){if(229===r.which)return;let n;if("Escape"===r.key?n=fn.escapeKeyDown:"Enter"===r.key?n=fn.enterKeyDown:"Tab"===r.key&&(n=r.shiftKey?fn.shiftTabKeyDown:fn.tabKeyDown,r.preventDefault()),n){const o=u({},t,{reason:n});e.current.publishEvent("cellEditStop",o,r)}}else if(t.isEditable){let n;if(!e.current.unstable_applyPipeProcessors("canStartEditing",!0,{event:r,cellParams:t,editMode:"cell"}))return;if(Xc(r)?n=pn.printableKeyDown:Jc(r)?n=pn.pasteKeyDown:"Enter"===r.key?n=pn.enterKeyDown:"Backspace"!==r.key&&"Delete"!==r.key||(n=pn.deleteKeyDown),n){const o=u({},t,{reason:n,key:r.key});e.current.publishEvent("cellEditStart",o,r)}}}),[e]),C=g.useCallback((t=>{const{id:r,field:n,reason:o}=t,l={id:r,field:n};o!==pn.printableKeyDown&&o!==pn.deleteKeyDown&&o!==pn.pasteKeyDown||(l.deleteValue=!0),e.current.startCellEditMode(l)}),[e]),v=g.useCallback((t=>{const{id:r,field:n,reason:o}=t;let l;e.current.runPendingEditCellValueMutation(r,n),o===fn.enterKeyDown?l="below":o===fn.tabKeyDown?l="right":o===fn.shiftTabKeyDown&&(l="left");const i="escapeKeyDown"===o;e.current.stopCellEditMode({id:r,field:n,ignoreModifications:i,cellToFocusAfter:l})}),[e]);var x;dr(e,"cellDoubleClick",p(h)),dr(e,"cellFocusOut",p(b)),dr(e,"cellKeyDown",p(w)),dr(e,"cellEditStart",p(C)),dr(e,"cellEditStop",p(v)),fr(e,"cellEditStart",t.onCellEditStart),fr(e,"cellEditStop",(x=t.onCellEditStop,async(...t)=>{if(x){const{id:r,field:n}=t[0],o=e.current.state.editRows,l=o[r][n]?.error;l||x(...t)}}));const S=g.useCallback(((t,r)=>{const n=za(e);return n[t]&&n[t][r]?ln.Edit:ln.View}),[e]),R=y((r=>{const l=r!==t.cellModesModel;c&&l&&c(r,{api:e.current}),t.cellModesModel&&l||(n(r),o.current=r,e.current.publishEvent("cellModesModelChange",r))})),I=g.useCallback(((e,t,r)=>{const n=u({},o.current);if(null!==r)n[e]=u({},n[e],{[t]:u({},r)});else{const r=n[e],o=F(r,[t].map(bp));n[e]=o,0===Object.keys(n[e]).length&&delete n[e]}R(n)}),[R]),M=g.useCallback(((t,r,n)=>{e.current.setState((e=>{const o=u({},e.editRows);return null!==n?o[t]=u({},o[t],{[r]:u({},n)}):(delete o[t][r],0===Object.keys(o[t]).length&&delete o[t]),u({},e,{editRows:o})}))}),[e]),k=g.useCallback((e=>{const{id:t,field:r}=e,n=F(e,qp);f(t,r),m(t,r,ln.View),I(t,r,u({mode:ln.Edit},n))}),[f,m,I]),P=y((async t=>{const{id:r,field:n,deleteValue:o,initialValue:l}=t,i=e.current.getCellValue(r,n);let a=i;o?a=_p(e.current.getColumn(n)):l&&(a=l);const s=e.current.getColumn(n),c=!!s.preProcessEditCellProps&&o;let d={value:a,error:!1,isProcessingProps:c};if(M(r,n,d),e.current.setCellFocus(r,n),c&&(d=await Promise.resolve(s.preProcessEditCellProps({id:r,row:e.current.getRow(r),props:d,hasChanged:a!==i})),e.current.getCellMode(r,n)===ln.Edit)){const t=za(e);M(r,n,u({},d,{value:t[r][n].value,isProcessingProps:!1}))}})),E=g.useCallback((e=>{const{id:t,field:r}=e,n=F(e,Xp);m(t,r,ln.Edit),I(t,r,u({mode:ln.View},n))}),[m,I]),H=y((async r=>{const{id:n,field:o,ignoreModifications:s,cellToFocusAfter:c="none"}=r;m(n,o,ln.Edit),e.current.runPendingEditCellValueMutation(n,o);const u=()=>{M(n,o,null),I(n,o,null),"none"!==c&&e.current.moveFocusToRelativeCell(n,o,c)};if(s)return void u();const d=za(e),{error:p,isProcessingProps:f}=d[n][o],g=e.current.getRow(n);if(p||f)return l.current[n][o].mode=ln.Edit,void I(n,o,{mode:ln.Edit});const h=e.current.getRowWithUpdatedValuesFromCellEditing(n,o);if(t.dataSource?.updateRow){if(nd(g,h))return void u();const t=()=>{l.current[n][o].mode=ln.Edit,I(n,o,{mode:ln.Edit})},r={rowId:n,updatedRow:h,previousRow:g};try{await e.current.dataSource.editRow(r),u()}catch{t()}}else if(i){const t=e=>{l.current[n][o].mode=ln.Edit,I(n,o,{mode:ln.Edit}),a&&a(e)};try{Promise.resolve(i(h,g,{rowId:n})).then((t=>{e.current.updateRows([t]),u()})).catch(t)}catch(b){t(b)}}else e.current.updateRows([h]),u()})),T={setCellEditingEditCellValue:g.useCallback((async t=>{const{id:r,field:n,value:o,debounceMs:l,unstable_skipValueParser:i}=t;f(r,n),m(r,n,ln.Edit);const a=e.current.getColumn(n),s=e.current.getRow(r);let c=o;a.valueParser&&!i&&(c=a.valueParser(o,s,a,e));let d=za(e),p=u({},d[r][n],{value:c,changeReason:l?"debouncedSetEditCellValue":"setEditCellValue"});if(a.preProcessEditCellProps){const e=o!==d[r][n].value;p=u({},p,{isProcessingProps:!0}),M(r,n,p),p=await Promise.resolve(a.preProcessEditCellProps({id:r,row:s,props:p,hasChanged:e}))}return e.current.getCellMode(r,n)!==ln.View&&(d=za(e),p=u({},p,{isProcessingProps:!1}),p.value=a.preProcessEditCellProps?d[r][n].value:c,M(r,n,p),d=za(e),!d[r]?.[n]?.error)}),[e,f,m,M]),getRowWithUpdatedValuesFromCellEditing:g.useCallback(((t,r)=>{const n=e.current.getColumn(r),o=za(e),l=e.current.getRow(t);if(!o[t]||!o[t][r])return e.current.getRow(t);const{value:i}=o[t][r];return n.valueSetter?n.valueSetter(i,l,n,e):u({},l,{[r]:i})}),[e])};Ti(e,{getCellMode:S,startCellEditMode:k,stopCellEditMode:E},"public"),Ti(e,T,"private"),g.useEffect((()=>{s&&R(s)}),[s,R]),d((()=>{const t=Fr(e),n=l.current;l.current=tn(r),Object.entries(r).forEach((([r,o])=>{Object.entries(o).forEach((([o,l])=>{const i=n[r]?.[o]?.mode||ln.View,a=t[r]?e.current.getRowId(t[r]):r;l.mode===ln.Edit&&i===ln.View?P(u({id:a,field:o},l)):l.mode===ln.View&&i===ln.Edit&&H(u({id:a,field:o},l))}))}))}),[e,r,P,H])})(e,t),((e,t)=>{const[r,n]=g.useState({}),o=g.useRef(r),l=g.useRef({}),i=g.useRef({}),a=g.useRef(void 0),s=g.useRef(null),{processRowUpdate:c,onProcessRowUpdateError:p,rowModesModel:f,onRowModesModelChange:m}=t,h=e=>(...r)=>{t.editMode===on.Row&&e(...r)},b=g.useCallback(((t,r)=>{const n=e.current.getCellParams(t,r);if(!e.current.isCellEditable(n))throw new Error(`MUI X: The cell with id=${t} and field=${r} is not editable.`)}),[e]),w=g.useCallback(((t,r)=>{if(e.current.getRowMode(t)!==r)throw new Error(`MUI X: The row with id=${t} is not in ${r} mode.`)}),[e]),C=g.useCallback((t=>{const r=za(e);return Object.values(r[t]).some((e=>e.error))}),[e]),v=g.useCallback(((t,r)=>{if(!t.isEditable)return;if(e.current.getRowMode(t.id)===an.Edit)return;const n=e.current.getRowParams(t.id),o=u({},n,{field:t.field,reason:gn.cellDoubleClick});e.current.publishEvent("rowEditStart",o,r)}),[e]),x=g.useCallback((e=>{s.current=e}),[]),S=g.useCallback(((t,r)=>{t.isEditable&&e.current.getRowMode(t.id)!==an.View&&(s.current=null,a.current=setTimeout((()=>{if(s.current?.id!==t.id){if(!e.current.getRow(t.id))return;if(e.current.getRowMode(t.id)===an.View)return;if(C(t.id))return;const n=e.current.getRowParams(t.id),o=u({},n,{field:t.field,reason:mn.rowFocusOut});e.current.publishEvent("rowEditStop",o,r)}})))}),[e,C]);g.useEffect((()=>()=>{clearTimeout(a.current)}),[]);const R=g.useCallback(((t,r)=>{if(t.cellMode===an.Edit){if(229===r.which)return;let n;if("Escape"===r.key)n=mn.escapeKeyDown;else if("Enter"===r.key)n=mn.enterKeyDown;else if("Tab"===r.key){const o=Fn(e).filter((r=>e.current.getColumn(r).type===ao||e.current.isCellEditable(e.current.getCellParams(t.id,r))));if(r.shiftKey?t.field===o[0]&&(n=mn.shiftTabKeyDown):t.field===o[o.length-1]&&(n=mn.tabKeyDown),r.preventDefault(),!n){const n=o.findIndex((e=>e===t.field)),l=o[r.shiftKey?n-1:n+1];e.current.setCellFocus(t.id,l)}}if(n){if(n!==mn.escapeKeyDown&&C(t.id))return;const o=u({},e.current.getRowParams(t.id),{reason:n,field:t.field});e.current.publishEvent("rowEditStop",o,r)}}else if(t.isEditable){let n;if(!e.current.unstable_applyPipeProcessors("canStartEditing",!0,{event:r,cellParams:t,editMode:"row"}))return;if(Xc(r)||Jc(r)?n=gn.printableKeyDown:"Enter"===r.key?n=gn.enterKeyDown:"Backspace"!==r.key&&"Delete"!==r.key||(n=gn.deleteKeyDown),n){const o=e.current.getRowParams(t.id),l=u({},o,{field:t.field,reason:n});e.current.publishEvent("rowEditStart",l,r)}}}),[e,C]),I=g.useCallback((t=>{const{id:r,field:n,reason:o}=t,l={id:r,fieldToFocus:n};o!==gn.printableKeyDown&&o!==gn.deleteKeyDown||(l.deleteValue=!!n),e.current.startRowEditMode(l)}),[e]),M=g.useCallback((t=>{const{id:r,reason:n,field:o}=t;let l;e.current.runPendingEditCellValueMutation(r),n===mn.enterKeyDown?l="below":n===mn.tabKeyDown?l="right":n===mn.shiftTabKeyDown&&(l="left");const i="escapeKeyDown"===n;e.current.stopRowEditMode({id:r,ignoreModifications:i,field:o,cellToFocusAfter:l})}),[e]);dr(e,"cellDoubleClick",h(v)),dr(e,"cellFocusIn",h(x)),dr(e,"cellFocusOut",h(S)),dr(e,"cellKeyDown",h(R)),dr(e,"rowEditStart",h(I)),dr(e,"rowEditStop",h(M)),fr(e,"rowEditStart",t.onRowEditStart),fr(e,"rowEditStop",t.onRowEditStop);const k=g.useCallback((r=>Aa(e,{rowId:r,editMode:t.editMode})?an.Edit:an.View),[e,t.editMode]),P=y((r=>{const l=r!==t.rowModesModel;m&&l&&m(r,{api:e.current}),t.rowModesModel&&l||(n(r),o.current=r,e.current.publishEvent("rowModesModelChange",r))})),E=g.useCallback(((e,t)=>{const r=u({},o.current);null!==t?r[e]=u({},t):delete r[e],P(r)}),[P]),H=g.useCallback(((t,r)=>{e.current.setState((e=>{const n=u({},e.editRows);return null!==r?n[t]=r:delete n[t],u({},e,{editRows:n})}))}),[e]),T=g.useCallback(((t,r,n)=>{e.current.setState((e=>{const o=u({},e.editRows);return null!==n?o[t]=u({},o[t],{[r]:u({},n)}):(delete o[t][r],0===Object.keys(o[t]).length&&delete o[t]),u({},e,{editRows:o})}))}),[e]),D=g.useCallback((e=>{const{id:t}=e,r=F(e,Qp);w(t,an.View),E(t,u({mode:an.Edit},r))}),[w,E]),O=y((t=>{const{id:r,fieldToFocus:n,deleteValue:o,initialValue:l}=t,a=e.current.getRow(r),s=Mn(e),c=s.reduce(((t,i)=>{const a=i.field;if(!e.current.getCellParams(r,a).isEditable)return t;const s=e.current.getColumn(a);let c=e.current.getCellValue(r,a);return n===a&&(o||l)&&(o?c=_p(s):l&&(c=l)),t[a]={value:c,error:!1,isProcessingProps:s.editable&&!!s.preProcessEditCellProps&&o},t}),{});i.current[r]=a,H(r,c),n&&e.current.setCellFocus(r,n),s.filter((e=>e.editable&&!!e.preProcessEditCellProps&&o)).forEach((t=>{const n=t.field,i=e.current.getCellValue(r,n),s=o?_p(t):l??i;Promise.resolve(t.preProcessEditCellProps({id:r,row:a,props:c[n],hasChanged:s!==i})).then((t=>{if(e.current.getRowMode(r)===an.Edit){const o=za(e);T(r,n,u({},t,{value:o[r][n].value,isProcessingProps:!1}))}}))}))})),$=g.useCallback((e=>{const{id:t}=e,r=F(e,Yp);w(t,an.Edit),E(t,u({mode:an.View},r))}),[w,E]),j=y((async r=>{const{id:n,ignoreModifications:o,field:a,cellToFocusAfter:s="none"}=r;e.current.runPendingEditCellValueMutation(n);const u=()=>{"none"!==s&&a&&e.current.moveFocusToRelativeCell(n,a,s),H(n,null),E(n,null),delete i.current[n]};if(o)return void u();const d=za(e),f=i.current[n];if(Object.values(d[n]).some((e=>e.isProcessingProps)))return void(l.current[n].mode=an.Edit);if(C(n))return l.current[n].mode=an.Edit,void E(n,{mode:an.Edit});const g=e.current.getRowWithUpdatedValuesFromRowEditing(n);if(t.dataSource?.updateRow){if(nd(f,g))return void u();const t=()=>{l.current[n].mode=an.Edit,E(n,{mode:an.Edit})},r={rowId:n,updatedRow:g,previousRow:f};try{await e.current.dataSource.editRow(r),u()}catch{t()}}else if(c){const t=e=>{l.current[n]&&(l.current[n].mode=an.Edit,E(n,{mode:an.Edit})),p&&p(e)};try{Promise.resolve(c(g,f,{rowId:n})).then((t=>{e.current.updateRows([t]),u()})).catch(t)}catch(m){t(m)}}else e.current.updateRows([g]),u()})),L={setRowEditingEditCellValue:g.useCallback((t=>{const{id:r,field:n,value:o,debounceMs:l,unstable_skipValueParser:i}=t;b(r,n);const a=e.current.getColumn(n),s=e.current.getRow(r);let c=o;a.valueParser&&!i&&(c=a.valueParser(o,s,a,e));let d=za(e),p=u({},d[r][n],{value:c,changeReason:l?"debouncedSetEditCellValue":"setEditCellValue"});return a.preProcessEditCellProps||T(r,n,p),new Promise((t=>{const o=[];if(a.preProcessEditCellProps){const l=p.value!==d[r][n].value;p=u({},p,{isProcessingProps:!0}),T(r,n,p);const i=d[r],f=F(i,[n].map(bp)),g=Promise.resolve(a.preProcessEditCellProps({id:r,row:s,props:p,hasChanged:l,otherFieldsProps:f})).then((o=>{e.current.getRowMode(r)!==an.View?(d=za(e),(o=u({},o,{isProcessingProps:!1})).value=a.preProcessEditCellProps?d[r][n].value:c,T(r,n,o)):t(!1)}));o.push(g)}Object.entries(d[r]).forEach((([l,i])=>{if(l===n)return;const a=e.current.getColumn(l);if(!a.preProcessEditCellProps)return;i=u({},i,{isProcessingProps:!0}),T(r,l,i),d=za(e);const c=d[r],p=F(c,[l].map(bp)),f=Promise.resolve(a.preProcessEditCellProps({id:r,row:s,props:i,hasChanged:!1,otherFieldsProps:p})).then((n=>{e.current.getRowMode(r)!==an.View?(n=u({},n,{isProcessingProps:!1}),T(r,l,n)):t(!1)}));o.push(f)})),Promise.all(o).then((()=>{e.current.getRowMode(r)===an.Edit?(d=za(e),t(!d[r][n].error)):t(!1)}))}))}),[e,b,T]),getRowWithUpdatedValuesFromRowEditing:g.useCallback((t=>{const r=za(e),n=e.current.getRow(t);if(!r[t])return e.current.getRow(t);let o=u({},i.current[t],n);return Object.entries(r[t]).forEach((([t,r])=>{const n=e.current.getColumn(t);n?.valueSetter?o=n.valueSetter(r.value,o,n,e):o[t]=r.value})),o}),[e])};Ti(e,{getRowMode:k,startRowEditMode:D,stopRowEditMode:$},"public"),Ti(e,L,"private"),g.useEffect((()=>{f&&P(f)}),[f,P]),d((()=>{const t=Fr(e),n=l.current;l.current=tn(r);const o=new Set([...Object.keys(r),...Object.keys(n)]);Array.from(o).forEach((o=>{const l=r[o]??{mode:an.View},i=n[o]?.mode||an.View,a=t[o]?e.current.getRowId(t[o]):o;l.mode===an.Edit&&i===an.View?O(u({id:a},l)):l.mode===an.View&&i===an.Edit&&j(u({id:a},l))}))}),[e,r,O,j])})(e,t);const r=g.useRef({}),{isCellEditable:n}=t,o=g.useCallback((e=>!ho(e.rowNode)&&!!e.colDef.editable&&!!e.colDef.renderEditCell&&(!n||n(e))),[n]);g.useEffect((()=>{const e=r.current;return()=>{Object.entries(e).forEach((([t,r])=>{Object.keys(r).forEach((r=>{const[n]=e[t][r];clearTimeout(n),delete e[t][r]}))}))}}),[]);const l=g.useCallback(((e,t)=>{if(r.current[e])if(t){if(r.current[e][t]){const[,n]=r.current[e][t];n()}}else Object.keys(r.current[e]).forEach((t=>{const[,n]=r.current[e][t];n()}))}),[]),i=g.useCallback((n=>{const{id:o,field:l,debounceMs:i}=n;return new Promise((a=>{((e,t,n,o)=>{if(!n)return void o();if(r.current[e]||(r.current[e]={}),r.current[e][t]){const[n]=r.current[e][t];clearTimeout(n)}const l=setTimeout((()=>{o(),delete r.current[e][t]}),n);r.current[e][t]=[l,()=>{const[n]=r.current[e][t];clearTimeout(n),o(),delete r.current[e][t]}]})(o,l,i,(async()=>{const r=t.editMode===on.Row?e.current.setRowEditingEditCellValue:e.current.setCellEditingEditCellValue;if(e.current.getCellMode(o,l)===ln.Edit){const e=await r(n);a(e)}}))}))}),[e,t.editMode]),a=g.useCallback(((r,n)=>t.editMode===on.Cell?e.current.getRowWithUpdatedValuesFromCellEditing(r,n):e.current.getRowWithUpdatedValuesFromRowEditing(r)),[e,t.editMode]),s=g.useCallback(((t,r)=>{const n=za(e);return n[t]?.[r]??null}),[e]),c={runPendingEditCellValueMutation:l};Ti(e,{isCellEditable:o,setEditCellValue:i,getRowWithUpdatedValues:a,unstable_getEditCellMeta:s},"public"),Ti(e,c,"private")})(e,n),((e,t)=>{const r=Di(e,"useGridFocus"),n=g.useRef(null),o=null!==e.current.rootElementRef.current,l=g.useCallback(((t,r)=>{t&&e.current.getRow(t.id)&&e.current.publishEvent("cellFocusOut",e.current.getCellParams(t.id,t.field),r)}),[e]),i=g.useCallback(((t,n)=>{const o=pl(e);o?.id===t&&o?.field===n||(e.current.setState((e=>(r.debug(`Focusing on cell with id=${t} and field=${n}`),u({},e,{tabIndex:{cell:{id:t,field:n},columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null},focus:{cell:{id:t,field:n},columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}})))),e.current.getRow(t)&&(o&&l(o,{}),e.current.publishEvent("cellFocusIn",e.current.getCellParams(t,n))))}),[e,r,l]),a=g.useCallback(((t,n={})=>{const o=pl(e);l(o,n),e.current.setState((e=>(r.debug(`Focusing on column header with colIndex=${t}`),u({},e,{tabIndex:{columnHeader:{field:t},columnHeaderFilter:null,cell:null,columnGroupHeader:null},focus:{columnHeader:{field:t},columnHeaderFilter:null,cell:null,columnGroupHeader:null}}))))}),[e,r,l]),s=g.useCallback(((t,n={})=>{const o=pl(e);l(o,n),e.current.setState((e=>(r.debug(`Focusing on column header filter with colIndex=${t}`),u({},e,{tabIndex:{columnHeader:null,columnHeaderFilter:{field:t},cell:null,columnGroupHeader:null},focus:{columnHeader:null,columnHeaderFilter:{field:t},cell:null,columnGroupHeader:null}}))))}),[e,r,l]),c=g.useCallback(((t,r,n={})=>{const o=pl(e);o&&e.current.publishEvent("cellFocusOut",e.current.getCellParams(o.id,o.field),n),e.current.setState((e=>u({},e,{tabIndex:{columnGroupHeader:{field:t,depth:r},columnHeader:null,columnHeaderFilter:null,cell:null},focus:{columnGroupHeader:{field:t,depth:r},columnHeader:null,columnHeaderFilter:null,cell:null}})))}),[e]),d=g.useCallback((()=>gl(e)),[e]),p=g.useCallback(((r,n,o)=>{let l=e.current.getColumnIndex(n);const i=En(e),a=ji(e,(t.pagination,t.paginationMode)),s=Ar(e),c=[].concat(s.top||[],a.rows,s.bottom||[]);let u=c.findIndex((e=>e.id===r));"right"===o?l+=1:"left"===o?l-=1:u+=1,l>=i.length?(u+=1,u<c.length&&(l=0)):l<0&&(u-=1,u>=0&&(l=i.length-1)),u=Zr(u,0,c.length-1);const d=c[u];if(!d)return;const p=e.current.unstable_getCellColSpanInfo(d.id,l);p&&p.spannedByColSpan&&("left"===o||"below"===o?l=p.leftVisibleCellIndex:"right"===o&&(l=p.rightVisibleCellIndex)),l=Zr(l,0,i.length-1);const f=i[l];e.current.setCellFocus(d.id,f.field)}),[e,t.pagination,t.paginationMode]),f=g.useCallback((({id:t,field:r})=>{e.current.setCellFocus(t,r)}),[e]),m=g.useCallback(((t,r)=>{"Enter"===r.key||"Tab"===r.key||"Shift"===r.key||Qc(r.key)||e.current.setCellFocus(t.id,t.field)}),[e]),h=g.useCallback((({field:t},r)=>{r.target===r.currentTarget&&e.current.setColumnHeaderFocus(t,r)}),[e]),b=g.useCallback((({fields:t,depth:r},n)=>{if(n.target!==n.currentTarget)return;const o=gl(e);null!==o&&o.depth===r&&t.includes(o.field)||e.current.setColumnGroupHeaderFocus(t[0],r,n)}),[e]),w=g.useCallback(((t,n)=>{n.relatedTarget?.getAttribute("class")?.includes(ir.columnHeader)||(r.debug("Clearing focus"),e.current.setState((e=>u({},e,{focus:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}}))))}),[r,e]),C=g.useCallback((e=>{n.current=e}),[]),v=g.useCallback((t=>{const r=n.current;n.current=null;const o=pl(e);if(!e.current.unstable_applyPipeProcessors("canUpdateFocus",!0,{event:t,cell:r}))return;if(!o)return void(r&&e.current.setCellFocus(r.id,r.field));if(r?.id===o.id&&r?.field===o.field)return;const i=e.current.getCellElement(o.id,o.field);i?.contains(t.target)||(r?e.current.setCellFocus(r.id,r.field):(e.current.setState((e=>u({},e,{focus:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}}))),l(o,t)))}),[e,l]),x=g.useCallback((t=>{if("view"===t.cellMode)return;const r=pl(e);r?.id===t.id&&r?.field===t.field||e.current.setCellFocus(t.id,t.field)}),[e]),S=g.useCallback((()=>{const r=pl(e);if(r&&!e.current.getRow(r.id)){const n=r.id;let o=null;if(void 0!==n){const r=e.current.getRowElement(n),l=r?.dataset.rowindex?Number(r?.dataset.rowindex):0,i=ji(e,(t.pagination,t.paginationMode)),a=i.rows[Zr(l,0,i.rows.length-1)];o=a?.id??null}e.current.setState((e=>u({},e,{focus:{cell:null===o?null:{id:o,field:r.field},columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}})))}}),[e,t.pagination,t.paginationMode]),R=y((()=>{const r=pl(e);if(!r)return;const n=ji(e,(t.pagination,t.paginationMode));if(n.rows.find((e=>e.id===r.id)))return;const o=En(e);e.current.setState((e=>u({},e,{tabIndex:{cell:{id:n.rows[0].id,field:o[0].field},columnGroupHeader:null,columnHeader:null,columnHeaderFilter:null}})))})),I={moveFocusToRelativeCell:p,setColumnGroupHeaderFocus:c,getColumnGroupHeaderFocus:d};Ti(e,{setCellFocus:i,setColumnHeaderFocus:a,setColumnHeaderFilterFocus:s},"public"),Ti(e,I,"private"),g.useEffect((()=>{const t=$(e.current.rootElementRef.current);return t.addEventListener("mouseup",v),()=>{t.removeEventListener("mouseup",v)}}),[e,o,v]),dr(e,"columnHeaderBlur",w),dr(e,"cellDoubleClick",f),dr(e,"cellMouseDown",C),dr(e,"cellKeyDown",m),dr(e,"cellModeChange",x),dr(e,"columnHeaderFocus",h),dr(e,"columnGroupHeaderFocus",b),dr(e,"rowsSet",S),dr(e,"paginationModelChange",R)})(e,n),((e,t)=>{const r=Di(e,"useGridPreferencesPanel"),n=g.useCallback((()=>{e.current.setState((t=>{if(!t.preferencePanel.open)return t;r.debug("Hiding Preferences Panel");const n=Va(e);return e.current.publishEvent("preferencePanelClose",{openedPanelValue:n.openedPanelValue}),u({},t,{preferencePanel:{open:!1}})}))}),[e,r]),o=g.useCallback(((t,n,o)=>{r.debug("Opening Preferences Panel"),e.current.setState((e=>u({},e,{preferencePanel:u({},e.preferencePanel,{open:!0,openedPanelValue:t,panelId:n,labelId:o})}))),e.current.publishEvent("preferencePanelOpen",{openedPanelValue:t})}),[r,e]);Ti(e,{showPreferences:o,hidePreferences:n},"public");const l=g.useCallback(((r,n)=>{const o=Va(e);return!n.exportOnlyDirtyModels||null!=t.initialState?.preferencePanel||o.open?u({},r,{preferencePanel:o}):r}),[e,t.initialState?.preferencePanel]),i=g.useCallback(((t,r)=>{const n=r.stateToRestore.preferencePanel;return null!=n&&e.current.setState((e=>u({},e,{preferencePanel:n}))),t}),[e]);wp(e,"exportState",l),wp(e,"restoreState",i)})(e,n),((e,t)=>{const n=Di(e,"useGridFilter");e.current.registerControlState({stateId:"filter",propModel:t.filterModel,propOnChange:t.onFilterModelChange,stateSelector:Bo,changeEvent:"filterModelChange"});const o=g.useCallback((()=>{e.current.setState((t=>{const r=Bo(e),n=e.current.getFilterState(r),o=u({},t,{filter:u({},t.filter,n)}),l=Ap(e,o);return u({},o,{visibleRowsLookup:l})})),e.current.publishEvent("filteredRowsSet")}),[e]),l=g.useCallback(((e,r)=>null==r||!1===r.filterable||t.disableColumnFilter?e:[...e,"columnMenuFilterItem"]),[t.disableColumnFilter]),i=g.useCallback((t=>{const r=Bo(e),n=[...r.items],o=n.findIndex((e=>e.id===t.id));-1===o?n.push(t):n[o]=t,e.current.setFilterModel(u({},r,{items:n}),"upsertFilterItem")}),[e]),a=g.useCallback((t=>{const r=Bo(e),n=[...r.items];t.forEach((e=>{const t=n.findIndex((t=>t.id===e.id));-1===t?n.push(e):n[t]=e})),e.current.setFilterModel(u({},r,{items:n}),"upsertFilterItems")}),[e]),s=g.useCallback((t=>{const r=Bo(e),n=r.items.filter((e=>e.id!==t.id));n.length!==r.items.length&&e.current.setFilterModel(u({},r,{items:n}),"deleteFilterItem")}),[e]),c=g.useCallback(((r,o,l)=>{if(n.debug("Displaying filter panel"),r){const n=Bo(e),o=n.items.filter((t=>{if(void 0!==t.value)return!Array.isArray(t.value)||0!==t.value.length;const r=e.current.getColumn(t.field),n=r.filterOperators?.find((e=>e.value===t.operator));return!(void 0===n?.requiresFilterValue||n?.requiresFilterValue)}));let l;const i=o.find((e=>e.field===r)),a=e.current.getColumn(r);l=i?o:t.disableMultipleColumnsFiltering?[zn({field:r,operator:a.filterOperators[0].value},e)]:[...o,zn({field:r,operator:a.filterOperators[0].value},e)],e.current.setFilterModel(u({},n,{items:l}))}e.current.showPreferences(Ga.filters,o,l)}),[e,n,t.disableMultipleColumnsFiltering]),p=g.useCallback((()=>{n.debug("Hiding filter panel"),e.current.hidePreferences()}),[e,n]),f=g.useCallback((t=>{const r=Bo(e);r.logicOperator!==t&&e.current.setFilterModel(u({},r,{logicOperator:t}),"changeLogicOperator")}),[e]),m=g.useCallback((t=>{const r=Bo(e);nd(r.quickFilterValues,t)||e.current.setFilterModel(u({},r,{quickFilterValues:[...t]}))}),[e]),h=g.useCallback(((r,o)=>{Bo(e)!==r&&(n.debug("Setting filter model"),e.current.updateControlState("filter",Bn(r,t.disableMultipleColumnsFiltering,e),o),e.current.unstable_applyFilters())}),[e,n,t.disableMultipleColumnsFiltering]),b=g.useCallback((r=>{const n=An(r,t.disableMultipleColumnsFiltering,e),o="client"===t.filterMode?((e,t,r)=>{const n=((e,t,r)=>{const{items:n}=e,o=n.map((e=>Nn(e,t))).filter((e=>!!e));if(0===o.length)return null;if(r||!function(){if(void 0!==Ln)return Ln;try{Ln=new Function("return true")()}catch(e){Ln=!1}return Ln}())return(e,t)=>{const r={};for(let n=0;n<o.length;n+=1){const l=o[n];t&&!t(l.item.field)||(r[l.item.id]=l.fn(e))}return r};const l=new Function("appliers","row","shouldApplyFilter",`"use strict";\n${o.map(((e,t)=>`const shouldApply${t} = !shouldApplyFilter || shouldApplyFilter(${JSON.stringify(e.item.field)});`)).join("\n")}\n\nconst result$$ = {\n${o.map(((e,t)=>`  ${JSON.stringify(String(e.item.id))}: !shouldApply${t} ? false : appliers[${t}].fn(row),`)).join("\n")}\n};\n\nreturn result$$;`.replaceAll("$$",String(Gn)));return Gn+=1,(e,t)=>l(o,e,t)})(e,t,r),o=((e,t)=>{const r=e.quickFilterValues?.filter(Boolean)??[];if(0===r.length)return null;const n=Wn(e)?Fn(t):Rn(t),o=[],{ignoreDiacritics:l}=t.current.rootProps,i=Cn(t);return n.forEach((e=>{const n=t.current.getColumn(e),a=n?.getApplyQuickFilterFn;a&&o.push({column:n,appliers:r.map((e=>{const t=l?Vn(e):e;return{fn:a(t,n,i)}}))})})),function(e,n){const a={};e:for(let s=0;s<r.length;s+=1){const c=r[s];for(let r=0;r<o.length;r+=1){const{column:u,appliers:d}=o[r],{field:p}=u;if(n&&!n(p))continue;const f=d[s];let g=t.current.getRowValue(e,u);if(null!==f.fn&&(l&&(g=Vn(g)),f.fn(g,e,u,i))){a[c]=!0;continue e}}a[c]=!1}return a}})(e,t);return function(e,t,r){r.passingFilterItems=n?.(e,t)??null,r.passingQuickFilterValues=o?.(e,t)??null}})(n,e,t.disableEval):null,l=e.current.applyStrategyProcessor("filtering",{isRowMatchingFilters:o,filterModel:n??wn()});return u({},l,{filterModel:n})}),[t.disableMultipleColumnsFiltering,t.filterMode,t.disableEval,e]),w={setFilterLogicOperator:f,unstable_applyFilters:o,deleteFilterItem:s,upsertFilterItem:i,upsertFilterItems:a,setFilterModel:h,showFilterPanel:c,hideFilterPanel:p,setQuickFilterValues:m,ignoreDiacritics:t.ignoreDiacritics,getFilterState:b};Ti(e,w,"public");const C=g.useCallback(((r,n)=>{const o=Bo(e);return o.items.forEach((e=>{delete e.fromInput})),n.exportOnlyDirtyModels&&null==t.filterModel&&null==t.initialState?.filter?.filterModel&&nd(o,wn())?r:u({},r,{filter:{filterModel:o}})}),[e,t.filterModel,t.initialState?.filter?.filterModel]),v=g.useCallback(((r,n)=>{const o=n.stateToRestore.filter?.filterModel;return null==o?r:(e.current.updateControlState("filter",Bn(o,t.disableMultipleColumnsFiltering,e),"restoreState"),u({},r,{callbacks:[...r.callbacks,e.current.unstable_applyFilters]}))}),[e,t.disableMultipleColumnsFiltering]),y=g.useCallback(((e,n)=>{if(n===Ga.filters){const e=t.slots.filterPanel;return r.jsx(e,u({},t.slotProps?.filterPanel))}return e}),[t.slots.filterPanel,t.slotProps?.filterPanel]),{getRowId:x}=t,S=E(Bp),R=g.useCallback((r=>{if("client"!==t.filterMode||!r.isRowMatchingFilters||!r.filterModel.items.length&&!r.filterModel.quickFilterValues?.length)return bn;const n=Fr(e),o={},{isRowMatchingFilters:l}=r,i={},a={passingFilterItems:null,passingQuickFilterValues:null},s=S.current(e.current.state.rows.dataRowIdToModelLookup);for(let t=0;t<s.length;t+=1){const n=s[t],c=x?x(n):n.id;l(n,void 0,a);const u=Kn([a.passingFilterItems],[a.passingQuickFilterValues],r.filterModel,e,i);u||(o[c]=u)}const c="auto-generated-group-footer-root";return n[c]&&(o[c]=!0),{filteredRowsLookup:o,filteredChildrenCountLookup:{},filteredDescendantCountLookup:{}}}),[e,t.filterMode,x,S]);wp(e,"columnMenu",l),wp(e,"exportState",C),wp(e,"restoreState",v),wp(e,"preferencePanel",y),yp(e,xp,"filtering",R),yp(e,xp,"visibleRowsLookupCreation",zp);const I=g.useCallback((()=>{n.debug("onColUpdated - GridColumns changed, applying filters");const t=Bo(e),r=In(e),o=t.items.filter((e=>e.field&&r[e.field]));o.length<t.items.length&&e.current.setFilterModel(u({},t,{items:o}))}),[e,n]),M=g.useCallback((t=>{"filtering"===t&&e.current.unstable_applyFilters()}),[e]),k=g.useCallback((()=>{e.current.setState((t=>u({},t,{visibleRowsLookup:Ap(e,t)})))}),[e]);dr(e,"rowsSet",o),dr(e,"columnsChange",I),dr(e,"activeStrategyProcessorChange",M),dr(e,"rowExpansionChange",k),dr(e,"columnVisibilityModelChange",(()=>{const t=Bo(e);t.quickFilterValues&&Wn(t)&&e.current.unstable_applyFilters()})),$i((()=>{e.current.unstable_applyFilters()})),d((()=>{void 0!==t.filterModel&&e.current.setFilterModel(t.filterModel)}),[e,n,t.filterModel])})(e,n),((e,t)=>{const r=Di(e,"useGridSorting");e.current.registerControlState({stateId:"sortModel",propModel:t.sortModel,propOnChange:t.onSortModelChange,stateSelector:Lo,changeEvent:"sortModelChange"});const n=g.useCallback(((t,r)=>{const n=Lo(e),o=n.findIndex((e=>e.field===t));let l=[...n];return o>-1?null==r?.sort?l.splice(o,1):l.splice(o,1,r):l=[...n,r],l}),[e]),o=g.useCallback(((r,n)=>{const o=Lo(e).find((e=>e.field===r.field));if(o){const e=void 0===n?Gr(r.sortingOrder??t.sortingOrder,o.sort):n;return void 0===e?void 0:u({},o,{sort:e})}return{field:r.field,sort:void 0===n?Gr(r.sortingOrder??t.sortingOrder):n}}),[e,t.sortingOrder]),l=g.useCallback(((e,r)=>null==r||!1===r.sortable||t.disableColumnSorting?e:(r.sortingOrder||t.sortingOrder).some((e=>!!e))?[...e,"columnMenuSortItem"]:e),[t.sortingOrder,t.disableColumnSorting]),i=g.useCallback((()=>{e.current.setState((n=>{if("server"===t.sortingMode)return r.debug("Skipping sorting rows as sortingMode = server"),u({},n,{sorting:u({},n.sorting,{sortedRows:bo(Hr(e),co,!1)})});const o=((e,t)=>{const r=e.map((e=>((e,t)=>{const r=t.current.getColumn(e.field);if(!r||null===e.sort)return null;let n;return n=r.getSortComparator?r.getSortComparator(e.sort):"desc"===e.sort?(...e)=>-1*r.sortComparator(...e):r.sortComparator,n?{getSortCellParams:e=>({id:e,field:r.field,rowNode:Tr(t,e),value:t.current.getCellValue(e,r.field),api:t.current}),comparator:n}:null})(e,t))).filter((e=>!!e));return 0===r.length?null:e=>e.map((e=>({node:e,params:r.map((t=>t.getSortCellParams(e.id)))}))).sort(((e,t)=>{return n=e,o=t,r.reduce(((e,t,r)=>{if(0!==e)return e;const l=n.params[r],i=o.params[r];return t.comparator(l.value,i.value,l,i)}),0);var n,o})).map((e=>e.node.id))})(Lo(e),e),l=e.current.applyStrategyProcessor("sorting",{sortRowList:o});return u({},n,{sorting:u({},n.sorting,{sortedRows:l})})})),e.current.publishEvent("sortedRowsSet")}),[e,r,t.sortingMode]),a=g.useCallback((n=>{Lo(e)!==n&&(r.debug("Setting sort model"),e.current.setState(Nr(n,t.disableMultipleColumnsSorting)),e.current.applySorting())}),[e,r,t.disableMultipleColumnsSorting]),s=g.useCallback(((r,l,i)=>{const a=e.current.getColumn(r),s=o(a,l);let c;c=!i||t.disableMultipleColumnsSorting?null==s?.sort?[]:[s]:n(a.field,s),e.current.setSortModel(c)}),[e,n,o,t.disableMultipleColumnsSorting]),c=g.useCallback((()=>Lo(e)),[e]),p=g.useCallback((()=>jo(e).map((e=>e.model))),[e]),f=g.useCallback((()=>$o(e)),[e]),m=g.useCallback((t=>e.current.getSortedRowIds()[t]),[e]);Ti(e,{getSortModel:c,getSortedRows:p,getSortedRowIds:f,getRowIdFromRowIndex:m,setSortModel:a,sortColumn:s,applySorting:i},"public");const h=g.useCallback(((r,n)=>{const o=Lo(e);return!n.exportOnlyDirtyModels||null!=t.sortModel||null!=t.initialState?.sorting?.sortModel||o.length>0?u({},r,{sorting:{sortModel:o}}):r}),[e,t.sortModel,t.initialState?.sorting?.sortModel]),b=g.useCallback(((r,n)=>{const o=n.stateToRestore.sorting?.sortModel;return null==o?r:(e.current.setState(Nr(o,t.disableMultipleColumnsSorting)),u({},r,{callbacks:[...r.callbacks,e.current.applySorting]}))}),[e,t.disableMultipleColumnsSorting]),w=g.useCallback((t=>{const r=Hr(e),n=r[co],o=t.sortRowList?t.sortRowList(n.children.map((e=>r[e]))):[...n.children];return null!=n.footerId&&o.push(n.footerId),o}),[e]);wp(e,"exportState",h),wp(e,"restoreState",b),yp(e,xp,"sorting",w);const C=g.useCallback((({field:e,colDef:r},n)=>{if(!r.sortable||t.disableColumnSorting)return;const o=n.shiftKey||n.metaKey||n.ctrlKey;s(e,void 0,o)}),[s,t.disableColumnSorting]),v=g.useCallback((({field:e,colDef:r},n)=>{r.sortable&&!t.disableColumnSorting&&("Enter"!==n.key||n.ctrlKey||n.metaKey||s(e,void 0,n.shiftKey))}),[s,t.disableColumnSorting]),y=g.useCallback((()=>{const t=Lo(e),r=In(e);if(t.length>0){const n=t.filter((e=>r[e.field]));n.length<t.length&&e.current.setSortModel(n)}}),[e]),x=g.useCallback((t=>{"sorting"===t&&e.current.applySorting()}),[e]);wp(e,"columnMenu",l),dr(e,"columnHeaderClick",C),dr(e,"columnHeaderKeyDown",v),dr(e,"rowsSet",e.current.applySorting),dr(e,"columnsChange",y),dr(e,"activeStrategyProcessorChange",x),$i((()=>{e.current.applySorting()})),d((()=>{void 0!==t.sortModel&&e.current.setSortModel(t.sortModel)}),[e,t.sortModel])})(e,n),((e,t)=>{const r=Di(e,"useDensity");e.current.registerControlState({stateId:"density",propModel:t.density,propOnChange:t.onDensityChange,stateSelector:mr,changeEvent:"densityChange"});const n=y((t=>{mr(e)!==t&&(r.debug(`Set grid density to ${t}`),e.current.setState((e=>u({},e,{density:t}))))}));Ti(e,{setDensity:n},"public");const o=g.useCallback(((r,n)=>{const o=mr(e);return n.exportOnlyDirtyModels&&null==t.density&&null==t.initialState?.density?r:u({},r,{density:o})}),[e,t.density,t.initialState?.density]),l=g.useCallback(((t,r)=>{const n=r.stateToRestore?.density?r.stateToRestore.density:mr(e);return e.current.setState((e=>u({},e,{density:n}))),t}),[e]);wp(e,"exportState",o),wp(e,"restoreState",l),g.useEffect((()=>{t.density&&e.current.setDensity(t.density)}),[e,t.density])})(e,n),((e,r)=>{const n=t(),o=Di(e,"useGridColumnResize"),l=E(Ef).current,i=g.useRef(null),a=g.useRef(null),s=H(),c=g.useRef(void 0),d=t=>{o.debug(`Updating width to ${t} for col ${l.colDef.field}`);const r=l.columnHeaderElement.offsetWidth,n=t-r,i=t-l.initialColWidth;if(i>0){const t=l.initialTotalWidth+i;e.current.rootElementRef?.current?.style.setProperty("--DataGrid-rowWidth",`${t}px`)}l.colDef.computedWidth=t,l.colDef.width=t,l.colDef.flex=0,l.columnHeaderElement.style.width=`${t}px`;const a=l.headerFilterElement;a&&(a.style.width=`${t}px`),l.groupHeaderElements.forEach((e=>{const r=e;let o;o="1"===r.getAttribute("aria-colspan")?`${t}px`:`${r.offsetWidth+n}px`,r.style.width=o})),l.cellElements.forEach((e=>{const r=e;let o;o="1"===r.getAttribute("aria-colspan")?`${t}px`:`${r.offsetWidth+n}px`,r.style.setProperty("--width",o)}));const s=e.current.unstable_applyPipeProcessors("isColumnPinned",!1,l.colDef.field);s===vn.LEFT&&(Ff(l.fillerLeft,"width",n),l.leftPinnedCellsAfter.forEach((e=>{Ff(e,"left",n)})),l.leftPinnedHeadersAfter.forEach((e=>{Ff(e,"left",n)}))),s===vn.RIGHT&&(Ff(l.fillerRight,"width",n),l.rightPinnedCellsBefore.forEach((e=>{Ff(e,"right",n)})),l.rightPinnedHeadersBefore.forEach((e=>{Ff(e,"right",n)})))},p=t=>{if(v(),l.previousMouseClickEvent){const r=l.previousMouseClickEvent,n=r.timeStamp,o=r.clientX,i=r.clientY;if(t.timeStamp-n<300&&t.clientX===o&&t.clientY===i)return l.previousMouseClickEvent=void 0,void e.current.publishEvent("columnResizeStop",null,t)}if(l.colDef){e.current.setColumnWidth(l.colDef.field,l.colDef.width),o.debug(`Updating col ${l.colDef.field} with new width: ${l.colDef.width}`);const t=Sn(e);l.groupHeaderElements.forEach((e=>{const r=e,n=`${e.getAttribute("data-fields").slice(2,-2).split("-|-").reduce(((e,r)=>!1!==t.columnVisibilityModel[r]?e+t.lookup[r].computedWidth:e),0)}px`;r.style.width=n}))}s.start(0,(()=>{e.current.publishEvent("columnResizeStop",null,t)}))},f=(t,r,o)=>{const s=e.current.rootElementRef.current;var c,u;l.initialColWidth=t.computedWidth,l.initialTotalWidth=e.current.getRootDimensions().rowWidth,l.colDef=t,l.columnHeaderElement=(c=e.current.columnHeadersContainerRef.current,u=t.field,c.querySelector(`[data-field="${_s(u)}"]`));const d=s.querySelector(`.${ir.headerFilterRow} [data-field="${_s(t.field)}"]`);d&&(l.headerFilterElement=d),l.groupHeaderElements=function(e,t){return Array.from(e.querySelectorAll(`[data-fields*="|-${_s(t)}-|"]`)??[])}(e.current.columnHeadersContainerRef?.current,t.field),l.cellElements=function(e,t){if(!Ks(e,ir.root))throw new Error("MUI X: The root element is not found.");const r=e.getAttribute("aria-colindex");if(!r)return[];const n=Number(r)-1,o=[];return t.virtualScrollerRef?.current?(Zs(t).forEach((e=>{const r=e.getAttribute("data-id");if(!r)return;let l=n;const i=t.unstable_getCellColSpanInfo(r,n);i&&i.spannedByColSpan&&(l=i.leftVisibleCellIndex);const a=e.querySelector(`[data-colindex="${l}"]`);a&&o.push(a)})),o):[]}(l.columnHeaderElement,e.current),l.fillerLeft=Qs(e.current,n?"filler--pinnedRight":"filler--pinnedLeft"),l.fillerRight=Qs(e.current,n?"filler--pinnedLeft":"filler--pinnedRight");const p=e.current.unstable_applyPipeProcessors("isColumnPinned",!1,l.colDef.field);l.leftPinnedCellsAfter=p!==vn.LEFT?[]:function(e,t,r){const n=ec(t);return Ys({api:e,colIndex:n,position:r?"right":"left",filterFn:e=>r?e<n:e>n})}(e.current,l.columnHeaderElement,n),l.rightPinnedCellsBefore=p!==vn.RIGHT?[]:function(e,t,r){const n=ec(t);return Ys({api:e,colIndex:n,position:r?"left":"right",filterFn:e=>r?e>n:e<n})}(e.current,l.columnHeaderElement,n),l.leftPinnedHeadersAfter=p!==vn.LEFT?[]:function(e,t,r){const n=ec(t);return Js({api:e,position:r?"right":"left",colIndex:n,filterFn:e=>r?e<n:e>n})}(e.current,l.columnHeaderElement,n),l.rightPinnedHeadersBefore=p!==vn.RIGHT?[]:function(e,t,r){const n=ec(t);return Js({api:e,position:r?"left":"right",colIndex:n,filterFn:(e,t)=>!t.classList.contains(ir["columnHeader--last"])&&(r?e>n:e<n)})}(e.current,l.columnHeaderElement,n),a.current=function(e,t){const r=e.classList.contains(ir["columnSeparator--sideRight"])?"Right":"Left";return t?function(e){return"Right"===e?"Left":"Right"}(r):r}(r,n),i.current=function(e,t,r){return"Left"===r?e-t.left:t.right-e}(o,l.columnHeaderElement.getBoundingClientRect(),a.current)},m=y(p),h=y((t=>{if(0===t.buttons)return void m(t);let r=Mf(i.current,t.clientX,l.columnHeaderElement.getBoundingClientRect(),a.current);r=Zr(r,l.colDef.minWidth,l.colDef.maxWidth),d(r);const n={element:l.columnHeaderElement,colDef:l.colDef,width:r};e.current.publishEvent("columnResize",n,t)})),b=y((e=>{If(e,c.current)&&p(e)})),w=y((t=>{const r=If(t,c.current);if(!r)return;if("mousemove"===t.type&&0===t.buttons)return void b(t);let n=Mf(i.current,r.x,l.columnHeaderElement.getBoundingClientRect(),a.current);n=Zr(n,l.colDef.minWidth,l.colDef.maxWidth),d(n);const o={element:l.columnHeaderElement,colDef:l.colDef,width:n};e.current.publishEvent("columnResize",o,t)})),C=y((t=>{const r=Ks(t.target,ir["columnSeparator--resizable"]);if(!r)return;const n=t.changedTouches[0];null!=n&&(c.current=n.identifier);const l=Ks(t.target,ir.columnHeader).getAttribute("data-field"),i=e.current.getColumn(l);o.debug(`Start Resize on col ${i.field}`),e.current.publishEvent("columnResizeStart",{field:l},t),f(i,r,n.clientX);const a=$(t.currentTarget);a.addEventListener("touchmove",w),a.addEventListener("touchend",b)})),v=g.useCallback((()=>{const t=$(e.current.rootElementRef.current);t.body.style.removeProperty("cursor"),t.removeEventListener("mousemove",h),t.removeEventListener("mouseup",m),t.removeEventListener("touchmove",w),t.removeEventListener("touchend",b),setTimeout((()=>{t.removeEventListener("click",kf,!0)}),100),l.columnHeaderElement&&(l.columnHeaderElement.style.pointerEvents="unset")}),[e,l,h,m,w,b]),x=g.useCallback((({field:t})=>{e.current.setState((e=>u({},e,{columnResize:u({},e.columnResize,{resizingColumnField:t})})))}),[e]),S=g.useCallback((()=>{e.current.setState((e=>u({},e,{columnResize:u({},e.columnResize,{resizingColumnField:""})})))}),[e]),R=y((({colDef:t},r)=>{if(0!==r.button)return;if(!r.currentTarget.classList.contains(ir["columnSeparator--resizable"]))return;r.preventDefault(),o.debug(`Start Resize on col ${t.field}`),e.current.publishEvent("columnResizeStart",{field:t.field},r),f(t,r.currentTarget,r.clientX);const n=$(e.current.rootElementRef.current);n.body.style.cursor="col-resize",l.previousMouseClickEvent=r.nativeEvent,n.addEventListener("mousemove",h),n.addEventListener("mouseup",m),n.addEventListener("click",kf,!0)})),I=y(((t,n)=>{if(r.disableAutosize)return;if(0!==n.button)return;const o=e.current.state.columns.lookup[t.field];!1!==o.resizable&&e.current.autosizeColumns(u({},r.autosizeOptions,{disableColumnVirtualization:!1,columns:[o.field]}))})),M=function(e){const t=g.useRef(void 0),r=()=>Vi(e),n=zt(e,r);return g.useEffect((()=>{t.current&&!1===n&&(t.current.resolve(),t.current=void 0)})),()=>{if(!t.current){if(!1===r())return Promise.resolve();t.current=function(){let e,t;const r=new Promise(((r,n)=>{e=r,t=n}));return r.resolve=e,r.reject=t,r}()}return t.current}}(e),k=g.useRef(!1),P=g.useCallback((async t=>{const n=e.current.rootElementRef?.current;if(!n)return;if(k.current)return;k.current=!0;const o=Sn(e),l=u({},La,t,{columns:t?.columns??o.orderedFields});l.columns=l.columns.filter((e=>!1!==o.columnVisibilityModel[e]));const i=l.columns.map((t=>e.current.state.columns.lookup[t]));try{!r.disableVirtualization&&l.disableColumnVirtualization&&(e.current.unstable_setColumnVirtualization(!1),await M());const t=function(e,t,r){const n={},o=e.current.rootElementRef.current;return o.classList.add(ir.autosizing),r.forEach((r=>{const o=function(e,t){const r=e.virtualScrollerRef.current;return Array.from(r.querySelectorAll(`:scope > div > div > div > [data-field="${_s(t)}"][role="gridcell"]`))}(e.current,r.field),l=o.map((e=>e.getBoundingClientRect().width??0)),i=t.includeOutliers?l:function(e,t){if(e.length<4)return e;const r=e.slice();r.sort(((e,t)=>e-t));const n=r[Math.floor(.25*r.length)],o=r[Math.floor(.75*r.length)-1],l=o-n,i=l<5?5:l*t;return r.filter((e=>e>n-i&&e<o+i))}(l,t.outliersFactor);if(t.includeHeaders){const t=(a=e.current,s=r.field,a.columnHeadersContainerRef.current.querySelector(`:scope > div > [data-field="${_s(s)}"][role="columnheader"]`));if(t){const e=t.querySelector(`.${ir.columnHeaderTitle}`),r=t.querySelector(`.${ir.columnHeaderTitleContainerContent}`),n=t.querySelector(`.${ir.iconButtonContainer}`),o=t.querySelector(`.${ir.menuIcon}`),l=e??r,a=window.getComputedStyle(t,null),s=parseInt(a.paddingLeft,10)+parseInt(a.paddingRight,10),c=l.scrollWidth+1+s+(n?.clientWidth??0)+(o?.clientWidth??0);i.push(c)}}var a,s;const c=r.minWidth!==-1/0&&void 0!==r.minWidth,u=r.maxWidth!==1/0&&void 0!==r.maxWidth,d=c?r.minWidth:0,p=u?r.maxWidth:1/0,f=0===i.length?0:Math.max(...i);n[r.field]=Zr(f,d,p)})),o.classList.remove(ir.autosizing),n}(e,l,i),n=i.map((e=>u({},e,{width:t[e.field],computedWidth:t[e.field],flex:0})));if(l.expand){const r=o.orderedFields.map((e=>o.lookup[e])).filter((e=>!1!==o.columnVisibilityModel[e.field])).reduce(((e,r)=>e+(t[r.field]??r.computedWidth??r.width)),0),l=e.current.getRootDimensions().viewportInnerSize.width-r;if(l>0){const e=l/(n.length||1);n.forEach((t=>{t.width+=e,t.computedWidth+=e}))}}e.current.updateColumns(n),n.forEach(((t,r)=>{if(t.width!==i[r].width){const r=t.width;e.current.publishEvent("columnWidthChange",{element:e.current.getColumnHeaderElement(t.field),colDef:t,width:r})}}))}finally{r.disableVirtualization||e.current.unstable_setColumnVirtualization(!0),k.current=!1}}),[e,M,r.disableVirtualization]);g.useEffect((()=>v),[v]),T((()=>{r.autosizeOnMount&&Promise.resolve().then((()=>{e.current.autosizeColumns(r.autosizeOptions)}))})),Oi(e,(()=>e.current.columnHeadersContainerRef?.current),"touchstart",C,{passive:!0}),Ti(e,{autosizeColumns:P},"public"),dr(e,"columnResizeStop",S),dr(e,"columnResizeStart",x),dr(e,"columnSeparatorMouseDown",R),dr(e,"columnSeparatorDoubleClick",I),fr(e,"columnResize",r.onColumnResize),fr(e,"columnWidthChange",r.onColumnWidthChange)})(e,n),((e,t)=>{((e,t)=>{const r=Di(e,"useGridPaginationMeta"),n=zt(e,Ml);e.current.registerControlState({stateId:"paginationMeta",propModel:t.paginationMeta,propOnChange:t.onPaginationMetaChange,stateSelector:Ml,changeEvent:"paginationMetaChange"});const o=g.useCallback((t=>{n!==t&&(r.debug("Setting 'paginationMeta' to",t),e.current.setState((e=>u({},e,{pagination:u({},e.pagination,{meta:t})}))))}),[e,r,n]);Ti(e,{setPaginationMeta:o},"public");const l=g.useCallback(((r,n)=>{const o=Ml(e);return n.exportOnlyDirtyModels&&null==t.paginationMeta&&null==t.initialState?.pagination?.meta?r:u({},r,{pagination:u({},r.pagination,{meta:o})})}),[e,t.paginationMeta,t.initialState?.pagination?.meta]),i=g.useCallback(((t,r)=>{const n=r.stateToRestore.pagination?.meta?r.stateToRestore.pagination.meta:Ml(e);return e.current.setState((e=>u({},e,{pagination:u({},e.pagination,{meta:n})}))),t}),[e]);wp(e,"exportState",l),wp(e,"restoreState",i),g.useEffect((()=>{t.paginationMeta&&e.current.setPaginationMeta(t.paginationMeta)}),[e,t.paginationMeta])})(e,t),((e,t)=>{const r=Di(e,"useGridPaginationModel"),n=zt(e,hr),o=g.useRef(Bo(e)),l=Math.floor(t.rowHeight*n);e.current.registerControlState({stateId:"paginationModel",propModel:t.paginationModel,propOnChange:t.onPaginationModelChange,stateSelector:Rl,changeEvent:"paginationModelChange"});const i=g.useCallback((t=>{const n=Rl(e);t!==n.page&&(r.debug(`Setting page to ${t}`),e.current.setPaginationModel({page:t,pageSize:n.pageSize}))}),[e,r]),a=g.useCallback((t=>{const n=Rl(e);t!==n.pageSize&&(r.debug(`Setting page size to ${t}`),e.current.setPaginationModel({pageSize:t,page:n.page}))}),[e,r]),s=g.useCallback((n=>{const o=Rl(e);n!==o&&(r.debug("Setting 'paginationModel' to",n),e.current.setState((e=>u({},e,{pagination:u({},e.pagination,{paginationModel:jp(e.pagination,t.signature,n)})})),"setPaginationModel"))}),[e,r,t.signature]);Ti(e,{setPage:i,setPageSize:a,setPaginationModel:s},"public");const c=g.useCallback(((r,n)=>{const o=Rl(e);return!n.exportOnlyDirtyModels||null!=t.paginationModel||null!=t.initialState?.pagination?.paginationModel||0!==o.page&&o.pageSize!==(t.autoPageSize?0:100)?u({},r,{pagination:u({},r.pagination,{paginationModel:o})}):r}),[e,t.paginationModel,t.initialState?.pagination?.paginationModel,t.autoPageSize]),d=g.useCallback(((r,n)=>{const o=n.stateToRestore.pagination?.paginationModel?u({},vl(t.autoPageSize),n.stateToRestore.pagination?.paginationModel):Rl(e);return e.current.setState((e=>u({},e,{pagination:u({},e.pagination,{paginationModel:jp(e.pagination,t.signature,o)})})),"stateRestorePreProcessing"),r}),[e,t.autoPageSize,t.signature]);wp(e,"exportState",c),wp(e,"restoreState",d);const p=g.useCallback((()=>{if(!t.autoPageSize)return;const r=e.current.getRootDimensions(),n=Math.floor(r.viewportInnerSize.height/l);e.current.setPageSize(n)}),[e,t.autoPageSize,l]),f=g.useCallback((t=>{if(null==t)return;const r=Rl(e);if(0===r.page)return;const n=El(e);r.page>n-1&&e.current.setPage(Math.max(0,n-1))}),[e]),m=g.useCallback((()=>{0!==Rl(e).page&&e.current.setPage(0),0!==e.current.getScrollPosition().top&&e.current.scroll({top:0})}),[e]),h=g.useCallback((t=>{const r=u({},t,{items:Jo(e)});nd(r,o.current)||(o.current=r,m())}),[e,m]);dr(e,"viewportInnerSizeChange",p),dr(e,"paginationModelChange",(()=>{const t=Rl(e);e.current.virtualScrollerRef?.current&&e.current.scrollToIndexes({rowIndex:t.page*t.pageSize})})),dr(e,"rowCountChange",f),dr(e,"sortModelChange",m),dr(e,"filterModelChange",h);const b=g.useRef(!0);g.useEffect((()=>{b.current?b.current=!1:t.pagination&&e.current.setState((e=>u({},e,{pagination:u({},e.pagination,{paginationModel:jp(e.pagination,t.signature,t.paginationModel)})})))}),[e,t.paginationModel,t.signature,t.pagination]),g.useEffect((()=>{e.current.setState((e=>{const r=!0===t.pagination;return e.pagination.paginationMode===t.paginationMode||e.pagination.enabled===r?e:u({},e,{pagination:u({},e.pagination,{paginationMode:t.paginationMode,enabled:!0===t.pagination})})}))}),[e,t.paginationMode,t.pagination]),g.useEffect(p,[p])})(e,t),((e,t)=>{const r=Di(e,"useGridRowCount"),n=zt(e,Qo),o=zt(e,Il),l=zt(e,Ml),i=zt(e,Rl),a=E((()=>Rl(e).pageSize));e.current.registerControlState({stateId:"paginationRowCount",propModel:t.rowCount,propOnChange:t.onRowCountChange,stateSelector:Il,changeEvent:"rowCountChange"});const s=g.useCallback((t=>{o!==t&&(r.debug("Setting 'rowCount' to",t),e.current.setState((e=>u({},e,{pagination:u({},e.pagination,{rowCount:t})}))))}),[e,r,o]);Ti(e,{setRowCount:s},"public");const c=g.useCallback(((r,n)=>{const o=Il(e);return n.exportOnlyDirtyModels&&null==t.rowCount&&null==t.initialState?.pagination?.rowCount?r:u({},r,{pagination:u({},r.pagination,{rowCount:o})})}),[e,t.rowCount,t.initialState?.pagination?.rowCount]),d=g.useCallback(((t,r)=>{const n=r.stateToRestore.pagination?.rowCount?r.stateToRestore.pagination.rowCount:Il(e);return e.current.setState((e=>u({},e,{pagination:u({},e.pagination,{rowCount:n})}))),t}),[e]);wp(e,"exportState",c),wp(e,"restoreState",d);const p=g.useCallback((r=>{"client"!==t.paginationMode&&a.current&&r.pageSize!==a.current&&(a.current=r.pageSize,-1===o&&e.current.setPage(0))}),[t.paginationMode,a,o,e]);dr(e,"paginationModelChange",p),g.useEffect((()=>{"client"===t.paginationMode?e.current.setRowCount(n):null!=t.rowCount&&e.current.setRowCount(t.rowCount)}),[e,t.paginationMode,n,t.rowCount]);const f=!1===l.hasNextPage;g.useEffect((()=>{f&&-1===o&&e.current.setRowCount(i.pageSize*i.page+n)}),[e,n,f,o,i])})(e,t)})(e,n),((e,t)=>{const{getRowHeight:r,getRowSpacing:n,getEstimatedRowHeight:o}=t,l=e.current.caches.rowsMeta.heights,i=g.useRef(-1),a=g.useRef(!1),s=g.useRef(!1),c=zt(e,hr),p=Li(e),f=zt(e,Ar),m=zt(e,qt),h=g.useCallback((t=>{const l=Kt(e).rowHeight,i=e.current.getRowHeightEntry(t.id);if(r){const e=r(u({},t,{densityFactor:c}));if("auto"===e){if(i.needsFirstMeasurement){const e=o?o(u({},t,{densityFactor:c})):l;i.content=e??l}a.current=!0,i.autoHeight=!0}else i.content=yo(e,l),i.needsFirstMeasurement=!1,i.autoHeight=!1}else i.content=l,i.needsFirstMeasurement=!1;if(n){const r=e.current.getRowIndexRelativeToVisibleRows(t.id),o=n(u({},t,{isFirstVisible:0===r,isLastVisible:r===p.rows.length-1,indexRelativeToCurrentPage:r}));i.spacingTop=o.top??0,i.spacingBottom=o.bottom??0}else i.spacingTop=0,i.spacingBottom=0;return e.current.unstable_applyPipeProcessors("rowHeight",i,t),i}),[e,p.rows,r,o,m,n,c]),b=g.useCallback((()=>{a.current=!1;const t=f.top.reduce(((e,t)=>{const r=h(t);return e+r.content+r.spacingTop+r.spacingBottom+r.detail}),0),r=f.bottom.reduce(((e,t)=>{const r=h(t);return e+r.content+r.spacingTop+r.spacingBottom+r.detail}),0),n=[],o=p.rows.reduce(((e,t)=>{n.push(e);const r=h(t);return e+(r.content+r.spacingTop+r.spacingBottom+r.detail)}),0);a.current||(i.current=1/0);const l=t!==e.current.state.rowsMeta.pinnedTopRowsTotalHeight||r!==e.current.state.rowsMeta.pinnedBottomRowsTotalHeight||o!==e.current.state.rowsMeta.currentPageTotalHeight,c={currentPageTotalHeight:o,positions:n,pinnedTopRowsTotalHeight:t,pinnedBottomRowsTotalHeight:r};e.current.setState((e=>u({},e,{rowsMeta:c}))),l&&e.current.updateDimensions(),s.current=!0}),[e,f,p.rows,h]),w=E((()=>new mf((t=>{for(let r=0;r<t.length;r+=1){const n=t[r],o=n.borderBoxSize&&n.borderBoxSize.length>0?n.borderBoxSize[0].blockSize:n.contentRect.height,l=n.target.__mui_id,i=Ji(e)?.id;if(i===l&&0===o)return;e.current.unstable_storeRowHeightMeasurement(l,o)}s.current||e.current.requestPipeProcessorsApplication("rowHeight")})))).current;Cp(e,"rowHeight",b),d((()=>{b()}),[b]);const C={unstable_getRowHeight:e=>l.get(e)?.content??m,unstable_setLastMeasuredRowIndex:e=>{a.current&&e>i.current&&(i.current=e)},unstable_storeRowHeightMeasurement:(t,r)=>{const n=e.current.getRowHeightEntry(t),o=n.content!==r;n.needsFirstMeasurement=!1,n.content=r,s.current&&=!o},resetRowHeights:()=>{l.clear(),b()}},v={hydrateRowsMeta:b,observeRowHeight:(e,t)=>(e.__mui_id=t,w.observe(e),()=>w.unobserve(e)),rowHasAutoHeight:e=>l.get(e)?.autoHeight??!1,getRowHeightEntry:e=>{let t=l.get(e);return void 0===t&&(t={content:m,spacingTop:0,spacingBottom:0,detail:0,autoHeight:!1,needsFirstMeasurement:!0},l.set(e,t)),t},getLastMeasuredRowIndex:()=>i.current};Ti(e,C,"public"),Ti(e,v,"private")})(e,n),((e,r)=>{const n=t(),o=Di(e,"useGridScroll"),l=e.current.columnHeadersContainerRef,i=e.current.virtualScrollerRef,a=zt(e,Wo),s=g.useCallback((t=>{const n=Kt(e),l=kr(e),s=r.listView?[Yi(e)]:En(e);if(null!=t.rowIndex&&0===l||0===s.length)return!1;o.debug(`Scrolling to cell at row ${t.rowIndex}, col: ${t.colIndex} `);let c={};if(void 0!==t.colIndex){const r=Dn(e);let o;if(void 0!==t.rowIndex){const r=a[t.rowIndex]?.id,n=e.current.unstable_getCellColSpanInfo(r,t.colIndex);n&&!n.spannedByColSpan&&(o=n.cellProps.width)}void 0===o&&(o=s[t.colIndex].computedWidth),c.left=lf({containerSize:n.viewportOuterSize.width,scrollPosition:Math.abs(i.current.scrollLeft),elementSize:o,elementOffset:r[t.colIndex]})}if(void 0!==t.rowIndex){const o=Ai(e),l=kl(e),a=Pl(e),s=r.pagination?t.rowIndex-l*a:t.rowIndex,u=o.positions[s+1]?o.positions[s+1]-o.positions[s]:o.currentPageTotalHeight-o.positions[s];c.top=lf({containerSize:n.viewportInnerSize.height,scrollPosition:i.current.scrollTop,elementSize:u,elementOffset:o.positions[s]})}return c=e.current.unstable_applyPipeProcessors("scrollToIndexes",c,t),(void 0!==typeof c.left||void 0!==typeof c.top)&&(e.current.scroll(c),!0)}),[o,e,i,r.pagination,a,r.listView]),c=g.useCallback((e=>{if(i.current&&void 0!==e.left&&l.current){const t=n?-1:1;l.current.scrollLeft=e.left,i.current.scrollLeft=t*e.left,o.debug(`Scrolling left: ${e.left}`)}i.current&&void 0!==e.top&&(i.current.scrollTop=e.top,o.debug(`Scrolling top: ${e.top}`)),o.debug("Scrolling, updating container, and viewport")}),[i,n,l,o]),u=g.useCallback((()=>i?.current?{top:i.current.scrollTop,left:i.current.scrollLeft}:{top:0,left:0}),[i]);Ti(e,{scroll:c,scrollToIndexes:s,getScrollPosition:u},"public")})(e,n),(e=>{const t=Di(e,"useGridColumnMenu"),r=g.useCallback((r=>{const n=Ca(e),o=r;(!0!==n.open||o!==n.field)&&(e.current.setState((e=>e.columnMenu.open&&e.columnMenu.field===r?e:(t.debug("Opening Column Menu"),u({},e,{columnMenu:{open:!0,field:r}})))),e.current.hidePreferences())}),[e,t]),n=g.useCallback((()=>{const r=Ca(e);if(r.field){const t=In(e),n=kn(e),o=Rn(e);let l=r.field;if(t[l]||(l=o[0]),!1===n[l]){const e=o.filter((e=>e===l||!1!==n[e])),t=e.indexOf(l);l=e[t+1]||e[t-1]}e.current.setColumnHeaderFocus(l)}const n={open:!1,field:void 0};(n.open!==r.open||n.field!==r.field)&&e.current.setState((e=>(t.debug("Hiding Column Menu"),u({},e,{columnMenu:n}))))}),[e,t]),o=g.useCallback((o=>{t.debug("Toggle Column Menu");const l=Ca(e);l.open&&l.field===o?n():r(o)}),[e,t,r,n]);Ti(e,{showColumnMenu:r,hideColumnMenu:n,toggleColumnMenu:o},"public"),dr(e,"columnResizeStart",n),dr(e,"virtualScrollerWheel",e.current.hideColumnMenu),dr(e,"virtualScrollerTouchMove",e.current.hideColumnMenu)})(e),((e,t)=>{const n=Di(e,"useGridCsvExport"),o=t.ignoreValueFormatterDuringExport,l=("object"==typeof o?o?.csvExport:o)||!1,i=g.useCallback(((t={})=>(n.debug("Get data as CSV"),function(e){const{columns:t,rowIds:r,csvOptions:n,ignoreValueFormatter:o,apiRef:l}=e,i=r.reduce(((e,r)=>`${e}${(({id:e,columns:t,getCellParams:r,csvOptions:n,ignoreValueFormatter:o})=>{const l=new Pp({csvOptions:n});return t.forEach((t=>{const i=r(e,t.field);l.addValue(kp(i,{ignoreValueFormatter:o,csvOptions:n}))})),l.getRowString()})({id:r,columns:t,getCellParams:l.current.getCellParams,ignoreValueFormatter:o,csvOptions:n})}\r\n`),"").trim();if(!n.includeHeaders)return i;const a=t.filter((e=>e.field!==zl.field)),s=[];if(n.includeColumnGroupsHeaders){const e=l.current.getAllGroupDetails();let t=0;const r=a.reduce(((e,r)=>{const n=l.current.getColumnGroupPath(r.field);return e[r.field]=n,t=Math.max(t,n.length),e}),{});for(let o=0;o<t;o+=1){const t=new Pp({csvOptions:n,sanitizeCellValue:Mp});s.push(t),a.forEach((n=>{const l=(r[n.field]||[])[o],i=e[l];t.addValue(i?i.headerName||i.groupId:"")}))}}const c=new Pp({csvOptions:n,sanitizeCellValue:Mp});return a.forEach((e=>{c.addValue(e.headerName||e.field)})),s.push(c),`${s.map((e=>e.getRowString())).join("\r\n")}\r\n${i}`.trim()}({columns:Op({apiRef:e,options:t}),rowIds:(t.getRowsToExport??$p)({apiRef:e}),csvOptions:{delimiter:t.delimiter||",",shouldAppendQuotes:t.shouldAppendQuotes??!0,includeHeaders:t.includeHeaders??!0,includeColumnGroupsHeaders:t.includeColumnGroupsHeaders??!0,escapeFormulas:t.escapeFormulas??!0},ignoreValueFormatter:l,apiRef:e}))),[n,e,l]),a=g.useCallback((e=>{n.debug("Export data as CSV");const t=i(e);!function(e,t="csv",r=document.title||"untitled"){const n=`${r}.${t}`;if("download"in HTMLAnchorElement.prototype){const t=URL.createObjectURL(e),r=document.createElement("a");return r.href=t,r.download=n,r.click(),void setTimeout((()=>{URL.revokeObjectURL(t)}))}throw new Error("MUI X: exportAs not supported.")}(new Blob([e?.utf8WithBom?new Uint8Array([239,187,191]):"",t],{type:"text/csv"}),"csv",e?.fileName)}),[n,i]);Ti(e,{getDataAsCsv:i,exportDataAsCsv:a},"public");const s=g.useCallback(((e,t)=>t.csvOptions?.disableToolbarButton?e:[...e,{component:r.jsx(td,{options:t.csvOptions}),componentName:"csvExport"}]),[]);wp(e,"exportMenu",s)})(e,n),((e,t)=>{const n=null!==e.current.rootElementRef.current,o=Di(e,"useGridPrintExport"),l=g.useRef(null),i=g.useRef(null),a=g.useRef({}),s=g.useRef([]),c=g.useRef(null);g.useEffect((()=>{l.current=$(e.current.rootElementRef.current)}),[e,n]);const d=g.useCallback(((t,r,n)=>new Promise((o=>{const l=Op({apiRef:e,options:{fields:t,allColumns:r}}).map((e=>e.field)),i=Mn(e),a={};i.forEach((e=>{a[e.field]=l.includes(e.field)})),n&&(a[zl.field]=!0),e.current.setColumnVisibilityModel(a),o()}))),[e]),p=g.useCallback((t=>{const r=t({apiRef:e}).reduce(((t,r)=>{const n=e.current.getRow(r);return n[uo]||t.push(n),t}),[]);e.current.setRows(r)}),[e]),f=g.useCallback(((r,n)=>{const o=u({copyStyles:!0,hideToolbar:!1,hideFooter:!1,includeCheckboxes:!1},n),i=r.contentDocument;if(!i)return;const a=Ai(e),s=e.current.rootElementRef.current,c=s.cloneNode(!0);c.querySelector(`.${ir.main}`).style.overflow="visible",c.style.contain="size";let d=s.querySelector(`.${ir.toolbarContainer}`)?.offsetHeight||0,p=s.querySelector(`.${ir.footerContainer}`)?.offsetHeight||0;const f=c.querySelector(`.${ir.footerContainer}`);o.hideToolbar&&(c.querySelector(`.${ir.toolbarContainer}`)?.remove(),d=0),o.hideFooter&&f&&(f.remove(),p=0);const g=a.currentPageTotalHeight+Ri(e,t)+d+p;c.style.height=`${g}px`,c.style.boxSizing="content-box",!o.hideFooter&&f&&(f.style.position="absolute",f.style.width="100%",f.style.top=g-p+"px");const m=document.createElement("div");m.appendChild(c),i.body.style.marginTop="0px",i.body.innerHTML=m.innerHTML;const h="function"==typeof o.pageStyle?o.pageStyle():o.pageStyle;if("string"==typeof h){const e=i.createElement("style");e.appendChild(i.createTextNode(h)),i.head.appendChild(e)}o.bodyClassName&&i.body.classList.add(...o.bodyClassName.split(" "));let b=[];if(o.copyStyles){const e=s.getRootNode();b=function(e,t){const r=[],n=t.querySelectorAll("style, link[rel='stylesheet']");for(let o=0;o<n.length;o+=1){const t=n[o];if("STYLE"===t.tagName){const r=e.createElement(t.tagName),n=t.sheet;if(n){let t="";for(let e=0;e<n.cssRules.length;e+=1)"string"==typeof n.cssRules[e].cssText&&(t+=`${n.cssRules[e].cssText}\r\n`);r.appendChild(e.createTextNode(t)),e.head.appendChild(r)}}else if(t.getAttribute("href")){const n=e.createElement(t.tagName);for(let e=0;e<t.attributes.length;e+=1){const r=t.attributes[e];r&&n.setAttribute(r.nodeName,r.nodeValue||"")}r.push(new Promise((e=>{n.addEventListener("load",(()=>e()))}))),e.head.appendChild(n)}}return r}(i,"ShadowRoot"===e.constructor.name?e:l.current)}Promise.all(b).then((()=>{r.contentWindow.print()}))}),[e,l,t]),m=g.useCallback((t=>{l.current.body.removeChild(t),e.current.restoreState(i.current||{}),i.current?.columns?.columnVisibilityModel||e.current.setColumnVisibilityModel(a.current),e.current.setState((e=>u({},e,{virtualization:c.current}))),e.current.setRows(s.current),i.current=null,a.current={},s.current=[]}),[e]),h=g.useCallback((async r=>{if(o.debug("Export data as Print"),!e.current.rootElementRef.current)throw new Error("MUI X: No grid root element available.");if(i.current=e.current.exportState(),a.current=kn(e),s.current=e.current.getSortedRows().filter((e=>!e[uo])),t.pagination){const t={page:0,pageSize:Xo(e)};e.current.setState((e=>u({},e,{pagination:u({},e.pagination,{paginationModel:jp(e.pagination,"DataGridPro",t)})})))}c.current=e.current.state.virtualization,e.current.setState((e=>u({},e,{virtualization:u({},e.virtualization,{enabled:!1,enabledForColumns:!1})}))),await d(r?.fields,r?.allColumns,r?.includeCheckboxes),p(r?.getRowsToExport??$p),await new Promise((e=>{requestAnimationFrame((()=>{e()}))}));const n=function(e){const t=document.createElement("iframe");return t.style.position="absolute",t.style.width="0px",t.style.height="0px",t.title=e||document.title,t}(r?.fileName);n.onload=()=>{f(n,r),n.contentWindow.matchMedia("print").addEventListener("change",(e=>{!1===e.matches&&m(n)}))},l.current.body.appendChild(n)}),[t,o,e,f,m,d,p]);Ti(e,{exportDataAsPrint:h},"public");const b=g.useCallback(((e,t)=>t.printOptions?.disableToolbarButton?e:[...e,{component:r.jsx(rd,{options:t.printOptions}),componentName:"printExport"}]),[]);wp(e,"exportMenu",b)})(e,n),((e,t)=>{const r=t.ignoreValueFormatterDuringExport,n=("object"==typeof r?r?.clipboardExport:r)||!1,o=t.clipboardCopyCellDelimiter,l=g.useCallback((t=>{if(!function(e){return(e.ctrlKey||e.metaKey)&&"C"===String.fromCharCode(e.keyCode)&&!e.shiftKey&&!e.altKey}(t))return;if(r=t.target,window.getSelection()?.toString()||r&&(r.selectionEnd||0)-(r.selectionStart||0)>0)return;var r;let l="";if(rl(e)>0)l=e.current.getDataAsCsv({includeHeaders:!1,delimiter:o,shouldAppendQuotes:!1,escapeFormulas:!1});else{const t=pl(e);if(t){const r=e.current.getCellParams(t.id,t.field);l=kp(r,{csvOptions:{delimiter:o,shouldAppendQuotes:!1,escapeFormulas:!1},ignoreValueFormatter:n})}}var i;l=e.current.unstable_applyPipeProcessors("clipboardCopy",l),l&&(i=l,navigator.clipboard?navigator.clipboard.writeText(i).catch((()=>{Ep(i)})):Ep(i),e.current.publishEvent("clipboardCopy",l))}),[e,n,o]);Oi(e,(()=>e.current.rootElementRef.current),"keydown",l),fr(e,"clipboardCopy",t.onClipboardCopy)})(e,n),function(e,t){const r=Di(e,"useResizeContainer"),n=g.useRef(!1),o=g.useRef(sf),l=zt(e,Tn),i=zt(e,hr),a=zt(e,df),s=g.useRef(!0),{rowHeight:c,headerHeight:p,groupHeaderHeight:f,headerFilterHeight:m,headersTotalHeight:h,leftPinnedWidth:b,rightPinnedWidth:w}=ff(t,e,i,l),C=g.useCallback((()=>Kt(e)),[e]),v=g.useCallback((t=>{e.current.setState((e=>u({},e,{dimensions:t}))),e.current.rootElementRef.current&&pf(e.current.rootElementRef.current,Kt(e))}),[e]),x=g.useCallback((()=>{const r=Kt(e);if(!r.isReady)return 0;const n=ji(e);if(t.getRowHeight){const t=Gi(e),r=t.lastRowIndex-t.firstRowIndex;return Math.min(r-1,n.rows.length)}const o=Math.floor(r.viewportInnerSize.height/c);return Math.min(o,n.rows.length)}),[e,t.getRowHeight,c]),S=g.useCallback((()=>{if(s.current)return;const r=function(e,t){if(void 0!==t)return t;if(null===e)return 0;const r=gf.get(e);if(void 0!==r)return r;const n=$(e).createElement("div");n.style.width="99px",n.style.height="99px",n.style.position="absolute",n.style.overflow="scroll",n.className="scrollDiv",e.appendChild(n);const o=n.offsetWidth-n.clientWidth;return e.removeChild(n),gf.set(e,o),o}(e.current.mainElementRef.current,t.scrollbarSize),n=Ai(e),l=h+n.pinnedTopRowsTotalHeight,i=n.pinnedBottomRowsTotalHeight,u={width:a,height:Zi(n.currentPageTotalHeight,1)};let d,g,C=!1,y=!1;if(t.autoHeight)y=!1,C=Math.round(a)>Math.round(o.current.width),d={width:o.current.width,height:l+i+u.height},g={width:Math.max(0,d.width-(y?r:0)),height:Math.max(0,d.height-(C?r:0))};else{d={width:o.current.width,height:o.current.height},g={width:Math.max(0,d.width),height:Math.max(0,d.height-l-i)};const e=u,t=g,n=e.width>t.width,a=e.height>t.height;(n||a)&&(y=a,C=e.width+(y?r:0)>t.width,C&&(y=e.height+r>t.height)),y&&(g.width-=r),C&&(g.height-=r)}const x=Math.max(d.width,a+(y?r:0)),S={width:a,height:l+u.height+i},R={isReady:!0,root:o.current,viewportOuterSize:d,viewportInnerSize:g,contentSize:u,minimumSize:S,hasScrollX:C,hasScrollY:y,scrollbarSize:r,headerHeight:p,groupHeaderHeight:f,headerFilterHeight:m,rowWidth:x,rowHeight:c,columnsTotalWidth:a,leftPinnedWidth:b,rightPinnedWidth:w,headersTotalHeight:h,topContainerHeight:l,bottomContainerHeight:i},I=e.current.state.dimensions;var M,k;nd(I,R)||(v(R),M=R.viewportInnerSize,k=I.viewportInnerSize,(M.width!==k.width||M.height!==k.height)&&e.current.publishEvent("viewportInnerSizeChange",R.viewportInnerSize),e.current.updateRenderContext?.())}),[e,v,t.scrollbarSize,t.autoHeight,c,p,f,m,a,h,b,w]),R=y(S),I=g.useMemo((()=>t.resizeThrottleMs>0?function(e,t=166){let r,n;const o=()=>{r=void 0,e(...n)};function l(...e){n=e,void 0===r&&(r=setTimeout(o,t))}return l.clear=()=>{clearTimeout(r),r=void 0},l}((()=>{R(),e.current.publishEvent("debouncedResize",o.current)}),t.resizeThrottleMs):void 0),[e,t.resizeThrottleMs,R]);g.useEffect((()=>I?.clear),[I]);const M={getRootDimensions:C},k={updateDimensions:S,getViewportPageSize:x};d(S,[S]),Ti(e,M,"public"),Ti(e,k,"private");const P=g.useCallback((t=>{pf(t,Kt(e))}),[e]),E=g.useCallback((e=>{if(o.current=e,0!==e.height||n.current||t.autoHeight||ea||(r.error(["The parent DOM element of the Data Grid has an empty height.","Please make sure that this element has an intrinsic height.","The grid displays with a height of 0px.","","More details: https://mui.com/r/x-data-grid-no-dimensions."].join("\n")),n.current=!0),0!==e.width||n.current||ea||(r.error(["The parent DOM element of the Data Grid has an empty width.","Please make sure that this element has an intrinsic width.","The grid displays with a width of 0px.","","More details: https://mui.com/r/x-data-grid-no-dimensions."].join("\n")),n.current=!0),s.current||!I)return s.current=!1,void S();I()}),[S,t.autoHeight,I,r]);fr(e,"rootMount",P),fr(e,"resize",E),fr(e,"debouncedResize",t.onResize)}(e,n),function(e,t){fr(e,"columnHeaderClick",t.onColumnHeaderClick),fr(e,"columnHeaderContextMenu",t.onColumnHeaderContextMenu),fr(e,"columnHeaderDoubleClick",t.onColumnHeaderDoubleClick),fr(e,"columnHeaderOver",t.onColumnHeaderOver),fr(e,"columnHeaderOut",t.onColumnHeaderOut),fr(e,"columnHeaderEnter",t.onColumnHeaderEnter),fr(e,"columnHeaderLeave",t.onColumnHeaderLeave),fr(e,"cellClick",t.onCellClick),fr(e,"cellDoubleClick",t.onCellDoubleClick),fr(e,"cellKeyDown",t.onCellKeyDown),fr(e,"preferencePanelClose",t.onPreferencePanelClose),fr(e,"preferencePanelOpen",t.onPreferencePanelOpen),fr(e,"menuOpen",t.onMenuOpen),fr(e,"menuClose",t.onMenuClose),fr(e,"rowDoubleClick",t.onRowDoubleClick),fr(e,"rowClick",t.onRowClick),fr(e,"stateChange",t.onStateChange)}(e,n),(e=>{const t=g.useCallback(((t={})=>e.current.unstable_applyPipeProcessors("exportState",{},t)),[e]),r=g.useCallback((t=>{e.current.unstable_applyPipeProcessors("restoreState",{callbacks:[]},{stateToRestore:t}).callbacks.forEach((e=>{e()}))}),[e]);Ti(e,{exportState:t,restoreState:r},"public")})(e),function(e,t){const r=r=>{e.current.setState((e=>u({},e,{virtualization:u({},e.virtualization,{enabled:r,enabledForColumns:r,enabledForRows:r&&!t.autoHeight})})))},n={unstable_setVirtualization:r,unstable_setColumnVirtualization:t=>{e.current.setState((e=>u({},e,{virtualization:u({},e.virtualization,{enabledForColumns:t})})))}};Ti(e,n,"public"),g.useEffect((()=>{r(!t.disableVirtualization)}),[t.disableVirtualization,t.autoHeight])}(e,n),function(e,t){const r=()=>{e.current.setState((t=>t.listViewColumn?u({},t,{listViewColumn:u({},t.listViewColumn,{computedWidth:Bf(e)})}):t))},n=g.useRef(null);dr(e,"viewportInnerSizeChange",(e=>{n.current!==e.width&&(n.current=e.width,r())})),dr(e,"columnVisibilityModelChange",r),d((()=>{const r=t.listViewColumn;r&&e.current.setState((t=>u({},t,{listViewColumn:u({},r,{computedWidth:Bf(e)})})))}),[e,t.listViewColumn]),g.useEffect((()=>{t.listView&&t.listViewColumn}),[t.listView,t.listViewColumn])}(e,n),((e,t)=>{const{api:r,strategyProcessor:n,events:o,setStrategyAvailability:l}=((e,t,r={})=>{const n=g.useCallback((()=>{e.current.setStrategyAvailability(vp.DataSource,Nf.Default,t.dataSource?()=>!0:()=>!1)}),[e,t.dataSource]),[o,l]=g.useState(!1),i=zt(e,Rl),a=g.useRef(0),s=t.onDataSourceError,c=E((()=>{const e=t.pageSizeOptions.map((e=>"number"==typeof e?e:e.value)).sort(((e,t)=>e-t)),r=Math.min(i.pageSize,e[0]);return new Gf(r)})).current,[d,p]=g.useState((()=>Uf(t.dataSourceCache,r.cacheOptions))),f=g.useCallback((async(n,l)=>{const i=t.dataSource?.getRows;if(!i)return;if(n&&n!==co&&"DataGrid"!==t.signature)return void r.fetchRowChildren?.([n]);r.clearDataSourceState?.();const p=u({},Vf(e),e.current.unstable_applyPipeProcessors("getRowsParams",{}),l),f=c.getCacheKeys(p).map((e=>d.get(e)));if(f.every((e=>void 0!==e)))return void e.current.applyStrategyProcessor("dataSourceRowsUpdate",{response:Gf.mergeResponses(f),fetchParams:p});(o||0===e.current.getRowsCount())&&e.current.setLoading(!0);const g=a.current+1;a.current=g;try{const t=await i(p);c.splitResponse(p,t).forEach(((e,t)=>d.set(t,e))),a.current===g&&e.current.applyStrategyProcessor("dataSourceRowsUpdate",{response:t,fetchParams:p})}catch(m){a.current===g&&(e.current.applyStrategyProcessor("dataSourceRowsUpdate",{error:m,fetchParams:p}),"function"==typeof s&&s(new Ka({message:m?.message,params:p,cause:m})))}finally{o&&a.current===g&&e.current.setLoading(!1)}}),[c,d,e,o,t.dataSource?.getRows,s,r,t.signature]),m=g.useCallback((()=>{l(e.current.getActiveStrategy(vp.DataSource)===Nf.Default)}),[e]),h=g.useCallback((t=>{if("error"in t)return void e.current.setRows([]);const{response:r}=t;void 0!==r.rowCount&&e.current.setRowCount(r.rowCount),e.current.setRows(r.rows),e.current.unstable_applyPipeProcessors("processDataSourceRows",{params:t.fetchParams,response:r},!0)}),[e]),b=t.dataSource?.updateRow,w=r.handleEditRow,C=g.useCallback((async t=>{if(b)try{const r=await b(t);return"function"==typeof w?(w(t,r),r):(e.current.updateNestedRows([r],[]),r&&!nd(r,t.previousRow)&&e.current.dataSource.cache.clear(),r)}catch(r){throw"function"==typeof s&&s(new _a({message:r?.message,params:t,cause:r})),r}}),[e,b,s,w]),v={dataSource:{fetchRows:f,cache:d,editRow:C}},y=g.useMemo((()=>j(f,0)),[f]),x=g.useRef(!0);return g.useEffect((()=>{if(x.current)return void(x.current=!1);if(void 0===t.dataSourceCache)return;const e=Uf(t.dataSourceCache,r.cacheOptions);p((t=>t!==e?e:t))}),[t.dataSourceCache,r.cacheOptions]),g.useEffect((()=>{t.dataSource&&(e.current.dataSource.cache.clear(),e.current.dataSource.fetchRows())}),[e,t.dataSource]),{api:{public:v},debouncedFetchRows:y,strategyProcessor:{strategyName:Nf.Default,group:"dataSourceRowsUpdate",processor:h},setStrategyAvailability:n,cacheChunkManager:c,cache:d,events:{strategyAvailabilityChange:m,sortModelChange:rn(o,(()=>y())),filterModelChange:rn(o,(()=>y())),paginationModelChange:rn(o,(()=>y()))}}})(e,t);Ti(e,r.public,"public"),yp(e,n.strategyName,n.group,n.processor),Object.entries(o).forEach((([t,r])=>{dr(e,t,r)})),g.useEffect((()=>{l()}),[l])})(e,n)};function _f(e){const{groupId:n,width:o,depth:l,maxDepth:i,fields:a,height:s,colIndex:c,hasFocus:d,tabIndex:p,isLastColumn:f,pinnedPosition:m,pinnedOffset:h}=e,b=pt(),w=t(),C=g.useRef(null),v=ut(),y=zt(v,hi),x=n?y[n]:{},{headerName:S=n??"",description:R="",headerAlign:M}=x;let k;const E=n&&y[n]?.renderHeaderGroup,F=g.useMemo((()=>({groupId:n,headerName:S,description:R,depth:l,maxDepth:i,fields:a,colIndex:c,isLastColumn:f})),[n,S,R,l,i,a,c,f]);n&&E&&(k=E(F));const H=u({},e,{classes:b.classes,headerAlign:M,depth:l,isDragging:!1}),T=S??n,D=P(),O=null===n?`empty-group-cell-${D}`:n,$=(e=>{const{classes:t,headerAlign:r,isDragging:n,isLastColumn:o,showLeftBorder:l,showRightBorder:i,groupId:a,pinnedPosition:s}=e,c={root:["columnHeader","left"===r&&"columnHeader--alignLeft","center"===r&&"columnHeader--alignCenter","right"===r&&"columnHeader--alignRight",n&&"columnHeader--moving",i&&"columnHeader--withRightBorder",l&&"columnHeader--withLeftBorder","withBorderColor",null===a?"columnHeader--emptyGroup":"columnHeader--filledGroup",s===Ro.LEFT&&"columnHeader--pinnedLeft",s===Ro.RIGHT&&"columnHeader--pinnedRight",o&&"columnHeader--last"],draggableContainer:["columnHeaderDraggableContainer"],titleContainer:["columnHeaderTitleContainer","withBorderColor"],titleContainerContent:["columnHeaderTitleContainerContent"]};return I(c,lr,t)})(H);g.useLayoutEffect((()=>{if(d){const e=C.current.querySelector('[tabindex="0"]')||C.current;e?.focus()}}),[v,d]);const j=g.useCallback((e=>t=>{Xs(t)||v.current.publishEvent(e,F,t)}),[v,F]),L=g.useMemo((()=>({onKeyDown:j("columnGroupHeaderKeyDown"),onFocus:j("columnGroupHeaderFocus"),onBlur:j("columnGroupHeaderBlur")})),[j]),z="function"==typeof x.headerClassName?x.headerClassName(F):x.headerClassName,A=g.useMemo((()=>os(u({},e.style),w,m,h)),[m,h,e.style,w]);return r.jsx(uc,u({ref:C,classes:$,columnMenuOpen:!1,colIndex:c,height:s,isResizing:!1,sortDirection:null,hasFocus:!1,tabIndex:p,isDraggable:!1,headerComponent:k,headerClassName:z,description:R,elementId:O,width:o,columnMenuIconButton:null,columnTitleIconButtons:null,resizable:!1,label:T,"aria-colspan":a.length,"data-fields":`|-${a.join("-|-")}-|`,style:A},L))}const qf=R("div",{name:"MuiDataGrid",slot:"ColumnHeaderRow"})({display:"flex"}),Xf=["className"],Qf=we("div",{name:"MuiDataGrid",slot:"ColumnHeaders"})({display:"flex",flexDirection:"column",borderTopLeftRadius:"var(--unstable_DataGrid-radius)",borderTopRightRadius:"var(--unstable_DataGrid-radius)"}),Yf=gt((function(e,t){const{className:o}=e,l=F(e,Xf),i=pt(),a=(e=>{const{classes:t}=e;return I({root:["columnHeaders"]},lr,t)})(i);return r.jsx(Qf,u({className:n(a.root,o),ownerState:i},l,{role:"presentation",ref:t}))})),Jf=["className","visibleColumns","sortColumnLookup","filterColumnLookup","columnHeaderTabIndexState","columnGroupHeaderTabIndexState","columnHeaderFocus","columnGroupHeaderFocus","headerGroupingMaxDepth","columnMenuState","columnVisibility","columnGroupsHeaderStructure","hasOtherElementInTabSequence"],Zf=or(gt((function(e,t){const{visibleColumns:o,sortColumnLookup:l,filterColumnLookup:i,columnHeaderTabIndexState:a,columnGroupHeaderTabIndexState:s,columnHeaderFocus:c,columnGroupHeaderFocus:d,headerGroupingMaxDepth:p,columnMenuState:f,columnVisibility:m,columnGroupsHeaderStructure:h,hasOtherElementInTabSequence:b}=e,w=F(e,Jf),{getInnerProps:C,getColumnHeadersRow:v,getColumnGroupHeadersRows:y}=(e=>{const{visibleColumns:t,sortColumnLookup:o,filterColumnLookup:l,columnHeaderTabIndexState:i,columnGroupHeaderTabIndexState:a,columnHeaderFocus:s,columnGroupHeaderFocus:c,headerGroupingMaxDepth:d,columnMenuState:p,columnVisibility:f,columnGroupsHeaderStructure:m,hasOtherElementInTabSequence:h}=e,[b,w]=g.useState(""),[C,v]=g.useState(""),y=Fi(),x=pt(),S=zt(y,mi),R=zt(y,Dn),I=zt(y,Wi),M=zt(y,Tn),k=zt(y,In),P=ua(R,I,M.left.length),E=zt(y,_t),F=zt(y,Jt),H=zt(y,Zt),T=zt(y,er),D=zt(y,rr),O=g.useCallback((e=>v(e.field)),[]),$=g.useCallback((()=>v("")),[]),j=g.useCallback((e=>w(e.field)),[]),L=g.useCallback((()=>w("")),[]),z=g.useMemo((()=>M.left.length?{firstColumnIndex:0,lastColumnIndex:M.left.length}:null),[M.left.length]),A=g.useMemo((()=>M.right.length?{firstColumnIndex:t.length-M.right.length,lastColumnIndex:t.length}:null),[M.right.length,t.length]);dr(y,"columnResizeStart",O),dr(y,"columnResizeStop",$),dr(y,"columnHeaderDragStart",j),dr(y,"columnHeaderDragEnd",L);const B=e=>{const{renderContext:r=I}=e||{},n=r.firstColumnIndex,o=r.lastColumnIndex;return{renderedColumns:t.slice(n,o),firstColumnToRender:n,lastColumnToRender:o}},V=(e,t,o,l=!1)=>{const i=e?.position===Ro.RIGHT,a=void 0===e?.position,s=M.right.length>0&&i||0===M.right.length&&a,c=P-o;return r.jsxs(g.Fragment,{children:[a&&r.jsx("div",{role:"presentation",style:{width:c}}),t,a&&r.jsx("div",{role:"presentation",className:n(ir.filler,l&&ir["filler--borderBottom"])}),s&&r.jsx(Ud,{header:!0,pinnedRight:i,borderBottom:l,borderTop:!1})]})},N=(e,t={})=>{const{renderedColumns:n,firstColumnToRender:a}=B(e),c=[];for(let d=0;d<n.length;d+=1){const f=n[d],g=a+d,m=0===g,w=null!==i&&i.field===f.field||m&&!h?0:-1,v=null!==s&&s.field===f.field,y=p.open&&p.field===f.field,S=e?.position,I=Vd(S,f.computedWidth,g,R,E,D),k=S===Ro.RIGHT?n[d-1]:n[d+1],P=!!k&&null!==s&&s.field===k.field,T=g+1===R.length-M.right.length,O=d,$=n.length,j=Gd(S,O),L=Nd(S,O,$,x.showColumnVerticalBorder,F);c.push(r.jsx(dc,u({},o[f.field],{columnMenuOpen:y,filterItemsCounter:l[f.field]&&l[f.field].length,headerHeight:H,isDragging:f.field===b,colDef:f,colIndex:g,isResizing:C===f.field,isLast:g===R.length-1,hasFocus:v,tabIndex:w,pinnedPosition:S,pinnedOffset:I,isLastUnpinned:T,isSiblingFocused:P,showLeftBorder:j,showRightBorder:L},t),f.field))}return V(e,c,0)},G=({depth:e,params:n})=>{const o=B(n);if(0===o.renderedColumns.length)return null;const{firstColumnToRender:l,lastColumnToRender:i}=o,s=m[e],p=t[l].field,g=S[p]?.[e]??null,h=s.findIndex((({groupId:e,columnFields:t})=>e===g&&t.includes(p))),b=t[i-1].field,w=S[b]?.[e]??null,C=s.findIndex((({groupId:e,columnFields:t})=>e===w&&t.includes(b))),v=s.slice(h,C+1).map((e=>u({},e,{columnFields:e.columnFields.filter((e=>!1!==f[e]))}))).filter((e=>e.columnFields.length>0)),y=v[0].columnFields.indexOf(p),I=v[0].columnFields.slice(0,y).reduce(((e,t)=>e+(k[t].computedWidth??0)),0);let M=l;const P=v.map((({groupId:t,columnFields:o},l)=>{const i=null!==c&&c.depth===e&&o.includes(c.field),s=null!==a&&a.depth===e&&o.includes(a.field)?0:-1,u={width:o.reduce(((e,t)=>e+k[t].computedWidth),0),fields:o,colIndex:M},p=n.position,f=Vd(p,u.width,M,R,E,D);M+=o.length;let g=l;return p===Ro.LEFT&&(g=M-1),r.jsx(_f,{groupId:t,width:u.width,fields:u.fields,colIndex:u.colIndex,depth:e,isLastColumn:l===v.length-1,maxDepth:d,height:T,hasFocus:i,tabIndex:s,pinnedPosition:p,pinnedOffset:f,showLeftBorder:Gd(p,g),showRightBorder:Nd(p,g,v.length,x.showColumnVerticalBorder,F)},l)}));return V(n,P,I)};return{renderContext:I,leftRenderContext:z,rightRenderContext:A,pinnedColumns:M,visibleColumns:t,columnPositions:R,getFillers:V,getColumnHeadersRow:()=>r.jsxs(qf,{role:"row","aria-rowindex":d+1,ownerState:x,className:ir["row--borderBottom"],style:{height:H},children:[z&&N({position:Ro.LEFT,renderContext:z},{disableReorder:!0}),N({renderContext:I}),A&&N({position:Ro.RIGHT,renderContext:A},{disableReorder:!0,separatorSide:ic.Left})]}),getColumnsToRender:B,getColumnGroupHeadersRows:()=>{if(0===d)return null;const e=[];for(let t=0;t<d;t+=1)e.push(r.jsxs(qf,{role:"row","aria-rowindex":t+1,ownerState:x,style:{height:T},children:[z&&G({depth:t,params:{position:Ro.LEFT,renderContext:z,maxLastColumn:z.lastColumnIndex}}),G({depth:t,params:{renderContext:I}}),A&&G({depth:t,params:{position:Ro.RIGHT,renderContext:A,maxLastColumn:A.lastColumnIndex}})]},t));return e},getPinnedCellOffset:Vd,isDragging:!!b,getInnerProps:()=>({role:"rowgroup"})}})({visibleColumns:o,sortColumnLookup:l,filterColumnLookup:i,columnHeaderTabIndexState:a,columnGroupHeaderTabIndexState:s,columnHeaderFocus:c,columnGroupHeaderFocus:d,headerGroupingMaxDepth:p,columnMenuState:f,columnVisibility:m,columnGroupsHeaderStructure:h,hasOtherElementInTabSequence:b});return r.jsxs(Yf,u({},w,C(),{ref:t,children:[y(),v()]}))}))),eg=gt((function(e,t){const n=ut().current.getLocaleText("noResultsOverlayLabel");return r.jsx(js,u({},e,{ref:t,children:n}))})),tg=new TextEncoder;let rg=2048,ng=new ArrayBuffer(rg),og=new Uint8Array(ng),lg=new Int32Array(ng);function ig(e,t){return e<<t|e>>>32-t}function ag(e){return function(e){return`rgba(from ${e} r g b / 1)`}(e)}function sg(e){return`${e.fontWeight} ${e.fontSize} / ${e.lineHeight} ${e.fontFamily}`}const cg=["id","label","labelId","material","disabled","slotProps","onChange","onKeyDown","onOpen","onClose","size","style","fullWidth"],ug=["onRowsPerPageChange","material","disabled"],dg=["material"],pg=["autoFocus","label","fullWidth","slotProps","className","material"],fg=["material"],gg=["material"],mg=["material"],hg=["material"],bg=["material"],wg=["material"],Cg=["material"],vg=["material"],yg=["material","label","className"],xg=["material"],Sg=["inert","iconStart","iconEnd","children","material"],Rg=["slotProps","material"],Ig=["id","multiple","freeSolo","options","getOptionLabel","isOptionEqualToValue","value","onChange","label","placeholder","slotProps","material"],Mg=["key"],kg=["inputProps","InputProps","InputLabelProps"],Pg=["slotProps","material"],Eg=["ref","open","children","className","clickAwayTouchEvent","clickAwayMouseEvent","flip","focusTrap","onExited","onClickAway","onDidShow","onDidHide","id","target","transition","placement","material"],Fg=["native"],Hg=R(Y)((({theme:e})=>({[`&.${J.positionEnd} .${te.sizeSmall}`]:{marginRight:e.spacing(-.75)}}))),Tg=R(N,{shouldForwardProp:e=>"fullWidth"!==e})((({theme:e})=>({gap:e.spacing(.5),margin:0,overflow:"hidden",[`& .${G.label}`]:{fontSize:e.typography.pxToRem(14),overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},variants:[{props:{fullWidth:!0},style:{width:"100%"}}]}))),Dg=R(ge,{shouldForwardProp:e=>"density"!==e})((({theme:e})=>({variants:[{props:{density:"compact"},style:{padding:e.spacing(.5)}}]}))),Og=R(pe)({[`& .${fe.primary}`]:{overflowX:"clip",textOverflow:"ellipsis",maxWidth:"300px"}}),$g=gt((function(e,t){const{id:n,label:o,labelId:l,material:i,disabled:a,slotProps:s,onChange:c,onKeyDown:d,onOpen:p,onClose:f,size:g,style:m,fullWidth:h}=e,b=F(e,cg),w={PaperProps:{onKeyDown:d}};return f&&(w.onClose=f),r.jsxs(_,{size:g,fullWidth:h,style:m,disabled:a,ref:t,children:[r.jsx(q,{id:l,htmlFor:n,shrink:!0,variant:"outlined",children:o}),r.jsx(X,u({id:n,labelId:l,label:o,displayEmpty:!0,onChange:c},b,{variant:"outlined",notched:!0,inputProps:s?.htmlInput,onOpen:p,MenuProps:w,size:g},i))]})})),jg=R(st)((({theme:e})=>({[`& .${Ye.selectLabel}`]:{display:"none",[e.breakpoints.up("sm")]:{display:"block"}},[`& .${Ye.input}`]:{display:"none",[e.breakpoints.up("sm")]:{display:"inline-flex"}}}))),Lg=gt((function(e,t){const{onRowsPerPageChange:n,material:o,disabled:l}=e,i=F(e,ug),a=g.useMemo((()=>{if(l)return{backIconButtonProps:{disabled:!0},nextIconButtonProps:{disabled:!0}}}),[l]),s=ut(),c=pt(),{estimatedRowCount:d}=c;return r.jsx(jg,u({component:"div",onRowsPerPageChange:y((e=>{n?.(Number(e.target.value))})),labelRowsPerPage:s.current.getLocaleText("paginationRowsPerPage"),labelDisplayedRows:e=>s.current.getLocaleText("paginationDisplayedRows")(u({},e,{estimated:d})),getItemAriaLabel:s.current.getLocaleText("paginationItemAriaLabel")},a,i,o,{ref:t}))})),zg=gt((function(e,t){const{material:n}=e,o=F(e,dg);return r.jsx(ce,u({},o,n,{ref:t}))})),Ag=gt((function(e,t){const{autoFocus:o,label:l,fullWidth:i,slotProps:a,className:s,material:c}=e,d=F(e,pg),p=g.useRef(null),f=v(p,t),m=g.useRef(null);return g.useEffect((()=>{if(o){const e=p.current?.querySelector("input");e?.focus({preventScroll:!0})}else!1===o&&m.current&&m.current.stop({})}),[o]),l?r.jsx(Tg,{className:s,control:r.jsx(Dg,u({},d,c,{inputProps:a?.htmlInput,ref:f,touchRippleRef:m})),label:l,fullWidth:i}):r.jsx(Dg,u({},d,c,{className:n(s,c?.className),inputProps:a?.htmlInput,ref:f,touchRippleRef:m}))})),Bg=gt((function(e,t){const{material:n}=e,o=F(e,fg);return r.jsx(O,u({},o,n,{ref:t}))})),Vg=gt((function(e,t){const{material:n}=e,o=F(e,gg);return r.jsx(ae,u({},o,n,{ref:t}))})),Ng=gt((function(e,t){const{material:n}=e,o=F(e,mg);return r.jsx(De,u({},o,n,{ref:t}))})),Gg=gt((function(e,t){const{material:n}=e,o=F(e,hg);return r.jsx(D,u({},o,n,{ref:t}))})),Wg=gt((function(e,t){const{material:n}=e,o=F(e,bg);return r.jsx(se,u({},o,n,{ref:t}))})),Ug=gt((function(e,t){const{material:n}=e,o=F(e,wg);return r.jsx(ee,u({},o,n,{ref:t}))})),Kg=gt((function(e,t){const{material:n}=e,o=F(e,Cg);return r.jsx(Z,u({},o,n,{ref:t}))})),_g=gt((function(e,t){const{material:n}=e,o=F(e,vg);return r.jsx(Ne,u({},o,n,{ref:t}))})),qg=gt((function(e,t){const{material:n,label:o,className:l}=e,i=F(e,yg);return o?r.jsx(Tg,{className:l,control:r.jsx(V,u({},i,n,{ref:t})),label:o}):r.jsx(V,u({},i,n,{className:l,ref:t}))})),Xg=gt((function(e,t){const{material:n}=e,o=F(e,xg);return r.jsx(oe,u({},o,n,{ref:t}))}));function Qg(e,t=!0){if(!e)return;const{slotProps:n,material:o}=e,l=F(e,Pg);t&&(l.startAdornment&&(l.startAdornment=r.jsx(Hg,{position:"start",children:l.startAdornment})),l.endAdornment&&(l.endAdornment=r.jsx(Hg,{position:"end",children:l.endAdornment})));for(const r in o)Object.hasOwn(o,r)&&(l[r]=o[r]);return n?.htmlInput&&(l.inputProps?l.inputProps=u({},l.inputProps,n?.htmlInput):l.inputProps=n?.htmlInput),l}const Yg={"bottom-start":"top left","bottom-end":"top right"};function Jg(e,t){return function(e,t){return void 0===e.focusTrap?t:r.jsx(A,{open:!0,disableEnforceFocus:!0,disableAutoFocus:!0,children:r.jsx("div",{tabIndex:-1,children:t})})}(e,function(e,t){return void 0===e.onClickAway?t:r.jsx(ve,{onClickAway:e.onClickAway,touchEvent:e.clickAwayTouchEvent,mouseEvent:e.clickAwayMouseEvent,children:t})}(e,t))}const Zg={baseAutocomplete:function(e){const t=pt(),{id:n,multiple:o,freeSolo:l,options:i,getOptionLabel:a,isOptionEqualToValue:s,value:c,onChange:d,label:p,placeholder:f,slotProps:g,material:m}=e,h=F(e,Ig);return r.jsx(ue,u({id:n,multiple:o,freeSolo:l,options:i,getOptionLabel:a,isOptionEqualToValue:s,value:c,onChange:d,renderTags:(e,t)=>e.map(((e,n)=>{const o=t({index:n}),{key:l}=o,i=F(o,Mg);return r.jsx(se,u({variant:"outlined",size:"small",label:"string"==typeof e?e:a?.(e)},i),l)})),renderInput:e=>{const{inputProps:n,InputProps:o,InputLabelProps:l}=e,i=F(e,kg);return r.jsx(Q,u({},i,{label:p,placeholder:f,inputProps:n,InputProps:Qg(o,!1),InputLabelProps:u({shrink:!0},l)},g?.textField,t.slotProps?.baseTextField))}},h,m))},baseBadge:zg,baseCheckbox:Ag,baseChip:Wg,baseCircularProgress:Bg,baseDivider:Vg,baseInput:function(e){return r.jsx(U,u({},Qg(e)))},baseLinearProgress:Ng,baseMenuList:Xg,baseMenuItem:function(e){const{inert:t,iconStart:n,iconEnd:o,children:l,material:i}=e,a=F(e,Sg);return t&&(a.disableRipple=!0),g.createElement(W,u({},a,i),[n&&r.jsx(ne,{children:n},"1"),r.jsx(Og,{children:l},"2"),o&&r.jsx(ne,{children:o},"3")])},baseTextField:function(e){const{slotProps:t,material:n}=e,o=F(e,Rg);return r.jsx(Q,u({variant:"outlined"},o,n,{inputProps:t?.htmlInput,InputProps:Qg(t?.input),InputLabelProps:u({shrink:!0},t?.inputLabel)}))},baseButton:Gg,baseIconButton:Ug,baseTooltip:Kg,basePagination:Lg,basePopper:function(e){const{open:t,children:n,className:o,flip:l,onExited:i,onDidShow:a,onDidHide:s,id:c,target:d,transition:p,placement:f,material:m}=e,h=F(e,Eg),b=g.useMemo((()=>{const e=[{name:"preventOverflow",options:{padding:8}}];return l&&e.push({name:"flip",enabled:!0,options:{rootBoundary:"document"}}),(a||s)&&e.push({name:"isPlaced",enabled:!0,phase:"main",fn:()=>{a?.()},effect:()=>()=>{s?.()}}),e}),[l,a,s]);let w;if(p){const t=e=>t=>{e&&e(),i&&i(t)};w=o=>Jg(e,r.jsx(B,u({},o.TransitionProps,{style:{transformOrigin:Yg[o.placement]},onExited:t(o.TransitionProps?.onExited),children:r.jsx(be,{children:n})})))}else w=Jg(e,n);return r.jsx(z,u({id:c,className:o,open:t,anchorEl:d,transition:p,placement:f,modifiers:b},h,m,{children:w}))},baseSelect:$g,baseSelectOption:function(e){let{native:t}=e,n=F(e,Fg);return t?r.jsx("option",u({},n)):r.jsx(W,u({},n))},baseSkeleton:_g,baseSwitch:qg},em=u({},Zg,{booleanCellTrueIcon:Nc,booleanCellFalseIcon:Lc,columnMenuIcon:jc,openFilterButtonIcon:Pc,filterPanelDeleteIcon:Lc,columnFilteredIcon:Ec,columnSelectorIcon:Hc,columnSortedAscendingIcon:Rc,columnSortedDescendingIcon:Ic,columnResizeIcon:Tc,densityCompactIcon:Dc,densityStandardIcon:Oc,densityComfortableIcon:$c,exportIcon:qc,moreActionsIcon:Gc,treeDataCollapseIcon:kc,treeDataExpandIcon:Mc,groupingCriteriaCollapseIcon:kc,groupingCriteriaExpandIcon:Mc,detailPanelExpandIcon:zc,detailPanelCollapseIcon:Ac,rowReorderIcon:Vc,quickFilterIcon:Fc,quickFilterClearIcon:Kc,columnMenuHideIcon:Wc,columnMenuSortAscendingIcon:Rc,columnMenuSortDescendingIcon:Ic,columnMenuUnsortIcon:null,columnMenuFilterIcon:Ec,columnMenuManageColumnsIcon:Uc,columnMenuClearIcon:Kc,loadIcon:Bc,filterPanelAddIcon:zc,filterPanelRemoveAllIcon:_c,columnReorderIcon:Vc,menuItemCheckIcon:Nc}),tm=we("div")({position:"sticky",zIndex:40,bottom:"calc(var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize))"}),rm=u({},em,{cell:ss,skeletonCell:gs,columnHeaderFilterIconButton:function(e){return e.counter?r.jsx(xc,u({},e)):null},columnHeaderSortIcon:yc,columnMenu:cu,columnHeaders:Zf,detailPanels:function(e){return null},bottomContainer:function(e){const t=I({root:["bottomContainer"]},lr,{});return r.jsx(tm,u({},e,{className:n(t.root,ir["container--bottom"]),role:"presentation"}))},footer:Bd,footerRowCount:lp,toolbar:function(e){const{showQuickFilter:t=!0,quickFilterProps:n,csvOptions:o,printOptions:l,additionalItems:i,additionalExportMenuItems:a}=e,s=ut(),c=pt(),[d,p]=g.useState(!1),f=g.useRef(null),m=P(),h=P(),b=!o?.disableToolbarButton||!l?.disableToolbarButton||a,w=()=>p(!1);return r.jsxs(Vs,{children:[c.label&&r.jsx(jd,{children:c.label}),!c.disableColumnSelector&&r.jsx(c.slots.baseTooltip,{title:s.current.getLocaleText("toolbarColumns"),children:r.jsx(Id,{render:r.jsx(Gs,{}),children:r.jsx(c.slots.columnSelectorIcon,{fontSize:"small"})})}),!c.disableColumnFilter&&r.jsx(c.slots.baseTooltip,{title:s.current.getLocaleText("toolbarFilters"),children:r.jsx(Sd,{render:(e,t)=>r.jsx(Gs,u({},e,{color:t.filterCount>0?"primary":"default",children:r.jsx(c.slots.baseBadge,{badgeContent:t.filterCount,color:"primary",variant:"dot",children:r.jsx(c.slots.openFilterButtonIcon,{fontSize:"small"})})}))})}),i,b&&(!c.disableColumnFilter||!c.disableColumnSelector)&&r.jsx($d,{}),b&&r.jsxs(g.Fragment,{children:[r.jsx(c.slots.baseTooltip,{title:s.current.getLocaleText("toolbarExport"),children:r.jsx(Gs,{ref:f,id:h,"aria-controls":m,"aria-haspopup":"true","aria-expanded":d?"true":void 0,onClick:()=>p(!d),children:r.jsx(c.slots.exportIcon,{fontSize:"small"})})}),r.jsx(oo,{target:f.current,open:d,onClose:w,position:"bottom-end",children:r.jsxs(c.slots.baseMenuList,u({id:m,"aria-labelledby":h,autoFocusItem:!0},c.slotProps?.baseMenuList,{children:[!l?.disableToolbarButton&&r.jsx(Ed,{render:r.jsx(c.slots.baseMenuItem,u({},c.slotProps?.baseMenuItem)),options:l,onClick:w,children:s.current.getLocaleText("toolbarExportPrint")}),!o?.disableToolbarButton&&r.jsx(kd,{render:r.jsx(c.slots.baseMenuItem,u({},c.slotProps?.baseMenuItem)),options:o,onClick:w,children:s.current.getLocaleText("toolbarExportCSV")}),a?.(w)]}))})]}),t&&r.jsxs(g.Fragment,{children:[r.jsx($d,{}),r.jsx(yd,u({},n))]})]})},pinnedRows:function(e){return null},loadingOverlay:Zd,noResultsOverlay:eg,noRowsOverlay:ep,noColumnsOverlay:tp,pagination:function(){const e=ut(),t=pt(),n=zt(e,Rl),o=zt(e,Il),l=zt(e,El),{paginationMode:i,loading:a}=t,s=-1===o&&"server"===i&&a,c=g.useMemo((()=>Math.max(0,l-1)),[l]),u=g.useMemo((()=>-1===o||n.page<=c?n.page:c),[c,n.page,o]),d=g.useCallback((t=>{e.current.setPageSize(t)}),[e]),p=g.useCallback(((t,r)=>{e.current.setPage(r)}),[e]),f=(e=>{for(let r=0;r<t.pageSizeOptions.length;r+=1){const n=t.pageSizeOptions[r];if("number"==typeof n){if(n===e)return!0}else if(n.value===e)return!0}return!1})(n.pageSize)?t.pageSizeOptions:[];return r.jsx(rp,{as:t.slots.basePagination,count:o,page:u,rowsPerPageOptions:f,rowsPerPage:n.pageSize,onPageChange:p,onRowsPerPageChange:d,disabled:s})},filterPanel:Lu,columnsPanel:function(e){const t=pt();return r.jsx(pu,u({},e,{children:r.jsx(t.slots.columnsManagement,u({},t.slotProps?.columnsManagement))}))},columnsManagement:function(e){const t=ut(),n=g.useRef(null),o=zt(t,Pn),l=zt(t,kn),i=pt(),[a,s]=g.useState(""),c=(e=>{const{classes:t}=e;return I({root:["columnsManagement"],header:["columnsManagementHeader"],searchInput:["columnsManagementSearchInput"],footer:["columnsManagementFooter"],row:["columnsManagementRow"]},lr,t)})(i),d=zt(t,Mn),p=zt(t,ha),f=zt(t,wa),m=g.useMemo((()=>p?Array.from(f.values()):d),[p,f,d]),{sort:h,searchPredicate:b=zu,autoFocusSearchField:w=!0,disableShowHideToggle:C=!1,disableResetButton:v=!1,toggleAllMode:y="all",getTogglableColumns:x,searchInputProps:S}=e,R=g.useMemo((()=>((e,t)=>{const r=new Set(Object.keys(e).filter((t=>!1===e[t]))),n=new Set(Object.keys(t).filter((e=>!1===t[e])));if(r.size!==n.size)return!1;let o=!0;return r.forEach((e=>{n.has(e)||(o=!1)})),o})(l,o)),[l,o]),M=g.useMemo((()=>{switch(h){case"asc":return[...m].sort(((e,t)=>Wu.compare(e.headerName||e.field,t.headerName||t.field)));case"desc":return[...m].sort(((e,t)=>-Wu.compare(e.headerName||e.field,t.headerName||t.field)));default:return m}}),[m,h]),k=e=>{const{name:r}=e.target;t.current.setColumnVisibility(r,!1===l[r])},P=g.useMemo((()=>{const e=x?x(M):null,t=e?M.filter((({field:t})=>e.includes(t))):M;return a?t.filter((e=>b(e,a.toLowerCase()))):t}),[M,a,b,x]),E=g.useCallback((e=>{const r=kn(t),n=u({},r),o=x?x(m):null;return("filteredOnly"===y?P:m).forEach((t=>{t.hideable&&(null==o||o.includes(t.field))&&(e?delete n[t.field]:n[t.field]=!1)})),t.current.setColumnVisibilityModel(n)}),[t,m,x,y,P]),F=g.useCallback((e=>{s(e.target.value)}),[]),H=g.useMemo((()=>P.filter((e=>e.hideable))),[P]),T=g.useMemo((()=>H.every((e=>null==l[e.field]||!1!==l[e.field]))),[l,H]),D=g.useMemo((()=>H.every((e=>!1===l[e.field]))),[l,H]),O=g.useRef(null);g.useEffect((()=>{w?n.current?.focus():O.current&&"function"==typeof O.current.focus&&O.current.focus()}),[w]);let $=!1;const j=e=>!1===$&&!1!==e.hideable&&($=!0,!0),L=g.useCallback((()=>{s(""),n.current?.focus()}),[]);return r.jsxs(g.Fragment,{children:[r.jsx(_u,{className:c.header,ownerState:i,children:r.jsx(qu,u({as:i.slots.baseTextField,ownerState:i,placeholder:t.current.getLocaleText("columnsManagementSearchTitle"),inputRef:n,className:c.searchInput,value:a,onChange:F,size:"small",type:"search",slotProps:{input:{startAdornment:r.jsx(i.slots.quickFilterIcon,{fontSize:"small"}),endAdornment:r.jsx(i.slots.baseIconButton,u({size:"small","aria-label":t.current.getLocaleText("columnsManagementDeleteIconLabel"),style:a?{visibility:"visible"}:{visibility:"hidden"},tabIndex:-1,onClick:L,edge:"end"},i.slotProps?.baseIconButton,{children:r.jsx(i.slots.quickFilterClearIcon,{fontSize:"small"})}))},htmlInput:{"aria-label":t.current.getLocaleText("columnsManagementSearchTitle")}},autoComplete:"off",fullWidth:!0},i.slotProps?.baseTextField,S))}),r.jsx(Ku,{ownerState:i,children:r.jsxs(Uu,{className:c.root,ownerState:i,children:[P.map((e=>r.jsx(i.slots.baseCheckbox,u({className:c.row,disabled:!1===e.hideable||p,checked:!1!==l[e.field],onClick:k,name:e.field,inputRef:j(e)?O:void 0,label:e.headerName||e.field,density:"compact",fullWidth:!0},i.slotProps?.baseCheckbox),e.field))),0===P.length&&r.jsx(Qu,{ownerState:i,children:t.current.getLocaleText("columnsManagementNoColumns")})]})}),C&&v?null:r.jsxs(Xu,{ownerState:i,className:c.footer,children:[C?r.jsx("span",{}):r.jsx(i.slots.baseCheckbox,u({disabled:0===H.length||p,checked:T,indeterminate:!T&&!D,onClick:()=>E(!T),name:t.current.getLocaleText("columnsManagementShowHideAllText"),label:t.current.getLocaleText("columnsManagementShowHideAllText"),density:"compact"},i.slotProps?.baseCheckbox)),v?null:r.jsx(i.slots.baseButton,u({onClick:()=>t.current.setColumnVisibilityModel(o),disabled:R||p},i.slotProps?.baseButton,{children:t.current.getLocaleText("columnsManagementReset")}))]})]})},panel:bu,row:cp}),nm={disableMultipleColumnsFiltering:!0,disableMultipleColumnsSorting:!0,throttleRowsMs:void 0,hideFooterRowCount:!1,pagination:!0,checkboxSelectionVisibleOnly:!1,disableColumnReorder:!0,keepColumnPositionIfDraggedOutside:!1,signature:"DataGrid",listView:!1},om=rm;class lm{constructor(){this.maxListeners=20,this.warnOnce=!1,this.events={}}on(e,t,r={}){let n=this.events[e];n||(n={highPriority:new Map,regular:new Map},this.events[e]=n),r.isFirst?n.highPriority.set(t,!0):n.regular.set(t,!0)}removeListener(e,t){this.events[e]&&(this.events[e].regular.delete(t),this.events[e].highPriority.delete(t))}removeAllListeners(){this.events={}}emit(e,...t){const r=this.events[e];if(!r)return;const n=Array.from(r.highPriority.keys()),o=Array.from(r.regular.keys());for(let l=n.length-1;l>=0;l-=1){const e=n[l];r.highPriority.has(e)&&e.apply(this,t)}for(let l=0;l<o.length;l+=1){const e=o[l];r.regular.has(e)&&e.apply(this,t)}}once(e,t){const r=this;this.on(e,(function n(...o){r.removeListener(e,n),t.apply(r,o)}))}}class im{static create(e){return new im(e)}constructor(e){this.value=void 0,this.listeners=void 0,this.subscribe=e=>(this.listeners.add(e),()=>{this.listeners.delete(e)}),this.getSnapshot=()=>this.value,this.update=e=>{this.value=e,this.listeners.forEach((t=>t(e)))},this.value=e,this.listeners=new Set}}const am=Symbol("mui.api_private");let sm=0;const cm={hooks:{useCSSVariables:function(){const e=he();return g.useMemo((()=>{const t=function(e){const t=2*e.length;t>rg&&(rg=t+(4-t%4),ng=new ArrayBuffer(rg),og=new Uint8Array(ng),lg=new Int32Array(ng));const r=0|tg.encodeInto(e,og).written;let n=0,o=374761393+(0+r|0)|0;if(r<16)for(;(n+3|0)<r;n=n+4|0)o=Math.imul(0|ig(o+Math.imul(0|lg[n],3266489917)|0,17),668265263);else{let e=606290984,t=-2048144777,l=0,i=1640531535;for(;(n+15|0)<r;n=n+16|0)e=Math.imul(0|ig(e+Math.imul(0|lg[n+0|0],2246822519)|0,13),2654435761),t=Math.imul(0|ig(t+Math.imul(0|lg[n+4|0],2246822519)|0,13),2654435761),l=Math.imul(0|ig(l+Math.imul(0|lg[n+8|0],2246822519)|0,13),2654435761),i=Math.imul(0|ig(i+Math.imul(0|lg[n+12|0],2246822519)|0,13),2654435761);for(o=(((ig(e,1)|0+ig(t,7)|0)+ig(l,12)|0)+ig(i,18)|0)+r|0;(n+3|0)<r;n=n+4|0)o=Math.imul(0|ig(o+Math.imul(0|lg[n],3266489917)|0,17),668265263)}for(;n<r;n=n+1|0)o=Math.imul(0|ig(o+Math.imul(0|og[n],374761393)|0,11),2654435761);return o=Math.imul(o^o>>>15,2246822519),o=Math.imul(o^o>>>13,3266489917),((o^o>>>16)>>>0).toString()}(function(e){const t=new WeakSet;return JSON.stringify(e,((e,r)=>{if(null!==r&&"object"==typeof r){if(t.has(r))return null;t.add(r)}return r}))}(e)),r=function(e){const t=function(e){return e.vars?e.vars.palette.TableCell.border:"light"===e.palette.mode?l(c(e.palette.divider,1),.88):i(c(e.palette.divider,1),.68)}(e),r=e.palette.DataGrid,n=r?.bg??(e.vars||e).palette.background.default,o=r?.headerBg??n,a=r?.pinnedBg??n,s=e.vars?`rgba(${e.vars.palette.background.defaultChannel} / ${e.vars.palette.action.disabledOpacity})`:c(e.palette.background.default,e.palette.action.disabledOpacity),u="dark"===e.palette.mode?`color-mix(in srgb, ${(e.vars||e).palette.background.paper} 95%, #fff)`:(e.vars||e).palette.background.paper,d=e.vars?`rgb(${e.vars.palette.primary.mainChannel})`:e.palette.primary.main,p=function(e){return e.vars?e.vars.shape.borderRadius:"number"==typeof e.shape.borderRadius?`${e.shape.borderRadius}px`:e.shape.borderRadius}(e),f=e.vars?.font?.body2??sg(e.typography.body2),g=e.vars?.font?.caption??sg(e.typography.caption),m=e.vars?.font?.body1??sg(e.typography.body1),h=yr.keys;return{[h.spacingUnit]:e.vars?e.vars.spacing??e.spacing(1):e.spacing(1),[h.colors.border.base]:t,[h.colors.background.base]:n,[h.colors.background.overlay]:u,[h.colors.background.backdrop]:s,[h.colors.foreground.base]:(e.vars||e).palette.text.primary,[h.colors.foreground.muted]:(e.vars||e).palette.text.secondary,[h.colors.foreground.accent]:(e.vars||e).palette.primary.dark,[h.colors.foreground.disabled]:(e.vars||e).palette.text.disabled,[h.colors.foreground.error]:(e.vars||e).palette.error.dark,[h.colors.interactive.hover]:(e.vars||e).palette.action.hover,[h.colors.interactive.hoverOpacity]:(e.vars||e).palette.action.hoverOpacity,[h.colors.interactive.focus]:ag((e.vars||e).palette.primary.main),[h.colors.interactive.focusOpacity]:(e.vars||e).palette.action.focusOpacity,[h.colors.interactive.disabled]:ag((e.vars||e).palette.action.disabled),[h.colors.interactive.disabledOpacity]:(e.vars||e).palette.action.disabledOpacity,[h.colors.interactive.selected]:d,[h.colors.interactive.selectedOpacity]:(e.vars||e).palette.action.selectedOpacity,[h.header.background.base]:o,[h.cell.background.pinned]:a,[h.radius.base]:p,[h.typography.fontFamily.base]:e.typography.fontFamily,[h.typography.fontWeight.light]:e.typography.fontWeightLight,[h.typography.fontWeight.regular]:e.typography.fontWeightRegular,[h.typography.fontWeight.medium]:e.typography.fontWeightMedium,[h.typography.fontWeight.bold]:e.typography.fontWeightBold,[h.typography.font.body]:f,[h.typography.font.small]:g,[h.typography.font.large]:m,[h.transitions.easing.easeIn]:e.transitions.easing.easeIn,[h.transitions.easing.easeOut]:e.transitions.easing.easeOut,[h.transitions.easing.easeInOut]:e.transitions.easing.easeInOut,[h.transitions.duration.short]:`${e.transitions.duration.shorter}ms`,[h.transitions.duration.base]:`${e.transitions.duration.short}ms`,[h.transitions.duration.long]:`${e.transitions.duration.standard}ms`,[h.shadows.base]:(e.vars||e).shadows[2],[h.shadows.overlay]:(e.vars||e).shadows[8],[h.zIndex.panel]:(e.vars||e).zIndex.modal,[h.zIndex.menu]:(e.vars||e).zIndex.modal}}(e);return{id:t,variables:r}}),[e])},useGridAriaAttributes:()=>{const e=Fi(),t=pt(),r=zt(e,En),n=zt(e,Xo),o=zt(e,wi),l=zt(e,Br),i=t["aria-label"],a=t["aria-labelledby"];return{role:"grid","aria-label":i||a||!t.label?i:t.label,"aria-labelledby":a,"aria-colcount":r.length,"aria-rowcount":o+1+l+n,"aria-multiselectable":ll(t)}},useGridRowAriaAttributes:()=>{const e=Fi(),t=zt(e,wi);return g.useCallback(((r,n)=>{const o={},l=n+t+2;return o["aria-rowindex"]=l,e.current.isRowSelectable(r.id)&&(o["aria-selected"]=e.current.isRowSelected(r.id)),o}),[e,t])},useCellAggregationResult:()=>null}},um=gt((function(e,t){const n=(e=>{const t=he(),r=g.useMemo((()=>h({props:e,theme:t,name:"MuiDataGrid"})),[t,e]),n=g.useMemo((()=>u({},f,r.localeText)),[r.localeText]),o=g.useMemo((()=>function({defaultSlots:e,slots:t}){const r=t;if(!r||0===Object.keys(r).length)return e;const n=u({},e);return Object.keys(r).forEach((e=>{const t=e;void 0!==r[t]&&(n[t]=r[t])})),n}({defaultSlots:om,slots:r.slots})),[r.slots]),l=g.useMemo((()=>Object.keys(af).reduce(((e,t)=>(e[t]=r[t]??af[t],e)),{})),[r]);return g.useMemo((()=>u({},r,l,{localeText:n,slots:o},(e=>u({},nm,e.dataSource?{filterMode:"server",sortingMode:"server",paginationMode:"server"}:{}))(r))),[r,n,o,l])})(e),o=function(e,t){const r=g.useRef(null),n=g.useRef(null);n.current||(n.current=function(e){const t=e.current?.[am];if(t)return t;const r={},n={state:r,store:im.create(r),instanceId:{id:sm}};return sm+=1,n.getPublicApi=()=>e.current,n.register=(t,r)=>{Object.keys(r).forEach((o=>{const l=r[o],i=n[o];if(!0===i?.spying?i.target=l:n[o]=l,"public"===t){const t=e.current,r=t[o];!0===r?.spying?r.target=l:t[o]=l}}))},n.register("private",{caches:{},eventManager:new lm}),n}(r)),r.current||(r.current=function(e){return{get state(){return e.current.state},get store(){return e.current.store},get instanceId(){return e.current.instanceId},[am]:e.current}}(n));const o=g.useCallback(((...e)=>{const[r,o,l={}]=e;if(l.defaultMuiPrevented=!1,(e=>void 0!==e.isPropagationStopped)(l)&&l.isPropagationStopped())return;const i=t.signature===ar.DataGridPro||t.signature===ar.DataGridPremium?{api:n.current.getPublicApi()}:{};n.current.eventManager.emit(r,o,l,i)}),[n,t.signature]),l=g.useCallback(((e,t,r)=>{n.current.eventManager.on(e,t,r);const o=n.current;return()=>{o.eventManager.removeListener(e,t)}}),[n]);return Ti(n,{subscribeEvent:l,publishEvent:o},"public"),e&&!e.current?.state&&(e.current=r.current),g.useImperativeHandle(e,(()=>r.current),[r]),g.useEffect((()=>{const e=n.current;return()=>{e.publishEvent("unmount")}}),[n]),n}(n.apiRef,n);return Kf(o,n),r.jsx(up,{privateApiRef:o,configuration:cm,props:n,children:r.jsx(Fs,u({className:n.className,style:n.style,sx:n.sx},n.slotProps?.root,{ref:t}))})})),dm=g.memo(um);um.propTypes={apiRef:b.shape({current:b.object}),"aria-label":b.string,"aria-labelledby":b.string,autoHeight:b.bool,autoPageSize:b.bool,autosizeOnMount:b.bool,autosizeOptions:b.shape({columns:b.arrayOf(b.string),disableColumnVirtualization:b.bool,expand:b.bool,includeHeaders:b.bool,includeOutliers:b.bool,outliersFactor:b.number}),cellModesModel:b.object,checkboxSelection:b.bool,classes:b.object,className:b.string,clipboardCopyCellDelimiter:b.string,columnBufferPx:b.number,columnGroupHeaderHeight:b.number,columnGroupingModel:b.arrayOf(b.object),columnHeaderHeight:b.number,columns:b.arrayOf(b.object).isRequired,columnVisibilityModel:b.object,dataSource:b.shape({getRows:b.func.isRequired,updateRow:b.func}),dataSourceCache:b.shape({clear:b.func.isRequired,get:b.func.isRequired,set:b.func.isRequired}),density:b.oneOf(["comfortable","compact","standard"]),disableAutosize:b.bool,disableColumnFilter:b.bool,disableColumnMenu:b.bool,disableColumnResize:b.bool,disableColumnSelector:b.bool,disableColumnSorting:b.bool,disableDensitySelector:b.bool,disableEval:b.bool,disableMultipleRowSelection:b.bool,disableRowSelectionOnClick:b.bool,disableVirtualization:b.bool,editMode:b.oneOf(["cell","row"]),estimatedRowCount:b.number,experimentalFeatures:b.shape({warnIfFocusStateIsNotSynced:b.bool}),filterDebounceMs:b.number,filterMode:b.oneOf(["client","server"]),filterModel:b.shape({items:b.arrayOf(b.shape({field:b.string.isRequired,id:b.oneOfType([b.number,b.string]),operator:b.string.isRequired,value:b.any})).isRequired,logicOperator:b.oneOf(["and","or"]),quickFilterExcludeHiddenColumns:b.bool,quickFilterLogicOperator:b.oneOf(["and","or"]),quickFilterValues:b.array}),getCellClassName:b.func,getDetailPanelContent:b.func,getEstimatedRowHeight:b.func,getRowClassName:b.func,getRowHeight:b.func,getRowId:b.func,getRowSpacing:b.func,hideFooter:b.bool,hideFooterPagination:b.bool,hideFooterSelectedRowCount:b.bool,ignoreDiacritics:b.bool,ignoreValueFormatterDuringExport:b.oneOfType([b.shape({clipboardExport:b.bool,csvExport:b.bool}),b.bool]),initialState:b.object,isCellEditable:b.func,isRowSelectable:b.func,keepNonExistentRowsSelected:b.bool,label:b.string,loading:b.bool,localeText:b.object,logger:b.shape({debug:b.func.isRequired,error:b.func.isRequired,info:b.func.isRequired,warn:b.func.isRequired}),logLevel:b.oneOf(["debug","error","info","warn",!1]),nonce:b.string,onCellClick:b.func,onCellDoubleClick:b.func,onCellEditStart:b.func,onCellEditStop:b.func,onCellKeyDown:b.func,onCellModesModelChange:b.func,onClipboardCopy:b.func,onColumnHeaderClick:b.func,onColumnHeaderContextMenu:b.func,onColumnHeaderDoubleClick:b.func,onColumnHeaderEnter:b.func,onColumnHeaderLeave:b.func,onColumnHeaderOut:b.func,onColumnHeaderOver:b.func,onColumnOrderChange:b.func,onColumnResize:b.func,onColumnVisibilityModelChange:b.func,onColumnWidthChange:b.func,onDataSourceError:b.func,onDensityChange:b.func,onFilterModelChange:b.func,onMenuClose:b.func,onMenuOpen:b.func,onPaginationMetaChange:b.func,onPaginationModelChange:b.func,onPreferencePanelClose:b.func,onPreferencePanelOpen:b.func,onProcessRowUpdateError:b.func,onResize:b.func,onRowClick:b.func,onRowCountChange:b.func,onRowDoubleClick:b.func,onRowEditStart:b.func,onRowEditStop:b.func,onRowModesModelChange:b.func,onRowSelectionModelChange:b.func,onSortModelChange:b.func,onStateChange:b.func,pageSizeOptions:b.arrayOf(b.oneOfType([b.number,b.shape({label:b.string.isRequired,value:b.number.isRequired})]).isRequired),pagination:b.oneOf([!0]),paginationMeta:b.shape({hasNextPage:b.bool}),paginationMode:b.oneOf(["client","server"]),paginationModel:b.shape({page:b.number.isRequired,pageSize:b.number.isRequired}),processRowUpdate:b.func,resizeThrottleMs:b.number,rowBufferPx:b.number,rowCount:b.number,rowHeight:b.number,rowModesModel:b.object,rows:b.arrayOf(b.object),rowSelection:b.bool,rowSelectionModel:b.shape({ids:b.instanceOf(Set).isRequired,type:b.oneOf(["exclude","include"]).isRequired}),rowSpacingType:b.oneOf(["border","margin"]),rowSpanning:b.bool,scrollbarSize:b.number,showCellVerticalBorder:b.bool,showColumnVerticalBorder:b.bool,showToolbar:b.bool,slotProps:b.object,slots:b.object,sortingMode:b.oneOf(["client","server"]),sortingOrder:b.arrayOf(b.oneOf(["asc","desc"])),sortModel:b.arrayOf(b.shape({field:b.string.isRequired,sort:b.oneOf(["asc","desc"])})),style:b.object,sx:b.oneOfType([b.arrayOf(b.oneOfType([b.func,b.object,b.bool])),b.func,b.object]),virtualizeColumnsWithAutoRowHeight:b.bool};export{dm as D};
