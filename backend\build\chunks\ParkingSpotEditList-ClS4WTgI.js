import{b as e,s as a,Q as t,j as s,R as l,e as r,z as n,c as o,a as i,J as c}from"../entries/index-CEzJO5Xy.js";import{r as u}from"./router-BtYqujaw.js";import{g as N}from"./CountryService-DnJKuIXr.js";import{M as d}from"./MultipleSelect-C7xTvWe9.js";import{I as A}from"./Input-BQdee9z7.js";import{F as I,I as p}from"./InputLabel-BbcIE26O.js";import{B as O}from"./Button-DGZYUY3P.js";const g=new e({fr:{NEW_LOCATION_HEADING:"Nouveau lieu",LOCATION_NAME:"Lieu",INVALID_LOCATION:"Ce lieu existe déjà.",LOCATION_CREATED:"Lieu créé avec succès.",COUNTRY:"Pays",PARKING_SPOTS:"Places de parking"},en:{NEW_LOCATION_HEADING:"New location",LOCATION_NAME:"Location",INVALID_LOCATION:"This location already exists.",LOCATION_CREATED:"Location created successfully.",COUNTRY:"Country",PARKING_SPOTS:"Parking spots"},es:{NEW_LOCATION_HEADING:"Nuevo lugar",LOCATION_NAME:"Lugar",INVALID_LOCATION:"Este lugar ya existe.",LOCATION_CREATED:"Lugar creado con éxito.",COUNTRY:"País",PARKING_SPOTS:"Plazas de aparcamiento"},ar:{NEW_LOCATION_HEADING:"موقع جديد",LOCATION_NAME:"الموقع",INVALID_LOCATION:"هذا الموقع موجود بالفعل.",LOCATION_CREATED:"تم إنشاء الموقع بنجاح.",COUNTRY:"البلد",PARKING_SPOTS:"أماكن الانتظار"}});a(g);const m=({value:e,multiple:a,label:o,required:i,variant:c,onChange:A})=>{const[I,p]=u.useState(!1),[O,g]=u.useState(!1),[m,T]=u.useState([]),[C,_]=u.useState(!0),[E,v]=u.useState(1),[h,L]=u.useState(""),[f,j]=u.useState([]);u.useEffect((()=>{const s=a?e:[e];e&&!t(f,s)&&j(s),(null===e||Array.isArray(e)&&0===e.length)&&f.length>0&&j([])}),[e,a,f]);const x=async(e,a,t)=>{try{if(C||1===e){g(!0);const s=await N(a,e,r.PAGE_SIZE),l=s&&s.length>0?s[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!l)return;const n=Array.isArray(l.pageInfo)&&l.pageInfo.length>0?l.pageInfo[0].totalRecords:0,o=1===e?l.resultData:[...m,...l.resultData];T(o),_(l.resultData.length>0),t&&t({rows:l.resultData,rowCount:n})}}catch(s){n(s)}finally{g(!1)}};return s.jsx(d,{loading:O,label:o||"",callbackFromMultipleSelect:e=>{A&&A(e)},options:m,selectedOptions:f,required:i||!1,multiple:a,type:l.Country,variant:c||"standard",ListboxProps:{onScroll:e=>{const a=e.currentTarget;if(C&&!O&&a.scrollTop+a.clientHeight>=a.scrollHeight-r.PAGE_OFFSET){const e=E+1;v(e),x(e,h)}}},onFocus:()=>{if(!I){const e=1;T([]),v(e),x(e,h,(()=>{p(!0)}))}},onInputChange:e=>{const a=e&&e.target&&"value"in e.target&&e.target.value||"";a!==h&&(T([]),v(1),L(a),x(1,a))},onClear:()=>{T([]),v(1),L(""),_(!0),x(1,"")}})},T=new e({fr:{NEW_PARKING_SPOT:"Nouvelle place de parking"},en:{NEW_PARKING_SPOT:"New parking spot"},es:{NEW_PARKING_SPOT:"Nueva plaza de aparcamiento"}});a(T);const C=e=>{const a=o.c(3);let t,l;return a[0]===Symbol.for("react.memo_cache_sentinel")?(t={pattern:"(-)?[0-9]+(\\.[0-9]+)?"},a[0]=t):t=a[0],a[1]!==e?(l=s.jsx(A,{type:"text",inputProps:t,...e}),a[1]=e,a[2]=l):l=a[2],l},_=e=>{const a=o.c(20),{title:t,values:l,onAdd:n,onUpdate:N,onDelete:d}=e;let g;a[0]!==l?(g=l||[],a[0]=l,a[1]=g):g=a[1];const[m,_]=u.useState(g);let E,v,h,L,f,j,x;return a[2]!==l?(E=()=>{l&&_(l)},v=[l],a[2]=l,a[3]=E,a[4]=v):(E=a[3],v=a[4]),u.useEffect(E,v),a[5]!==t?(h=t&&s.jsx("span",{className:"title",children:t}),a[5]=t,a[6]=h):h=a[6],a[7]!==d||a[8]!==N||a[9]!==m?(L=m.map(((e,a)=>s.jsxs("div",{className:"row",children:[e.values&&r._LANGUAGES.map(((t,l)=>s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(p,{className:"required",children:`${i.NAME} (${t.label})`}),s.jsx(A,{type:"text",value:e.values[l]&&e.values[l].value||"",required:!0,onChange:e=>{const s=c(m),r=s[a];r._id?r.values[l].value=e.target.value:r.values[l]={language:t.code,value:e.target.value},N&&N(r,a),_(s)},autoComplete:"off"})]},t.code))),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(p,{className:"required",children:i.LATITUDE}),s.jsx(C,{value:e.latitude,required:!0,onChange:e=>{const t=c(m);t[a].latitude=e.target.value,_(t),N&&N(t[a],a)}})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(p,{className:"required",children:i.LONGITUDE}),s.jsx(C,{value:e.longitude,required:!0,onChange:e=>{const t=c(m);t[a].longitude=e.target.value,_(t),N&&N(t[a],a)}})]}),s.jsx("div",{className:"row-actions",children:s.jsx(O,{variant:"outlined",className:"btn-margin-bottom",size:"small",color:"error",onClick:()=>{d?d(e,a):(m.splice(a,1),_(c(m)))},children:i.DELETE})})]},e._id||a))),a[7]=d,a[8]=N,a[9]=m,a[10]=L):L=a[10],a[11]!==L?(f=s.jsx("div",{className:"rows",children:L}),a[11]=L,a[12]=f):f=a[12],a[13]!==n||a[14]!==m?(j=s.jsx("div",{className:"global-actions",children:s.jsx(O,{variant:"outlined",className:"btn-margin-bottom",size:"small",color:"inherit",onClick:()=>{const e={latitude:"",longitude:"",values:[]};n?n(e):m.push(e)},children:T.NEW_PARKING_SPOT})}),a[13]=n,a[14]=m,a[15]=j):j=a[15],a[16]!==h||a[17]!==f||a[18]!==j?(x=s.jsxs("div",{className:"parking-spot-edit-list",children:[h,f,j]}),a[16]=h,a[17]=f,a[18]=j,a[19]=x):x=a[19],x};export{m as C,C as P,_ as a,g as s};
