import{r}from"./router-BtYqujaw.js";import{j as e,i as t,k as o}from"../entries/index-CEzJO5Xy.js";import{g as a,a as s,s as l,c as n,m as i}from"./Button-DGZYUY3P.js";import{c}from"./Grow-CjOKj0i1.js";import{u}from"./useSlot-CtA82Ni6.js";const d=c(e.jsx("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}));function p(r){return a("MuiAvatar",r)}s("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);const m=l("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(r,e)=>{const{ownerState:t}=r;return[e.root,e[t.variant],t.colorDefault&&e.colorDefault]}})(i((({theme:r})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:r.typography.fontFamily,fontSize:r.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(r.vars||r).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(r.vars||r).palette.background.default,...r.vars?{backgroundColor:r.vars.palette.Avatar.defaultBg}:{backgroundColor:r.palette.grey[400],...r.applyStyles("dark",{backgroundColor:r.palette.grey[600]})}}}]})))),f=l("img",{name:"MuiAvatar",slot:"Img"})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),g=l(d,{name:"MuiAvatar",slot:"Fallback"})({width:"75%",height:"75%"}),v=r.forwardRef((function(a,s){const l=t({props:a,name:"MuiAvatar"}),{alt:i,children:c,className:d,component:v="div",slots:h={},slotProps:y={},imgProps:w,sizes:S,src:b,srcSet:x,variant:k="circular",...j}=l;let P=null;const A={...l,component:v,variant:k},F=function({crossOrigin:e,referrerPolicy:t,src:o,srcSet:a}){const[s,l]=r.useState(!1);return r.useEffect((()=>{if(!o&&!a)return;l(!1);let r=!0;const s=new Image;return s.onload=()=>{r&&l("loaded")},s.onerror=()=>{r&&l("error")},s.crossOrigin=e,s.referrerPolicy=t,s.src=o,a&&(s.srcset=a),()=>{r=!1}}),[e,t,o,a]),s}({...w,..."function"==typeof y.img?y.img(A):y.img,src:b,srcSet:x}),R=b||x,D=R&&"error"!==F;A.colorDefault=!D,delete A.ownerState;const M=(r=>{const{classes:e,variant:t,colorDefault:o}=r;return n({root:["root",t,o&&"colorDefault"],img:["img"],fallback:["fallback"]},p,e)})(A),[z,C]=u("root",{ref:s,className:o(M.root,d),elementType:m,externalForwardedProps:{slots:h,slotProps:y,component:v,...j},ownerState:A}),[I,N]=u("img",{className:M.img,elementType:f,externalForwardedProps:{slots:h,slotProps:{img:{...w,...y.img}}},additionalProps:{alt:i,src:b,srcSet:x,sizes:S},ownerState:A}),[T,q]=u("fallback",{className:M.fallback,elementType:g,externalForwardedProps:{slots:h,slotProps:y},shouldForwardComponentProp:!0,ownerState:A});return P=D?e.jsx(I,{...N}):c||0===c?c:R&&i?i[0]:e.jsx(T,{...q}),e.jsx(z,{...C,children:P})}));export{v as A};
