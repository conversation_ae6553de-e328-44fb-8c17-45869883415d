import{c as e,j as o,a as t,J as s,a5 as i,e as a,F as r,a8 as n}from"../entries/index-CEzJO5Xy.js";import{r as l,d as m}from"./router-BtYqujaw.js";import{L as c}from"./Layout-BQBjg4Lf.js";import{s as p,a as d,S as j,b as u}from"./booking-filter-BlQ6aHSB.js";import{B as f}from"./BookingList-Dn-WOL9Q.js";import{u as g,a as h,z as S,s as b}from"./zod-4O8Zwsja.js";import{L as x}from"./LocationSelectList-DYJOB_U9.js";import{D as k}from"./DatePicker-B527VDUP.js";import{A as C}from"./Accordion-D82axyqp.js";import{F as L}from"./InputLabel-BbcIE26O.js";import{T as _}from"./TextField-BAse--ht.js";import{B as v}from"./Button-DGZYUY3P.js";import{I as y}from"./IconButton-CnBvmeAK.js";import{C as N}from"./Clear-BpXDeTL8.js";import{S as O}from"./Search-BNrZEqND.js";import{h as w}from"./SupplierService-DSnTbAgG.js";import"./vendor-dblfw9z9.js";import"./BookingStatus-Bg6x_fQB.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-CtA82Ni6.js";import"./Paper-CcwAvfvc.js";import"./Backdrop-Bzn12VyM.js";import"./BookingService-BJ4R0IJT.js";import"./MenuItem-suKfXYI2.js";import"./Menu-ZU0DMgjT.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-Cay5TZBz.js";import"./listItemTextClasses-DFwCkkgK.js";import"./Link-B-UCzRRJ.js";import"./format-4arn0GRM.js";import"./Check-D745pofy.js";import"./DataGrid-DM8uCtAG.js";import"./getThemeProps-gt86ccpv.js";import"./Switch-BWPUOSX1.js";import"./useFormControl-B7jXtRD7.js";import"./SwitchBase-BIeqtL5F.js";import"./Tooltip-BkJF6Mu0.js";import"./Toolbar-CNUITE_K.js";import"./KeyboardArrowRight-BV-h2cWM.js";import"./Chip-CAtDqtgp.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Autocomplete-CviOU_ku.js";import"./Input-BQdee9z7.js";import"./OutlinedInput-g8mR4MM3.js";import"./ListItemText-DBn_RuMq.js";import"./Checkbox-CDqupZJG.js";import"./DialogTitle-BZXwroUN.js";import"./Edit-DIF9Bumd.js";import"./Delete-CnqjtpsJ.js";import"./fr-CaQg1DLH.js";import"./LocationService-BtQFgoWL.js";import"./MultipleSelect-C7xTvWe9.js";import"./Avatar-Dix3YM8x.js";import"./Flag-BR6CpE1z.js";import"./DatePicker-CyKPL9FL.js";import"./useMobilePicker-Cpitw7qm.js";import"./FormHelperText-DFSsjBsL.js";import"./ListItem-D1VHRhQp.js";const T=S.object({from:S.date().optional(),to:S.date().optional(),pickupLocation:S.string().optional(),dropOffLocation:S.string().optional(),keyword:S.string().optional()}),A=i=>{const a=e.c(57),{collapse:r,className:n,language:m,onSubmit:c}=i,d=l.useRef(null);let j;a[0]===Symbol.for("react.memo_cache_sentinel")?(j={resolver:b(T),mode:"onChange"},a[0]=j):j=a[0];const{control:u,register:f,handleSubmit:S,setValue:w}=g(j);let A;a[1]!==u?(A={control:u},a[1]=u,a[2]=A):A=a[2];const{from:D,to:I,keyword:P}=h(A),[F,B]=l.useState(void 0);let R;a[3]!==c?(R=e=>{let o={from:e.from,to:e.to,pickupLocation:e.pickupLocation,dropOffLocation:e.dropOffLocation,keyword:e.keyword};e.from||e.to||e.pickupLocation||e.dropOffLocation||e.keyword||(o=null),c&&c(s(o))},a[3]=c,a[4]=R):R=a[4];const E=R,W=(n?`${n} `:"")+"booking-filter";let M,H,U,G,K,z,J,Q,V,Y,Z;a[5]!==E||a[6]!==S?(M=S(E),a[5]=E,a[6]=S,a[7]=M):M=a[7],a[8]===Symbol.for("react.memo_cache_sentinel")?(H=o.jsx("input",{autoComplete:"false",name:"hidden",type:"text",style:{display:"none"}}),a[8]=H):H=a[8],a[9]!==f?(U=f("from"),a[9]=f,a[10]=U):U=a[10],a[11]!==w||a[12]!==I?(G=e=>{if(e){I&&I.getTime()<=e.getTime()&&w("to",void 0);const o=new Date(e);o.setDate(e.getDate()+1),B(o)}else B(void 0);w("from",e||void 0)},a[11]=w,a[12]=I,a[13]=G):G=a[13],a[14]!==D||a[15]!==m||a[16]!==U||a[17]!==G?(K=o.jsx(L,{fullWidth:!0,margin:"dense",children:o.jsx(k,{...U,label:t.FROM,value:D,onChange:G,language:m,variant:"standard"})}),a[14]=D,a[15]=m,a[16]=U,a[17]=G,a[18]=K):K=a[18],a[19]!==f?(z=f("to"),a[19]=f,a[20]=z):z=a[20],a[21]!==w?(J=e=>w("to",e||void 0),a[21]=w,a[22]=J):J=a[22],a[23]!==m||a[24]!==F||a[25]!==z||a[26]!==J||a[27]!==I?(Q=o.jsx(L,{fullWidth:!0,margin:"dense",children:o.jsx(k,{...z,label:t.TO,minDate:F,value:I,onChange:J,language:m,variant:"standard"})}),a[23]=m,a[24]=F,a[25]=z,a[26]=J,a[27]=I,a[28]=Q):Q=a[28],a[29]!==w?(V=o.jsx(L,{fullWidth:!0,margin:"dense",children:o.jsx(x,{label:p.PICK_UP_LOCATION,variant:"standard",onChange:e=>w("pickupLocation",e.length>0?e[0]._id:"")})}),a[29]=w,a[30]=V):V=a[30],a[31]!==w?(Y=o.jsx(L,{fullWidth:!0,margin:"dense",children:o.jsx(x,{label:p.DROP_OFF_LOCATION,variant:"standard",onChange:e=>w("dropOffLocation",e.length>0?e[0]._id:"")})}),a[31]=w,a[32]=Y):Y=a[32],a[33]!==f?(Z=f("keyword"),a[33]=f,a[34]=Z):Z=a[34];const $=P||"";let q,X,ee,oe,te,se;return a[35]!==w?(q=e=>w("keyword",e.target.value),a[35]=w,a[36]=q):q=a[36],a[37]!==P||a[38]!==w?(X={input:{endAdornment:P?o.jsx(y,{size:"small",onClick:()=>{w("keyword",""),d.current?.focus()},children:o.jsx(N,{className:"d-adornment-icon"})}):o.jsx(O,{className:"d-adornment-icon"})}},a[37]=P,a[38]=w,a[39]=X):X=a[39],a[40]!==Z||a[41]!==$||a[42]!==q||a[43]!==X?(ee=o.jsx(L,{fullWidth:!0,margin:"dense",children:o.jsx(_,{...Z,inputRef:d,variant:"standard",value:$,onChange:q,placeholder:t.SEARCH_PLACEHOLDER,slotProps:X,className:"bf-search"})}),a[40]=Z,a[41]=$,a[42]=q,a[43]=X,a[44]=ee):ee=a[44],a[45]===Symbol.for("react.memo_cache_sentinel")?(oe=o.jsx(v,{type:"submit",variant:"contained",className:"btn-primary btn-search",fullWidth:!0,children:t.SEARCH}),a[45]=oe):oe=a[45],a[46]!==Q||a[47]!==V||a[48]!==Y||a[49]!==ee||a[50]!==M||a[51]!==K?(te=o.jsxs("form",{autoComplete:"off",onSubmit:M,children:[H,K,Q,V,Y,ee,oe]}),a[46]=Q,a[47]=V,a[48]=Y,a[49]=ee,a[50]=M,a[51]=K,a[52]=te):te=a[52],a[53]!==r||a[54]!==te||a[55]!==W?(se=o.jsx(C,{title:t.SEARCH,collapse:r,className:W,children:te}),a[53]=r,a[54]=te,a[55]=W,a[56]=se):se=a[56],se},D=()=>{const t=e.c(22),s=m(),[p,g]=l.useState(),[h,S]=l.useState(!1),[b,x]=l.useState(!1);let k;t[0]===Symbol.for("react.memo_cache_sentinel")?(k=[],t[0]=k):k=t[0];const[C,L]=l.useState(k),[_,y]=l.useState();let N;t[1]===Symbol.for("react.memo_cache_sentinel")?(N=i().map(I),t[1]=N):N=t[1];const[O,T]=l.useState(N),[D,P]=l.useState(),[F,B]=l.useState(!0),[R,E]=l.useState(0);let W,M,H;t[2]!==p?(W=()=>{if(p&&p.verified){const e=document.querySelector("div.col-1");e&&E(e.clientHeight)}},M=[p],t[2]=p,t[3]=W,t[4]=M):(W=t[3],M=t[4]),l.useEffect(W,M),t[5]===Symbol.for("react.memo_cache_sentinel")?(H=e=>{y(e)},t[5]=H):H=t[5];const U=H;let G;t[6]===Symbol.for("react.memo_cache_sentinel")?(G=e=>{T(e)},t[6]=G):G=t[6];const K=G;let z;t[7]===Symbol.for("react.memo_cache_sentinel")?(z=e=>{P(e)},t[7]=z):z=t[7];const J=z;let Q;t[8]===Symbol.for("react.memo_cache_sentinel")?(Q=async e=>{if(e){const o=r(e);g(e),x(o),S(!o),B(o);const t=await w(),s=o?n(t):[e._id??""];L(t),y(s),S(!0),B(!1)}},t[8]=Q):Q=t[8];const V=Q;let Y,Z;return t[9]!==b||t[10]!==C||t[11]!==D||t[12]!==h||t[13]!==F||t[14]!==s||t[15]!==R||t[16]!==O||t[17]!==_||t[18]!==p?(Y=p&&o.jsxs("div",{className:"bookings",children:[o.jsx("div",{className:"col-1",children:h&&o.jsxs(o.Fragment,{children:[o.jsx(v,{variant:"contained",className:"btn-primary cl-new-booking",size:"small",onClick:()=>s("/create-booking"),children:d.NEW_BOOKING}),b&&o.jsx(j,{suppliers:C,onChange:U,className:"cl-supplier-filter"}),o.jsx(u,{onChange:K,className:"cl-status-filter"}),o.jsx(A,{onSubmit:J,language:p&&p.language||a.DEFAULT_LANGUAGE,className:"cl-booking-filter",collapse:!a.isMobile})]})}),o.jsx("div",{className:"col-2",children:o.jsx(f,{containerClassName:"bookings",offset:R,language:p.language,loggedUser:p,suppliers:_,statuses:O,filter:D,loading:F,hideDates:a.isMobile,checkboxSelection:!a.isMobile})})]}),t[9]=b,t[10]=C,t[11]=D,t[12]=h,t[13]=F,t[14]=s,t[15]=R,t[16]=O,t[17]=_,t[18]=p,t[19]=Y):Y=t[19],t[20]!==Y?(Z=o.jsx(c,{onLoad:V,strict:!0,children:Y}),t[20]=Y,t[21]=Z):Z=t[21],Z};function I(e){return e.value}export{D as default};
