import{j as E,b as I,e,s as _}from"../entries/index-xsXxT3-W.js";import{c as P}from"./Grow-Cp8xsNYl.js";const R=P(E.jsx("path",{d:"M5 20h14v-2H5zm0-10h4v6h6v-6h4l-7-7z"})),A=new I({fr:{CREATE_SUPPLIER_HEADING:"Nouveau fournisseur",INVALID_SUPPLIER_NAME:"Ce fournisseur existe déjà.",SUPPLIER_IMAGE_SIZE_ERROR:`L'image doit être au format ${e.SUPPLIER_IMAGE_WIDTH}x${e.SUPPLIER_IMAGE_HEIGHT}`,RECOMMENDED_IMAGE_SIZE:`Taille d'image recommandée : ${e.SUPPLIER_IMAGE_WIDTH}x${e.SUPPLIER_IMAGE_HEIGHT}`},en:{CREATE_SUPPLIER_HEADING:"New supplier",INVALID_SUPPLIER_NAME:"This supplier already exists.",SUPPLIER_IMAGE_SIZE_ERROR:`The image must be in the format ${e.SUPPLIER_IMAGE_WIDTH}x${e.SUPPLIER_IMAGE_HEIGHT}`,RECOMMENDED_IMAGE_SIZE:`Recommended image size: ${e.SUPPLIER_IMAGE_WIDTH}x${e.SUPPLIER_IMAGE_HEIGHT}`},es:{CREATE_SUPPLIER_HEADING:"Nuevo proveedor",INVALID_SUPPLIER_NAME:"Este proveedor ya existe.",SUPPLIER_IMAGE_SIZE_ERROR:`La imagen debe tener el formato ${e.SUPPLIER_IMAGE_WIDTH}x${e.SUPPLIER_IMAGE_HEIGHT}`,RECOMMENDED_IMAGE_SIZE:`Tamaño de imagen recomendado: ${e.SUPPLIER_IMAGE_WIDTH}x${e.SUPPLIER_IMAGE_HEIGHT}`}});_(A);export{R as U,A as s};
