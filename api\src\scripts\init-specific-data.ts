import 'dotenv/config'
import bcrypt from 'bcrypt'
import * as bookcarsTypes from ':bookcars-types'
import * as env from '../config/env.config'
import * as logger from '../common/logger'
import * as databaseHelper from '../common/databaseHelper'
import User from '../models/User'
import Country from '../models/Country'
import Location from '../models/Location'
import LocationValue from '../models/LocationValue'

/**
 * Initialize specific data for dress rental system
 * Creates ONLY the specific supplier, location, and country as requested
 */
const initializeSpecificData = async (): Promise<boolean> => {
  try {
    logger.info('Clearing existing data and initializing specific data...')

    // Clear existing data
    await clearExistingData()

    // Create Palestine country (only this country)
    const country = await createPalestineCountry()
    logger.info('Palestine country created')

    // Create Sofia Bridal & Soiree Boutique supplier (only this supplier)
    const supplier = await createSofiaBridalSupplier()
    logger.info('Sofia Bridal & Soiree Boutique supplier created')

    // Create Al-Jalbouni Circle location (only this location)
    const location = await createAlJalbouniLocation(country._id!.toString(), supplier._id!.toString())
    logger.info('Al-Jalbouni Circle location created')

    logger.info('Specific data initialization completed successfully')
    return true
  } catch (err) {
    logger.error('Error while initializing specific data:', err)
    return false
  }
}

/**
 * Clear existing data from relevant collections
 */
const clearExistingData = async () => {
  logger.info('Clearing existing countries, locations, and suppliers...')
  
  // Clear suppliers (keep admin users)
  await User.deleteMany({ type: bookcarsTypes.UserType.Supplier })
  
  // Clear locations and location values
  await Location.deleteMany({})
  await Country.deleteMany({})
  await LocationValue.deleteMany({})
  
  logger.info('Existing data cleared')
}

/**
 * Create Sofia Bridal & Soiree Boutique supplier
 */
const createSofiaBridalSupplier = async () => {
  const salt = await bcrypt.genSalt(10)
  const passwordHash = await bcrypt.hash('supplier123', salt)

  const supplier = new User({
    email: '<EMAIL>',
    fullName: 'Sofia Bridal & Soiree Boutique',
    password: passwordHash,
    language: 'ar',
    type: bookcarsTypes.UserType.Supplier,
    active: true,
    verified: true,
    blacklisted: false,
    enableEmailNotifications: true,
    phone: '0599123456',
    location: 'Al-Jalbouni Circle, Jenin, Palestine',
    bio: 'Premium dress rental boutique in Jenin specializing in wedding and evening gowns',
    payLater: true,
    minimumRentalDays: 1,
    supplierDressLimit: 100,
    notifyAdminOnNewDress: true
  })

  await supplier.save()
  return supplier
}

/**
 * Create Palestine country with Arabic and English names
 */
const createPalestineCountry = async () => {
  // Create location values for Palestine
  const palestineValues = [
    new LocationValue({
      language: 'ar',
      value: 'فلسطين'
    }),
    new LocationValue({
      language: 'en',
      value: 'Palestine'
    }),
    new LocationValue({
      language: 'fr',
      value: 'Palestine'
    })
  ]

  await LocationValue.insertMany(palestineValues)

  // Create country
  const country = new Country({
    values: palestineValues.map(v => v._id)
  })

  await country.save()
  return country
}

/**
 * Create Al-Jalbouni Circle, Jenin location
 */
const createAlJalbouniLocation = async (countryId: string, supplierId: string) => {
  // Create location values for Al-Jalbouni Circle, Jenin
  const locationValues = [
    new LocationValue({
      language: 'ar',
      value: 'دوار الجلبوني، جنين، فلسطين'
    }),
    new LocationValue({
      language: 'en',
      value: 'Al-Jalbouni Circle, Jenin, Palestine'
    }),
    new LocationValue({
      language: 'fr',
      value: 'Cercle Al-Jalbouni, Jenin, Palestine'
    })
  ]

  await LocationValue.insertMany(locationValues)

  // Create location (Jenin coordinates)
  const location = new Location({
    country: countryId,
    latitude: 32.4603,
    longitude: 35.2957,
    values: locationValues.map(v => v._id),
    supplier: supplierId
  })

  await location.save()
  return location
}

// Run the initialization if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    if (await databaseHelper.connect(env.DB_URI, env.DB_SSL, env.DB_DEBUG)) {
      await initializeSpecificData()
      await databaseHelper.close()
      logger.info('Database connection closed')
      process.exit(0)
    } else {
      logger.error('Failed to connect to database')
      process.exit(1)
    }
  })()
}

export { initializeSpecificData }
