import{r as t}from"./router-BtYqujaw.js";import{i as e,j as s,k as o}from"../entries/index-xsXxT3-W.js";import{a as n,i as r}from"./isHostComponent-DR4iSCFs.js";import{g as i,a,s as d,c as l,u as c,m as p}from"./Button-BeKLLPpp.js";import{b as u}from"./Menu-C_-X8cS7.js";function m(t){return i("MuiListItem",t)}function g(t){return i("MuiListItemButton",t)}a("MuiListItem",["root","container","dense","alignItemsFlexStart","divider","gutters","padding","secondaryAction"]);const b=a("MuiListItemButton",["root","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","selected"]);function v(t){return i("MuiListItemSecondaryAction",t)}a("MuiListItemSecondaryAction",["root","disableGutters"]);const S=d("div",{name:"MuiListItemSecondaryAction",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:s}=t;return[e.root,s.disableGutters&&e.disableGutters]}})({position:"absolute",right:16,top:"50%",transform:"translateY(-50%)",variants:[{props:({ownerState:t})=>t.disableGutters,style:{right:0}}]}),f=t.forwardRef((function(n,r){const i=e({props:n,name:"MuiListItemSecondaryAction"}),{className:a,...d}=i,c=t.useContext(u),p={...i,disableGutters:c.disableGutters},m=(t=>{const{disableGutters:e,classes:s}=t;return l({root:["root",e&&"disableGutters"]},v,s)})(p);return s.jsx(S,{className:o(m.root,a),ownerState:p,ref:r,...d})}));f.muiName="ListItemSecondaryAction";const y=d("div",{name:"MuiListItem",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:s}=t;return[e.root,s.dense&&e.dense,"flex-start"===s.alignItems&&e.alignItemsFlexStart,s.divider&&e.divider,!s.disableGutters&&e.gutters,!s.disablePadding&&e.padding,s.hasSecondaryAction&&e.secondaryAction]}})(p((({theme:t})=>({display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",width:"100%",boxSizing:"border-box",textAlign:"left",variants:[{props:({ownerState:t})=>!t.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:t})=>!t.disablePadding&&t.dense,style:{paddingTop:4,paddingBottom:4}},{props:({ownerState:t})=>!t.disablePadding&&!t.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:t})=>!t.disablePadding&&!!t.secondaryAction,style:{paddingRight:48}},{props:({ownerState:t})=>!!t.secondaryAction,style:{[`& > .${b.root}`]:{paddingRight:48}}},{props:{alignItems:"flex-start"},style:{alignItems:"flex-start"}},{props:({ownerState:t})=>t.divider,style:{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:t})=>t.button,style:{transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}}}},{props:({ownerState:t})=>t.hasSecondaryAction,style:{paddingRight:48}}]})))),x=d("li",{name:"MuiListItem",slot:"Container"})({position:"relative"}),h=t.forwardRef((function(i,a){const d=e({props:i,name:"MuiListItem"}),{alignItems:p="center",children:g,className:b,component:v,components:S={},componentsProps:h={},ContainerComponent:I="li",ContainerProps:{className:w,...A}={},dense:L=!1,disableGutters:j=!1,disablePadding:G=!1,divider:M=!1,secondaryAction:P,slotProps:C={},slots:R={},...N}=d,B=t.useContext(u),k=t.useMemo((()=>({dense:L||B.dense||!1,alignItems:p,disableGutters:j})),[p,B.dense,L,j]),F=t.useRef(null),D=t.Children.toArray(g),T=D.length&&n(D[D.length-1],["ListItemSecondaryAction"]),$={...d,alignItems:p,dense:k.dense,disableGutters:j,disablePadding:G,divider:M,hasSecondaryAction:T},z=(t=>{const{alignItems:e,classes:s,dense:o,disableGutters:n,disablePadding:r,divider:i,hasSecondaryAction:a}=t;return l({root:["root",o&&"dense",!n&&"gutters",!r&&"padding",i&&"divider","flex-start"===e&&"alignItemsFlexStart",a&&"secondaryAction"],container:["container"]},m,s)})($),H=c(F,a),J=R.root||S.Root||y,V=C.root||h.root||{},Y={className:o(z.root,V.className,b),...N};let Z=v||"li";return T?(Z=Y.component||v?Z:"div","li"===I&&("li"===Z?Z="div":"li"===Y.component&&(Y.component="div")),s.jsx(u.Provider,{value:k,children:s.jsxs(x,{as:I,className:o(z.container,w),ref:H,ownerState:$,...A,children:[s.jsx(J,{...V,...!r(J)&&{as:Z,ownerState:{...$,...V.ownerState}},...Y,children:D}),D.pop()]})})):s.jsx(u.Provider,{value:k,children:s.jsxs(J,{...V,as:Z,ref:H,...!r(J)&&{ownerState:{...$,...V.ownerState}},...Y,children:[D,P&&s.jsx(f,{children:P})]})})}));export{h as L,g,b as l};
