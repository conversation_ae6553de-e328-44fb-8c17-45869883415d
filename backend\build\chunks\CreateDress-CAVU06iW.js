import{u as e,e as s,j as t,S as r,R as a,a as i,P as o,z as l}from"../entries/index-xsXxT3-W.js";import{d as n,r as c}from"./router-BtYqujaw.js";import{L as m}from"./Layout-DaeN7D4t.js";import{b as d,e as u}from"./DressService-DkS6e_O5.js";import{E as j}from"./Error-DRzAdbbx.js";import{S as p}from"./SimpleBackdrop-CqsJhYJ4.js";import{A as h}from"./Avatar-CvDHTACZ.js";import{S as x,D as f,a as S,b as g,c as N}from"./create-dress-CqJ0gf4a.js";import{L as C}from"./LocationSelectList-BP49A3oC.js";import{P as b}from"./Paper-C-atefOs.js";import{I as E}from"./Info-CNP9gYBt.js";import{F as I,I as y}from"./InputLabel-C8rcdOGQ.js";import{I as v}from"./Input-D1AdR9CM.js";import{F as M}from"./FormHelperText-DDZ4BMA4.js";import{T as R}from"./TextField-D_yQOTzE.js";import{F as A,S as k}from"./Switch-C5asfh_w.js";import{B as L}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-DiTut-u0.js";import"./Backdrop-Czag2Ija.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./SupplierService-9DC5V5ZJ.js";import"./MultipleSelect-DovAF4K6.js";import"./Autocomplete-CWN5GAd4.js";import"./OutlinedInput-BX8yFQbF.js";import"./useFormControl-B7jXtRD7.js";import"./Chip-MGF1mKZa.js";import"./IconButton-CxOCoGF3.js";import"./Flag-CMGasDVj.js";import"./MenuItem-P0BnGnrT.js";import"./Menu-C_-X8cS7.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-DEridHif.js";import"./listItemTextClasses-BcbgzvlE.js";import"./SwitchBase-DrUkTXjH.js";const W=()=>{const W=n(),{user:_}=e(),[P,O]=c.useState(!1),[D,T]=c.useState(!1),[q,B]=c.useState(!1),[U,G]=c.useState(!1),[F,$]=c.useState(!1),[w,Y]=c.useState(""),[z,Z]=c.useState(""),[H,V]=c.useState(""),[Q,J]=c.useState([]),[K,X]=c.useState(""),[ee,se]=c.useState(""),[te,re]=c.useState(""),[ae,ie]=c.useState(""),[oe,le]=c.useState(""),[ne,ce]=c.useState(""),[me,de]=c.useState(""),[ue,je]=c.useState(""),[pe,he]=c.useState(!1),[xe,fe]=c.useState([]),[Se,ge]=c.useState(!0),[Ne,Ce]=c.useState(!1),[be,Ee]=c.useState(!1),[Ie,ye]=c.useState(""),[ve,Me]=c.useState(""),[Re,Ae]=c.useState(""),[ke,Le]=c.useState(""),[We,_e]=c.useState(""),[Pe,Oe]=c.useState(""),[De,Te]=c.useState(!1),[qe,Be]=c.useState(""),[Ue,Ge]=c.useState(""),[Fe,$e]=c.useState(String(s.MINIMUM_AGE)),[we,Ye]=c.useState(!0),[ze,Ze]=c.useState(!1),[He,Ve]=c.useState(""),Qe=e=>{const t=Number.parseInt(e,10);return!Number.isNaN(t)&&t>=s.MINIMUM_AGE},Je=e=>""===e?-1:Number(e),Ke=e=>e&&Number(e)||null;return t.jsxs(m,{onLoad:async()=>{T(!0),Ze(!1),G(!1),$(!1);try{const e=JSON.parse(localStorage.getItem("bc-user")||"{}");if(e){const s=o(e);O(s),s&&V(e._id),B(!0)}else Ze(!0)}catch(e){l(e)}finally{T(!1)}},strict:!0,children:[t.jsx("div",{className:"create-dress",children:t.jsxs(b,{className:"dress-form dress-form-wrapper",elevation:10,style:q?{}:{display:"none"},children:[t.jsx("h1",{className:"dress-form-title",children:r.NEW_DRESS}),t.jsxs("form",{onSubmit:async e=>{try{if(e.preventDefault(),T(!0),!Qe(Fe))return Ze(!0),void G(!1);if(!w)return G(!0),void $(!1);const s={loggedUser:_._id,name:z,supplier:H,minimumAge:Number.parseInt(Fe,10),locations:Q.map((e=>e._id)),dailyPrice:Number(K),discountedDailyPrice:Ke(ee),biWeeklyPrice:Ke(te),discountedBiWeeklyPrice:Ke(ae),weeklyPrice:Ke(oe),discountedWeeklyPrice:Ke(ne),monthlyPrice:Ke(me),discountedMonthlyPrice:Ke(ue),deposit:Number(He),available:Se,fullyBooked:Ne,comingSoon:be,type:Ie,size:ve,color:ke,length:Number(We),material:Pe,customizable:De,image:w,cancellation:Je(qe),amendments:Je(Ue),isDateBasedPrice:pe,dateBasedPrices:xe,range:"",accessories:[]},t=await u(s);t&&t._id?W("/dresses"):l()}catch(s){l(s)}finally{T(!1)}},children:[t.jsx(h,{type:a.Dress,mode:"create",record:null,size:"large",readonly:!1,onBeforeUpload:()=>{T(!0)},onChange:e=>{T(!1),Y(e),G(!1),$(!1)},onValidate:e=>{e||$(!0)},color:"disabled",className:"avatar-ctn"}),t.jsxs("div",{className:"info",children:[t.jsx(E,{}),t.jsx("span",{children:r.RECOMMENDED_IMAGE_SIZE})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{className:"required",children:i.NAME}),t.jsx(v,{id:"name",type:"text",required:!0,onChange:e=>{Z(e.target.value)},autoComplete:"off"})]}),!P&&t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(x,{label:i.SUPPLIER,required:!0,onChange:e=>{V(e.length>0?e[0]._id:"")}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(C,{label:r.LOCATIONS,multiple:!0,required:!0,onChange:e=>{J(e)}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(f,{label:r.DRESS_TYPE,required:!0,onChange:e=>{ye(e)}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(S,{label:r.DRESS_SIZE,required:!0,onChange:e=>{Me(e)}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(g,{label:r.DRESS_STYLE,required:!0,onChange:e=>{Ae(e)}})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(N,{label:r.MATERIAL,required:!0,onChange:e=>{Oe(e)}})}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{className:"required",children:r.COLOR}),t.jsx(v,{id:"color",type:"text",required:!0,onChange:e=>{Le(e.target.value)},autoComplete:"off"})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsxs(y,{className:"required",children:[r.LENGTH," (cm)"]}),t.jsx(v,{id:"length",type:"number",required:!0,onChange:e=>{_e(e.target.value)},autoComplete:"off"})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{className:"required",children:`${r.PRICE} (${i.CURRENCY})`}),t.jsx(v,{id:"price",type:"number",required:!0,onChange:e=>{X(e.target.value)},autoComplete:"off"})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{children:`${r.DEPOSIT} (${i.CURRENCY})`}),t.jsx(v,{id:"deposit",type:"number",onChange:e=>{Ve(e.target.value)},autoComplete:"off"})]}),t.jsxs(I,{fullWidth:!0,margin:"dense",children:[t.jsx(y,{className:"required",children:i.MINIMUM_AGE}),t.jsx(v,{id:"minimum-age",type:"number",required:!0,error:!we,onChange:e=>{$e(e.target.value);const s=Qe(e.target.value);Ye(s),s||Ze(!0)},autoComplete:"off",value:Fe}),t.jsx(M,{error:!we,children:!we&&i.MINIMUM_AGE_NOT_VALID||""})]}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(R,{label:`${r.CANCELLATION} (${i.CURRENCY})`,slotProps:{htmlInput:{inputMode:"numeric",pattern:"^\\d+(\\.\\d+)?$"}},onChange:e=>{Be(e.target.value)},variant:"standard",autoComplete:"off",value:qe})}),t.jsx(I,{fullWidth:!0,margin:"dense",children:t.jsx(R,{label:`${r.AMENDMENTS} (${i.CURRENCY})`,slotProps:{htmlInput:{inputMode:"numeric",pattern:"^\\d+(\\.\\d+)?$"}},onChange:e=>{Ge(e.target.value)},variant:"standard",autoComplete:"off",value:Ue})}),t.jsx(I,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:t.jsx(A,{control:t.jsx(k,{checked:De,onChange:e=>{Te(e.target.checked)},color:"primary"}),label:r.CUSTOMIZABLE,className:"checkbox-fcl"})}),t.jsx(I,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:t.jsx(A,{control:t.jsx(k,{checked:Se,onChange:e=>{ge(e.target.checked)},color:"primary"}),label:r.AVAILABLE,className:"checkbox-fcl"})}),t.jsx(I,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:t.jsx(A,{control:t.jsx(k,{checked:Ne,onChange:e=>{Ce(e.target.checked)},color:"primary"}),label:r.FULLY_BOOKED,className:"checkbox-fcl"})}),t.jsx(I,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:t.jsx(A,{control:t.jsx(k,{checked:be,onChange:e=>{Ee(e.target.checked)},color:"primary"}),label:r.COMING_SOON,className:"checkbox-fcl"})}),t.jsxs("div",{className:"buttons",children:[t.jsx(L,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:D,children:i.CREATE}),t.jsx(L,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{w&&await d(w),W("/dresses")},children:i.CANCEL})]}),t.jsxs("div",{className:"form-error",children:[U&&t.jsx(j,{message:i.IMAGE_REQUIRED}),F&&t.jsx(j,{message:r.IMAGE_SIZE_ERROR}),ze&&t.jsx(j,{message:i.FORM_ERROR})]})]})]})}),D&&t.jsx(p,{text:i.LOADING})]})};export{W as default};
