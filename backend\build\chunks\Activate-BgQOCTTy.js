import{b as s,s as e,c as r,u as a,j as t,a as o,e as i,w as n,d as c,g as d,x as m,z as l,A as p,B as u,C as h}from"../entries/index-xsXxT3-W.js";import{d as j,r as A}from"./router-BtYqujaw.js";import{u as E,z as w,s as f}from"./zod-4O8Zwsja.js";import{s as x,L as N}from"./Layout-DaeN7D4t.js";import{s as S}from"./change-password-CrWxmtFu.js";import{s as T}from"./reset-password-xiMRPUfP.js";import I from"./NoMatch-DMPclUW6.js";import{E as _}from"./Error-FiYP5RHa.js";import{P as C}from"./Paper-C-atefOs.js";import{B as v}from"./Button-BeKLLPpp.js";import{F as P,I as g}from"./InputLabel-C8rcdOGQ.js";import{I as b}from"./Input-D1AdR9CM.js";import{F as D}from"./FormHelperText-DDZ4BMA4.js";import"./vendor-dblfw9z9.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const O=new s({fr:{ACTIVATE_HEADING:"Activation du compte",TOKEN_EXPIRED:"Votre lien d'activation du compte a expiré.",ACTIVATE:"Activer"},en:{ACTIVATE_HEADING:"Account Activation",TOKEN_EXPIRED:"Your account activation link expired.",ACTIVATE:"Activate"},es:{ACTIVATE_HEADING:"Activación de cuenta",TOKEN_EXPIRED:"Su enlace de activación de cuenta ha expirado.",ACTIVATE:"Activar"}});e(O);const R=w.object({password:w.string().min(i.PASSWORD_MIN_LENGTH,{message:o.PASSWORD_ERROR}),confirmPassword:w.string()}).refine((s=>s.password===s.confirmPassword),{path:["confirmPassword"],message:o.PASSWORDS_DONT_MATCH}),y=()=>{const s=r.c(38),e=j(),{setUser:i,setUserLoaded:w}=a(),[y,H]=A.useState(""),[V,W]=A.useState(""),[G,L]=A.useState(""),[k,F]=A.useState(!1),[M,q]=A.useState(!1),[K,U]=A.useState(!1),[X,z]=A.useState(!1),[B,J]=A.useState(!1);let Y;s[0]===Symbol.for("react.memo_cache_sentinel")?(Y={resolver:f(R),mode:"onSubmit"},s[0]=Y):Y=s[0];const{register:Q,handleSubmit:Z,formState:$,setError:ss,clearErrors:es}=E(Y),{errors:rs,isSubmitting:as}=$;let ts;s[1]!==V||s[2]!==e||s[3]!==i||s[4]!==w||s[5]!==G||s[6]!==y?(ts=async s=>{const{password:r}=s;try{const s={userId:y,token:G,password:r};if(200===await n(s)){const s=await c({email:V,password:r});if(200===s.status){const r=await d(s.data._id);J(!0),i(r),w(!0),200===await m(y)?e("/"):l()}else l()}else l()}catch(a){l(a)}},s[1]=V,s[2]=e,s[3]=i,s[4]=w,s[5]=G,s[6]=y,s[7]=ts):ts=s[7];const os=ts;let is;s[8]!==V?(is=async()=>{try{200===await p(V,!1)?u(o.ACTIVATION_EMAIL_SENT):l()}catch(s){l(s)}},s[8]=V,s[9]=is):is=s[9];const ns=is;let cs;s[10]!==ss?(cs=async s=>{if(s)U(!0);else{const s=new URLSearchParams(window.location.search);if(s.has("u")&&s.has("e")&&s.has("t")){const r=s.get("u"),a=s.get("e"),t=s.get("t");if(r&&a&&t)try{const e=await h(r,a,t);if(200===e){if(H(r),W(a),L(t),F(!0),s.has("r")){const e="true"===s.get("r");z(e)}}else 204===e?(W(a),q(!0)):U(!0)}catch(e){const s=e;console.error(s),ss("root",{})}else U(!0)}else U(!0)}},s[10]=ss,s[11]=cs):cs=s[11];const ds=cs;let ms,ls,ps,us,hs;return s[12]!==ns||s[13]!==e||s[14]!==M?(ms=M&&t.jsx("div",{className:"resend",children:t.jsxs(C,{className:"resend-form",elevation:10,children:[t.jsx("h1",{children:O.ACTIVATE_HEADING}),t.jsxs("div",{className:"resend-form-content",children:[t.jsx("span",{children:O.TOKEN_EXPIRED}),t.jsx(v,{type:"button",variant:"contained",size:"small",className:"btn-primary btn-resend",onClick:ns,children:x.RESEND}),t.jsx("p",{className:"go-to-home",children:t.jsx(v,{variant:"text",onClick:()=>e("/"),className:"btn-lnk",children:o.GO_TO_HOME})})]})]})}),s[12]=ns,s[13]=e,s[14]=M,s[15]=ms):ms=s[15],s[16]!==es||s[17]!==rs.confirmPassword||s[18]!==rs.password||s[19]!==Z||s[20]!==as||s[21]!==e||s[22]!==os||s[23]!==Q||s[24]!==X||s[25]!==k?(ls=k&&t.jsx("div",{className:"activate",children:t.jsxs(C,{className:"activate-form",elevation:10,children:[t.jsx("h1",{children:X?T.RESET_PASSWORD_HEADING:O.ACTIVATE_HEADING}),t.jsxs("form",{onSubmit:Z(os),children:[t.jsxs(P,{fullWidth:!0,margin:"dense",error:!!rs.password,children:[t.jsx(g,{className:"required",children:S.NEW_PASSWORD}),t.jsx(b,{...Q("password"),type:"password",required:!0,autoComplete:"new-password",onChange:()=>es()}),t.jsx(D,{error:!!rs.password,children:rs.password?.message||""})]}),t.jsxs(P,{fullWidth:!0,margin:"dense",error:!!rs.confirmPassword,children:[t.jsx(g,{className:"required",children:o.CONFIRM_PASSWORD}),t.jsx(b,{...Q("confirmPassword"),type:"password",required:!0,autoComplete:"new-password",onChange:()=>es()}),t.jsx(D,{error:!!rs.confirmPassword,children:rs.confirmPassword?.message||""})]}),t.jsxs("div",{className:"buttons",children:[t.jsx(v,{type:"submit",className:"btn-primary btn-margin btn-margin-bottom",variant:"contained",disabled:as,children:X?o.UPDATE:O.ACTIVATE}),t.jsx(v,{variant:"outlined",color:"primary",className:"btn-margin-bottom",onClick:()=>e("/"),children:o.CANCEL})]})]})]})}),s[16]=es,s[17]=rs.confirmPassword,s[18]=rs.password,s[19]=Z,s[20]=as,s[21]=e,s[22]=os,s[23]=Q,s[24]=X,s[25]=k,s[26]=ls):ls=s[26],s[27]!==B||s[28]!==K?(ps=!B&&K&&t.jsx(I,{hideHeader:!0}),s[27]=B,s[28]=K,s[29]=ps):ps=s[29],s[30]!==rs.root?(us=rs.root&&t.jsx(_,{}),s[30]=rs.root,s[31]=us):us=s[31],s[32]!==ds||s[33]!==ms||s[34]!==ls||s[35]!==ps||s[36]!==us?(hs=t.jsxs(N,{onLoad:ds,strict:!1,children:[ms,ls,ps,us]}),s[32]=ds,s[33]=ms,s[34]=ls,s[35]=ps,s[36]=us,s[37]=hs):hs=s[37],hs};export{y as default};
