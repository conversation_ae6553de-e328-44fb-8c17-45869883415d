import{b as e,s,j as a,F as t,a as r,e as o,J as i,K as n,z as l,B as u}from"../entries/index-xsXxT3-W.js";import{d as c,r as m}from"./router-BtYqujaw.js";import{L as d}from"./Layout-DaeN7D4t.js";import{s as p}from"./create-country-DGdOfzjM.js";import{s as f}from"./suppliers-Dwe__EFi.js";import{b as j,v,u as h}from"./CountryService-CPWL_VJK.js";import g from"./NoMatch-DMPclUW6.js";import{E as x}from"./Error-FiYP5RHa.js";import{S as y}from"./SimpleBackdrop-CqsJhYJ4.js";import{S as N}from"./SupplierBadge-ehv63WPF.js";import{P as S}from"./Paper-C-atefOs.js";import{F as U,a as A,I as C}from"./InputLabel-C8rcdOGQ.js";import{I as E}from"./Input-D1AdR9CM.js";import{F as T}from"./FormHelperText-DDZ4BMA4.js";import{B as P}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Czag2Ija.js";import"./useSlot-DiTut-u0.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const _=new e({fr:{UPDATE_COUNTRY:"Modification du pays",COUNTRY_UPDATED:"Pays modifié avec succès."},en:{UPDATE_COUNTRY:"Country update",COUNTRY_UPDATED:"Country updated successfully."},es:{UPDATE_COUNTRY:"Actualización del país",COUNTRY_UPDATED:"País actualizado correctamente."}});s(_);const b=()=>{const e=c(),[s,b]=m.useState(),[D,w]=m.useState(!1),[L,R]=m.useState(!1),[O,I]=m.useState([]),[Y,B]=m.useState([]),[F,z]=m.useState(!1),[G,k]=m.useState(!1),[H,W]=m.useState(),[M,q]=m.useState(!1),J=()=>{let e=!1;if(!H||!H.values)return l(),e;for(let s=0;s<O.length;s+=1)if(O[s].name!==H.values[s].value){e=!0;break}return q(e),e};return a.jsxs(d,{onLoad:async e=>{if(e&&e.verified){R(!0),b(e);const a=new URLSearchParams(window.location.search);if(a.has("loc")){const r=a.get("loc");if(r&&""!==r)try{const s=await j(r);if(!t(e)&&e._id!==s.supplier?._id)return R(!1),void z(!0);if(s&&s.values){o._LANGUAGES.forEach((e=>{s.values&&!s.values.some((s=>s.language===e.code))&&s.values.push({language:e.code,value:""})}));const e=s.values.map((e=>({language:e.language||"",name:e.value||""})));W(s),I(e),w(!0),R(!1)}else R(!1),z(!0)}catch(s){l(s),R(!1),k(!0),w(!1)}else R(!1),z(!0)}else R(!1),z(!0)}},strict:!0,children:[!G&&!F&&H&&H.values&&a.jsx("div",{className:"update-country",children:a.jsxs(S,{className:"country-form country-form-wrapper",elevation:10,style:D?{}:{display:"none"},children:[a.jsx("h1",{className:"country-form-title",children:_.UPDATE_COUNTRY}),a.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(!H||!H.values)return void l();if(!J())return;let e=!0;const s=i(Y);for(let a=0;a<Y.length;a+=1)s[a]=!1;for(let a=0;a<O.length;a+=1){const t=O[a];if(t.name!==H.values[a].value){const r=200===await v(t);e=e&&r,r||(s[a]=!0)}}if(B(s),e)if(200===await h(H._id,O)){const e=i(H);for(let s=0;s<O.length;s+=1){const a=O[s];e.values[s].value=a.name}W(e),u(_.COUNTRY_UPDATED)}else R(!1),l()}catch(s){l(s)}},children:[t(s)&&H.supplier&&a.jsxs(U,{fullWidth:!0,margin:"dense",children:[a.jsx(A,{children:f.SUPPLIER}),a.jsx(N,{supplier:H.supplier})]}),H.values.map(((e,s)=>a.jsxs(U,{fullWidth:!0,margin:"dense",children:[a.jsx(C,{className:"required",children:`${r.NAME} (${o._LANGUAGES.filter((s=>s.code===e.language))[0].label})`}),a.jsx(E,{type:"text",value:O[s]&&O[s].name||"",error:Y[s],required:!0,onChange:e=>{const a=i(O);a[s].name=e.target.value;const t=n(Y);t[s]=!1,J(),I(a),B(t)},autoComplete:"off"}),a.jsx(T,{error:Y[s],children:Y[s]&&p.INVALID_COUNTRY||""})]},e.language))),a.jsxs("div",{className:"buttons",children:[a.jsx(P,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:!M,children:r.SAVE}),a.jsx(P,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>e("/countries"),children:r.CANCEL})]})]})]})}),L&&a.jsx(y,{text:r.PLEASE_WAIT}),G&&a.jsx(x,{}),F&&a.jsx(g,{hideHeader:!0})]})};export{b as default};
