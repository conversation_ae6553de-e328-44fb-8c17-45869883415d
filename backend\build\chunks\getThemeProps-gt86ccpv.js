import{g as e}from"./vendor-dblfw9z9.js";import{at as n}from"../entries/index-CEzJO5Xy.js";var r,t,o,s,p,a={exports:{}};function c(){return t?r:(t=1,r="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}function i(){if(s)return o;s=1;var e=c();function n(){}function r(){}return r.resetWarningCache=n,o=function(){function t(n,r,t,o,s,p){if(p!==e){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function o(){return t}t.isRequired=t;var s={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:o,element:t,elementType:t,instanceOf:o,node:t,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:r,resetWarningCache:n};return s.PropTypes=s,s}}function u(){return p||(p=1,a.exports=i()()),a.exports}const f=e(u());function m(e){const{theme:r,name:t,props:o}=e;return r&&r.components&&r.components[t]&&r.components[t].defaultProps?n(r.components[t].defaultProps,o):o}export{f as P,m as g};
