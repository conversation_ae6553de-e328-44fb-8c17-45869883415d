import{k as s}from"../entries/index-xsXxT3-W.js";function t(t,e){if(!t)return e;function r(s,t){const e={};return Object.keys(t).forEach((r=>{(function(s,t){const e=s.charCodeAt(2);return"o"===s[0]&&"n"===s[1]&&e>=65&&e<=90&&"function"==typeof t})(r,t[r])&&"function"==typeof s[r]&&(e[r]=(...e)=>{s[r](...e),t[r](...e)})})),e}if("function"==typeof t||"function"==typeof e)return n=>{const a="function"==typeof e?e(n):e,o="function"==typeof t?t({...n,...a}):t,y=s(n?.className,a?.className,o?.className),c=r(o,a);return{...a,...o,...c,...!!y&&{className:y},...a?.style&&o?.style&&{style:{...a.style,...o.style}},...a?.sx&&o?.sx&&{sx:[...Array.isArray(a.sx)?a.sx:[a.sx],...Array.isArray(o.sx)?o.sx:[o.sx]]}}};const n=e,a=r(t,n),o=s(n?.className,t?.className);return{...e,...t,...a,...!!o&&{className:o},...n?.style&&t?.style&&{style:{...n.style,...t.style}},...n?.sx&&t?.sx&&{sx:[...Array.isArray(n.sx)?n.sx:[n.sx],...Array.isArray(t.sx)?t.sx:[t.sx]]}}}export{t as m};
