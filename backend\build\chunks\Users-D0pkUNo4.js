import{b as e,s,c as t,ab as a,a as r,J as i,j as o,ac as l,e as n,ad as c,z as m,R as d,G as u,ae as p,F as h,U as j}from"../entries/index-CEzJO5Xy.js";import{r as f,d as x}from"./router-BtYqujaw.js";import{L as S}from"./Layout-BQBjg4Lf.js";import{S as E}from"./Search-CuqoyoO6.js";import{s as g}from"./user-list-4CESz5z_.js";import{D as v,a as N,b}from"./Grow-CjOKj0i1.js";import{D as C}from"./DialogTitle-BZXwroUN.js";import{B as _}from"./Button-DGZYUY3P.js";import{B as y}from"./Badge-B3LKl4T2.js";import{T as k}from"./Tooltip-BkJF6Mu0.js";import{B as I}from"./Box-CHHh9iS3.js";import{V as w}from"./Check-D745pofy.js";import{L as R}from"./Link-B-UCzRRJ.js";import{I as T}from"./IconButton-CnBvmeAK.js";import{D}from"./Delete-CnqjtpsJ.js";import{E as L}from"./Edit-DIF9Bumd.js";import{A}from"./Avatar-Dix3YM8x.js";import{A as U}from"./AccountCircle-khVEeiad.js";import{D as M}from"./DataGrid-DM8uCtAG.js";import"./vendor-dblfw9z9.js";import"./zod-4O8Zwsja.js";import"./TextField-BAse--ht.js";import"./useFormControl-B7jXtRD7.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./Paper-CcwAvfvc.js";import"./OutlinedInput-g8mR4MM3.js";import"./InputLabel-BbcIE26O.js";import"./isHostComponent-DR4iSCFs.js";import"./ownerWindow-ChLfdzZL.js";import"./Input-BQdee9z7.js";import"./FormHelperText-DFSsjBsL.js";import"./Menu-ZU0DMgjT.js";import"./mergeSlotProps-Cay5TZBz.js";import"./Clear-BpXDeTL8.js";import"./Search-BNrZEqND.js";import"./getThemeProps-gt86ccpv.js";import"./Switch-BWPUOSX1.js";import"./SwitchBase-BIeqtL5F.js";import"./MenuItem-suKfXYI2.js";import"./listItemTextClasses-DFwCkkgK.js";import"./Toolbar-CNUITE_K.js";import"./KeyboardArrowRight-BV-h2cWM.js";import"./Chip-CAtDqtgp.js";import"./Autocomplete-CviOU_ku.js";import"./ListItemText-DBn_RuMq.js";import"./Checkbox-CDqupZJG.js";const P=new e({fr:{NEW_USER:"Nouvel utilisateur"},en:{NEW_USER:"New user"},es:{NEW_USER:"Nuevo usuario"},ar:{NEW_USER:"مستخدم جديد"}});s(P);const G=e=>{const s=t.c(17),{className:n,onChange:c}=e,m=a(),[d,u]=f.useState(m.map(F)),[p,h]=f.useState(!0);let j;s[0]===Symbol.for("react.memo_cache_sentinel")?(j=[],s[0]=j):j=s[0];const x=f.useRef(j);let S,E;s[1]===Symbol.for("react.memo_cache_sentinel")?(S=()=>{x.current.forEach(z)},E=[],s[1]=S,s[2]=E):(S=s[1],E=s[2]),f.useEffect(S,E);const g=e=>{const s=e.currentTarget.getAttribute("data-value");if(e.currentTarget.checked)d.push(s),d.length===m.length&&h(!0);else{const e=d.findIndex((e=>e===s));d.splice(e,1),0===d.length&&h(!1)}u(d),c&&c(i(d))};let v;s[3]!==g?(v=e=>{const s=e.currentTarget.previousSibling;s.checked=!s.checked;const t=e;t.currentTarget=s,g(t)},s[3]=g,s[4]=v):v=s[4];const N=v,b=()=>{if(p)x.current.forEach(B),h(!1),u([]);else{x.current.forEach(W);const e=m.map(O);h(!0),u(e),c&&c(i(e))}},C=(n?`${n} `:"")+"user-type-filter";let _;s[5]!==g||s[6]!==N?(_=(e,s)=>o.jsxs("li",{children:[o.jsx("input",{ref:e=>{x.current[s]=e},type:"checkbox","data-value":e.value,className:"user-type-checkbox",onChange:g}),o.jsx("span",{onClick:N,className:`bs bs-${e.value}`,role:"button",tabIndex:0,children:l(e.value)})]},e.value),s[5]=g,s[6]=N,s[7]=_):_=s[7];const y=m.map(_);let k;s[8]!==y?(k=o.jsx("ul",{className:"user-type-list",children:y}),s[8]=y,s[9]=k):k=s[9];const I=p?r.UNCHECK_ALL:r.CHECK_ALL;let w,R;return s[10]!==b||s[11]!==I?(w=o.jsx("div",{className:"filter-actions",children:o.jsx("span",{onClick:b,className:"uncheckall",role:"button",tabIndex:0,children:I})}),s[10]=b,s[11]=I,s[12]=w):w=s[12],s[13]!==w||s[14]!==C||s[15]!==k?(R=o.jsxs("div",{className:C,children:[k,w]}),s[13]=w,s[14]=C,s[15]=k,s[16]=R):R=s[16],R};function F(e){return e.value}function z(e){e.checked=!0}function B(e){e.checked=!1}function W(e){e.checked=!0}function O(e){return e.value}const H=({types:e,keyword:s,user:t,hideDesktopColumns:a,checkboxSelection:h,onLoad:j})=>{const S=x(),[E,P]=f.useState(),[G,F]=f.useState(0),[z,B]=f.useState(n.PAGE_SIZE),[W,O]=f.useState([]),[H,K]=f.useState([]),[$,Z]=f.useState(0),[V,J]=f.useState(!0),[Q,Y]=f.useState(""),[X,q]=f.useState([]),[ee,se]=f.useState(!1),[te,ae]=f.useState(),[re,ie]=f.useState(s),[oe,le]=f.useState(!1),[ne,ce]=f.useState({pageSize:n.PAGE_SIZE,page:0});f.useEffect((()=>{F(ne.page),B(ne.pageSize)}),[ne]);const me=async(e,s)=>{try{if(s&&te){J(!0);const t={user:s&&s._id||"",types:te},a=await c(t,re||"",e+1,z),r=a&&a.length>0?a[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!r)return void m();const i=Array.isArray(r.pageInfo)&&r.pageInfo.length>0?r.pageInfo[0].totalRecords:0,o=r.resultData;K(o),Z(i),j&&j({rows:r.resultData,rowCount:i})}}catch(t){m(t)}finally{J(!1)}};f.useEffect((()=>{ae(e)}),[e]),f.useEffect((()=>{ie(s||"")}),[s]),f.useEffect((()=>{te&&me(G,E)}),[G]),f.useEffect((()=>{if(te)if(0===G)me(0,E);else{const e=i(ne);e.page=0,ce(e)}}),[z]);const de=e=>{const s=[{field:"fullName",headerName:r.USER,flex:1,renderCell:({row:e,value:s})=>{const t=e;let a;if(t.avatar)if(t.type===d.Supplier)a=o.jsx("img",{src:u(n.CDN_USERS,e.avatar),alt:e.fullName});else{const e=t.avatar?t.avatar.startsWith("http")?t.avatar:u(n.CDN_USERS,t.avatar):"",s=o.jsx(A,{src:e,className:"avatar-small"});a=t.verified?o.jsx(y,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:o.jsx(k,{title:r.VERIFIED,children:o.jsx(I,{borderRadius:"50%",className:"user-avatar-verified-small",children:o.jsx(w,{className:"user-avatar-verified-icon-small"})})}),children:s}):o.jsx(y,{overlap:"circular",children:s})}else{const e=o.jsx(U,{className:"avatar-small",color:"disabled"});a=t.verified?o.jsx(y,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:o.jsx(k,{title:r.VERIFIED,children:o.jsx(I,{borderRadius:"50%",className:"user-avatar-verified-small",children:o.jsx(w,{className:"user-avatar-verified-icon-small"})})}),children:e}):o.jsx(y,{overlap:"circular",children:e})}return o.jsxs(R,{href:`/user?u=${e._id}`,className:"us-user",children:[o.jsx("span",{className:"us-avatar",children:a}),o.jsx("span",{children:s})]})},valueGetter:e=>e},{field:"email",headerName:r.EMAIL,flex:1,valueGetter:e=>e},{field:"phone",headerName:r.PHONE,flex:1,valueGetter:e=>e},{field:"type",headerName:r.TYPE,flex:1,renderCell:({value:e})=>o.jsx("span",{className:`bs us-${e?.toLowerCase()}`,children:l(e)}),valueGetter:e=>e},{field:"action",headerName:"",sortable:!1,disableColumnMenu:!0,renderCell:({row:s})=>{const t=s;return e.type===d.Admin||t.supplier===e._id?o.jsxs("div",{children:[o.jsx(k,{title:r.UPDATE,children:o.jsx(T,{onClick:()=>S(`/update-user?u=${s._id}`),children:o.jsx(L,{})})}),o.jsx(k,{title:r.DELETE,children:o.jsx(T,{onClick:e=>{e.stopPropagation(),Y(s._id||""),se(!0)},children:o.jsx(D,{})})})]}):o.jsx(o.Fragment,{})},renderHeader:()=>X.length>0?o.jsxs("div",{children:[o.jsx("div",{style:{width:40,display:"inline-block"}}),o.jsx(k,{title:g.DELETE_SELECTION,children:o.jsx(T,{onClick:()=>{se(!0)},children:o.jsx(D,{})})})]}):o.jsx(o.Fragment,{})}];return a&&s.splice(1,3),s};return f.useEffect((()=>{if(t&&te){P(t);const e=de(t);if(O(e),0===G)me(0,t);else{const e=i(ne);e.page=0,ce(e)}}}),[t,te,re]),f.useEffect((()=>{if(E&&oe){const e=de(E);O(e),le(!1)}}),[E,X,oe]),o.jsxs("div",{className:"us-list",children:[E&&W.length>0&&o.jsx(M,{checkboxSelection:h,getRowId:e=>e._id,columns:W,rows:H,rowCount:$,loading:V,initialState:{pagination:{paginationModel:{pageSize:n.PAGE_SIZE}}},pageSizeOptions:[n.PAGE_SIZE,50,100],pagination:!0,paginationMode:"server",paginationModel:ne,onPaginationModelChange:ce,onRowSelectionModelChange:e=>{q(Array.from(new Set(e.ids)).map((e=>e.toString()))),le(!0)},getRowClassName:e=>e.row.blacklisted?"us-blacklisted":"",disableRowSelectionOnClick:!0}),o.jsxs(v,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ee,children:[o.jsx(C,{className:"dialog-header",children:r.CONFIRM_TITLE}),o.jsx(N,{className:"dialog-content",children:0===X.length?g.DELETE_USER:g.DELETE_USERS}),o.jsxs(b,{className:"dialog-actions",children:[o.jsx(_,{onClick:()=>{se(!1),Y("")},variant:"contained",className:"btn-secondary",children:r.CANCEL}),o.jsx(_,{onClick:async()=>{try{const e=X.length>0?X:[Q];se(!1),200===await p(e)?X.length>0?K(H.filter((e=>!X.includes(e._id)))):K(H.filter((e=>e._id!==Q))):m()}catch(e){m(e)}finally{J(!1)}},variant:"contained",color:"error",children:r.DELETE})]})]})]})},K=()=>{const e=t.c(11),s=x(),[r,i]=f.useState(),[l,c]=f.useState(!1),[m,d]=f.useState(),[u,p]=f.useState("");let g;e[0]===Symbol.for("react.memo_cache_sentinel")?(g=e=>{d(e)},e[0]=g):g=e[0];const v=g;let N;e[1]===Symbol.for("react.memo_cache_sentinel")?(N=e=>{p(e)},e[1]=N):N=e[1];const b=N;let C;e[2]===Symbol.for("react.memo_cache_sentinel")?(C=e=>{const s=h(e),t=s?a().map($):[j.Supplier,j.User];i(e),c(s),d(t)},e[2]=C):C=e[2];const y=C;let k,I;return e[3]!==l||e[4]!==u||e[5]!==s||e[6]!==m||e[7]!==r?(k=r&&o.jsxs("div",{className:"users",children:[o.jsx("div",{className:"col-1",children:o.jsxs("div",{className:"div.col-1-container",children:[o.jsx(E,{onSubmit:b,className:"search"}),l&&o.jsx(G,{className:"user-type-filter",onChange:v}),o.jsx(_,{variant:"contained",className:"btn-primary new-user",size:"small",onClick:()=>s("/create-user"),children:P.NEW_USER})]})}),o.jsx("div",{className:"col-2",children:o.jsx(H,{user:r,types:m,keyword:u,checkboxSelection:!n.isMobile&&l,hideDesktopColumns:n.isMobile})})]}),e[3]=l,e[4]=u,e[5]=s,e[6]=m,e[7]=r,e[8]=k):k=e[8],e[9]!==k?(I=o.jsx(S,{onLoad:y,strict:!0,children:k}),e[9]=k,e[10]=I):I=e[10],I};function $(e){return e.value}export{K as default};
