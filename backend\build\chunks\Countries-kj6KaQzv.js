import{b as e,s,u as t,e as o,j as r,F as a,a as i,z as n,H as c,c as l}from"../entries/index-CEzJO5Xy.js";import{d as m,r as d}from"./router-BtYqujaw.js";import{L as u}from"./Layout-BQBjg4Lf.js";import{S as p}from"./Search-CuqoyoO6.js";import{g as N,c as j,d as E}from"./CountryService-DnJKuIXr.js";import{P as T}from"./Pager-C0zDCFUN.js";import{P as x}from"./Progress-CNEDa8ss.js";import{C as h,a as C}from"./ArrowForwardIos-BMce9t8T.js";import{T as I}from"./Backdrop-Bzn12VyM.js";import{L as _}from"./Menu-ZU0DMgjT.js";import{L as y}from"./ListItem-D1VHRhQp.js";import{L as O}from"./ListItemAvatar-Bv6onK36.js";import{A as S}from"./Avatar-Dix3YM8x.js";import{C as f}from"./Flag-BR6CpE1z.js";import{L}from"./ListItemText-DBn_RuMq.js";import{T as D}from"./Tooltip-BkJF6Mu0.js";import{I as A}from"./IconButton-CnBvmeAK.js";import{E as R}from"./Edit-DIF9Bumd.js";import{D as g}from"./Delete-CnqjtpsJ.js";import{D as w,a as Y,b}from"./Grow-CjOKj0i1.js";import{D as U}from"./DialogTitle-BZXwroUN.js";import{B as P}from"./Button-DGZYUY3P.js";import{I as v}from"./InfoBox-Csm94Gbd.js";import"./vendor-dblfw9z9.js";import"./zod-4O8Zwsja.js";import"./TextField-BAse--ht.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-CtA82Ni6.js";import"./OutlinedInput-g8mR4MM3.js";import"./InputLabel-BbcIE26O.js";import"./isHostComponent-DR4iSCFs.js";import"./ownerWindow-ChLfdzZL.js";import"./Input-BQdee9z7.js";import"./FormHelperText-DFSsjBsL.js";import"./Clear-BpXDeTL8.js";import"./Search-BNrZEqND.js";import"./Paper-CcwAvfvc.js";import"./mergeSlotProps-Cay5TZBz.js";import"./listItemTextClasses-DFwCkkgK.js";import"./Info-C_WcR51V.js";const M=new e({fr:{NEW_COUNTRY:"Nouveau pays",DELETE_COUNTRY:"Êtes-vous sûr de vouloir supprimer ce pays ?",CANNOT_DELETE_COUNTRY:"Ce pays ne peut pas être supprimé car il est lié à des lieux.",EMPTY_LIST:"Pas de pays.",COUNTRY:"pays",COUNTRIES:"pays"},en:{NEW_COUNTRY:"New country",DELETE_COUNTRY:"Are you sure you want to delete this country?",CANNOT_DELETE_COUNTRY:"This country cannot be deleted because it is related to locations.",EMPTY_LIST:"No countries.",COUNTRY:"country",COUNTRIES:"countries"},es:{NEW_COUNTRY:"Nuevo país",DELETE_COUNTRY:"¿Estás seguro de que quieres eliminar este país?",CANNOT_DELETE_COUNTRY:"Este país no puede ser eliminado porque está relacionado con ubicaciones.",EMPTY_LIST:"No hay países.",COUNTRY:"país",COUNTRIES:"países"},ar:{NEW_COUNTRY:"بلد جديد",DELETE_COUNTRY:"هل أنت متأكد من أنك تريد حذف هذا البلد؟",CANNOT_DELETE_COUNTRY:"لا يمكن حذف هذا البلد لأنه مرتبط بمواقع.",EMPTY_LIST:"لا توجد بلدان.",COUNTRY:"بلد",COUNTRIES:"بلدان"}});s(M);const F=({keyword:e,onLoad:s,onDelete:l})=>{const u=m(),{user:p}=t(),[v,F]=d.useState(e),[G,k]=d.useState(!0),[W,H]=d.useState(!1),[q,B]=d.useState(!1),[z,Z]=d.useState([]),[$,K]=d.useState(0),[J,Q]=d.useState(0),[V,X]=d.useState(1),[ee,se]=d.useState(!1),[te,oe]=d.useState(!1),[re,ae]=d.useState(""),[ie,ne]=d.useState(-1),ce=async(e,t)=>{try{H(!0);const r=await N(t||"",e,o.PAGE_SIZE),a=r&&r.length>0?r[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!a)return void n();const i=Array.isArray(a.pageInfo)&&a.pageInfo.length>0?a.pageInfo[0].totalRecords:0;let l=[];l=o.PAGINATION_MODE===c.PAGINATION_MODE.INFINITE_SCROLL||o.isMobile?1===e?a.resultData:[...z,...a.resultData]:a.resultData,Z(l),K((e-1)*o.PAGE_SIZE+l.length),Q(i),B(a.resultData.length>0),((o.PAGINATION_MODE===c.PAGINATION_MODE.INFINITE_SCROLL||o.isMobile)&&1===e||o.PAGINATION_MODE===c.PAGINATION_MODE.CLASSIC&&!o.isMobile)&&window.scrollTo(0,0),s&&s({rows:a.resultData,rowCount:i})}catch(r){n(r)}finally{H(!1),k(!1)}};d.useEffect((()=>{e!==v&&ce(1,e),F(e||"")}),[e,v]),d.useEffect((()=>{ce(V,v)}),[V]),d.useEffect((()=>{if(o.isMobile){const e=document.querySelector("body");e&&(e.onscroll=()=>{q&&!W&&window.scrollY>0&&window.scrollY+window.innerHeight+o.INFINITE_SCROLL_OFFSET>=document.body.scrollHeight&&(H(!0),X(V+1))})}}),[q,W,V,v]);const le=async e=>{try{const s=e.currentTarget.getAttribute("data-id"),t=Number(e.currentTarget.getAttribute("data-index")),o=await j(s);204===o?(se(!0),ae(s),ne(t)):200===o?oe(!0):n()}catch(s){n(s)}};return p&&r.jsxs(r.Fragment,{children:[r.jsxs("section",{className:"country-list",children:[0===z.length?!G&&!W&&r.jsx(h,{variant:"outlined",className:"empty-list",children:r.jsx(C,{children:r.jsx(I,{color:"textSecondary",children:M.EMPTY_LIST})})}):r.jsx(_,{className:"country-list-items",children:z.map(((e,s)=>r.jsxs(y,{className:"country-list-item",secondaryAction:(a(p)||e.supplier?._id===p._id)&&r.jsxs("div",{children:[r.jsx(D,{title:i.UPDATE,children:r.jsx(A,{edge:"end",onClick:()=>u(`/update-country?loc=${e._id}`),children:r.jsx(R,{})})}),r.jsx(D,{title:i.DELETE,children:r.jsx(A,{edge:"end","data-id":e._id,"data-index":s,onClick:le,children:r.jsx(g,{})})})]}),children:[r.jsx(O,{children:r.jsx(S,{children:r.jsx(f,{})})}),r.jsx(L,{primary:r.jsx(I,{className:"country-title",children:e.name})})]},e._id)))}),r.jsxs(w,{disableEscapeKeyDown:!0,maxWidth:"xs",open:te,children:[r.jsx(U,{className:"dialog-header",children:i.INFO}),r.jsx(Y,{children:M.CANNOT_DELETE_COUNTRY}),r.jsx(b,{className:"dialog-actions",children:r.jsx(P,{onClick:()=>{oe(!1)},variant:"contained",className:"btn-secondary",children:i.CLOSE})})]}),r.jsxs(w,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ee,children:[r.jsx(U,{className:"dialog-header",children:i.CONFIRM_TITLE}),r.jsx(Y,{children:M.DELETE_COUNTRY}),r.jsxs(b,{className:"dialog-actions",children:[r.jsx(P,{onClick:()=>{se(!1),ae(""),ne(-1)},variant:"contained",className:"btn-secondary",children:i.CANCEL}),r.jsx(P,{onClick:async()=>{try{if(""!==re&&ie>-1)if(H(!0),se(!1),200===await E(re)){const e=$-1;z.splice(ie,1),Z(z),K(e),Q(J-1),ae(""),ne(-1),H(!1),l&&l(e)}else n(),ae(""),ne(-1),H(!1);else n(),se(!1),ae(""),ne(-1)}catch(e){n(e)}},variant:"contained",color:"error",children:i.DELETE})]})]}),W&&r.jsx(x,{})]}),!o.isMobile&&r.jsx(T,{page:V,pageSize:o.PAGE_SIZE,rowCount:$,totalRecords:J,onNext:()=>X(V+1),onPrevious:()=>X(V-1)})]})},G=()=>{const e=l.c(17),s=m(),[t,o]=d.useState(""),[a,i]=d.useState(-1);let n;e[0]===Symbol.for("react.memo_cache_sentinel")?(n=e=>{o(e)},e[0]=n):n=e[0];const c=n;let N;e[1]===Symbol.for("react.memo_cache_sentinel")?(N=e=>{e&&i(e.rowCount)},e[1]=N):N=e[1];const j=N;let E;e[2]===Symbol.for("react.memo_cache_sentinel")?(E=e=>{i(e)},e[2]=E):E=e[2];const T=E,x=k;let h,C,I,_,y,O;return e[3]===Symbol.for("react.memo_cache_sentinel")?(h=r.jsx(p,{className:"search",onSubmit:c}),e[3]=h):h=e[3],e[4]!==s||e[5]!==a?(C=a>-1&&r.jsx(P,{variant:"contained",className:"btn-primary new-country",size:"small",onClick:()=>s("/create-country"),children:M.NEW_COUNTRY}),e[4]=s,e[5]=a,e[6]=C):C=e[6],e[7]!==a?(I=a>0&&r.jsx(v,{value:`${a} ${a>1?M.COUNTRIES:M.COUNTRY}`,className:"country-count"}),e[7]=a,e[8]=I):I=e[8],e[9]!==C||e[10]!==I?(_=r.jsx("div",{className:"col-1",children:r.jsxs("div",{className:"col-1-container",children:[h,C,I]})}),e[9]=C,e[10]=I,e[11]=_):_=e[11],e[12]!==t?(y=r.jsx("div",{className:"col-2",children:r.jsx(F,{keyword:t,onLoad:j,onDelete:T})}),e[12]=t,e[13]=y):y=e[13],e[14]!==_||e[15]!==y?(O=r.jsx(u,{onLoad:x,strict:!0,children:r.jsxs("div",{className:"countries",children:[_,y]})}),e[14]=_,e[15]=y,e[16]=O):O=e[16],O};function k(){}export{G as default};
