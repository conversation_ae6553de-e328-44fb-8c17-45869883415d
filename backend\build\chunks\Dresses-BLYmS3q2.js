import{j as e,c as t,S as s,a as r,T as c,V as n,W as a,X as l,e as i,Y as o,J as d,z as u,h as m,F as h}from"../entries/index-xsXxT3-W.js";import{r as j,d as p}from"./router-BtYqujaw.js";import{h as x}from"./SupplierService-9DC5V5ZJ.js";import{L as S}from"./Layout-DaeN7D4t.js";import{D as f}from"./DressList-qxSdI7YO.js";import{I as v,F as g}from"./InputLabel-C8rcdOGQ.js";import{M as b}from"./MenuItem-P0BnGnrT.js";import{S as _,I as k,T as N}from"./TextField-D_yQOTzE.js";import{s as T}from"./cars-xqBbVU4C.js";import{A as E}from"./Accordion-Z5bnZGK6.js";import{I as L}from"./IconButton-CxOCoGF3.js";import{S as C}from"./Search-CKOds7xB.js";import{C as I}from"./Clear-CDOl64hX.js";import{B as y}from"./Button-BeKLLPpp.js";import{c as A}from"./Grow-Cp8xsNYl.js";import"./vendor-dblfw9z9.js";import"./DressService-DkS6e_O5.js";import"./Pager-B4DUIA8f.js";import"./ArrowForwardIos-BCaVe-sv.js";import"./Paper-C-atefOs.js";import"./SimpleBackdrop-CqsJhYJ4.js";import"./Backdrop-Czag2Ija.js";import"./useSlot-DiTut-u0.js";import"./SupplierBadge-ehv63WPF.js";import"./Tooltip-CKMkVqOx.js";import"./Straighten-Isz6BfHc.js";import"./Check-BO6X9Q-4.js";import"./Visibility-D3efFHY1.js";import"./Edit-Bc0UCPtn.js";import"./Delete-BfnPAJno.js";import"./Info-CNP9gYBt.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";import"./Menu-C_-X8cS7.js";import"./mergeSlotProps-DEridHif.js";import"./listItemTextClasses-BcbgzvlE.js";import"./OutlinedInput-BX8yFQbF.js";import"./Input-D1AdR9CM.js";import"./FormHelperText-DDZ4BMA4.js";const R=A(e.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),D=n=>{const a=t.c(11),{className:l,onChange:i}=n,[o,d]=j.useState("");let u;a[0]!==i?(u=e=>{const t=e.target.value;d(t),i&&i(t)},a[0]=i,a[1]=u):u=a[1];const m=u;let h,p,x,S,f;return a[2]===Symbol.for("react.memo_cache_sentinel")?(h=e.jsx(v,{id:"type-label",children:s.DRESS_TYPE}),a[2]=h):h=a[2],a[3]===Symbol.for("react.memo_cache_sentinel")?(p=e.jsx(b,{value:"",children:r.ALL}),x=c().map(U),a[3]=p,a[4]=x):(p=a[3],x=a[4]),a[5]!==m||a[6]!==o?(S=e.jsxs(_,{labelId:"type-label",value:o,label:s.DRESS_TYPE,onChange:m,autoWidth:!0,children:[p,x]}),a[5]=m,a[6]=o,a[7]=S):S=a[7],a[8]!==l||a[9]!==S?(f=e.jsxs(g,{className:l,children:[h,S]}),a[8]=l,a[9]=S,a[10]=f):f=a[10],f};function U(t){return e.jsx(b,{value:t,children:n(t)},t)}const V=c=>{const n=t.c(14),{className:i,onChange:o}=c,[d,u]=j.useState("");let m;n[0]!==o?(m=e=>{const t=e.target.value;u(t),o&&o(t)},n[0]=o,n[1]=m):m=n[1];const h=m;let p,x,S,f,k,N,T,E;return n[2]===Symbol.for("react.memo_cache_sentinel")?(p=e.jsx(v,{id:"size-label",children:s.DRESS_SIZE}),n[2]=p):p=n[2],n[3]===Symbol.for("react.memo_cache_sentinel")?(x=e.jsx(b,{value:"",children:r.ALL}),n[3]=x):x=n[3],n[4]===Symbol.for("react.memo_cache_sentinel")?(S=e.jsx(b,{value:l.Small,children:a(l.Small)}),n[4]=S):S=n[4],n[5]===Symbol.for("react.memo_cache_sentinel")?(f=e.jsx(b,{value:l.Medium,children:a(l.Medium)}),n[5]=f):f=n[5],n[6]===Symbol.for("react.memo_cache_sentinel")?(k=e.jsx(b,{value:l.Large,children:a(l.Large)}),n[6]=k):k=n[6],n[7]===Symbol.for("react.memo_cache_sentinel")?(N=e.jsx(b,{value:l.ExtraLarge,children:a(l.ExtraLarge)}),n[7]=N):N=n[7],n[8]!==h||n[9]!==d?(T=e.jsxs(_,{labelId:"size-label",value:d,label:s.DRESS_SIZE,onChange:h,autoWidth:!0,children:[x,S,f,k,N]}),n[8]=h,n[9]=d,n[10]=T):T=n[10],n[11]!==i||n[12]!==T?(E=e.jsxs(g,{className:i,children:[p,T]}),n[11]=i,n[12]=T,n[13]=E):E=n[13],E},H=c=>{const n=t.c(14),{className:a,onChange:l}=c,[i,o]=j.useState("");let d;n[0]!==l?(d=e=>{const t=e.target.value;o(t),l&&l(t)},n[0]=l,n[1]=d):d=n[1];const u=d;let m,h,p,x,S,f,k,N;return n[2]===Symbol.for("react.memo_cache_sentinel")?(m=e.jsx(v,{id:"style-label",children:s.DRESS_STYLE}),n[2]=m):m=n[2],n[3]===Symbol.for("react.memo_cache_sentinel")?(h=e.jsx(b,{value:"",children:r.ALL}),p=e.jsx(b,{value:"traditional",children:s.STYLE_TRADITIONAL}),x=e.jsx(b,{value:"modern",children:s.STYLE_MODERN}),S=e.jsx(b,{value:"vintage",children:s.STYLE_VINTAGE}),f=e.jsx(b,{value:"designer",children:s.STYLE_DESIGNER}),n[3]=h,n[4]=p,n[5]=x,n[6]=S,n[7]=f):(h=n[3],p=n[4],x=n[5],S=n[6],f=n[7]),n[8]!==u||n[9]!==i?(k=e.jsxs(_,{labelId:"style-label",value:i,label:s.DRESS_STYLE,onChange:u,autoWidth:!0,children:[h,p,x,S,f]}),n[8]=u,n[9]=i,n[10]=k):k=n[10],n[11]!==a||n[12]!==k?(N=e.jsxs(g,{className:a,children:[m,k]}),n[11]=a,n[12]=k,n[13]=N):N=n[13],N},P=s=>{const c=t.c(55),{className:n,collapse:a,onChange:l}=s,o=j.useRef(null),d=j.useRef(null),u=j.useRef(null),m=j.useRef(null);let h,p,x;c[0]===Symbol.for("react.memo_cache_sentinel")?(h=()=>{m.current&&(m.current.checked=!0)},p=[],c[0]=h,c[1]=p):(h=c[0],p=c[1]),j.useEffect(h,p),c[2]!==l?(x=e=>{if("checked"in e.currentTarget&&e.currentTarget.checked){const e=-1;o.current&&(o.current.checked=!1),d.current&&(d.current.checked=!1),u.current&&(u.current.checked=!1),l&&l(e)}},c[2]=l,c[3]=x):x=c[3];const S=x;let f;c[4]!==S?(f=e=>{const t=e.currentTarget.previousSibling;if(!t.checked){t.checked=!t.checked;const s=e;s.currentTarget=t,S(s)}},c[4]=S,c[5]=f):f=c[5];const v=f;let g;c[6]!==l?(g=e=>{if("checked"in e.currentTarget&&e.currentTarget.checked){const e=i.DEPOSIT_FILTER_VALUE_1;m.current&&(m.current.checked=!1),d.current&&(d.current.checked=!1),u.current&&(u.current.checked=!1),l&&l(e)}},c[6]=l,c[7]=g):g=c[7];const b=g;let _;c[8]!==b?(_=e=>{const t=e.currentTarget.previousSibling;if(!t.checked){t.checked=!t.checked;const s=e;s.currentTarget=t,b(s)}},c[8]=b,c[9]=_):_=c[9];const k=_;let N;c[10]!==l?(N=e=>{if("checked"in e.currentTarget&&e.currentTarget.checked){const e=i.DEPOSIT_FILTER_VALUE_2;m.current&&(m.current.checked=!1),o.current&&(o.current.checked=!1),u.current&&(u.current.checked=!1),l&&l(e)}},c[10]=l,c[11]=N):N=c[11];const L=N;let C;c[12]!==L?(C=e=>{const t=e.currentTarget.previousSibling;if(!t.checked){t.checked=!t.checked;const s=e;s.currentTarget=t,L(s)}},c[12]=L,c[13]=C):C=c[13];const I=C;let y;c[14]!==l?(y=e=>{if("checked"in e.currentTarget&&e.currentTarget.checked){const e=i.DEPOSIT_FILTER_VALUE_3;m.current&&(m.current.checked=!1),o.current&&(o.current.checked=!1),d.current&&(d.current.checked=!1),l&&l(e)}},c[14]=l,c[15]=y):y=c[15];const A=y;let R;c[16]!==A?(R=e=>{const t=e.currentTarget.previousSibling;if(!t.checked){t.checked=!t.checked;const s=e;s.currentTarget=t,A(s)}},c[16]=A,c[17]=R):R=c[17];const D=R,U=(n?`${n} `:"")+"deposit-filter";let V,H,P,Y,w,F,M,O,B,W,z,G,Z,$;return c[18]!==b?(V=e.jsx("input",{ref:o,type:"radio",className:"deposit-radio",onChange:b}),c[18]=b,c[19]=V):V=c[19],c[20]!==k?(H=e.jsx("span",{onClick:k,role:"button",tabIndex:0,children:T.LESS_THAN_VALUE_1}),c[20]=k,c[21]=H):H=c[21],c[22]!==V||c[23]!==H?(P=e.jsxs("div",{className:"filter-element",children:[V,H]}),c[22]=V,c[23]=H,c[24]=P):P=c[24],c[25]!==L?(Y=e.jsx("input",{ref:d,type:"radio",className:"deposit-radio",onChange:L}),c[25]=L,c[26]=Y):Y=c[26],c[27]!==I?(w=e.jsx("span",{onClick:I,role:"button",tabIndex:0,children:T.LESS_THAN_VALUE_2}),c[27]=I,c[28]=w):w=c[28],c[29]!==Y||c[30]!==w?(F=e.jsxs("div",{className:"filter-element",children:[Y,w]}),c[29]=Y,c[30]=w,c[31]=F):F=c[31],c[32]!==A?(M=e.jsx("input",{ref:u,type:"radio",className:"deposit-radio",onChange:A}),c[32]=A,c[33]=M):M=c[33],c[34]!==D?(O=e.jsx("span",{onClick:D,role:"button",tabIndex:0,children:T.LESS_THAN_VALUE_3}),c[34]=D,c[35]=O):O=c[35],c[36]!==M||c[37]!==O?(B=e.jsxs("div",{className:"filter-element",children:[M,O]}),c[36]=M,c[37]=O,c[38]=B):B=c[38],c[39]!==S?(W=e.jsx("input",{ref:m,type:"radio",className:"deposit-radio",onChange:S}),c[39]=S,c[40]=W):W=c[40],c[41]!==v?(z=e.jsx("span",{onClick:v,role:"button",tabIndex:0,children:r.ALL}),c[41]=v,c[42]=z):z=c[42],c[43]!==W||c[44]!==z?(G=e.jsxs("div",{className:"filter-element",children:[W,z]}),c[43]=W,c[44]=z,c[45]=G):G=c[45],c[46]!==P||c[47]!==F||c[48]!==B||c[49]!==G?(Z=e.jsxs("div",{className:"filter-elements",children:[P,F,B,G]}),c[46]=P,c[47]=F,c[48]=B,c[49]=G,c[50]=Z):Z=c[50],c[51]!==a||c[52]!==U||c[53]!==Z?($=e.jsx(E,{title:T.DEPOSIT,collapse:a,className:U,children:Z}),c[51]=a,c[52]=U,c[53]=Z,c[54]=$):$=c[54],$},Y=[o.Available,o.Unavailable],w=s=>{const c=t.c(43),{className:n,onChange:a}=s,[l,i]=j.useState(!1);let m;c[0]===Symbol.for("react.memo_cache_sentinel")?(m=[],c[0]=m):m=c[0];const[h,p]=j.useState(m),x=j.useRef(null),S=j.useRef(null);let f,v,g;c[1]!==l?(f=()=>{l&&x.current&&S.current&&(x.current.checked=!0,S.current.checked=!0)},v=[l],c[1]=l,c[2]=f,c[3]=v):(f=c[2],v=c[3]),j.useEffect(f,v),c[4]!==a?(g=e=>{a&&a(d(0===e.length?Y:e))},c[4]=a,c[5]=g):g=c[5];const b=g;let _;c[6]!==b||c[7]!==h?(_=e=>{e.currentTarget instanceof HTMLInputElement?(e.currentTarget.checked?(h.push(o.Available),2===h.length&&i(!0)):(h.splice(h.findIndex(F),1),0===h.length&&i(!1)),p(h),b(h)):u()},c[6]=b,c[7]=h,c[8]=_):_=c[8];const k=_;let N;c[9]!==k?(N=e=>{const t=e.currentTarget.previousSibling;t.checked=!t.checked;const s=e;s.currentTarget=t,k(s)},c[9]=k,c[10]=N):N=c[10];const L=N;let C;c[11]!==b||c[12]!==h?(C=e=>{e.currentTarget instanceof HTMLInputElement?(e.currentTarget.checked?(h.push(o.Unavailable),2===h.length&&i(!0)):(h.splice(h.findIndex(M),1),0===h.length&&i(!1)),p(h),b(h)):u()},c[11]=b,c[12]=h,c[13]=C):C=c[13];const I=C;let y;c[14]!==I?(y=e=>{const t=e.currentTarget.previousSibling;t.checked=!t.checked;const s=e;s.currentTarget=t,I(s)},c[14]=I,c[15]=y):y=c[15];const A=y;let R;c[16]!==l||c[17]!==a?(R=()=>{if(x.current&&S.current)if(l)x.current.checked=!1,S.current.checked=!1,i(!1),p([]);else{x.current.checked=!0,S.current.checked=!0;const e=[o.Available,o.Unavailable];i(!0),p(e),a&&a(d(e))}else u()},c[16]=l,c[17]=a,c[18]=R):R=c[18];const D=R,U=(n?`${n} `:"")+"availability-filter";let V,H,P,w,O,B;c[19]!==k?(V=e.jsx("input",{ref:x,type:"checkbox",className:"availability-checkbox",onChange:k}),c[19]=k,c[20]=V):V=c[20],c[21]!==L?(H=e.jsx("span",{role:"button",tabIndex:0,onClick:L,children:T.AVAILABLE}),c[21]=L,c[22]=H):H=c[22],c[23]!==V||c[24]!==H?(P=e.jsxs("div",{className:"filter-element",children:[V,H]}),c[23]=V,c[24]=H,c[25]=P):P=c[25],c[26]!==I?(w=e.jsx("input",{ref:S,type:"checkbox",className:"availability-checkbox",onChange:I}),c[26]=I,c[27]=w):w=c[27],c[28]!==A?(O=e.jsx("span",{role:"button",tabIndex:0,onClick:A,children:T.UNAVAILABLE}),c[28]=A,c[29]=O):O=c[29],c[30]!==w||c[31]!==O?(B=e.jsxs("div",{className:"filter-element",children:[w,O]}),c[30]=w,c[31]=O,c[32]=B):B=c[32];const W=l?r.UNCHECK_ALL:r.CHECK_ALL;let z,G,Z;return c[33]!==D||c[34]!==W?(z=e.jsx("div",{className:"filter-actions",children:e.jsx("span",{role:"button",tabIndex:0,onClick:D,className:"uncheckall",children:W})}),c[33]=D,c[34]=W,c[35]=z):z=c[35],c[36]!==P||c[37]!==B||c[38]!==z?(G=e.jsxs("div",{className:"filter-elements",children:[P,B,z]}),c[36]=P,c[37]=B,c[38]=z,c[39]=G):G=c[39],c[40]!==U||c[41]!==G?(Z=e.jsx(E,{title:T.AVAILABILITY,className:U,children:G}),c[40]=U,c[41]=G,c[42]=Z):Z=c[42],Z};function F(e){return e===o.Available}function M(e){return e===o.Unavailable}const O=c=>{const n=t.c(15),{className:a,onChange:l}=c,[i,o]=j.useState("");let d;n[0]!==l?(d=e=>{const t=e.target.value;o(t),l&&l(t)},n[0]=l,n[1]=d):d=n[1];const u=d;let m,h,p,x,S,f,k,N,T;return n[2]===Symbol.for("react.memo_cache_sentinel")?(m=e.jsx(v,{id:"rentals-count-label",children:s.RENTALS_COUNT}),n[2]=m):m=n[2],n[3]===Symbol.for("react.memo_cache_sentinel")?(h=e.jsx(b,{value:"",children:r.ALL}),p=e.jsx(b,{value:"0",children:"0"}),x=e.jsx(b,{value:"1-5",children:"1-5"}),S=e.jsx(b,{value:"6-10",children:"6-10"}),f=e.jsx(b,{value:"11-20",children:"11-20"}),k=e.jsx(b,{value:"20+",children:"20+"}),n[3]=h,n[4]=p,n[5]=x,n[6]=S,n[7]=f,n[8]=k):(h=n[3],p=n[4],x=n[5],S=n[6],f=n[7],k=n[8]),n[9]!==u||n[10]!==i?(N=e.jsxs(_,{labelId:"rentals-count-label",value:i,label:s.RENTALS_COUNT,onChange:u,autoWidth:!0,children:[h,p,x,S,f,k]}),n[9]=u,n[10]=i,n[11]=N):N=n[11],n[12]!==a||n[13]!==N?(T=e.jsxs(g,{className:a,children:[m,N]}),n[12]=a,n[13]=N,n[14]=T):T=n[14],T},B=s=>{const r=t.c(23),{value:c,placeholder:n,onChange:a,onSubmit:l,className:i}=s,o=void 0===c?"":c,d=void 0===n?"Search...":n,[u,m]=j.useState(o);let h;r[0]!==a?(h=e=>{const t=e.target.value;m(t),a&&a(t)},r[0]=a,r[1]=h):h=r[1];const p=h;let x;r[2]!==l||r[3]!==u?(x=e=>{e.preventDefault(),l&&l(u)},r[2]=l,r[3]=u,r[4]=x):x=r[4];const S=x;let f;r[5]!==a||r[6]!==l?(f=()=>{m(""),a&&a(""),l&&l("")},r[5]=a,r[6]=l,r[7]=f):f=r[7];const v=f;let g,b,_,T,E;return r[8]===Symbol.for("react.memo_cache_sentinel")?(g=e.jsx(k,{position:"start",children:e.jsx(L,{type:"submit",size:"small",children:e.jsx(C,{})})}),r[8]=g):g=r[8],r[9]!==v||r[10]!==u?(b=u&&e.jsx(k,{position:"end",children:e.jsx(L,{onClick:v,size:"small",children:e.jsx(I,{})})}),r[9]=v,r[10]=u,r[11]=b):b=r[11],r[12]!==b?(_={startAdornment:g,endAdornment:b},r[12]=b,r[13]=_):_=r[13],r[14]!==p||r[15]!==d||r[16]!==u||r[17]!==_?(T=e.jsx(N,{value:u,onChange:p,placeholder:d,variant:"outlined",size:"small",fullWidth:!0,InputProps:_}),r[14]=p,r[15]=d,r[16]=u,r[17]=_,r[18]=T):T=r[18],r[19]!==i||r[20]!==S||r[21]!==T?(E=e.jsx("form",{onSubmit:S,className:i,children:T}),r[19]=i,r[20]=S,r[21]=T,r[22]=E):E=r[22],E},W=()=>{const t=p(),[r,c]=j.useState(),[n,a]=j.useState(!1),[l,i]=j.useState([]),[o,d]=j.useState([]),[v,g]=j.useState(""),[b,_]=j.useState(""),[k,N]=j.useState(""),[T,E]=j.useState(""),[L,C]=j.useState(""),[I,A]=j.useState(""),[U,Y]=j.useState(""),[F,M]=j.useState(!1),[W,z]=j.useState(!0),[G,Z]=j.useState(0);return j.useEffect((()=>{(async()=>{try{const e=m();if(e){const t=h(e);c(e),a(t);const s=await x(),r=t?s.map((e=>e._id)):[e._id];i(s),d(r.filter((e=>void 0!==e)))}}catch(e){u(e)}finally{z(!1)}})()}),[]),e.jsx(S,{children:r&&e.jsxs("div",{className:"dresses",children:[e.jsx("div",{className:"col-1",children:e.jsxs("div",{className:"col-1-container",children:[e.jsx(B,{className:"dress-search",onSubmit:e=>{Y(e),M(!0)}}),e.jsx(y,{variant:"contained",className:"btn-primary new-dress",startIcon:e.jsx(R,{}),onClick:()=>{t("/create-dress")},children:s.NEW_DRESS}),e.jsxs("div",{className:"dress-filters",children:[e.jsx(D,{className:"dress-filter",onChange:e=>{g(e),M(!0)}}),e.jsx(V,{className:"dress-filter",onChange:e=>{_(e),M(!0)}}),e.jsx(H,{className:"dress-filter",onChange:e=>{N(e),M(!0)}}),e.jsx(P,{className:"dress-filter",onChange:e=>{E(e.toString()),M(!0)}}),e.jsx(O,{className:"dress-filter",onChange:e=>{A(e),M(!0)}}),n&&e.jsx(w,{className:"dress-filter",onChange:e=>{C(e.join(",")),M(!0)}})]})]})}),e.jsx("div",{className:"col-2",children:e.jsx("div",{className:"col-2-container",children:e.jsx(f,{user:r,suppliers:o,dressType:v?[v]:[],dressSize:b?[b]:[],dressStyle:k?[k]:[],deposit:T?Number(T):void 0,availability:L?L.split(","):[],rentalsCount:I,keyword:U,loading:W,onLoad:e=>{e&&void 0!==e.rowCount&&Z(e.rowCount)},onDelete:e=>{Z(e)}})})})]})})};export{W as default};
