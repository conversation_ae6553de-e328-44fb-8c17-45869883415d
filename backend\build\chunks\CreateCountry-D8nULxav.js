import{u as e,j as s,e as t,a as r,J as a,P as o,B as n,z as i}from"../entries/index-xsXxT3-W.js";import{d as m,r as l}from"./router-BtYqujaw.js";import{L as c}from"./Layout-DaeN7D4t.js";import{s as u}from"./create-country-DGdOfzjM.js";import{v as d,a as p}from"./CountryService-CPWL_VJK.js";import{P as j}from"./Paper-C-atefOs.js";import{F as f,I as h}from"./InputLabel-C8rcdOGQ.js";import{I as x}from"./Input-D1AdR9CM.js";import{F as N}from"./FormHelperText-DDZ4BMA4.js";import{B as y}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const C=()=>{const C=m(),{user:v}=e(),[b,g]=l.useState(!1),[E,A]=l.useState([]),[I,L]=l.useState([]);return s.jsx(c,{onLoad:()=>{g(!0)},strict:!0,children:s.jsx("div",{className:"create-country",children:s.jsxs(j,{className:"country-form country-form-wrapper",elevation:10,style:b?{}:{display:"none"},children:[s.jsx("h1",{className:"country-form-title",children:u.NEW_COUNTRY_HEADING}),s.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{let e=!0;const s=a(I);for(let t=0;t<I.length;t+=1)s[t]=!1;for(let t=0;t<E.length;t+=1){const r=E[t],a=200===await d(r);e=e&&a,a||(s[t]=!0)}if(L(s),e){const e={names:E,supplier:o(v)?v?._id:void 0};if(200===await p(e)){const e=a(E);for(let s=0;s<E.length;s+=1)e[s].name="";A(e),n(u.COUNTRY_CREATED)}else i()}}catch(s){i(s)}},children:[t._LANGUAGES.map(((e,t)=>s.jsxs(f,{fullWidth:!0,margin:"dense",children:[s.jsx(h,{className:"required",children:`${r.NAME} (${e.label})`}),s.jsx(x,{type:"text",value:E[t]&&E[t].name||"",error:I[t],required:!0,onChange:s=>{const r=a(E);r[t]={language:e.code,name:s.target.value},A(r);const o=a(I);o[t]=!1,L(o)},autoComplete:"off"}),s.jsx(N,{error:I[t],children:I[t]&&u.INVALID_COUNTRY||""})]},e.code))),s.jsxs("div",{className:"buttons",children:[s.jsx(y,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",children:r.CREATE}),s.jsx(y,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>C("/countries"),children:r.CANCEL})]})]})]})})})};export{C as default};
