import{c as e,j as t}from"../entries/index-xsXxT3-W.js";import{r as s}from"./router-BtYqujaw.js";const c=c=>{const l=e.c(20),{title:a,className:n,collapse:r,offsetHeight:i,children:o}=c,d=void 0===i?0:i,m=s.useRef(null);let p;l[0]!==d?(p=e=>{e.currentTarget.classList.toggle("accordion-active");const t=e.currentTarget.nextElementSibling,s=t.classList.contains("panel-collapse");t.style.maxHeight||s?(s&&(t.classList.remove("panel-collapse"),t.classList.add("panel")),t.style.maxHeight=""):t.style.maxHeight=`${t.scrollHeight+d}px`},l[0]=d,l[1]=p):p=l[1];const x=p;let g,u,f,h;l[2]!==r?(g=()=>{r&&m.current&&m.current.classList.add("accordion-active")},u=[r],l[2]=r,l[3]=g,l[4]=u):(g=l[3],u=l[4]),s.useEffect(g,u),l[5]!==r||l[6]!==d?(f=()=>{if(r&&m.current){const e=m.current.nextElementSibling;e.style.maxHeight=`${e.scrollHeight+d}px`}},l[5]=r,l[6]=d,l[7]=f):f=l[7],l[8]!==d?(h=[d],l[8]=d,l[9]=h):h=l[9],s.useEffect(f,h);const H=(n?`${n} `:"")+"accordion-container";let j;l[10]!==x||l[11]!==a?(j=t.jsx("span",{ref:m,className:"accordion",onClick:x,role:"button",tabIndex:0,children:a}),l[10]=x,l[11]=a,l[12]=j):j=l[12];const v=r?"panel-collapse":"panel";let L,b;return l[13]!==o||l[14]!==v?(L=t.jsx("div",{className:v,children:o}),l[13]=o,l[14]=v,l[15]=L):L=l[15],l[16]!==L||l[17]!==H||l[18]!==j?(b=t.jsxs("div",{className:H,children:[j,L]}),l[16]=L,l[17]=H,l[18]=j,l[19]=b):b=l[19],b};export{c as A};
