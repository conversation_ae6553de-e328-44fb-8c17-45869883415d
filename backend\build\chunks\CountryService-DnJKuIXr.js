import{aC as t,p as e}from"../entries/index-CEzJO5Xy.js";const a=e=>t.post("/api/validate-country",e,{withCredentials:!0}).then((t=>t.status)),n=e=>t.post("/api/create-country",e,{withCredentials:!0}).then((t=>t.status)),s=(e,a)=>t.put(`/api/update-country/${e}`,a,{withCredentials:!0}).then((t=>t.status)),i=e=>t.delete(`/api/delete-country/${encodeURIComponent(e)}`,{withCredentials:!0}).then((t=>t.status)),o=a=>t.get(`/api/country/${encodeURIComponent(a)}/${e()}`,{withCredentials:!0}).then((t=>t.data)),d=(a,n,s)=>t.get(`/api/countries/${n}/${s}/${e()}/?s=${encodeURIComponent(a)}`,{withCredentials:!0}).then((t=>t.data)),r=e=>t.get(`/api/check-country/${encodeURIComponent(e)}`,{withCredentials:!0}).then((t=>t.status));export{n as a,o as b,r as c,i as d,d as g,s as u,a as v};
