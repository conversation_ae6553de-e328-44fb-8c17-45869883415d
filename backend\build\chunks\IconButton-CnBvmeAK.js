import{r as e}from"./router-BtYqujaw.js";import{i as o,j as a,k as t,l as r,aq as i}from"../entries/index-CEzJO5Xy.js";import{a as n,g as s,f as l,s as d,c as p,n as c,m as g,b as u,C as m}from"./Button-DGZYUY3P.js";function v(e){return s("MuiIconButton",e)}const y=n("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),b=d(c,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,o)=>{const{ownerState:a}=e;return[o.root,a.loading&&o.loading,"default"!==a.color&&o[`color${r(a.color)}`],a.edge&&o[`edge${r(a.edge)}`],o[`size${r(a.size)}`]]}})(g((({theme:e})=>({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),variants:[{props:e=>!e.disableRipple,style:{"--IconButton-hoverBg":e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:i(e.palette.action.active,e.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]}))),g((({theme:e})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(e.palette).filter(u()).map((([o])=>({props:{color:o},style:{color:(e.vars||e).palette[o].main}}))),...Object.entries(e.palette).filter(u()).map((([o])=>({props:{color:o},style:{"--IconButton-hoverBg":e.vars?`rgba(${(e.vars||e).palette[o].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:i((e.vars||e).palette[o].main,e.palette.action.hoverOpacity)}}))),{props:{size:"small"},style:{padding:5,fontSize:e.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:e.typography.pxToRem(28)}}],[`&.${y.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled},[`&.${y.loading}`]:{color:"transparent"}})))),h=d("span",{name:"MuiIconButton",slot:"LoadingIndicator"})((({theme:e})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(e.vars||e).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]}))),f=e.forwardRef((function(e,i){const n=o({props:e,name:"MuiIconButton"}),{edge:s=!1,children:d,className:c,color:g="default",disabled:u=!1,disableFocusRipple:y=!1,size:f="medium",id:I,loading:z=null,loadingIndicator:R,...x}=n,B=l(I),$=R??a.jsx(m,{"aria-labelledby":B,color:"inherit",size:16}),j={...n,edge:s,color:g,disabled:u,disableFocusRipple:y,loading:z,loadingIndicator:$,size:f},S=(e=>{const{classes:o,disabled:a,color:t,edge:i,size:n,loading:s}=e,l={root:["root",s&&"loading",a&&"disabled","default"!==t&&`color${r(t)}`,i&&`edge${r(i)}`,`size${r(n)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return p(l,v,o)})(j);return a.jsxs(b,{id:z?B:I,className:t(S.root,c),centerRipple:!0,focusRipple:!y,disabled:u||z,ref:i,...x,ownerState:j,children:["boolean"==typeof z&&a.jsx("span",{className:S.loadingWrapper,style:{display:"contents"},children:a.jsx(h,{className:S.loadingIndicator,ownerState:j,children:z&&$})}),d]})}));export{f as I,y as i};
