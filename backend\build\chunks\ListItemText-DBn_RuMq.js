import{r}from"./router-BtYqujaw.js";import{i as e,k as o,j as s}from"../entries/index-CEzJO5Xy.js";import{b as t}from"./Menu-ZU0DMgjT.js";import{g as a,l as n}from"./listItemTextClasses-DFwCkkgK.js";import{u as i}from"./useSlot-CtA82Ni6.js";import{s as p,c as m}from"./Button-DGZYUY3P.js";import{T as y,t as d}from"./Backdrop-Bzn12VyM.js";const l=p("div",{name:"MuiListItemText",slot:"Root",overridesResolver:(r,e)=>{const{ownerState:o}=r;return[{[`& .${n.primary}`]:e.primary},{[`& .${n.secondary}`]:e.secondary},e.root,o.inset&&e.inset,o.primary&&o.secondary&&e.multiline,o.dense&&e.dense]}})({flex:"1 1 auto",minWidth:0,marginTop:4,marginBottom:4,[`.${d.root}:where(& .${n.primary})`]:{display:"block"},[`.${d.root}:where(& .${n.secondary})`]:{display:"block"},variants:[{props:({ownerState:r})=>r.primary&&r.secondary,style:{marginTop:6,marginBottom:6}},{props:({ownerState:r})=>r.inset,style:{paddingLeft:56}}]}),c=r.forwardRef((function(n,p){const d=e({props:n,name:"MuiListItemText"}),{children:c,className:u,disableTypography:x=!1,inset:T=!1,primary:f,primaryTypographyProps:w,secondary:h,secondaryTypographyProps:j,slots:g={},slotProps:S={},...b}=d,{dense:v}=r.useContext(t);let P=null!=f?f:c,$=h;const L={...d,disableTypography:x,inset:T,primary:!!P,secondary:!!$,dense:v},k=(r=>{const{classes:e,inset:o,primary:s,secondary:t,dense:n}=r;return m({root:["root",o&&"inset",n&&"dense",s&&t&&"multiline"],primary:["primary"],secondary:["secondary"]},a,e)})(L),B={slots:g,slotProps:{primary:w,secondary:j,...S}},[N,F]=i("root",{className:o(k.root,u),elementType:l,externalForwardedProps:{...B,...b},ownerState:L,ref:p}),[I,M]=i("primary",{className:k.primary,elementType:y,externalForwardedProps:B,ownerState:L}),[R,C]=i("secondary",{className:k.secondary,elementType:y,externalForwardedProps:B,ownerState:L});return null==P||P.type===y||x||(P=s.jsx(I,{variant:v?"body2":"body1",component:M?.variant?void 0:"span",...M,children:P})),null==$||$.type===y||x||($=s.jsx(R,{variant:"body2",color:"textSecondary",...C,children:$})),s.jsxs(N,{...F,children:[P,$]})}));export{c as L};
