import{b as e,s as t,j as s,F as a,R as o,a as r,e as i,J as n,K as l,z as m,B as u}from"../entries/index-CEzJO5Xy.js";import{d as c,r as d}from"./router-BtYqujaw.js";import{L as p}from"./Layout-BQBjg4Lf.js";import{C as j,s as g,P as f,a as h}from"./ParkingSpotEditList-ClS4WTgI.js";import{s as v}from"./suppliers-DKbqsTuE.js";import{e as A,v as S,u as x}from"./LocationService-BtQFgoWL.js";import L from"./NoMatch-jvHCs4x8.js";import{E as T}from"./Error-koMug0_G.js";import{S as N}from"./SimpleBackdrop-Bf3qjF13.js";import{A as C}from"./Avatar-BtfxKR-8.js";import{S as O}from"./SupplierBadge-C4yEjxHC.js";import{P as I}from"./Paper-CcwAvfvc.js";import{F as E,a as D,I as P}from"./InputLabel-BbcIE26O.js";import{I as _}from"./Input-BQdee9z7.js";import{F as U}from"./FormHelperText-DFSsjBsL.js";import{B as y}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./CountryService-DnJKuIXr.js";import"./MultipleSelect-C7xTvWe9.js";import"./Autocomplete-CviOU_ku.js";import"./OutlinedInput-g8mR4MM3.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-CtA82Ni6.js";import"./TextField-BAse--ht.js";import"./Backdrop-Bzn12VyM.js";import"./Menu-ZU0DMgjT.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-Cay5TZBz.js";import"./AccountCircle-khVEeiad.js";import"./Chip-CAtDqtgp.js";import"./IconButton-CnBvmeAK.js";import"./Avatar-Dix3YM8x.js";import"./Flag-BR6CpE1z.js";import"./DressService-J0XavNJj.js";import"./Badge-B3LKl4T2.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";const b=new e({fr:{UPDATE_LOCATION:"Modification du lieu",LOCATION_UPDATED:"Lieu modifié avec succès."},en:{UPDATE_LOCATION:"Location update",LOCATION_UPDATED:"Location updated successfully."},es:{UPDATE_LOCATION:"Actualización del lugar",LOCATION_UPDATED:"Lugar actualizado correctamente."},ar:{UPDATE_LOCATION:"تحديث الموقع",LOCATION_UPDATED:"تم تحديث الموقع بنجاح."}});t(b);const w=()=>{const e=c(),[t,w]=d.useState(),[B,k]=d.useState(!1),[G,W]=d.useState(!1),[F,z]=d.useState([]),[R,H]=d.useState([]),[M,K]=d.useState(!1),[q,J]=d.useState(!1),[V,$]=d.useState(),[Y,Q]=d.useState(),[X,Z]=d.useState(""),[ee,te]=d.useState(""),[se,ae]=d.useState(""),[oe,re]=d.useState([]);return s.jsxs(p,{onLoad:async e=>{if(e&&e.verified){W(!0),w(e);const s=new URLSearchParams(window.location.search);if(s.has("loc")){const o=s.get("loc");if(o&&""!==o)try{const t=await A(o);if(!a(e)&&e._id!==t.supplier?._id)return W(!1),void K(!0);if(t&&t.values){i._LANGUAGES.forEach((e=>{t.values&&!t.values.some((t=>t.language===e.code))&&t.values.push({language:e.code,value:""})}));const e=t.values.map((e=>({language:e.language||"",name:e.value||""})));$(t),Q(t.country),z(e),te(t.longitude&&t.longitude.toString()||""),ae(t.latitude&&t.latitude.toString()||""),re(t.parkingSpots||[]),k(!0),W(!1)}else W(!1),K(!0)}catch(t){m(t),W(!1),J(!0),k(!1)}else W(!1),K(!0)}else W(!1),K(!0)}},strict:!0,children:[!q&&!M&&V&&V.values&&s.jsx("div",{className:"update-location",children:s.jsxs(I,{className:"location-form location-form-wrapper",elevation:10,style:B?{}:{display:"none"},children:[s.jsx("h1",{className:"location-form-title",children:b.UPDATE_LOCATION}),s.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(!Y||!V||!V.values)return void m();let e=!0;const t=n(R);for(let s=0;s<R.length;s+=1)t[s]=!1;for(let s=0;s<F.length;s+=1){const a=F[s];if(a.name!==V.values[s].value){const o=200===await S({language:a.language,name:a.name});e=e&&o,o||(t[s]=!0)}}if(H(t),e){const e={country:Y._id,latitude:se?Number(se):void 0,longitude:ee?Number(ee):void 0,names:F,image:X,parkingSpots:oe},{status:t,data:s}=await x(V._id,e);200===t?($(s),u(b.LOCATION_UPDATED)):(W(!1),m())}}catch(t){m(t)}},children:[s.jsx(C,{type:o.Location,mode:"update",record:V,size:"large",readonly:!1,onBeforeUpload:()=>{W(!0)},onChange:e=>{W(!1),Z(e)},color:"disabled",className:"avatar-ctn"}),a(t)&&V.supplier&&s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsx(D,{children:v.SUPPLIER}),s.jsx(O,{supplier:V.supplier})]}),s.jsx(E,{fullWidth:!0,margin:"dense",children:s.jsx(j,{label:g.COUNTRY,variant:"standard",value:Y,onChange:e=>{if(e.length>0){const t=e[0],s={_id:t._id,name:t.name};Q(s)}else Q(void 0)},required:!0})}),V.values.map(((e,t)=>s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsx(P,{className:"required",children:`${r.NAME} (${i._LANGUAGES.filter((t=>t.code===e.language))[0].label})`}),s.jsx(_,{type:"text",value:F[t]&&F[t].name||"",error:R[t],required:!0,onChange:e=>{const s=n(F);s[t].name=e.target.value;const a=l(R);a[t]=!1,(()=>{let e=!1;if(!V||!V.values)return m(),e;for(let t=0;t<F.length;t+=1)if(F[t].name!==V.values[t].value){e=!0;break}})(),z(s),H(a)},autoComplete:"off"}),s.jsx(U,{error:R[t],children:R[t]&&g.INVALID_LOCATION||""})]},e.language))),s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsx(P,{children:r.LATITUDE}),s.jsx(f,{value:se,onChange:e=>{ae(e.target.value)}})]}),s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsx(P,{children:r.LONGITUDE}),s.jsx(f,{value:ee,onChange:e=>{te(e.target.value)}})]}),s.jsx(h,{title:g.PARKING_SPOTS,values:oe,onAdd:e=>{const t=n(oe);t.push(e),re(t)},onUpdate:(e,t)=>{const s=n(oe);s[t]=e,re(s)},onDelete:(e,t)=>{const s=n(oe);s.splice(t,1),re(s)}}),s.jsxs("div",{className:"buttons",children:[s.jsx(y,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",children:r.SAVE}),s.jsx(y,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>e("/locations"),children:r.CANCEL})]})]})]})}),G&&s.jsx(N,{text:r.PLEASE_WAIT}),q&&s.jsx(T,{}),M&&s.jsx(L,{hideHeader:!0})]})};export{w as default};
