import React, { useState, useEffect } from 'react'
import {
  InputLabel,
  Select,
  MenuItem,
  FormControl,
  SelectChangeEvent,
  FormHelperText,
  Avatar,
  ListItemAvatar,
  ListItemText
} from '@mui/material'
import * as bookcarsTypes from ':bookcars-types'
import * as bookcarsHelper from ':bookcars-helper'
import env from '@/config/env.config'
import * as DressService from '@/services/DressService'

interface DressSelectListProps {
  value?: string
  label?: string
  required?: boolean
  multiple?: boolean
  variant?: 'standard' | 'outlined' | 'filled'
  supplier?: string
  onChange?: (values: bookcarsTypes.Option[]) => void
}

const DressSelectList: React.FC<DressSelectListProps> = ({
  value: dressValue,
  label,
  required,
  multiple,
  variant,
  supplier,
  onChange
}) => {
  const [value, setValue] = useState<string | string[]>(multiple ? [] : '')
  const [dresses, setDresses] = useState<bookcarsTypes.Dress[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDresses = async () => {
      try {
        setLoading(true)
        const payload: bookcarsTypes.GetDressesPayload = {
          suppliers: supplier ? [supplier] : [],
          availability: [bookcarsTypes.Availablity.Available]
        }
        
        const data = await DressService.getDresses('', payload, 1, 1000)
        const _data = data && data.length > 0 ? data[0] : { pageInfo: { totalRecord: 0 }, resultData: [] }
        
        if (_data && _data.resultData) {
          setDresses(_data.resultData)
        }
      } catch (err) {
        console.error('Error fetching dresses:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchDresses()
  }, [supplier])

  useEffect(() => {
    if (dressValue !== undefined) {
      setValue(dressValue)
    }
  }, [dressValue])

  const handleChange = (event: SelectChangeEvent<string | string[]>) => {
    const selectedValue = event.target.value
    setValue(selectedValue)

    if (onChange) {
      if (multiple) {
        const selectedValues = Array.isArray(selectedValue) ? selectedValue : [selectedValue]
        const selectedOptions = selectedValues
          .map(val => dresses.find(dress => dress._id === val))
          .filter(Boolean)
          .map(dress => ({
            _id: dress!._id!,
            name: dress!.name
          }))
        onChange(selectedOptions)
      } else {
        const selectedDress = dresses.find(dress => dress._id === selectedValue)
        if (selectedDress) {
          onChange([{
            _id: selectedDress._id!,
            name: selectedDress.name
          }])
        } else {
          onChange([])
        }
      }
    }
  }

  return (
    <FormControl fullWidth margin="dense" variant={variant || 'standard'}>
      <InputLabel className={required ? 'required' : ''}>{label}</InputLabel>
      <Select
        value={value}
        onChange={handleChange}
        multiple={multiple}
        required={required}
        disabled={loading}
        renderValue={(selected) => {
          if (multiple) {
            const selectedArray = Array.isArray(selected) ? selected : []
            return selectedArray
              .map(val => dresses.find(dress => dress._id === val)?.name)
              .filter(Boolean)
              .join(', ')
          } else {
            const selectedDress = dresses.find(dress => dress._id === selected)
            return selectedDress?.name || ''
          }
        }}
      >
        {dresses.map((dress) => (
          <MenuItem key={dress._id} value={dress._id}>
            <ListItemAvatar>
              <Avatar
                src={dress.image ? bookcarsHelper.joinURL(env.CDN_DRESSES, dress.image) : undefined}
                sx={{ width: 32, height: 32 }}
              />
            </ListItemAvatar>
            <ListItemText 
              primary={dress.name}
              secondary={`${dress.type} - ${dress.size} - ${dress.color}`}
            />
          </MenuItem>
        ))}
      </Select>
      {loading && <FormHelperText>Loading dresses...</FormHelperText>}
    </FormControl>
  )
}

export default DressSelectList
