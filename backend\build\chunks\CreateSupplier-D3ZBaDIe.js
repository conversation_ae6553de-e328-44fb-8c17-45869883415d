import{u as e,j as s,R as r,a as i,U as a,K as t,L as o,D as n,p as l,M as m,z as p}from"../entries/index-xsXxT3-W.js";import{d as c,r as d}from"./router-BtYqujaw.js";import{u,a as j,s as h}from"./zod-4O8Zwsja.js";import{C as f,s as x}from"./ContractList-hpnQO1ah.js";import{L as g}from"./Layout-DaeN7D4t.js";import{s as N}from"./create-supplier-BpB8o_Zh.js";import{b as R,v as y}from"./SupplierService-9DC5V5ZJ.js";import{E}from"./Error-DRzAdbbx.js";import{S as C}from"./SimpleBackdrop-CqsJhYJ4.js";import{A as L}from"./Avatar-CvDHTACZ.js";import{P as D}from"./Paper-C-atefOs.js";import{I as A}from"./Info-CNP9gYBt.js";import{F as I,I as S}from"./InputLabel-C8rcdOGQ.js";import{I as b}from"./Input-D1AdR9CM.js";import{F as v}from"./FormHelperText-DDZ4BMA4.js";import{F as _,S as O}from"./Switch-C5asfh_w.js";import{B as w}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./IconButton-CxOCoGF3.js";import"./Delete-BfnPAJno.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-DiTut-u0.js";import"./Backdrop-Czag2Ija.js";import"./DressService-DkS6e_O5.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./SwitchBase-DrUkTXjH.js";const W=()=>{const W=c(),{user:P}=e(),[T,M]=d.useState(!1),[B,k]=d.useState(!1),[q,U]=d.useState(!1),[F,G]=d.useState(""),[z,H]=d.useState(!1),[V,Y]=d.useState([]),{control:Q,register:J,handleSubmit:K,setValue:X,setError:Z,clearErrors:$,setFocus:ee,formState:{errors:se,isSubmitting:re},trigger:ie}=u({resolver:h(x),mode:"onBlur",defaultValues:{fullName:"",email:"",phone:"",location:"",bio:"",payLater:!1,licenseRequired:!1,minimumRentalDays:"",priceChangeRate:"",supplierDressLimit:"",notifyAdminOnNewDress:!1}}),{payLater:ae,licenseRequired:te,notifyAdminOnNewDress:oe}=j({control:Q});return s.jsxs(g,{onLoad:e=>{e&&e.verified&&M(!0)},strict:!0,admin:!0,children:[s.jsx("div",{className:"create-supplier",children:s.jsxs(D,{className:"supplier-form",elevation:10,style:T?{}:{display:"none"},children:[s.jsx("h1",{className:"supplier-form-title",children:N.CREATE_SUPPLIER_HEADING}),s.jsxs("form",{onSubmit:K((async e=>{try{let s=await y({fullName:e.fullName});if(200!==s)return Z("fullName",{message:N.INVALID_SUPPLIER_NAME}),void ee("fullName");if(s=await n({email:e.email}),200!==s)return Z("email",{message:i.EMAIL_ALREADY_REGISTERED}),void ee("email");if(!F)return H(!0),void U(!1);const a={email:e.email,fullName:e.fullName,phone:e.phone,location:e.location,bio:e.bio,language:l(),type:r.Supplier,avatar:F,payLater:e.payLater,licenseRequired:e.licenseRequired,contracts:V,minimumRentalDays:e.minimumRentalDays?Number(e.minimumRentalDays):void 0,priceChangeRate:e.priceChangeRate?Number(e.priceChangeRate):void 0,supplierDressLimit:e.supplierDressLimit?Number(e.supplierDressLimit):void 0,notifyAdminOnNewDress:e.notifyAdminOnNewDress};s=await m(a),200===s?W("/suppliers"):U(!0)}catch(s){p(s)}}),(()=>{const e=Object.keys(se)[0];e&&ee(e)})),children:[s.jsx(L,{type:r.Supplier,mode:"create",record:null,size:"large",readonly:!1,onBeforeUpload:()=>{k(!0)},onChange:e=>{k(!1),G(e),null!==e&&H(!1)},color:"disabled",className:"avatar-ctn"}),s.jsxs("div",{className:"info",children:[s.jsx(A,{}),s.jsx("span",{children:N.RECOMMENDED_IMAGE_SIZE})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{className:"required",children:i.FULL_NAME}),s.jsx(b,{...J("fullName"),type:"text",error:!!se.fullName,required:!0,autoComplete:"off"}),s.jsx(v,{error:!!se.fullName,children:se.fullName?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{className:"required",children:i.EMAIL}),s.jsx(b,{...J("email"),type:"text",autoComplete:"off",required:!0,error:!!se.email}),s.jsx(v,{error:!!se.email,children:se.email?.message||""})]}),s.jsx(I,{fullWidth:!0,margin:"dense",children:s.jsx(_,{control:s.jsx(O,{...J("payLater"),checked:ae,onChange:e=>{X("payLater",e.target.checked)},color:"primary"}),label:i.PAY_LATER})}),s.jsx(I,{fullWidth:!0,margin:"dense",children:s.jsx(_,{control:s.jsx(O,{...J("licenseRequired"),checked:te,onChange:e=>{X("licenseRequired",e.target.checked)},color:"primary"}),label:i.LICENSE_REQUIRED})}),s.jsxs("div",{className:"info",children:[s.jsx(A,{}),s.jsx("span",{children:i.OPTIONAL})]}),s.jsx(I,{fullWidth:!0,margin:"dense",children:s.jsx(_,{control:s.jsx(O,{...J("notifyAdminOnNewDress"),checked:oe,disabled:P?.type===a.Supplier,onChange:e=>{X("notifyAdminOnNewDress",e.target.checked)},color:"primary"}),label:i.NOTIFY_ADMIN_ON_NEW_CAR})}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.SUPPLIER_CAR_LIMIT}),s.jsx(b,{...J("supplierDressLimit"),type:"text",autoComplete:"off",error:!!se.supplierDressLimit,onChange:()=>$("supplierDressLimit")}),s.jsx(v,{error:!!se.supplierDressLimit,children:se.supplierDressLimit?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.MIN_RENTAL_DAYS}),s.jsx(b,{...J("minimumRentalDays"),type:"text",autoComplete:"off",error:!!se.minimumRentalDays,onChange:()=>$("minimumRentalDays")}),s.jsx(v,{error:!!se.minimumRentalDays,children:se.minimumRentalDays?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.PRICE_CHANGE_RATE}),s.jsx(b,{...J("priceChangeRate"),type:"text",autoComplete:"off",error:!!se.priceChangeRate,onChange:()=>$("priceChangeRate")}),s.jsx(v,{error:!!se.priceChangeRate,children:se.priceChangeRate?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.PHONE}),s.jsx(b,{...J("phone",{onBlur:()=>ie("phone")}),type:"text",autoComplete:"off",error:!!se.phone,onChange:()=>$("phone")}),s.jsx(v,{error:!!se.phone,children:se.phone?.message||""})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.LOCATION}),s.jsx(b,{...J("location"),type:"text",autoComplete:"off"})]}),s.jsxs(I,{fullWidth:!0,margin:"dense",children:[s.jsx(S,{children:i.BIO}),s.jsx(b,{...J("bio"),type:"text",autoComplete:"off"})]}),s.jsx(I,{fullWidth:!0,margin:"dense",children:s.jsx(f,{onUpload:(e,s)=>{const r=t(V),i=r.find((s=>s.language===e));i?i.file=s:r.push({language:e,file:s}),Y(r)},onDelete:e=>{const s=t(V);s.find((s=>s.language===e)).file=null,Y(s)}})}),s.jsxs("div",{className:"buttons",children:[s.jsx(w,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:re,children:i.CREATE}),s.jsx(w,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{try{F&&await o(F);for(const e of V)e.file&&await R(e.file);W("/suppliers")}catch{W("/suppliers")}},children:i.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[q&&s.jsx(E,{message:i.GENERIC_ERROR}),z&&s.jsx(E,{message:i.IMAGE_REQUIRED})]})]})]})}),B&&s.jsx(C,{text:i.PLEASE_WAIT})]})};export{W as default};
