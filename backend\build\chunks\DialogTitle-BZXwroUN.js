import{r as o}from"./router-BtYqujaw.js";import{i as t,j as s,k as r}from"../entries/index-CEzJO5Xy.js";import{e as a,f as e}from"./Grow-CjOKj0i1.js";import{s as i,c as n}from"./Button-DGZYUY3P.js";import{T as m}from"./Backdrop-Bzn12VyM.js";const p=i(m,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),f=o.forwardRef((function(i,m){const f=t({props:i,name:"MuiDialogTitle"}),{className:c,id:l,...u}=f,d=f,j=(o=>{const{classes:t}=o;return n({root:["root"]},e,t)})(d),{titleId:x=l}=o.useContext(a);return s.jsx(p,{component:"h2",className:r(j.root,c),ownerState:d,ref:m,variant:"h6",id:l??x,...u})}));export{f as D};
