import{z as e}from"./zod-4O8Zwsja.js";import{j as s,a,N as t,b as n,s as o,c as i,O as l,K as c,z as r,e as m}from"../entries/index-CEzJO5Xy.js";import{r as d}from"./router-BtYqujaw.js";import{c as f,b as p,e as u,f as h}from"./SupplierService-DSnTbAgG.js";import{I as j}from"./IconButton-CnBvmeAK.js";import{c as N}from"./Grow-CjOKj0i1.js";import{D as g}from"./Delete-CnqjtpsJ.js";const _=N(s.jsx("path",{d:"M5 20h14v-2H5zm0-10h4v6h6v-6h4l-7-7z"})),T=e.object({fullName:e.string(),email:e.string().email({message:a.EMAIL_NOT_VALID}),phone:e.string().refine((e=>!e||t.isMobilePhone(e)),{message:a.PHONE_NOT_VALID}).optional(),location:e.string().optional(),bio:e.string().optional(),blacklisted:e.boolean().optional(),payLater:e.boolean().optional(),licenseRequired:e.boolean().optional(),minimumRentalDays:e.string().refine((e=>!e||/^\d+$/.test(e)),{message:a.FIELD_NOT_VALID}).optional(),priceChangeRate:e.string().refine((e=>!e||/^-?\d+(\.\d+)?$/.test(e)),{message:a.FIELD_NOT_VALID}).optional(),supplierDressLimit:e.string().refine((e=>!e||/^\d+$/.test(e)),{message:a.FIELD_NOT_VALID}).optional(),notifyAdminOnNewDress:e.boolean().optional()}),D=new n({fr:{TITLE:"Contrats"},en:{TITLE:"Contracts"},es:{TITLE:"Contratos"}});o(D);const L=e=>{const a=i.c(20),{supplier:t,onUpload:n,onDelete:o}=e;let N;a[0]===Symbol.for("react.memo_cache_sentinel")?(N=[],a[0]=N):N=a[0];const[T,L]=d.useState(N),[I,b]=d.useState("");let x,C,E;a[1]!==t?.contracts?(x=()=>{const e=[];for(const s of m._LANGUAGES){const a={code:s.code,label:s.label,filename:t?.contracts?.find((e=>e.language===s.code))?.file||null};e.push(a)}L(e)},a[1]=t?.contracts,a[2]=x):x=a[2],a[3]!==t?(C=[t],a[3]=t,a[4]=C):C=a[4],d.useEffect(x,C),a[5]!==I||a[6]!==T||a[7]!==n||a[8]!==t?(E=e=>{if(!e.target.files)return void r();const s=new FileReader,a=e.target.files[0];s.onloadend=async()=>{try{let e=null;if(t){const s=await u(t._id,I,a);200===s.status?e=s.data:r()}else{const s=T.find((e=>e.code===I)).filename;s&&await p(s),e=await h(I,a)}if(e){const s=c(T);s.find((e=>e.code===I)).filename=e,L(s),n&&n(I,e)}}catch(e){r(e)}},s.readAsDataURL(a)},a[5]=I,a[6]=T,a[7]=n,a[8]=t,a[9]=E):E=a[9];const y=E;let A,v,O,w;return a[10]===Symbol.for("react.memo_cache_sentinel")?(A=s.jsx("div",{className:"title",children:D.TITLE}),a[10]=A):A=a[10],a[11]!==T||a[12]!==o||a[13]!==t?(v=T.map((e=>s.jsxs("div",{className:"contract",children:[s.jsx("span",{className:"label",children:e.label}),s.jsx("span",{className:"filename",children:e.filename?s.jsx("a",{href:`${l(t?m.CDN_CONTRACTS:m.CDN_TEMP_CONTRACTS,"/")}/${e.filename}`,children:e.filename}):"-"}),s.jsxs("div",{className:"actions",children:[s.jsx(j,{size:"small",onClick:async()=>{b(e.code);const s=document.getElementById("upload-contract");s.value="",setTimeout((()=>{s.click()}),0)},children:s.jsx(_,{className:"upload-icon"})}),e.filename&&s.jsx(j,{size:"small",onClick:async()=>{try{let s;if(s=t?await f(t._id,e.code):await p(e.filename),200===s){const s=c(T);s.find((s=>s.code===e.code)).filename=null,L(s),o&&o(e.code)}else r()}catch(s){r(s)}},children:s.jsx(g,{className:"delete-icon"})})]})]},e.code))),a[11]=T,a[12]=o,a[13]=t,a[14]=v):v=a[14],a[15]!==y?(O=s.jsx("input",{id:"upload-contract",type:"file",hidden:!0,onChange:y}),a[15]=y,a[16]=O):O=a[16],a[17]!==v||a[18]!==O?(w=s.jsxs("div",{className:"contracts",children:[A,v,O]}),a[17]=v,a[18]=O,a[19]=w):w=a[19],w};export{L as C,T as s};
