import{r}from"./router-BtYqujaw.js";import{i as e,k as o,j as s}from"../entries/index-xsXxT3-W.js";import{b as t}from"./Menu-C_-X8cS7.js";import{g as a,l as n}from"./listItemTextClasses-BcbgzvlE.js";import{u as i}from"./useSlot-DiTut-u0.js";import{s as p,c as m}from"./Button-BeKLLPpp.js";import{T as y,t as d}from"./Backdrop-Czag2Ija.js";const l=p("div",{name:"MuiListItemText",slot:"Root",overridesResolver:(r,e)=>{const{ownerState:o}=r;return[{[`& .${n.primary}`]:e.primary},{[`& .${n.secondary}`]:e.secondary},e.root,o.inset&&e.inset,o.primary&&o.secondary&&e.multiline,o.dense&&e.dense]}})({flex:"1 1 auto",minWidth:0,marginTop:4,marginBottom:4,[`.${d.root}:where(& .${n.primary})`]:{display:"block"},[`.${d.root}:where(& .${n.secondary})`]:{display:"block"},variants:[{props:({ownerState:r})=>r.primary&&r.secondary,style:{marginTop:6,marginBottom:6}},{props:({ownerState:r})=>r.inset,style:{paddingLeft:56}}]}),c=r.forwardRef((function(n,p){const d=e({props:n,name:"MuiListItemText"}),{children:c,className:u,disableTypography:x=!1,inset:f=!1,primary:T,primaryTypographyProps:w,secondary:h,secondaryTypographyProps:j,slots:g={},slotProps:b={},...v}=d,{dense:P}=r.useContext(t);let S=null!=T?T:c,$=h;const k={...d,disableTypography:x,inset:f,primary:!!S,secondary:!!$,dense:P},B=(r=>{const{classes:e,inset:o,primary:s,secondary:t,dense:n}=r;return m({root:["root",o&&"inset",n&&"dense",s&&t&&"multiline"],primary:["primary"],secondary:["secondary"]},a,e)})(k),L={slots:g,slotProps:{primary:w,secondary:j,...b}},[N,F]=i("root",{className:o(B.root,u),elementType:l,externalForwardedProps:{...L,...v},ownerState:k,ref:p}),[I,M]=i("primary",{className:B.primary,elementType:y,externalForwardedProps:L,ownerState:k}),[R,C]=i("secondary",{className:B.secondary,elementType:y,externalForwardedProps:L,ownerState:k});return null==S||S.type===y||x||(S=s.jsx(I,{variant:P?"body2":"body1",component:M?.variant?void 0:"span",...M,children:S})),null==$||$.type===y||x||($=s.jsx(R,{variant:"body2",color:"textSecondary",...C,children:$})),s.jsxs(N,{...F,children:[S,$]})}));export{c as L};
