import{u as e,j as t,e as s,R as o,a,J as r,z as i,P as n,B as l}from"../entries/index-CEzJO5Xy.js";import{r as m}from"./router-BtYqujaw.js";import{L as c}from"./Layout-BQBjg4Lf.js";import{s as p,C as d,P as j,a as u}from"./ParkingSpotEditList-ClS4WTgI.js";import{a as h,v as g,b as f}from"./LocationService-BtQFgoWL.js";import{A as x}from"./Avatar-BtfxKR-8.js";import{S as v}from"./SimpleBackdrop-Bf3qjF13.js";import{P as S}from"./Paper-CcwAvfvc.js";import{F as C,I as N}from"./InputLabel-BbcIE26O.js";import{I as A}from"./Input-BQdee9z7.js";import{F as I}from"./FormHelperText-DFSsjBsL.js";import{B as b}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./CountryService-DnJKuIXr.js";import"./MultipleSelect-C7xTvWe9.js";import"./Autocomplete-CviOU_ku.js";import"./OutlinedInput-g8mR4MM3.js";import"./useFormControl-B7jXtRD7.js";import"./useSlot-CtA82Ni6.js";import"./TextField-BAse--ht.js";import"./Backdrop-Bzn12VyM.js";import"./Menu-ZU0DMgjT.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-Cay5TZBz.js";import"./AccountCircle-khVEeiad.js";import"./Chip-CAtDqtgp.js";import"./IconButton-CnBvmeAK.js";import"./Avatar-Dix3YM8x.js";import"./Flag-BR6CpE1z.js";import"./DressService-J0XavNJj.js";import"./Badge-B3LKl4T2.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";const L=()=>{const{user:L}=e(),[T,y]=m.useState(!1),[E,O]=m.useState([]),[P,_]=m.useState([]),[w,B]=m.useState(),[D,W]=m.useState(!1),[k,G]=m.useState(),[U,F]=m.useState(""),[R,H]=m.useState(""),[z,q]=m.useState([]);return t.jsxs(c,{onLoad:()=>{y(!0)},strict:!0,children:[t.jsx("div",{className:"create-location",children:t.jsxs(S,{className:"location-form location-form-wrapper",elevation:10,style:T?{}:{display:"none"},children:[t.jsx("h1",{className:"location-form-title",children:p.NEW_LOCATION_HEADING}),t.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(!w)return void i();let e=!0;const t=r(P);for(let s=0;s<P.length;s+=1)t[s]=!1;for(let s=0;s<E.length;s+=1){const o=E[s],a=200===await g({language:o.language,name:o.name});e=e&&a,a||(t[s]=!0)}if(_(t),e){const e={country:w?._id,latitude:R?Number(R):void 0,longitude:U?Number(U):void 0,names:E,image:k,parkingSpots:z,supplier:n(L)?L?._id:void 0};if(200===await f(e)){const e=r(E);for(let t=0;t<E.length;t+=1)e[t].name="";O(e),G(void 0),B(null),F(""),H(""),q([]),l(p.LOCATION_CREATED)}else i()}}catch(t){i(t)}},children:[t.jsx(x,{type:o.Location,avatar:k,mode:"create",record:null,size:"large",readonly:!1,onBeforeUpload:()=>{W(!0)},onChange:e=>{W(!1),G(e)},color:"disabled",className:"avatar-ctn"}),t.jsx(C,{fullWidth:!0,margin:"dense",children:t.jsx(d,{label:p.COUNTRY,variant:"standard",onChange:e=>{B(e.length>0?e[0]:null)},value:w,required:!0})}),s._LANGUAGES.map(((e,s)=>t.jsxs(C,{fullWidth:!0,margin:"dense",children:[t.jsx(N,{className:"required",children:`${a.NAME} (${e.label})`}),t.jsx(A,{type:"text",value:E[s]&&E[s].name||"",error:P[s],required:!0,onChange:t=>{const o=r(E);o[s]={language:e.code,name:t.target.value},O(o);const a=r(P);a[s]=!1,_(a)},autoComplete:"off"}),t.jsx(I,{error:P[s],children:P[s]&&p.INVALID_LOCATION||""})]},e.code))),t.jsxs(C,{fullWidth:!0,margin:"dense",children:[t.jsx(N,{children:a.LATITUDE}),t.jsx(j,{value:R,onChange:e=>{H(e.target.value)}})]}),t.jsxs(C,{fullWidth:!0,margin:"dense",children:[t.jsx(N,{children:a.LONGITUDE}),t.jsx(j,{value:U,onChange:e=>{F(e.target.value)}})]}),t.jsx(u,{title:p.PARKING_SPOTS,values:z,onAdd:e=>{const t=r(z);t.push(e),q(t)},onUpdate:(e,t)=>{const s=r(z);s[t]=e,q(s)},onDelete:(e,t)=>{const s=r(z);s.splice(t,1),q(s)}}),t.jsxs("div",{className:"buttons",children:[t.jsx(b,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",children:a.CREATE}),t.jsx(b,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{k&&await h(k),window.location.href="/locations"},children:a.CANCEL})]})]})]})}),D&&t.jsx(v,{text:a.PLEASE_WAIT})]})};export{L as default};
