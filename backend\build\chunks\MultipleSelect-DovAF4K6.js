import{j as e,R as s,G as n,e as a,c as t}from"../entries/index-xsXxT3-W.js";import{r}from"./router-BtYqujaw.js";import{A as i}from"./Autocomplete-CWN5GAd4.js";import{A as o}from"./Avatar-Dvwllg8p.js";import{A as l}from"./AccountCircle-DdIeIbov.js";import{L as m}from"./LocationService-6NvQT9iL.js";import{C as p}from"./Flag-CMGasDVj.js";import{C as c}from"./Chip-MGF1mKZa.js";import{T as d,I as u}from"./TextField-D_yQOTzE.js";const j=r.forwardRef(((s,n)=>{const a=t.c(7);let i,o;a[0]!==s?(({children:i,...o}=s),a[0]=s,a[1]=i,a[2]=o):(i=a[1],o=a[2]);const l=r.useRef(null);let m,p;return a[3]===Symbol.for("react.memo_cache_sentinel")?(m=()=>l.current,a[3]=m):m=a[3],r.useImperativeHandle(n,m),a[4]!==i||a[5]!==o?(p=e.jsx("ul",{...o,ref:l,role:"list-box",children:i}),a[4]=i,a[5]=o,a[6]=p):p=a[6],p})),x=({label:t,reference:x,selectedOptions:h,key:g,required:N,options:f,ListboxProps:y,loading:E,multiple:_,type:A,variant:C,readOnly:I,callbackFromMultipleSelect:v,onFocus:P,onInputChange:S,onClear:b,onOpen:k})=>{const[O,R]=r.useState([]),[D,F]=r.useState("");return f||(f=[]),r.useEffect((()=>{h&&R(h),h&&0===h.length&&F("")}),[h,A]),e.jsx("div",{className:"multiple-select",children:e.jsx(i,{readOnly:I,options:f,value:_?O:O.length>0?O[0]:null,getOptionLabel:e=>e&&e.name||"",isOptionEqualToValue:(e,s)=>e._id===s._id,onChange:(e,s)=>{if(!e||"keydown"!==e.type||!("key"in e)||"Enter"!==e.key)if(g=g||"",_)R(s),v&&v(s,g,x),0===s.length&&b&&b();else{const e=s&&[s]||[];R(e),v&&v(e,g,x),s||b&&b()}},onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},clearOnBlur:!1,clearOnEscape:!1,loading:E,multiple:_,handleHomeEndKeys:!1,renderInput:r=>{const{inputProps:i}=r;if(i.autoComplete="off",A===s.User&&!_&&1===O.length&&O[0]){const s=O[0];return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:s.image?e.jsx(o,{src:n(a.CDN_USERS,s.image),className:"avatar-small suo"}):e.jsx(l,{className:"avatar-small suo",color:"disabled"})}),r.InputProps.startAdornment]})}}})}if(A===s.Supplier&&!_&&1===O.length&&O[0]){const s=O[0];return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:e.jsx("div",{className:"supplier-ia",children:e.jsx("img",{src:n(a.CDN_USERS,s.image),alt:s.name})})}),r.InputProps.startAdornment]})}}})}if(A===s.Location&&!_&&1===O.length&&O[0])return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:e.jsx(m,{})}),r.InputProps.startAdornment]})}}});if(A===s.Country&&!_&&1===O.length&&O[0])return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:e.jsx(p,{})}),r.InputProps.startAdornment]})}}});if(A===s.Dress&&!_&&1===O.length&&O[0]){const s=O[0];return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:e.jsx("img",{src:n(a.CDN_CARS,s.image),alt:s.name,style:{height:a.SELECTED_CAR_OPTION_IMAGE_HEIGHT}})}),r.InputProps.startAdornment]})}}})}return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N&&O&&0===O.length})},inputValue:D,onInputChange:(e,s)=>{F(s),S&&S(e)},renderTags:(e,s)=>e.map(((e,n)=>r.createElement(c,{...s({index:n}),key:e._id,label:e.name}))),renderOption:(t,i)=>{"key"in t&&delete t.key;const c=t;return A===s.User?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image",children:i.image?e.jsx(o,{src:n(a.CDN_USERS,i.image),className:"avatar-medium"}):e.jsx(l,{className:"avatar-medium",color:"disabled"})}),e.jsx("span",{className:"option-name",children:i.name})):A===s.Supplier?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image supplier-ia",children:e.jsx("img",{src:n(a.CDN_USERS,i.image),alt:i.name})}),e.jsx("span",{className:"option-name",children:i.name})):A===s.Location?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image",children:e.jsx(m,{})}),e.jsx("span",{className:"option-name",children:i.name})):A===s.Country?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image",children:e.jsx(p,{})}),e.jsx("span",{className:"option-name",children:i.name})):A===s.Dress?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image car-ia",children:e.jsx("img",{src:n(a.CDN_CARS,i.image),alt:i.name,style:{height:a.CAR_OPTION_IMAGE_HEIGHT}})}),e.jsx("span",{className:"car-option-name",children:i.name})):r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{children:i.name}))},onFocus:P||void 0,onOpen:k||void 0,slotProps:{listbox:{component:j,...y}}})})};export{x as M};
