import{r as e}from"./router-BtYqujaw.js";import{j as t,i as o,k as n,l as r,aq as a}from"../entries/index-CEzJO5Xy.js";import{i}from"./Input-BQdee9z7.js";import{i as l}from"./InputLabel-BbcIE26O.js";import{o as s}from"./OutlinedInput-g8mR4MM3.js";import{A as p,f as u}from"./TextField-BAse--ht.js";import{c,d,s as g,P as f}from"./Grow-CjOKj0i1.js";import{f as m,e as h,g as b,a as x,s as v,c as y,m as I}from"./Button-DGZYUY3P.js";import{u as $}from"./useSlot-CtA82Ni6.js";import{u as O}from"./AccountCircle-khVEeiad.js";import{P as S}from"./Paper-CcwAvfvc.js";import{C as P}from"./Chip-CAtDqtgp.js";import{I as C}from"./IconButton-CnBvmeAK.js";const k=c(t.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}));function w(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}const A=function(e={}){const{ignoreAccents:t=!0,ignoreCase:o=!0,limit:n,matchFrom:r="any",stringify:a,trim:i=!1}=e;return(e,{inputValue:l,getOptionLabel:s})=>{let p=i?l.trim():l;o&&(p=p.toLowerCase()),t&&(p=w(p));const u=p?e.filter((e=>{let n=(a||s)(e);return o&&(n=n.toLowerCase()),t&&(n=w(n)),"start"===r?n.startsWith(p):n.includes(p)})):e;return"number"==typeof n?u.slice(0,n):u}}(),L=e=>null!==e.current&&e.current.parentElement?.contains(document.activeElement),T=[];function R(e,t,o,n){if(t||null==e||n)return"";const r=o(e);return"string"==typeof r?r:""}function j(e){return b("MuiListSubheader",e)}x("MuiListSubheader",["root","colorPrimary","colorInherit","gutters","inset","sticky"]);const M=v("li",{name:"MuiListSubheader",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,"default"!==o.color&&t[`color${r(o.color)}`],!o.disableGutters&&t.gutters,o.inset&&t.inset,!o.disableSticky&&t.sticky]}})(I((({theme:e})=>({boxSizing:"border-box",lineHeight:"48px",listStyle:"none",color:(e.vars||e).palette.text.secondary,fontFamily:e.typography.fontFamily,fontWeight:e.typography.fontWeightMedium,fontSize:e.typography.pxToRem(14),variants:[{props:{color:"primary"},style:{color:(e.vars||e).palette.primary.main}},{props:{color:"inherit"},style:{color:"inherit"}},{props:({ownerState:e})=>!e.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:e})=>e.inset,style:{paddingLeft:72}},{props:({ownerState:e})=>!e.disableSticky,style:{position:"sticky",top:0,zIndex:1,backgroundColor:(e.vars||e).palette.background.paper}}]})))),D=e.forwardRef((function(e,a){const i=o({props:e,name:"MuiListSubheader"}),{className:l,color:s="default",component:p="li",disableGutters:u=!1,disableSticky:c=!1,inset:d=!1,...g}=i,f={...i,color:s,component:p,disableGutters:u,disableSticky:c,inset:d},m=(e=>{const{classes:t,color:o,disableGutters:n,inset:a,disableSticky:i}=e,l={root:["root","default"!==o&&`color${r(o)}`,!n&&"gutters",a&&"inset",!i&&"sticky"]};return y(l,j,t)})(f);return t.jsx(M,{as:p,className:n(m.root,l),ref:a,ownerState:f,...g})}));function N(e){return b("MuiAutocomplete",e)}D&&(D.muiSkipListHighlight=!0);const z=x("MuiAutocomplete",["root","expanded","fullWidth","focused","focusVisible","tag","tagSizeSmall","tagSizeMedium","hasPopupIcon","hasClearIcon","inputRoot","input","inputFocused","endAdornment","clearIndicator","popupIndicator","popupIndicatorOpen","popper","popperDisablePortal","paper","listbox","loading","noOptions","option","groupLabel","groupUl"]);var E,F;const V=v("div",{name:"MuiAutocomplete",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e,{fullWidth:n,hasClearIcon:a,hasPopupIcon:i,inputFocused:l,size:s}=o;return[{[`& .${z.tag}`]:t.tag},{[`& .${z.tag}`]:t[`tagSize${r(s)}`]},{[`& .${z.inputRoot}`]:t.inputRoot},{[`& .${z.input}`]:t.input},{[`& .${z.input}`]:l&&t.inputFocused},t.root,n&&t.fullWidth,i&&t.hasPopupIcon,a&&t.hasClearIcon]}})({[`&.${z.focused} .${z.clearIndicator}`]:{visibility:"visible"},"@media (pointer: fine)":{[`&:hover .${z.clearIndicator}`]:{visibility:"visible"}},[`& .${z.tag}`]:{margin:3,maxWidth:"calc(100% - 6px)"},[`& .${z.inputRoot}`]:{[`.${z.hasPopupIcon}&, .${z.hasClearIcon}&`]:{paddingRight:30},[`.${z.hasPopupIcon}.${z.hasClearIcon}&`]:{paddingRight:56},[`& .${z.input}`]:{width:0,minWidth:30}},[`& .${i.root}`]:{paddingBottom:1,"& .MuiInput-input":{padding:"4px 4px 4px 0px"}},[`& .${i.root}.${l.sizeSmall}`]:{[`& .${i.input}`]:{padding:"2px 4px 3px 0"}},[`& .${s.root}`]:{padding:9,[`.${z.hasPopupIcon}&, .${z.hasClearIcon}&`]:{paddingRight:39},[`.${z.hasPopupIcon}.${z.hasClearIcon}&`]:{paddingRight:65},[`& .${z.input}`]:{padding:"7.5px 4px 7.5px 5px"},[`& .${z.endAdornment}`]:{right:9}},[`& .${s.root}.${l.sizeSmall}`]:{paddingTop:6,paddingBottom:6,paddingLeft:6,[`& .${z.input}`]:{padding:"2.5px 4px 2.5px 8px"}},[`& .${u.root}`]:{paddingTop:19,paddingLeft:8,[`.${z.hasPopupIcon}&, .${z.hasClearIcon}&`]:{paddingRight:39},[`.${z.hasPopupIcon}.${z.hasClearIcon}&`]:{paddingRight:65},[`& .${u.input}`]:{padding:"7px 4px"},[`& .${z.endAdornment}`]:{right:9}},[`& .${u.root}.${l.sizeSmall}`]:{paddingBottom:1,[`& .${u.input}`]:{padding:"2.5px 4px"}},[`& .${l.hiddenLabel}`]:{paddingTop:8},[`& .${u.root}.${l.hiddenLabel}`]:{paddingTop:0,paddingBottom:0,[`& .${z.input}`]:{paddingTop:16,paddingBottom:17}},[`& .${u.root}.${l.hiddenLabel}.${l.sizeSmall}`]:{[`& .${z.input}`]:{paddingTop:8,paddingBottom:9}},[`& .${z.input}`]:{flexGrow:1,textOverflow:"ellipsis",opacity:0},variants:[{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{[`& .${z.tag}`]:{margin:2,maxWidth:"calc(100% - 4px)"}}},{props:{inputFocused:!0},style:{[`& .${z.input}`]:{opacity:1}}},{props:{multiple:!0},style:{[`& .${z.inputRoot}`]:{flexWrap:"wrap"}}}]}),H=v("div",{name:"MuiAutocomplete",slot:"EndAdornment"})({position:"absolute",right:0,top:"50%",transform:"translate(0, -50%)"}),W=v(C,{name:"MuiAutocomplete",slot:"ClearIndicator"})({marginRight:-2,padding:4,visibility:"hidden"}),B=v(C,{name:"MuiAutocomplete",slot:"PopupIndicator",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.popupIndicator,o.popupOpen&&t.popupIndicatorOpen]}})({padding:2,marginRight:-2,variants:[{props:{popupOpen:!0},style:{transform:"rotate(180deg)"}}]}),G=v(f,{name:"MuiAutocomplete",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[{[`& .${z.option}`]:t.option},t.popper,o.disablePortal&&t.popperDisablePortal]}})(I((({theme:e})=>({zIndex:(e.vars||e).zIndex.modal,variants:[{props:{disablePortal:!0},style:{position:"absolute"}}]})))),K=v(S,{name:"MuiAutocomplete",slot:"Paper"})(I((({theme:e})=>({...e.typography.body1,overflow:"auto"})))),q=v("div",{name:"MuiAutocomplete",slot:"Loading"})(I((({theme:e})=>({color:(e.vars||e).palette.text.secondary,padding:"14px 16px"})))),U=v("div",{name:"MuiAutocomplete",slot:"NoOptions"})(I((({theme:e})=>({color:(e.vars||e).palette.text.secondary,padding:"14px 16px"})))),_=v("ul",{name:"MuiAutocomplete",slot:"Listbox"})(I((({theme:e})=>({listStyle:"none",margin:0,padding:"8px 0",maxHeight:"40vh",overflow:"auto",position:"relative",[`& .${z.option}`]:{minHeight:48,display:"flex",overflow:"hidden",justifyContent:"flex-start",alignItems:"center",cursor:"pointer",paddingTop:6,boxSizing:"border-box",outline:"0",WebkitTapHighlightColor:"transparent",paddingBottom:6,paddingLeft:16,paddingRight:16,[e.breakpoints.up("sm")]:{minHeight:"auto"},[`&.${z.focused}`]:{backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},'&[aria-disabled="true"]':{opacity:(e.vars||e).palette.action.disabledOpacity,pointerEvents:"none"},[`&.${z.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},'&[aria-selected="true"]':{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:a(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${z.focused}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:a(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:(e.vars||e).palette.action.selected}},[`&.${z.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:a(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}}}})))),J=v(D,{name:"MuiAutocomplete",slot:"GroupLabel"})(I((({theme:e})=>({backgroundColor:(e.vars||e).palette.background.paper,top:-8})))),Q=v("ul",{name:"MuiAutocomplete",slot:"GroupUl"})({padding:0,[`& .${z.option}`]:{paddingLeft:24}}),X=e.forwardRef((function(a,i){const l=o({props:a,name:"MuiAutocomplete"}),{autoComplete:s=!1,autoHighlight:u=!1,autoSelect:c=!1,blurOnSelect:b=!1,ChipProps:x,className:v,clearIcon:I=E||(E=t.jsx(k,{fontSize:"small"})),clearOnBlur:C=!l.freeSolo,clearOnEscape:w=!1,clearText:j="Clear",closeText:M="Close",componentsProps:D,defaultValue:z=(l.multiple?[]:null),disableClearable:X=!1,disableCloseOnSelect:Y=!1,disabled:Z=!1,disabledItemsFocusable:ee=!1,disableListWrap:te=!1,disablePortal:oe=!1,filterOptions:ne,filterSelectedOptions:re=!1,forcePopupIcon:ae="auto",freeSolo:ie=!1,fullWidth:le=!1,getLimitTagsText:se=e=>`+${e}`,getOptionDisabled:pe,getOptionKey:ue,getOptionLabel:ce,isOptionEqualToValue:de,groupBy:ge,handleHomeEndKeys:fe=!l.freeSolo,id:me,includeInputInList:he=!1,inputValue:be,limitTags:xe=-1,ListboxComponent:ve,ListboxProps:ye,loading:Ie=!1,loadingText:$e="Loading…",multiple:Oe=!1,noOptionsText:Se="No options",onChange:Pe,onClose:Ce,onHighlightChange:ke,onInputChange:we,onOpen:Ae,open:Le,openOnFocus:Te=!1,openText:Re="Open",options:je,PaperComponent:Me,PopperComponent:De,popupIcon:Ne=F||(F=t.jsx(p,{})),readOnly:ze=!1,renderGroup:Ee,renderInput:Fe,renderOption:Ve,renderTags:He,renderValue:We,selectOnFocus:Be=!l.freeSolo,size:Ge="medium",slots:Ke={},slotProps:qe={},value:Ue,..._e}=l,{getRootProps:Je,getInputProps:Qe,getInputLabelProps:Xe,getPopupIndicatorProps:Ye,getClearProps:Ze,getItemProps:et,getListboxProps:tt,getOptionProps:ot,value:nt,dirty:rt,expanded:at,id:it,popupOpen:lt,focused:st,focusedItem:pt,anchorEl:ut,setAnchorEl:ct,inputValue:dt,groupedOptions:gt}=function(t){const{unstable_isActiveElementInListbox:o=L,unstable_classNamePrefix:n="Mui",autoComplete:r=!1,autoHighlight:a=!1,autoSelect:i=!1,blurOnSelect:l=!1,clearOnBlur:s=!t.freeSolo,clearOnEscape:p=!1,componentName:u="useAutocomplete",defaultValue:c=(t.multiple?T:null),disableClearable:f=!1,disableCloseOnSelect:b=!1,disabled:x,disabledItemsFocusable:v=!1,disableListWrap:y=!1,filterOptions:I=A,filterSelectedOptions:$=!1,freeSolo:S=!1,getOptionDisabled:P,getOptionKey:C,getOptionLabel:k=e=>e.label??e,groupBy:w,handleHomeEndKeys:j=!t.freeSolo,id:M,includeInputInList:D=!1,inputValue:N,isOptionEqualToValue:z=(e,t)=>e===t,multiple:E=!1,onChange:F,onClose:V,onHighlightChange:H,onInputChange:W,onOpen:B,open:G,openOnFocus:K=!1,options:q,readOnly:U=!1,renderValue:_,selectOnFocus:J=!t.freeSolo,value:Q}=t,X=m(M);let Y=k;Y=e=>{const t=k(e);return"string"!=typeof t?String(t):t};const Z=e.useRef(!1),ee=e.useRef(!0),te=e.useRef(null),oe=e.useRef(null),[ne,re]=e.useState(null),[ae,ie]=e.useState(-1),le=a?0:-1,se=e.useRef(le),pe=e.useRef(R(c??Q,E,Y)).current,[ue,ce]=d({controlled:Q,default:c,name:u}),[de,ge]=d({controlled:N,default:pe,name:u,state:"inputValue"}),[fe,me]=e.useState(!1),he=e.useCallback(((e,t,o)=>{if(!(E?ue.length<t.length:null!==t)&&!s)return;const n=R(t,E,Y,_);de!==n&&(ge(n),W&&W(e,n,o))}),[Y,de,E,W,ge,s,ue,_]),[be,xe]=d({controlled:G,default:!1,name:u,state:"open"}),[ve,ye]=e.useState(!0),Ie=!E&&null!=ue&&de===Y(ue),$e=be&&!U,Oe=$e?I(q.filter((e=>!$||!(E?ue:[ue]).some((t=>null!==t&&z(e,t))))),{inputValue:Ie&&ve?"":de,getOptionLabel:Y}):[],Se=O({filteredOptions:Oe,value:ue,inputValue:de});e.useEffect((()=>{const e=ue!==Se.value;fe&&!e||S&&!e||he(null,ue,"reset")}),[ue,he,fe,Se.value,S]);const Pe=be&&Oe.length>0&&!U,Ce=h((e=>{if(-1===e)te.current.focus();else{const t=_?"data-item-index":"data-tag-index";ne.querySelector(`[${t}="${e}"]`).focus()}}));e.useEffect((()=>{E&&ae>ue.length-1&&(ie(-1),Ce(-1))}),[ue,E,ae,Ce]);const ke=h((({event:e,index:t,reason:o})=>{if(se.current=t,-1===t?te.current.removeAttribute("aria-activedescendant"):te.current.setAttribute("aria-activedescendant",`${X}-option-${t}`),H&&["mouse","keyboard","touch"].includes(o)&&H(e,-1===t?null:Oe[t],o),!oe.current)return;const r=oe.current.querySelector(`[role="option"].${n}-focused`);r&&(r.classList.remove(`${n}-focused`),r.classList.remove(`${n}-focusVisible`));let a=oe.current;if("listbox"!==oe.current.getAttribute("role")&&(a=oe.current.parentElement.querySelector('[role="listbox"]')),!a)return;if(-1===t)return void(a.scrollTop=0);const i=oe.current.querySelector(`[data-option-index="${t}"]`);if(i&&(i.classList.add(`${n}-focused`),"keyboard"===o&&i.classList.add(`${n}-focusVisible`),a.scrollHeight>a.clientHeight&&"mouse"!==o&&"touch"!==o)){const e=i,t=a.clientHeight+a.scrollTop,o=e.offsetTop+e.offsetHeight;o>t?a.scrollTop=o-a.clientHeight:e.offsetTop-e.offsetHeight*(w?1.3:0)<a.scrollTop&&(a.scrollTop=e.offsetTop-e.offsetHeight*(w?1.3:0))}})),we=h((({event:e,diff:t,direction:o="next",reason:n})=>{if(!$e)return;const a=function(e,t){if(!oe.current||e<0||e>=Oe.length)return-1;let o=e;for(;;){const n=oe.current.querySelector(`[data-option-index="${o}"]`),r=!v&&(!n||n.disabled||"true"===n.getAttribute("aria-disabled"));if(n&&n.hasAttribute("tabindex")&&!r)return o;if(o="next"===t?(o+1)%Oe.length:(o-1+Oe.length)%Oe.length,o===e)return-1}}((()=>{const e=Oe.length-1;if("reset"===t)return le;if("start"===t)return 0;if("end"===t)return e;const o=se.current+t;return o<0?-1===o&&D?-1:y&&-1!==se.current||Math.abs(t)>1?0:e:o>e?o===e+1&&D?-1:y||Math.abs(t)>1?e:0:o})(),o);if(ke({index:a,reason:n,event:e}),r&&"reset"!==t)if(-1===a)te.current.value=de;else{const e=Y(Oe[a]);te.current.value=e,0===e.toLowerCase().indexOf(de.toLowerCase())&&de.length>0&&te.current.setSelectionRange(de.length,e.length)}})),Ae=e.useCallback((()=>{if(!$e)return;const e=(()=>{if(-1!==se.current&&Se.filteredOptions&&Se.filteredOptions.length!==Oe.length&&Se.inputValue===de&&(E?ue.length===Se.value.length&&Se.value.every(((e,t)=>Y(ue[t])===Y(e))):(e=Se.value,t=ue,(e?Y(e):"")===(t?Y(t):"")))){const e=Se.filteredOptions[se.current];if(e)return Oe.findIndex((t=>Y(t)===Y(e)))}var e,t;return-1})();if(-1!==e)return void(se.current=e);const t=E?ue[0]:ue;if(0!==Oe.length&&null!=t){if(oe.current)if(null==t)se.current>=Oe.length-1?ke({index:Oe.length-1}):ke({index:se.current});else{const e=Oe[se.current];if(E&&e&&-1!==ue.findIndex((t=>z(e,t))))return;const o=Oe.findIndex((e=>z(e,t)));-1===o?we({diff:"reset"}):ke({index:o})}}else we({diff:"reset"})}),[Oe.length,!E&&ue,$,we,ke,$e,de,E]),Le=h((e=>{g(oe,e),e&&Ae()}));e.useEffect((()=>{Ae()}),[Ae]);const Te=e=>{be||(xe(!0),ye(!0),B&&B(e))},Re=(e,t)=>{be&&(xe(!1),V&&V(e,t))},je=(e,t,o,n)=>{if(E){if(ue.length===t.length&&ue.every(((e,o)=>e===t[o])))return}else if(ue===t)return;F&&F(e,t,o,n),ce(t)},Me=e.useRef(!1),De=(e,t,o="selectOption",n="options")=>{let r=o,a=t;if(E){a=Array.isArray(ue)?ue.slice():[];const e=a.findIndex((e=>z(t,e)));-1===e?a.push(t):"freeSolo"!==n&&(a.splice(e,1),r="removeOption")}he(e,a,r),je(e,a,r,{option:t}),b||e&&(e.ctrlKey||e.metaKey)||Re(e,r),(!0===l||"touch"===l&&Me.current||"mouse"===l&&!Me.current)&&te.current.blur()},Ne=(e,t)=>{if(!E)return;""===de&&Re(e,"toggleInput");let o=ae;-1===ae?""===de&&"previous"===t&&(o=ue.length-1):(o+="next"===t?1:-1,o<0&&(o=0),o===ue.length&&(o=-1)),o=function(e,t){if(-1===e)return-1;let o=e;for(;;){if("next"===t&&o===ue.length||"previous"===t&&-1===o)return-1;const e=_?"data-item-index":"data-tag-index",n=ne.querySelector(`[${e}="${o}"]`);if(n&&n.hasAttribute("tabindex")&&!n.disabled&&"true"!==n.getAttribute("aria-disabled"))return o;o+="next"===t?1:-1}}(o,t),ie(o),Ce(o)},ze=e=>{Z.current=!0,ge(""),W&&W(e,"","clear"),je(e,E?[]:null,"clear")},Ee=e=>t=>{if(e.onKeyDown&&e.onKeyDown(t),!t.defaultMuiPrevented&&(-1===ae||["ArrowLeft","ArrowRight"].includes(t.key)||(ie(-1),Ce(-1)),229!==t.which))switch(t.key){case"Home":$e&&j&&(t.preventDefault(),we({diff:"start",direction:"next",reason:"keyboard",event:t}));break;case"End":$e&&j&&(t.preventDefault(),we({diff:"end",direction:"previous",reason:"keyboard",event:t}));break;case"PageUp":t.preventDefault(),we({diff:-5,direction:"previous",reason:"keyboard",event:t}),Te(t);break;case"PageDown":t.preventDefault(),we({diff:5,direction:"next",reason:"keyboard",event:t}),Te(t);break;case"ArrowDown":t.preventDefault(),we({diff:1,direction:"next",reason:"keyboard",event:t}),Te(t);break;case"ArrowUp":t.preventDefault(),we({diff:-1,direction:"previous",reason:"keyboard",event:t}),Te(t);break;case"ArrowLeft":!E&&_?Ce(0):Ne(t,"previous");break;case"ArrowRight":!E&&_?Ce(-1):Ne(t,"next");break;case"Enter":if(-1!==se.current&&$e){const e=Oe[se.current],o=!!P&&P(e);if(t.preventDefault(),o)return;De(t,e,"selectOption"),r&&te.current.setSelectionRange(te.current.value.length,te.current.value.length)}else S&&""!==de&&!1===Ie&&(E&&t.preventDefault(),De(t,de,"createOption","freeSolo"));break;case"Escape":$e?(t.preventDefault(),t.stopPropagation(),Re(t,"escape")):p&&(""!==de||E&&ue.length>0||_)&&(t.preventDefault(),t.stopPropagation(),ze(t));break;case"Backspace":if(E&&!U&&""===de&&ue.length>0){const e=-1===ae?ue.length-1:ae,o=ue.slice();o.splice(e,1),je(t,o,"removeOption",{option:ue[e]})}E||!_||U||(ce(null),Ce(-1));break;case"Delete":if(E&&!U&&""===de&&ue.length>0&&-1!==ae){const e=ae,o=ue.slice();o.splice(e,1),je(t,o,"removeOption",{option:ue[e]})}E||!_||U||(ce(null),Ce(-1))}},Fe=e=>{me(!0),K&&!Z.current&&Te(e)},Ve=e=>{o(oe)?te.current.focus():(me(!1),ee.current=!0,Z.current=!1,i&&-1!==se.current&&$e?De(e,Oe[se.current],"blur"):i&&S&&""!==de?De(e,de,"blur","freeSolo"):s&&he(e,ue,"blur"),Re(e,"blur"))},He=e=>{const t=e.target.value;de!==t&&(ge(t),ye(!1),W&&W(e,t,"input")),""===t?f||E||je(e,null,"clear"):Te(e)},We=e=>{const t=Number(e.currentTarget.getAttribute("data-option-index"));se.current!==t&&ke({event:e,index:t,reason:"mouse"})},Be=e=>{ke({event:e,index:Number(e.currentTarget.getAttribute("data-option-index")),reason:"touch"}),Me.current=!0},Ge=e=>{const t=Number(e.currentTarget.getAttribute("data-option-index"));De(e,Oe[t],"selectOption"),Me.current=!1},Ke=e=>t=>{const o=ue.slice();o.splice(e,1),je(t,o,"removeOption",{option:ue[e]})},qe=e=>{je(e,null,"removeOption",{option:ue})},Ue=e=>{be?Re(e,"toggleInput"):Te(e)},_e=e=>{e.currentTarget.contains(e.target)&&e.target.getAttribute("id")!==X&&e.preventDefault()},Je=e=>{e.currentTarget.contains(e.target)&&(te.current.focus(),J&&ee.current&&te.current.selectionEnd-te.current.selectionStart===0&&te.current.select(),ee.current=!1)},Qe=e=>{x||""!==de&&be||Ue(e)};let Xe=S&&de.length>0;Xe=Xe||(E?ue.length>0:null!==ue);let Ye=Oe;return w&&(Ye=Oe.reduce(((e,t,o)=>{const n=w(t);return e.length>0&&e[e.length-1].group===n?e[e.length-1].options.push(t):e.push({key:o,index:o,group:n,options:[t]}),e}),[])),x&&fe&&Ve(),{getRootProps:(e={})=>({...e,onKeyDown:Ee(e),onMouseDown:_e,onClick:Je}),getInputLabelProps:()=>({id:`${X}-label`,htmlFor:X}),getInputProps:()=>({id:X,value:de,onBlur:Ve,onFocus:Fe,onChange:He,onMouseDown:Qe,"aria-activedescendant":$e?"":null,"aria-autocomplete":r?"both":"list","aria-controls":Pe?`${X}-listbox`:void 0,"aria-expanded":Pe,autoComplete:"off",ref:te,autoCapitalize:"none",spellCheck:"false",role:"combobox",disabled:x}),getClearProps:()=>({tabIndex:-1,type:"button",onClick:ze}),getItemProps:({index:e=0}={})=>({...E&&{key:e},..._?{"data-item-index":e}:{"data-tag-index":e},tabIndex:-1,...!U&&{onDelete:E?Ke(e):qe}}),getPopupIndicatorProps:()=>({tabIndex:-1,type:"button",onClick:Ue}),getTagProps:({index:e})=>({key:e,"data-tag-index":e,tabIndex:-1,...!U&&{onDelete:Ke(e)}}),getListboxProps:()=>({role:"listbox",id:`${X}-listbox`,"aria-labelledby":`${X}-label`,ref:Le,onMouseDown:e=>{e.preventDefault()}}),getOptionProps:({index:e,option:t})=>{const o=(E?ue:[ue]).some((e=>null!=e&&z(t,e))),n=!!P&&P(t);return{key:C?.(t)??Y(t),tabIndex:-1,role:"option",id:`${X}-option-${e}`,onMouseMove:We,onClick:Ge,onTouchStart:Be,"data-option-index":e,"aria-disabled":n,"aria-selected":o}},id:X,inputValue:de,value:ue,dirty:Xe,expanded:$e&&ne,popupOpen:$e,focused:fe||-1!==ae,anchorEl:ne,setAnchorEl:re,focusedItem:ae,focusedTag:ae,groupedOptions:Ye}}({...l,componentName:"Autocomplete"}),ft=!X&&!Z&&rt&&!ze,mt=(!ie||!0===ae)&&!1!==ae,{onMouseDown:ht}=Qe(),{ref:bt,...xt}=tt(),vt=ce||(e=>e.label??e),yt={...l,disablePortal:oe,expanded:at,focused:st,fullWidth:le,getOptionLabel:vt,hasClearIcon:ft,hasPopupIcon:mt,inputFocused:-1===pt,popupOpen:lt,size:Ge},It=(e=>{const{classes:t,disablePortal:o,expanded:n,focused:a,fullWidth:i,hasClearIcon:l,hasPopupIcon:s,inputFocused:p,popupOpen:u,size:c}=e,d={root:["root",n&&"expanded",a&&"focused",i&&"fullWidth",l&&"hasClearIcon",s&&"hasPopupIcon"],inputRoot:["inputRoot"],input:["input",p&&"inputFocused"],tag:["tag",`tagSize${r(c)}`],endAdornment:["endAdornment"],clearIndicator:["clearIndicator"],popupIndicator:["popupIndicator",u&&"popupIndicatorOpen"],popper:["popper",o&&"popperDisablePortal"],paper:["paper"],listbox:["listbox"],loading:["loading"],noOptions:["noOptions"],option:["option"],groupLabel:["groupLabel"],groupUl:["groupUl"]};return y(d,N,t)})(yt),$t={slots:{paper:Me,popper:De,...Ke},slotProps:{chip:x,listbox:ye,...D,...qe}},[Ot,St]=$("listbox",{elementType:_,externalForwardedProps:$t,ownerState:yt,className:It.listbox,additionalProps:xt,ref:bt}),[Pt,Ct]=$("paper",{elementType:S,externalForwardedProps:$t,ownerState:yt,className:It.paper}),[kt,wt]=$("popper",{elementType:f,externalForwardedProps:$t,ownerState:yt,className:It.popper,additionalProps:{disablePortal:oe,style:{width:ut?ut.clientWidth:null},role:"presentation",anchorEl:ut,open:lt}});let At;const Lt=e=>({className:It.tag,disabled:Z,...et(e)});if(He&&Oe&&nt.length>0?At=He(nt,Lt,yt):We&&nt?At=We(nt,Lt,yt):Oe&&nt.length>0&&(At=nt.map(((e,o)=>{const{key:n,...r}=Lt({index:o});return t.jsx(P,{label:vt(e),size:Ge,...r,...$t.slotProps.chip},n)}))),xe>-1&&Array.isArray(At)){const e=At.length-xe;!st&&e>0&&(At=At.splice(0,xe),At.push(t.jsx("span",{className:It.tag,children:se(e)},At.length)))}const Tt=Ee||(e=>t.jsxs("li",{children:[t.jsx(J,{className:It.groupLabel,ownerState:yt,component:"div",children:e.group}),t.jsx(Q,{className:It.groupUl,ownerState:yt,children:e.children})]},e.key)),Rt=Ve||((e,o)=>{const{key:n,...r}=e;return t.jsx("li",{...r,children:vt(o)},n)}),jt=(e,t)=>{const o=ot({option:e,index:t});return Rt({...o,className:It.option},e,{selected:o["aria-selected"],index:t,inputValue:dt},yt)},Mt=$t.slotProps.clearIndicator,Dt=$t.slotProps.popupIndicator;return t.jsxs(e.Fragment,{children:[t.jsx(V,{ref:i,className:n(It.root,v),ownerState:yt,...Je(_e),children:Fe({id:it,disabled:Z,fullWidth:!0,size:"small"===Ge?"small":void 0,InputLabelProps:Xe(),InputProps:{ref:ct,className:It.inputRoot,startAdornment:At,onMouseDown:e=>{e.target===e.currentTarget&&ht(e)},...(ft||mt)&&{endAdornment:t.jsxs(H,{className:It.endAdornment,ownerState:yt,children:[ft?t.jsx(W,{...Ze(),"aria-label":j,title:j,ownerState:yt,...Mt,className:n(It.clearIndicator,Mt?.className),children:I}):null,mt?t.jsx(B,{...Ye(),disabled:Z,"aria-label":lt?M:Re,title:lt?M:Re,ownerState:yt,...Dt,className:n(It.popupIndicator,Dt?.className),children:Ne}):null]})}},inputProps:{className:It.input,disabled:Z,readOnly:ze,...Qe()}})}),ut?t.jsx(G,{as:kt,...wt,children:t.jsxs(K,{as:Pt,...Ct,children:[Ie&&0===gt.length?t.jsx(q,{className:It.loading,ownerState:yt,children:$e}):null,0!==gt.length||ie||Ie?null:t.jsx(U,{className:It.noOptions,ownerState:yt,role:"presentation",onMouseDown:e=>{e.preventDefault()},children:Se}),gt.length>0?t.jsx(Ot,{as:ve,...St,children:gt.map(((e,t)=>ge?Tt({key:e.key,group:e.group,children:e.options.map(((t,o)=>jt(t,e.index+o)))}):jt(e,t)))}):null]})}):null]})}));export{X as A};
