import{e as s,F as e,j as a,S as i,G as l,Z as t,a as n,_ as r,$ as c,a0 as d,a1 as o,W as m,a2 as j,a3 as x,z as h,H as A}from"../entries/index-xsXxT3-W.js";import{d as N,r as p}from"./router-BtYqujaw.js";import{D as S,g as u,c as E,d as f}from"./DressService-DkS6e_O5.js";import{P as I}from"./Pager-B4DUIA8f.js";import{S as g}from"./SimpleBackdrop-CqsJhYJ4.js";import{S as v}from"./SupplierBadge-ehv63WPF.js";import{C as L,a as T}from"./ArrowForwardIos-BCaVe-sv.js";import{T as O}from"./Backdrop-Czag2Ija.js";import{T as D}from"./Tooltip-CKMkVqOx.js";import{C as b,L as w}from"./Straighten-Isz6BfHc.js";import{V as y}from"./Check-BO6X9Q-4.js";import{C}from"./Clear-CDOl64hX.js";import{I as _}from"./IconButton-CxOCoGF3.js";import{V as R}from"./Visibility-D3efFHY1.js";import{E as B}from"./Edit-Bc0UCPtn.js";import{D as F}from"./Delete-BfnPAJno.js";import{D as M,a as G,b as P,d as k}from"./Grow-Cp8xsNYl.js";import{B as U}from"./Button-BeKLLPpp.js";import{I as z}from"./Info-CNP9gYBt.js";const V=({suppliers:V,keyword:Q,dressSpecs:Y,dressType:Z,dressSize:J,dressStyle:K,deposit:X,availability:H,reload:W,dresses:q,user:$,booking:ss,className:es,loading:as,hideSupplier:is,hidePrice:ls,language:ts,range:ns,rentalsCount:rs,onLoad:cs,onDelete:ds})=>{const os=N(),[ms,js]=p.useState(),[xs,hs]=p.useState(!0),[As,Ns]=p.useState(!1),[ps,Ss]=p.useState(!1),[us,Es]=p.useState([]),[fs,Is]=p.useState(1),[gs,vs]=p.useState(0),[Ls,Ts]=p.useState(0),[Os,Ds]=p.useState(!1),[bs,ws]=p.useState(""),[ys,Cs]=p.useState(-1),[_s,Rs]=p.useState(!1);p.useEffect((()=>{if(s.isMobile){const e=document.querySelector("body");e&&(e.onscroll=()=>{ps&&!As&&window.scrollY>0&&window.scrollY+window.innerHeight+s.INFINITE_SCROLL_OFFSET>=document.body.scrollHeight&&(Ns(!0),Is(fs+1))})}}),[ps,As,fs]);const Bs=async(e,a,i,l,t,n,r,c,d,o,m)=>{try{Ns(!0);const r={suppliers:a??[],dressSpecs:l,dressType:t,dressSize:n,deposit:c,availability:d,ranges:o},m=await u(i||"",r,e,s.DRESSES_PAGE_SIZE),j=m&&m.length>0?m[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!j)return void h();const x=Array.isArray(j.pageInfo)&&j.pageInfo.length>0?j.pageInfo[0].totalRecords:0;let N=[];N=s.PAGINATION_MODE===A.PAGINATION_MODE.INFINITE_SCROLL||s.isMobile?1===e?j.resultData:[...us,...j.resultData]:j.resultData,Es(N),vs((e-1)*s.DRESSES_PAGE_SIZE+N.length),Ts(x),Ss(j.resultData.length>0),((s.PAGINATION_MODE===A.PAGINATION_MODE.INFINITE_SCROLL||s.isMobile)&&1===e||s.PAGINATION_MODE===A.PAGINATION_MODE.CLASSIC&&!s.isMobile)&&window.scrollTo(0,0),cs&&cs({rows:j.resultData,rowCount:x})}catch(j){h(j)}finally{Ns(!1),hs(!1)}};p.useEffect((()=>{V&&(V.length>0?Bs(fs,V,Q,Y,Z,J,0,X||0,H,ns):(Es([]),vs(0),Ss(!1),cs&&cs({rows:[],rowCount:0}),hs(!1)))}),[fs,V,Q,Y,Z,J,K,X,H,ns,rs]),p.useEffect((()=>{q&&(Es(q),vs(q.length),Ss(!1),cs&&cs({rows:q,rowCount:q.length}))}),[q]),p.useEffect((()=>{Is(1)}),[V,Q,Y,Z,J,K,X,H,ns,rs]),p.useEffect((()=>{W&&(Is(1),Bs(1,V,Q,Y,Z,J,0,X,H,ns))}),[W,V,Q,Y,Z,J,K,X,H,ns,rs]),p.useEffect((()=>{js($)}),[$]);const Fs=async s=>{try{const e=s.currentTarget.getAttribute("data-id"),a=Number(s.currentTarget.getAttribute("data-index")),i=await E(e);200===i?Rs(!0):204===i?(Ds(!0),ws(e),Cs(a)):h()}catch(e){h(e)}},Ms=(s,e)=>{let i=!1;return ss&&("cancellation"===s&&ss.cancellation&&e>0&&(i=!0),"amendments"===s&&ss.amendments&&e>0&&(i=!0)),-1===e?a.jsx(C,{className:"unavailable"}):0===e||i?a.jsx(y,{className:"available"}):a.jsx(z,{className:"extra-info"})},Gs=e(ms);return ms&&a.jsxs(a.Fragment,{children:[a.jsxs("section",{className:(es?`${es} `:"")+"dress-list",children:[0===us.length?!xs&&!As&&!as&&a.jsx(L,{variant:"outlined",className:"empty-list",children:a.jsx(T,{children:a.jsx(O,{color:"textSecondary",children:i.EMPTY_LIST})})}):us.map(((e,h)=>{const A=Gs||e.supplier._id===ms._id;return a.jsxs("article",{children:[a.jsxs("div",{className:"dress",children:[a.jsx("img",{src:l(s.CDN_DRESSES,e.image),alt:e.name,className:"dress-img"}),a.jsxs("div",{className:"dress-footer",children:[a.jsx("div",{className:"dress-footer-row1",children:a.jsxs("div",{className:"rating",children:[e.rating&&e.rating>=1&&a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"value",children:e.rating.toFixed(2)}),a.jsx("img",{alt:"Rating",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJYSURBVFhHxZdBdtMwEIZn5LQ0XeUGdd8j77FrewLSE9AdhA30BMAJaE5QeoKWBS+wKydoOQFlS7swN8iC99KX2BpmZDnvuZFaiTjpt9HItjy/ZGlmDI8N2jaa8bD7Vil8IbbW9L3dvz4zNyL5LwGTb91jInhvuwaFeLT28vfAdoOJFmBmjnBquzU0wWHsSijbBsPOP1pzjvvu+YgSILPnJi17TlL7TDBRAkJmGLsKwQICZl8RtQpBm5BO086kvfaTzRABQrY+nu7hYTayfS9OAeLwdqO1y0dtt6VgCxB7YtvboWQIdJ5r+IMIVxu3+ZVL0EyAOM03199pIjnfnfJq47AouCxoOmj3s0wuGAHj4bNUYXHBZugSL0qmabovIswmZOeyc1flXEiREhNJq1MQ+30XBpUyeaQUgGS+xyrhDWp8GgFaw4m0q6Qg/CytEdDu31xyIjEXVgFpfVIlrVoc4Ah2xqH0je0uBZkoO59FyrlAtEwRd50LcwKEZYhwORecAoTJ1+4FAfRsd1GyJ6+ut61do4oDS4Vn6T3mXgE8+8aCE6H/XU4Bky9dGdBcQiLo/C3fOYdTQI7U1Lef0Urc73QKSBJ4bs3GUIA71qzhFICEjdcDvj3lFMAPhwgYFZo+yL8A2w8mM04+zvLMI0D/sKYTrpoGXPNtb76++SQxXVOyz2MeSGjoFOmJhKZCkiK0thI8Cy6ncv77Kcupu5hxKj/mc3dgL1WMuALac43zRkJ5GSfNowRpi38+fxHiuWRNe/texsOnPSQ64J/XnXJswSvlFv3IAPwDtS/grYKzhw8AAAAASUVORK5CYII="})]}),e.rentals&&e.rentals>0&&a.jsx("span",{className:"rentals",children:`(${e.rentals} ${i.RENTALS||"rentals"})`})]})}),!is&&a.jsx(v,{supplier:e.supplier})]})]}),a.jsxs("div",{className:"dress-info",children:[a.jsxs("div",{className:"dress-info-header",children:[a.jsx("div",{className:"name",children:a.jsx("h2",{children:e.name})}),!ls&&a.jsx("div",{className:"price",children:`${t(e.dailyPrice,n.CURRENCY,ts)}${n.DAILY}`})]}),a.jsxs("ul",{className:"dress-info-list",children:[e.type!==r.Unknown&&a.jsx("li",{className:"dress-type",children:a.jsx(D,{title:c(e.type),placement:"top",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(S,{}),a.jsx("span",{className:"dress-info-list-text",children:d(e.type)})]})})}),a.jsx("li",{className:"dress-size",children:a.jsx(D,{title:o(e.size),placement:"top",children:a.jsx("div",{className:"dress-info-list-item",children:a.jsx("span",{className:"dress-info-list-text",children:m(e.size)})})})}),a.jsx("li",{className:"dress-color",children:a.jsx(D,{title:i.COLOR||"Color",placement:"top",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(b,{}),a.jsx("span",{className:"dress-info-list-text",children:e.color})]})})}),a.jsx("li",{className:"dress-length",children:a.jsx(D,{title:i.LENGTH||"Length",placement:"top",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(w,{}),a.jsx("span",{className:"dress-info-list-text",children:`${e.length} ${i.CM||"cm"}`})]})})}),e.customizable&&a.jsx("li",{className:"dress-customizable",children:a.jsx(D,{title:i.CUSTOMIZABLE_TOOLTIP||"This dress can be customized",placement:"top",children:a.jsx("div",{className:"dress-info-list-item",children:a.jsx(y,{className:"available"})})})})]}),a.jsxs("ul",{className:"extras-list",children:[A&&a.jsxs(a.Fragment,{children:[a.jsx("li",{className:e.available?"dress-available":"dress-unavailable",children:a.jsx(D,{title:e.available?i.DRESS_AVAILABLE_TOOLTIP||"This dress is available":i.DRESS_UNAVAILABLE_TOOLTIP||"This dress is unavailable",children:a.jsxs("div",{className:"dress-info-list-item",children:[e.available?a.jsx(y,{}):a.jsx(C,{}),e.available?a.jsx("span",{className:"dress-info-list-text",children:i.DRESS_AVAILABLE||"Available"}):a.jsx("span",{className:"dress-info-list-text",children:i.DRESS_UNAVAILABLE||"Unavailable"})]})})}),e.fullyBooked&&a.jsx("li",{className:"dress-unavailable",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(C,{}),a.jsx("span",{className:"dress-info-list-text",children:i.FULLY_BOOKED||"Fully Booked"})]})}),e.comingSoon&&a.jsx("li",{className:"dress-coming-soon",children:a.jsxs("div",{className:"dress-info-list-item",children:[a.jsx(y,{}),a.jsx("span",{className:"dress-info-list-text",children:i.COMING_SOON||"Coming Soon"})]})})]}),e.cancellation>-1&&a.jsx("li",{children:a.jsx(D,{title:ss?"":e.cancellation>-1?i.CANCELLATION_TOOLTIP||"The booking can be cancelled":j(e.cancellation,ts),placement:"left",children:a.jsxs("div",{className:"dress-info-list-item",children:[Ms("cancellation",e.cancellation),a.jsx("span",{className:"dress-info-list-text",children:j(e.cancellation,ts)})]})})}),e.amendments>-1&&a.jsx("li",{children:a.jsx(D,{title:ss?"":e.amendments>-1?i.AMENDMENTS_TOOLTIP||"The booking can be modified":x(e.amendments,ts),placement:"left",children:a.jsxs("div",{className:"dress-info-list-item",children:[Ms("amendments",e.amendments),a.jsx("span",{className:"dress-info-list-text",children:x(e.amendments,ts)})]})})})]}),a.jsx("div",{className:"action",children:A&&a.jsxs(a.Fragment,{children:[a.jsx(D,{title:i.VIEW_DRESS||"View Dress",children:a.jsx(_,{onClick:()=>os(`/dress?dr=${e._id}`),children:a.jsx(R,{})})}),a.jsx(D,{title:n.UPDATE,children:a.jsx(_,{onClick:()=>os(`/update-dress?dr=${e._id}`),children:a.jsx(B,{})})}),a.jsx(D,{title:n.DELETE,children:a.jsx(_,{"data-id":e._id,"data-index":h,onClick:Fs,children:a.jsx(F,{})})})]})})]})]},e._id)})),a.jsxs(M,{disableEscapeKeyDown:!0,maxWidth:"xs",open:_s,children:[a.jsx(G,{className:"dialog-header",children:n.INFO}),a.jsx(P,{children:i.CANNOT_DELETE_DRESS||"This dress cannot be deleted because it is linked to bookings"}),a.jsx(k,{className:"dialog-actions",children:a.jsx(U,{onClick:()=>{Rs(!1)},variant:"contained",className:"btn-secondary",children:n.CLOSE})})]}),a.jsxs(M,{disableEscapeKeyDown:!0,maxWidth:"xs",open:Os,children:[a.jsx(G,{className:"dialog-header",children:n.CONFIRM_TITLE}),a.jsx(P,{children:i.DELETE_DRESS||"Are you sure you want to delete this dress?"}),a.jsxs(k,{className:"dialog-actions",children:[a.jsx(U,{onClick:()=>{Ds(!1),ws("")},variant:"contained",className:"btn-secondary",children:n.CANCEL}),a.jsx(U,{onClick:async()=>{try{if(""!==bs&&ys>-1)if(Ds(!1),200===await f(bs)){const s=gs-1;us.splice(ys,1),Es(us),vs(s),Ts(Ls-1),ws(""),Cs(-1),ds&&ds(s),Ns(!1)}else h(),ws(""),Cs(-1),Ns(!1);else h(),ws(""),Cs(-1),Ds(!1)}catch(s){h(s)}},variant:"contained",color:"error",children:n.DELETE})]})]})]}),!s.isMobile&&a.jsx(I,{page:fs,pageSize:s.DRESSES_PAGE_SIZE,rowCount:gs,totalRecords:Ls,onNext:()=>Is(fs+1),onPrevious:()=>Is(fs-1)}),As&&a.jsx(g,{text:n.LOADING})]})||a.jsx(a.Fragment,{})};export{V as D};
