import{g as t,d as n,n as s,a as e,b as o}from"./normalizeInterval-a5pcD5yp.js";import{a as r,b as a,e as u,c as i}from"./useMobilePicker-Cpitw7qm.js";import{t as c,c as d,n as m,m as E}from"./format-4arn0GRM.js";import{b as f,s as l}from"../entries/index-CEzJO5Xy.js";function h(t,n,s){const{years:e=0,months:o=0,weeks:u=0,days:i=0,hours:m=0,minutes:E=0,seconds:f=0}=n,l=c(t,s?.in),h=o||e?r(l,o+12*e):l,_=i||u?a(h,i+7*u):h;return d(t,+_+1e3*(f+60*(E+60*m)))}function _(t,n){const s=+c(t)-+c(n);return s<0?-1:s>0?1:s}function A(r,a){const{start:d,end:f}=s(a?.in,r),l={},A=function(t,n){const[s,e]=m(undefined,t,n),o=_(s,e),r=Math.abs(function(t,n){const[s,e]=m(void 0,t,n);return s.getFullYear()-e.getFullYear()}(s,e));s.setFullYear(1584),e.setFullYear(1584);const a=o*(r-+(_(s,e)===-o));return 0===a?0:a}(f,d);A&&(l.years=A);const R=h(d,{years:l.years}),T=function(t,n){const[s,e,o]=m(undefined,t,t,n),r=_(e,o),a=Math.abs(function(t,n){const[s,e]=m(void 0,t,n);return 12*(s.getFullYear()-e.getFullYear())+(s.getMonth()-e.getMonth())}(e,o));if(a<1)return 0;1===e.getMonth()&&e.getDate()>27&&e.setDate(30),e.setMonth(e.getMonth()-r*a);let d=_(e,o)===-r;(function(t,n){const s=c(t,n?.in);return+u(s,n)===+i(s,n)})(s)&&1===a&&1===_(s,o)&&(d=!1);const E=r*(a-+d);return 0===E?0:E}(f,R);T&&(l.months=T);const g=h(R,{months:l.months}),D=e(f,g);D&&(l.days=D);const I=h(g,{days:l.days}),M=function(n,s,e){const[o,r]=m(e?.in,n,s),a=(+o-+r)/E;return t(e?.roundingMethod)(a)}(f,I);M&&(l.hours=M);const y=h(I,{hours:l.hours}),H=o(f,y);H&&(l.minutes=H);const F=function(s,e){const o=n(s,e)/1e3;return t(void 0)(o)}(f,h(y,{minutes:l.minutes}));return F&&(l.seconds=F),l}const R=new f({fr:{CREATE_USER_HEADING:"Nouvelle utilisateur",BIRTH_DATE:"Date de naissance"},en:{CREATE_USER_HEADING:"New user",BIRTH_DATE:"Birth date"},es:{CREATE_USER_HEADING:"Nuevo usuario",BIRTH_DATE:"Fecha de nacimiento"},ar:{CREATE_USER_HEADING:"مستخدم جديد",BIRTH_DATE:"تاريخ الميلاد"}});l(R);export{A as i,R as s};
