import{c as e,a5 as s,R as t,j as o,a as r,e as i,ae as a,z as c,g as m,F as n,a8 as l}from"../entries/index-CEzJO5Xy.js";import{d as p,r as j}from"./router-BtYqujaw.js";import{s as d}from"./user-list-4CESz5z_.js";import{L as u}from"./Layout-BQBjg4Lf.js";import{S as h}from"./SimpleBackdrop-Bf3qjF13.js";import{A as f}from"./Avatar-BtfxKR-8.js";import{B as S}from"./BookingList-Dn-WOL9Q.js";import x from"./NoMatch-jvHCs4x8.js";import{h as _}from"./SupplierService-DSnTbAgG.js";import{T as y}from"./Backdrop-Bzn12VyM.js";import{T as b}from"./Tooltip-BkJF6Mu0.js";import{I as v}from"./IconButton-CnBvmeAK.js";import{E as g}from"./Edit-DIF9Bumd.js";import{D as N}from"./Delete-CnqjtpsJ.js";import{D as C}from"./DialogTitle-BZXwroUN.js";import{a as T,b as E,D}from"./Grow-CjOKj0i1.js";import{B as L}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./DressService-J0XavNJj.js";import"./LocationService-BtQFgoWL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./useSlot-CtA82Ni6.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./BookingService-BJ4R0IJT.js";import"./InputLabel-BbcIE26O.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";import"./TextField-BAse--ht.js";import"./OutlinedInput-g8mR4MM3.js";import"./Input-BQdee9z7.js";import"./FormHelperText-DFSsjBsL.js";import"./Menu-ZU0DMgjT.js";import"./Paper-CcwAvfvc.js";import"./mergeSlotProps-Cay5TZBz.js";import"./MenuItem-suKfXYI2.js";import"./listItemTextClasses-DFwCkkgK.js";import"./BookingStatus-Bg6x_fQB.js";import"./Link-B-UCzRRJ.js";import"./format-4arn0GRM.js";import"./DataGrid-DM8uCtAG.js";import"./getThemeProps-gt86ccpv.js";import"./Switch-BWPUOSX1.js";import"./SwitchBase-BIeqtL5F.js";import"./Toolbar-CNUITE_K.js";import"./KeyboardArrowRight-BV-h2cWM.js";import"./Chip-CAtDqtgp.js";import"./Autocomplete-CviOU_ku.js";import"./ListItemText-DBn_RuMq.js";import"./Checkbox-CDqupZJG.js";import"./fr-CaQg1DLH.js";const k=()=>{const k=e.c(44),B=p();let A;k[0]===Symbol.for("react.memo_cache_sentinel")?(A=s().map(w),k[0]=A):A=k[0];const I=A,[U,M]=j.useState(),[P,R]=j.useState(),[F,H]=j.useState(!1),[O,K]=j.useState(!0),[G,W]=j.useState(!1),[z,J]=j.useState(!1);let Q;k[1]===Symbol.for("react.memo_cache_sentinel")?(Q=[],k[1]=Q):Q=k[1];const[$,q]=j.useState(Q),[V,X]=j.useState(0);let Y,Z,ee;k[2]!==F?(Y=()=>{if(F){const e=document.querySelector(".col-1");e&&X(e.clientHeight)}},Z=[F],k[2]=F,k[3]=Y,k[4]=Z):(Y=k[3],Z=k[4]),j.useEffect(Y,Z),k[5]===Symbol.for("react.memo_cache_sentinel")?(ee=()=>{K(!0)},k[5]=ee):ee=k[5];const se=ee;let te;k[6]===Symbol.for("react.memo_cache_sentinel")?(te=()=>{K(!1)},k[6]=te):te=k[6];const oe=te;let re;k[7]===Symbol.for("react.memo_cache_sentinel")?(re=()=>{J(!0)},k[7]=re):re=k[7];const ie=re;let ae;k[8]!==B||k[9]!==P?(ae=async()=>{try{P?(J(!1),200===await a([P._id])?B("/users"):(c(),K(!1))):c()}catch(e){c(e)}},k[8]=B,k[9]=P,k[10]=ae):ae=k[10];const ce=ae;let me;k[11]===Symbol.for("react.memo_cache_sentinel")?(me=()=>{J(!1)},k[11]=me):me=k[11];const ne=me;let le;k[12]===Symbol.for("react.memo_cache_sentinel")?(le=async e=>{if(e&&e.verified){K(!0);const t=new URLSearchParams(window.location.search);if(t.has("u")){const o=t.get("u");if(o&&""!==o)try{const s=await m(o);if(s){const t=t=>{q(t),M(e),R(s),H(!0),K(!1)};if(n(e)){const e=await _();t(l(e))}else t([e._id])}else K(!1),W(!0)}catch(s){c(s),K(!1),H(!1)}else K(!1),W(!0)}else K(!1),W(!0)}},k[12]=le):le=k[12];const pe=le,je=U&&P&&(U.type===t.Admin||U._id===P._id||U.type===t.Supplier&&U._id===P.supplier),de=P&&P.type===t.Supplier;let ue;k[13]===Symbol.for("react.memo_cache_sentinel")?(ue=[],k[13]=ue):ue=k[13];let he,fe,Se,xe,_e,ye,be,ve,ge,Ne=ue;if(U&&P)if(de&&U._id===P._id||U.type===t.Admin&&P.type===t.Supplier){const e=P._id;let s;k[14]!==e?(s=[e],k[14]=e,k[15]=s):s=k[15],Ne=s}else if(U.type===t.Supplier&&P.type===t.User){const e=U._id;let s;k[16]!==e?(s=[e],k[16]=e,k[17]=s):s=k[17],Ne=s}else U.type===t.Admin&&(Ne=$);return k[18]!==Ne||k[19]!==je||k[20]!==U||k[21]!==B||k[22]!==V||k[23]!==de||k[24]!==P||k[25]!==F?(he=U&&P&&F&&o.jsxs("div",{className:"user",children:[o.jsxs("div",{className:"col-1",children:[o.jsx("section",{className:"user-avatar-sec",children:o.jsx(f,{record:P,type:P.type,mode:"update",size:"large",hideDelete:!0,onBeforeUpload:se,onChange:oe,color:"disabled",className:de?"supplier-avatar":"user-avatar",readonly:!0,verified:!0})}),o.jsx(y,{variant:"h4",className:"user-name",children:P.fullName}),P.bio&&o.jsx(y,{variant:"h6",className:"user-info",children:P.bio}),P.location&&o.jsx(y,{variant:"h6",className:"user-info",children:P.location}),P.phone&&o.jsx(y,{variant:"h6",className:"user-info",children:P.phone}),o.jsxs("div",{className:"user-actions",children:[je&&o.jsx(b,{title:r.UPDATE,children:o.jsx(v,{onClick:()=>B(`/update-user?u=${P._id}`),children:o.jsx(g,{})})}),je&&o.jsx(b,{title:r.DELETE,children:o.jsx(v,{"data-id":P._id,onClick:ie,children:o.jsx(N,{})})})]})]}),o.jsx("div",{className:"col-2",children:Ne.length>0&&o.jsx(S,{containerClassName:"user",offset:V,loggedUser:U,user:de?void 0:P,suppliers:Ne,statuses:I,hideDates:i.isMobile,checkboxSelection:!i.isMobile,hideSupplierColumn:de,language:U.language})})]}),k[18]=Ne,k[19]=je,k[20]=U,k[21]=B,k[22]=V,k[23]=de,k[24]=P,k[25]=F,k[26]=he):he=k[26],k[27]===Symbol.for("react.memo_cache_sentinel")?(fe=o.jsx(C,{className:"dialog-header",children:r.CONFIRM_TITLE}),Se=o.jsx(T,{children:d.DELETE_USER}),k[27]=fe,k[28]=Se):(fe=k[27],Se=k[28]),k[29]===Symbol.for("react.memo_cache_sentinel")?(xe=o.jsx(L,{onClick:ne,variant:"contained",className:"btn-secondary",children:r.CANCEL}),k[29]=xe):xe=k[29],k[30]!==ce?(_e=o.jsxs(E,{className:"dialog-actions",children:[xe,o.jsx(L,{onClick:ce,variant:"contained",color:"error",children:r.DELETE})]}),k[30]=ce,k[31]=_e):_e=k[31],k[32]!==z||k[33]!==_e?(ye=o.jsxs(D,{disableEscapeKeyDown:!0,maxWidth:"xs",open:z,children:[fe,Se,_e]}),k[32]=z,k[33]=_e,k[34]=ye):ye=k[34],k[35]!==O?(be=O&&o.jsx(h,{text:r.LOADING}),k[35]=O,k[36]=be):be=k[36],k[37]!==G?(ve=G&&o.jsx(x,{hideHeader:!0}),k[37]=G,k[38]=ve):ve=k[38],k[39]!==he||k[40]!==ye||k[41]!==be||k[42]!==ve?(ge=o.jsxs(u,{onLoad:pe,strict:!0,children:[he,ye,be,ve]}),k[39]=he,k[40]=ye,k[41]=be,k[42]=ve,k[43]=ge):ge=k[43],ge};function w(e){return e.value}export{k as default};
