import{R as e,j as r,a as t,a9 as s,U as a,e as i,F as n,z as o,p as l,M as u,au as m,L as d,av as p,N as c,D as j}from"../entries/index-xsXxT3-W.js";import{d as h,r as f}from"./router-BtYqujaw.js";import{L as x}from"./Layout-DaeN7D4t.js";import{s as g}from"./create-supplier-BpB8o_Zh.js";import{s as S,D as E,i as v}from"./DriverLicense-CG4rHL0U.js";import{v as N}from"./SupplierService-9DC5V5ZJ.js";import{E as I}from"./Error-DRzAdbbx.js";import{S as A}from"./SimpleBackdrop-CqsJhYJ4.js";import{A as C}from"./Avatar-CvDHTACZ.js";import{D as y}from"./DatePicker-mLpxfIrI.js";import{P as _}from"./Paper-C-atefOs.js";import{I as L}from"./Info-CNP9gYBt.js";import{F as D,I as R}from"./InputLabel-C8rcdOGQ.js";import{S as T}from"./TextField-D_yQOTzE.js";import{M as b}from"./MenuItem-P0BnGnrT.js";import{I as M}from"./Input-D1AdR9CM.js";import{F as P}from"./FormHelperText-DDZ4BMA4.js";import{F as U,S as W}from"./Switch-C5asfh_w.js";import{B as w}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-DiTut-u0.js";import"./Backdrop-Czag2Ija.js";import"./normalizeInterval-CrKB48xo.js";import"./fr-DJt_zj3p.js";import"./DatePicker-BDzBD9XN.js";import"./getThemeProps-DSP27jpP.js";import"./IconButton-CxOCoGF3.js";import"./useFormControl-B7jXtRD7.js";import"./ListItem-Bmdw8GrH.js";import"./isHostComponent-DR4iSCFs.js";import"./Menu-C_-X8cS7.js";import"./mergeSlotProps-DEridHif.js";import"./Chip-MGF1mKZa.js";import"./OutlinedInput-BX8yFQbF.js";import"./Visibility-D3efFHY1.js";import"./Delete-BfnPAJno.js";import"./DressService-DkS6e_O5.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./listItemTextClasses-BcbgzvlE.js";import"./SwitchBase-DrUkTXjH.js";const B=()=>{const B=h(),[O,F]=f.useState(),[k,G]=f.useState(!1),[q,H]=f.useState(""),[Y,z]=f.useState(""),[V,$]=f.useState(""),[Q,X]=f.useState(""),[Z,J]=f.useState(""),[K,ee]=f.useState(!1),[re,te]=f.useState(!1),[se,ae]=f.useState(!1),[ie,ne]=f.useState(!1),[oe,le]=f.useState(!1),[ue,me]=f.useState(""),[de,pe]=f.useState(!1),[ce,je]=f.useState(""),[he,fe]=f.useState(!0),[xe,ge]=f.useState(!0),[Se,Ee]=f.useState(!1),[ve,Ne]=f.useState(!1),[Ie,Ae]=f.useState(),[Ce,ye]=f.useState(!0),[_e,Le]=f.useState(""),[De,Re]=f.useState(),[Te,be]=f.useState(""),[Me,Pe]=f.useState(""),[Ue,We]=f.useState(!1),we=async e=>{if(!e)return le(!1),!0;try{return 200===await N({fullName:e})?(le(!1),ee(!1),!0):(le(!0),pe(!1),ee(!1),!1)}catch(r){return o(r),!0}},Be=async e=>{if(!e)return te(!1),fe(!0),!1;if(!c.isEmail(e))return te(!1),fe(!1),!1;try{return 200===await j({email:e})?(te(!1),fe(!0),!0):(te(!0),fe(!0),pe(!1),ee(!1),!1)}catch(r){return o(r),!0}},Oe=e=>{if(e){const r=c.isMobilePhone(e);return ge(r),r}return ge(!0),!0},Fe=r=>{if(r&&m(r)&&ce===e.User){const e=(v({start:r,end:new Date}).years??0)>=i.MINIMUM_AGE;return ye(e),e}return ye(!0),!0},ke=ce===e.Supplier,Ge=ce===e.User;return r.jsxs(x,{onLoad:r=>{if(r&&r.verified){const t=n(r);F(r),G(t),je(t?"":e.User),ae(!0)}},strict:!0,children:[O&&r.jsx("div",{className:"create-user",children:r.jsxs(_,{className:"user-form user-form-wrapper",elevation:10,style:se?{}:{display:"none"},children:[r.jsx("h1",{className:"user-form-title",children:S.CREATE_USER_HEADING}),r.jsxs("form",{onSubmit:async r=>{try{if(r.preventDefault(),!O)return void o();if(ce===e.Supplier){if(!(await we(q)))return}else le(!1);if(!(await Be(Y)))return;if(!Oe(V))return;if(!Fe(Ie))return;if(ce===e.Supplier&&!ue)return pe(!0),void ee(!1);const t=l(),s=k?void 0:O._id,a={email:Y,phone:V,location:Q,bio:Z,fullName:q,type:ce,avatar:ue,birthDate:Ie,language:t,supplier:s,license:De,minimumRentalDays:_e?Number(_e):void 0,priceChangeRate:Te?Number(Te):void 0,supplierDressLimit:Me?Number(Me):void 0,notifyAdminOnNewDress:ce===e.Supplier?Ue:void 0};ce===e.Supplier&&(a.payLater=Se,a.licenseRequired=ve),200===await u(a)?B("/users"):ee(!0)}catch(t){o(t)}},children:[r.jsx(C,{type:ce,mode:"create",record:null,size:"large",readonly:!1,onBeforeUpload:()=>{ne(!0)},onChange:r=>{ne(!1),me(r),null!==r&&ce===e.Supplier&&pe(!1)},color:"disabled",className:"avatar-ctn"}),ke&&r.jsxs("div",{className:"info",children:[r.jsx(L,{}),r.jsx("span",{children:g.RECOMMENDED_IMAGE_SIZE})]}),k&&r.jsxs(D,{fullWidth:!0,margin:"dense",style:{marginTop:ke?0:39},children:[r.jsx(R,{className:"required",children:t.TYPE}),r.jsxs(T,{label:t.TYPE,value:ce,onChange:async r=>{const t=r.target.value;je(t),t===e.Supplier?await we(q):le(!1)},variant:"standard",required:!0,fullWidth:!0,children:[r.jsx(b,{value:e.Admin,children:s(a.Admin)}),r.jsx(b,{value:e.Supplier,children:s(a.Supplier)}),r.jsx(b,{value:e.User,children:s(a.User)})]})]}),r.jsxs(D,{fullWidth:!0,margin:"dense",children:[r.jsx(R,{className:"required",children:t.FULL_NAME}),r.jsx(M,{id:"full-name",type:"text",error:oe,required:!0,onBlur:async r=>{ce===e.Supplier?await we(r.target.value):le(!1)},onChange:e=>{H(e.target.value),e.target.value||le(!1)},autoComplete:"off"}),r.jsx(P,{error:oe,children:oe&&g.INVALID_SUPPLIER_NAME||""})]}),r.jsxs(D,{fullWidth:!0,margin:"dense",children:[r.jsx(R,{className:"required",children:t.EMAIL}),r.jsx(M,{id:"email",type:"text",error:!he||re,onBlur:async e=>{await Be(e.target.value)},onChange:e=>{z(e.target.value),e.target.value||(te(!1),fe(!0))},autoComplete:"off",required:!0}),r.jsxs(P,{error:!he||re,children:[!he&&t.EMAIL_NOT_VALID||"",re&&t.EMAIL_ALREADY_REGISTERED||""]})]}),Ge&&r.jsxs(r.Fragment,{children:[r.jsxs(D,{fullWidth:!0,margin:"dense",children:[r.jsx(y,{label:S.BIRTH_DATE,value:Ie,required:!0,onChange:e=>{if(e){const r=Fe(e);Ae(e),ye(r)}},language:O&&O.language||i.DEFAULT_LANGUAGE}),r.jsx(P,{error:!Ce,children:!Ce&&t.BIRTH_DATE_NOT_VALID||""})]}),r.jsx(E,{className:"driver-license-field",onUpload:e=>{Re(e)}})]}),r.jsxs(D,{fullWidth:!0,margin:"dense",children:[r.jsx(R,{className:Ge?"required":"",children:t.PHONE}),r.jsx(M,{id:"phone",type:"text",onBlur:e=>{Oe(e.target.value)},onChange:e=>{$(e.target.value),e.target.value||ge(!0)},error:!xe,required:!!Ge,autoComplete:"off"}),r.jsx(P,{error:!xe,children:!xe&&t.PHONE_NOT_VALID||""})]}),r.jsxs(D,{fullWidth:!0,margin:"dense",children:[r.jsx(R,{children:t.LOCATION}),r.jsx(M,{id:"location",type:"text",onChange:e=>{X(e.target.value)},autoComplete:"off"})]}),r.jsxs(D,{fullWidth:!0,margin:"dense",children:[r.jsx(R,{children:t.BIO}),r.jsx(M,{id:"bio",type:"text",onChange:e=>{J(e.target.value)},autoComplete:"off"})]}),ke&&r.jsxs(r.Fragment,{children:[r.jsx(D,{fullWidth:!0,margin:"dense",children:r.jsx(U,{control:r.jsx(W,{checked:Se,onChange:e=>{Ee(e.target.checked)},color:"primary"}),label:t.PAY_LATER})}),r.jsx(D,{fullWidth:!0,margin:"dense",children:r.jsx(U,{control:r.jsx(W,{checked:ve,onChange:e=>{Ne(e.target.checked)},color:"primary"}),label:t.LICENSE_REQUIRED})}),r.jsx(D,{fullWidth:!0,margin:"dense",children:r.jsx(U,{control:r.jsx(W,{checked:Ue,disabled:O?.type===a.Supplier,onChange:e=>{We(e.target.checked)},color:"primary"}),label:t.NOTIFY_ADMIN_ON_NEW_CAR})}),r.jsxs(D,{fullWidth:!0,margin:"dense",children:[r.jsx(R,{children:t.SUPPLIER_CAR_LIMIT}),r.jsx(M,{type:"text",onChange:e=>{Pe(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^\\d+$"}}})]}),r.jsxs(D,{fullWidth:!0,margin:"dense",children:[r.jsx(R,{children:t.MIN_RENTAL_DAYS}),r.jsx(M,{type:"text",onChange:e=>{Le(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^\\d+$"}}})]}),r.jsxs(D,{fullWidth:!0,margin:"dense",children:[r.jsx(R,{children:t.PRICE_CHANGE_RATE}),r.jsx(M,{type:"text",onChange:e=>{be(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^-?\\d+(\\.\\d+)?$"}}})]})]}),r.jsxs("div",{className:"buttons",children:[r.jsx(w,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",children:t.CREATE}),r.jsx(w,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{try{ue&&await d(ue),De&&await p(De),B("/users")}catch{B("/users")}},children:t.CANCEL})]}),r.jsxs("div",{className:"form-error",children:[K&&r.jsx(I,{message:t.GENERIC_ERROR}),de&&r.jsx(I,{message:t.IMAGE_REQUIRED})]})]})]})}),ie&&r.jsx(A,{text:t.PLEASE_WAIT})]})};export{B as default};
