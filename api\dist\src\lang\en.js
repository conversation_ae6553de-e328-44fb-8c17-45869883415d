import * as env from "../config/env.config.js";
export const en = {
  ERROR: 'Internal Error: ',
  DB_ERROR: 'Database Failure: ',
  SMTP_ERROR: 'SMTP Error - Failed to send email: ',
  ACCOUNT_ACTIVATION_SUBJECT: 'Account Activation',
  <PERSON><PERSON><PERSON><PERSON>: 'Hello ',
  ACCOUNT_ACTIVATION_LINK: 'Please activate your account by clicking the link:',
  REGARDS: `Kind regards,<br>${env.WEBSITE_NAME} team`,
  ACCOUNT_ACTIVATION_TECHNICAL_ISSUE: 'Technical Issue! Please click on resend to validate your email.',
  ACCOUNT_ACTIVATION_LINK_EXPIRED: 'Your validation link may have expired. Please click on resend to validate your email.',
  ACCOUNT_ACTIVATION_LINK_ERROR: 'We were unable to find a user for this verification. Please Sign up.',
  ACCOUNT_ACTIVATION_SUCCESS: 'Your account was successfully verified.',
  ACCOUNT_ACTIVATION_RESEND_ERROR: 'We were unable to find a user with that email. Make sure your Email is correct.',
  ACCOUNT_ACTIVATION_ACCOUNT_VERIFIED: 'This account has already been verified. Please sign in.',
  ACCOUNT_ACTIVATION_EMAIL_SENT_PART_1: 'A validation email has been sent to ',
  ACCOUNT_ACTIVATION_EMAIL_SENT_PART_2: ". It will be expire after one day. If you didn't receive validation email click on resend.",
  CAR_IMAGE_REQUIRED: "Car's image field can't be blank: ",
  CAR_IMAGE_NOT_FOUND: 'Image file not found: ',
  PASSWORD_RESET_SUBJECT: 'Password Reset',
  PASSWORD_RESET_LINK: 'Please reset your password by clicking the link:',
  BOOKING_CONFIRMED_SUBJECT_PART1: 'Your booking',
  BOOKING_CONFIRMED_SUBJECT_PART2: 'is confirmed.',
  BOOKING_CONFIRMED_PART1: 'Your booking',
  BOOKING_CONFIRMED_PART2: 'is confirmed and the payment was successfully done.',
  BOOKING_CONFIRMED_PART3: ' Please present yourself to our agency ',
  BOOKING_CONFIRMED_PART4: ' (',
  BOOKING_CONFIRMED_PART5: ') on ',
  BOOKING_CONFIRMED_PART6: ` (${env.TIMEZONE}) to pick up your vehicle `,
  BOOKING_CONFIRMED_PART7: '.',
  BOOKING_CONFIRMED_PART8: "Please bring your ID, driver's license and warranty check with you.",
  BOOKING_CONFIRMED_PART9: 'You must drop-off the vehicle to our agency ',
  BOOKING_CONFIRMED_PART10: ' (',
  BOOKING_CONFIRMED_PART11: ') on ',
  BOOKING_CONFIRMED_PART12: ` (${env.TIMEZONE}).`,
  BOOKING_CONFIRMED_PART13: 'Please respect the pick-up and drop-off dates and times.',
  BOOKING_CONFIRMED_PART14: 'You can follow your booking on: ',
  BOOKING_PAY_LATER_NOTIFICATION: 'confirmed the booking',
  BOOKING_PAID_NOTIFICATION: 'paid the booking',
  CANCEL_BOOKING_NOTIFICATION: 'made a request to cancel the booking',
  BOOKING_UPDATED_NOTIFICATION_PART1: 'The status of the booking',
  BOOKING_UPDATED_NOTIFICATION_PART2: 'was updated.',
  CONTACT_SUBJECT: 'New Message from Contact Form',
  SUBJECT: 'Subject',
  FROM: 'From',
  MESSAGE: 'Message',
  LOCATION_IMAGE_NOT_FOUND: 'Location image not found',
  NEW_CAR_NOTIFICATION_PART1: 'The supplier ',
  NEW_CAR_NOTIFICATION_PART2: ' created a new car.'
};