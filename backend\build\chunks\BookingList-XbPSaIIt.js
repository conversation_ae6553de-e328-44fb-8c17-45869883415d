import{b as e,s,c as a,j as i,ac as t,a as l,ad as r,e as n,J as o,ae as d,af as c,ag as u,G as E,ah as m,ai as h,Z as S,z as N,F as x,aj as p}from"../entries/index-xsXxT3-W.js";import{r as f,d as j}from"./router-BtYqujaw.js";import{s as g}from"./cars-xqBbVU4C.js";import{g as T,B as v,u as b,d as I}from"./BookingStatus-BaSj8uqV.js";import{I as _}from"./InputLabel-C8rcdOGQ.js";import{S as C}from"./TextField-D_yQOTzE.js";import{M as O}from"./MenuItem-P0BnGnrT.js";import{L as D}from"./Link-sHEcszvT.js";import{f as A,a as L,e as P}from"./fr-DJt_zj3p.js";import{V as k}from"./Check-BO6X9Q-4.js";import{B as R}from"./Button-BeKLLPpp.js";import{D as y}from"./DataGrid-DJUEcXft.js";import{D as U,a as G,b as w,d as M}from"./Grow-Cp8xsNYl.js";import{T as B}from"./Tooltip-CKMkVqOx.js";import{I as K}from"./IconButton-CxOCoGF3.js";import{E as $}from"./Edit-Bc0UCPtn.js";import{D as F}from"./Delete-BfnPAJno.js";const V=new e({fr:{CAR:"Voiture",SUPPLIER:"Fournisseur",DRIVER:"Conducteur",PRICE:"Prix",STATUS:"Statut",UPDATE_SELECTION:"Modifier la sélection",DELETE_SELECTION:"Supprimer la sélection",UPDATE_STATUS:"Modification du statut",NEW_STATUS:"Nouveau statut",DELETE_BOOKING:"Êtes-vous sûr de vouloir supprimer cette réservation ?",DELETE_BOOKINGS:"Êtes-vous sûr de vouloir supprimer les réservations sélectionnées ?",EMPTY_LIST:"Pas de réservations.",DAYS:"Jours",COST:"Total"},en:{CAR:"Car",SUPPLIER:"Supplier",DRIVER:"Driver",PRICE:"Price",STATUS:"Status",UPDATE_SELECTION:"Edit selection",DELETE_SELECTION:"Delete selection",UPDATE_STATUS:"Status modification",NEW_STATUS:"New status",DELETE_BOOKING:"Are you sure you want to delete this booking?",DELETE_BOOKINGS:"Are you sure you want to delete the selected bookings?",EMPTY_LIST:"No bookings.",DAYS:"Days",COST:"COST"},es:{CAR:"Coche",SUPPLIER:"Proveedor",DRIVER:"Conductor",PRICE:"Precio",STATUS:"Estado",UPDATE_SELECTION:"Modificar selección",DELETE_SELECTION:"Eliminar selección",UPDATE_STATUS:"Modificación del estado",NEW_STATUS:"Nuevo estado",DELETE_BOOKING:"¿Estás seguro de que quieres eliminar esta reserva?",DELETE_BOOKINGS:"¿Estás seguro de que quieres eliminar las reservas seleccionadas?",EMPTY_LIST:"Sin reservas.",DAYS:"Días",COST:"Coste"}});s(V);const Y=e=>{const s=a.c(18),{value:n,label:o,required:d,variant:c,disabled:u,style:E,onChange:m}=e,[h,S]=f.useState("");let N,x,p;s[0]!==n||s[1]!==h?(N=()=>{n&&n!==h&&S(n)},x=[n,h],s[0]=n,s[1]=h,s[2]=N,s[3]=x):(N=s[2],x=s[3]),f.useEffect(N,x),s[4]!==m?(p=e=>{S(e.target.value),m&&m(e.target.value)},s[4]=m,s[5]=p):p=s[5];const j=p;let g,T,v;return s[6]!==E?(g=E||{},s[6]=E,s[7]=g):g=s[7],s[8]!==u||s[9]!==j||s[10]!==o||s[11]!==d||s[12]!==h||s[13]!==c?(T=u?i.jsx("span",{className:`bs-s-sv bs-s-${h}`,style:{marginTop:5},children:t(h)}):i.jsxs(i.Fragment,{children:[i.jsx(_,{className:d?"required":"",children:o}),i.jsxs(C,{label:o,value:h,onChange:j,variant:c||"standard",required:d,fullWidth:!0,renderValue:q,children:[i.jsx(O,{value:r.Void,className:"bs-s bs-s-void",children:l.BOOKING_STATUS_VOID}),i.jsx(O,{value:r.Pending,className:"bs-s bs-s-pending",children:l.BOOKING_STATUS_PENDING}),i.jsx(O,{value:r.Deposit,className:"bs-s bs-s-deposit",children:l.BOOKING_STATUS_DEPOSIT}),i.jsx(O,{value:r.Paid,className:"bs-s bs-s-paid",children:l.BOOKING_STATUS_PAID}),i.jsx(O,{value:r.Reserved,className:"bs-s bs-s-reserved",children:l.BOOKING_STATUS_RESERVED}),i.jsx(O,{value:r.Cancelled,className:"bs-s bs-s-cancelled",children:l.BOOKING_STATUS_CANCELLED})]})]}),s[8]=u,s[9]=j,s[10]=o,s[11]=d,s[12]=h,s[13]=c,s[14]=T):T=s[14],s[15]!==g||s[16]!==T?(v=i.jsx("div",{style:g,children:T}),s[15]=g,s[16]=T,s[17]=v):v=s[17],v};function q(e){return i.jsx("span",{className:`bs-s-sv bs-s-${e}`,children:t(e)})}const W=({suppliers:e,statuses:s,filter:a,car:r,dress:_,offset:C,user:O,loggedUser:q,containerClassName:W,hideDates:z,hideDressColumn:Z,hideSupplierColumn:H,language:J,checkboxSelection:Q,onLoad:X})=>{const ee=j(),[se,ae]=f.useState(),[ie,te]=f.useState(),[le,re]=f.useState(0),[ne,oe]=f.useState(n.isMobile?n.BOOKINGS_MOBILE_PAGE_SIZE:n.BOOKINGS_PAGE_SIZE),[de,ce]=f.useState([]),[ue,Ee]=f.useState([]),[me,he]=f.useState(0),[Se,Ne]=f.useState(!1),[xe,pe]=f.useState(""),[fe,je]=f.useState([]),[ge,Te]=f.useState(-1),[ve,be]=f.useState(e),[Ie,_e]=f.useState(s),[Ce,Oe]=f.useState(),[De,Ae]=f.useState(a),[Le,Pe]=f.useState(r||""),[ke,Re]=f.useState(_||""),[ye,Ue]=f.useState(!1),[Ge,we]=f.useState(!1),[Me,Be]=f.useState(0),[Ke,$e]=f.useState({pageSize:n.BOOKINGS_PAGE_SIZE,page:0}),[Fe,Ve]=f.useState(!0);f.useEffect((()=>{n.isMobile||(re(Ke.page),oe(Ke.pageSize))}),[Ke]);const Ye=async(e,s,a,i)=>{try{const a=n.isMobile?n.BOOKINGS_MOBILE_PAGE_SIZE:ne;if(ve&&Ie){Ve(!0);const t={suppliers:ve,statuses:Ie,filter:De||void 0,dress:i||ke,user:s&&s._id||void 0},l=await T(t,e+1,a),r=l&&l.length>0?l[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!r)return void N();const o=Array.isArray(r.pageInfo)&&r.pageInfo.length>0?r.pageInfo[0].totalRecords:0;if(n.isMobile){const s=0===e?r.resultData:[...ue,...r.resultData];Ee(s),he(o),Ne(r.resultData.length>0),X&&X({rows:r.resultData,rowCount:o})}else Ee(r.resultData),he(o),X&&X({rows:r.resultData,rowCount:o})}else Ee([]),he(0),X&&X({rows:[],rowCount:0})}catch(t){N(t)}finally{Ve(!1)}};f.useEffect((()=>{be(e)}),[e]),f.useEffect((()=>{_e(s)}),[s]),f.useEffect((()=>{Ae(a)}),[a]),f.useEffect((()=>{Pe(r||""),r&&Ye(le,ie)}),[r]),f.useEffect((()=>{Re(_||""),_&&Ye(le,ie,0,_)}),[_]),f.useEffect((()=>{Be(C||0)}),[C]),f.useEffect((()=>{te(O),O&&Ye(le,O)}),[O]),f.useEffect((()=>{ve&&Ie&&se&&Ye(le,ie)}),[le]),f.useEffect((()=>{if(ve&&Ie&&se)if(0===le)Ye(0,ie);else{const e=o(Ke);e.page=0,$e(e)}}),[ne]);const qe=e=>{if(e){const s=new Date(e);return`${p(s.getDate())}-${p(s.getMonth()+1)}-${s.getFullYear()}`}throw new Error("Invalid date")},We=()=>{const e=[{field:"driver",headerName:V.DRIVER,flex:1,renderCell:({row:e,value:s})=>i.jsx(D,{href:`/user?u=${e.driver._id}`,children:s}),valueGetter:e=>e?.fullName},{field:"from",headerName:l.FROM,flex:1,valueGetter:e=>qe(e)},{field:"to",headerName:l.TO,flex:1,valueGetter:e=>qe(e)},{field:"price",headerName:V.PRICE,flex:1,renderCell:({value:e})=>i.jsx("span",{className:"bp",children:e}),valueGetter:e=>S(e,l.CURRENCY,J)},{field:"status",headerName:V.STATUS,flex:1,renderCell:({value:e})=>i.jsx(v,{value:e,showIcon:!0}),valueGetter:e=>e},{field:"action",headerName:"",sortable:!1,disableColumnMenu:!0,renderCell:({row:e})=>i.jsxs("div",{children:[i.jsx(B,{title:l.UPDATE,children:i.jsx(K,{onClick:()=>ee(`/update-booking?b=${e._id}`),children:i.jsx($,{})})}),i.jsx(B,{title:l.DELETE,children:i.jsx(K,{onClick:s=>{s.stopPropagation(),pe(e._id||""),we(!0)},children:i.jsx(F,{})})})]}),renderHeader:()=>fe.length>0?i.jsxs("div",{children:[i.jsx(B,{title:V.UPDATE_SELECTION,children:i.jsx(K,{onClick:()=>{Ue(!0)},children:i.jsx($,{})})}),i.jsx(B,{title:V.DELETE_SELECTION,children:i.jsx(K,{onClick:()=>{we(!0)},children:i.jsx(F,{})})})]}):i.jsx(i.Fragment,{})}];return z&&e.splice(1,2),Z||e.unshift({field:"dress",headerName:V.DRESS,flex:1,renderCell:({row:e,value:s})=>i.jsx(D,{href:`/dress?dr=${e.dress?._id}`,children:s}),valueGetter:e=>e?.name}),x(se)&&!H&&e.unshift({field:"supplier",headerName:l.SUPPLIER,flex:1,renderCell:({row:e,value:s})=>i.jsx(D,{href:`/supplier?c=${e.supplier._id}`,className:"cell-supplier",children:i.jsx("img",{src:E(n.CDN_USERS,e.supplier.avatar),alt:s})}),valueGetter:e=>e?.fullName}),e};f.useEffect((()=>{if(ve&&Ie&&se){const e=We();if(ce(e),0===le)Ye(0,ie);else{const e=o(Ke);e.page=0,$e(e)}}}),[ve,Ie,De]),f.useEffect((()=>{const e=We();ce(e)}),[fe]),f.useEffect((()=>{ae(q||void 0)}),[q]),f.useEffect((()=>{if(n.isMobile){const e=W?document.querySelector(`.${W}`):document.querySelector("div.bookings");e&&(e.onscroll=e=>{if(Se&&!Fe){const s=e.target;s.scrollTop>0&&s.offsetHeight+s.scrollTop+n.INFINITE_SCROLL_OFFSET>=s.scrollHeight&&(Ve(!0),re(le+1))}})}}),[W,le,Se,Fe,Me]);const ze=e=>{const s=e.currentTarget.getAttribute("data-id"),a=Number(e.currentTarget.getAttribute("data-index"));pe(s),Te(a),we(!0),pe(s),Te(a)},Ze="fr"===J,He=Ze?L:P,Je=Ze?"eee d LLL yyyy kk:mm":"eee, d LLL yyyy, p",Qe=n.SUPPLIER_IMAGE_HEIGHT+10;return i.jsxs("div",{className:"bs-list",children:[se&&(n.isMobile?i.jsx(i.Fragment,{children:ue.map(((e,s)=>{const a=new Date(e.from),r=new Date(e.to);return d(a,r),i.jsxs("div",{className:"booking-details",children:[i.jsx("div",{className:`bs bs-${e.status}`,children:i.jsx("span",{children:t(e.status)})}),e.dress&&i.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[i.jsx("span",{className:"booking-detail-title",children:V.DRESS}),i.jsx("div",{className:"booking-detail-value",children:i.jsx(D,{href:`dress/?dr=${e.dress._id}`,children:e.dress.name})})]}),i.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[i.jsx("span",{className:"booking-detail-title",children:V.DRIVER}),i.jsx("div",{className:"booking-detail-value",children:i.jsx(D,{href:`user/?u=${e.driver._id}`,children:e.driver.fullName})})]}),i.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[i.jsx("span",{className:"booking-detail-title",children:V.DAYS}),i.jsx("div",{className:"booking-detail-value",children:`${c(d(a,r))} (${u(A(a,Je,{locale:He}))} - ${u(A(r,Je,{locale:He}))})`})]}),i.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[i.jsx("span",{className:"booking-detail-title",children:l.PICK_UP_LOCATION}),i.jsx("div",{className:"booking-detail-value",children:e.pickupLocation.name})]}),i.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[i.jsx("span",{className:"booking-detail-title",children:l.DROP_OFF_LOCATION}),i.jsx("div",{className:"booking-detail-value",children:e.dropOffLocation.name})]}),i.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[i.jsx("span",{className:"booking-detail-title",children:l.SUPPLIER}),i.jsx("div",{className:"booking-detail-value",children:i.jsxs("div",{className:"car-supplier",children:[i.jsx("img",{src:E(n.CDN_USERS,e.supplier.avatar),alt:e.supplier.fullName}),i.jsx("span",{className:"car-supplier-name",children:e.supplier.fullName})]})})]}),(e.cancellation||e.amendments)&&i.jsx(i.Fragment,{children:i.jsxs("div",{className:"extras",children:[i.jsx("span",{className:"extras-title",children:l.OPTIONS}),e.cancellation&&i.jsxs("div",{className:"extra",children:[i.jsx(k,{className:"extra-icon"}),i.jsx("span",{className:"extra-title",children:g.CANCELLATION}),i.jsx("span",{className:"extra-text",children:m(e.dress?.cancellation,J)})]}),e.amendments&&i.jsxs("div",{className:"extra",children:[i.jsx(k,{className:"extra-icon"}),i.jsx("span",{className:"extra-title",children:g.AMENDMENTS}),i.jsx("span",{className:"extra-text",children:h(e.dress?.amendments,J)})]})]})}),i.jsxs("div",{className:"booking-detail",style:{height:Qe},children:[i.jsx("span",{className:"booking-detail-title",children:V.COST}),i.jsx("div",{className:"booking-detail-value booking-price",children:S(e.price,l.CURRENCY,J)})]}),i.jsxs("div",{className:"bs-buttons",children:[i.jsx(R,{variant:"contained",className:"btn-primary",size:"small",onClick:()=>ee(`/update-booking?b=${e._id}`),children:l.UPDATE}),i.jsx(R,{variant:"contained",className:"btn-secondary",size:"small","data-id":e._id,"data-index":s,onClick:ze,children:l.DELETE})]})]},e._id)}))}):i.jsx(y,{checkboxSelection:Q,getRowId:e=>e._id,columns:de,rows:ue,rowCount:me,loading:Fe,initialState:{pagination:{paginationModel:{pageSize:n.BOOKINGS_PAGE_SIZE}}},pageSizeOptions:[n.BOOKINGS_PAGE_SIZE,50,100],pagination:!0,paginationMode:"server",paginationModel:Ke,onPaginationModelChange:$e,onRowSelectionModelChange:e=>{je(Array.from(new Set(e.ids)).map((e=>e.toString())))},disableRowSelectionOnClick:!0,className:"booking-grid"})),i.jsxs(U,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ye,children:[i.jsx(G,{className:"dialog-header",children:V.UPDATE_STATUS}),i.jsx(w,{className:"bs-update-status",children:i.jsx(Y,{label:V.NEW_STATUS,onChange:e=>{Oe(e)}})}),i.jsxs(M,{className:"dialog-actions",children:[i.jsx(R,{onClick:()=>{Ue(!1)},variant:"contained",className:"btn-secondary",children:l.CANCEL}),i.jsx(R,{onClick:async()=>{try{if(!Ce)return void N();const e={ids:fe,status:Ce};200===await b(e)?(ue.forEach((e=>{e._id&&fe.includes(e._id)&&(e.status=Ce)})),Ee(o(ue))):N(),Ue(!1)}catch(e){N(e)}},variant:"contained",className:"btn-primary",children:l.UPDATE})]})]}),i.jsxs(U,{disableEscapeKeyDown:!0,maxWidth:"xs",open:Ge,children:[i.jsx(G,{className:"dialog-header",children:l.CONFIRM_TITLE}),i.jsx(w,{className:"dialog-content",children:0===fe.length?V.DELETE_BOOKING:V.DELETE_BOOKINGS}),i.jsxs(M,{className:"dialog-actions",children:[i.jsx(R,{onClick:()=>{we(!1),pe("")},variant:"contained",className:"btn-secondary",children:l.CANCEL}),i.jsx(R,{onClick:async()=>{try{if(n.isMobile){const e=[xe];200===await I(e)?(ue.splice(ge,1),Ee(ue),pe(""),Te(-1)):N(),we(!1)}else{const e=fe.length>0?fe:[xe];200===await I(e)?fe.length>0?Ee(ue.filter((e=>e._id&&!fe.includes(e._id)))):Ee(ue.filter((e=>e._id!==xe))):N(),we(!1)}}catch(e){N(e)}},variant:"contained",color:"error",children:l.DELETE})]})]})]})};export{W as B};
