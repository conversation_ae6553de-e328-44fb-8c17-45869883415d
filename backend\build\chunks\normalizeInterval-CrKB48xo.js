import{n as t,B as n,t as e,b as s}from"./fr-DJt_zj3p.js";function r(e,s,r){const[a,u]=t(r?.in,e,s),c=o(a,u),g=Math.abs(n(a,u));a.setDate(a.getDate()-c*g);const i=c*(g-Number(o(a,u)===-c));return 0===i?0:i}function o(t,n){const e=t.getFullYear()-n.getFullYear()||t.getMonth()-n.getMonth()||t.getDate()-n.getDate()||t.getHours()-n.getHours()||t.getMinutes()-n.getMinutes()||t.getSeconds()-n.getSeconds()||t.getMilliseconds()-n.getMilliseconds();return e<0?-1:e>0?1:e}function a(t){return n=>{const e=(t?Math[t]:Math.trunc)(n);return 0===e?0:e}}function u(t,n){return+e(t)-+e(n)}function c(t,n,e){const r=u(t,n)/s;return a(e?.roundingMethod)(r)}function g(n,e){const[s,r]=t(n,e.start,e.end);return{start:s,end:r}}export{r as a,c as b,u as d,a as g,g as n};
