import{k as e}from"../entries/index-xsXxT3-W.js";import{u as t}from"./Button-BeKLLPpp.js";function o(e,t,o){return void 0===e||"string"==typeof e?t:{...t,ownerState:{...t.ownerState,...o}}}function r(e,t,o){return"function"==typeof e?e(t,o):e}function s(e,t=[]){if(void 0===e)return{};const o={};return Object.keys(e).filter((o=>o.match(/^on[A-Z]/)&&"function"==typeof e[o]&&!t.includes(o))).forEach((t=>{o[t]=e[t]})),o}function n(e){if(void 0===e)return{};const t={};return Object.keys(e).filter((t=>!(t.match(/^on[A-Z]/)&&"function"==typeof e[t]))).forEach((o=>{t[o]=e[o]})),t}function a(t){const{getSlotProps:o,additionalProps:r,externalSlotProps:a,externalForwardedProps:l,className:c}=t;if(!o){const t=e(r?.className,c,l?.className,a?.className),o={...r?.style,...l?.style,...a?.style},s={...r,...l,...a};return t.length>0&&(s.className=t),Object.keys(o).length>0&&(s.style=o),{props:s,internalRef:void 0}}const i=s({...l,...a}),f=n(a),p=n(l),m=o(i),u=e(m?.className,r?.className,c,l?.className,a?.className),d={...m?.style,...r?.style,...l?.style,...a?.style},y={...m,...r,...p,...f};return u.length>0&&(y.className=u),Object.keys(d).length>0&&(y.style=d),{props:y,internalRef:m.ref}}function l(e,s){const{className:n,elementType:l,ownerState:c,externalForwardedProps:i,internalForwardedProps:f,shouldForwardComponentProp:p=!1,...m}=s,{component:u,slots:d={[e]:void 0},slotProps:y={[e]:void 0},...N}=i,P=d[e]||l,h=r(y[e],c),{props:{component:w,...v},internalRef:x}=a({className:n,...m,externalForwardedProps:"root"===e?N:void 0,externalSlotProps:h}),g=t(x,h?.ref,s.ref),j="root"===e?w||u:w;return[P,o(P,{..."root"===e&&!u&&!d[e]&&f,..."root"!==e&&!d[e]&&f,...v,...j&&!p&&{as:j},...j&&p&&{component:j},ref:g},c)]}export{o as a,s as e,a as m,r,l as u};
