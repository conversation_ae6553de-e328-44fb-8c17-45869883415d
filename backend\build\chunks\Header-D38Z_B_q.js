import{i as e,j as r,k as o,l as t,m as a,b as s,s as n,c as i,u as c,n as l,o as p,e as m,p as d,q as h,r as u,y as x,a as j,f as S,t as v}from"../entries/index-xsXxT3-W.js";import{r as f,d as y}from"./router-BtYqujaw.js";import{g as C}from"./BankDetailsService-Cq-yeaEa.js";import{S as g,A}from"./Avatar-CvDHTACZ.js";import{T as b}from"./Backdrop-Czag2Ija.js";import{M as k}from"./MenuItem-P0BnGnrT.js";import{M as T,c as B}from"./Grow-Cp8xsNYl.js";import{g as N,a as E,s as _,c as I,m as D,b as z,r as O,B as M}from"./Button-BeKLLPpp.js";import{P as w,u as R}from"./Paper-C-atefOs.js";import{T as P,L as H}from"./Toolbar-BTa0QYME.js";import{I as L}from"./IconButton-CxOCoGF3.js";import{u as U}from"./useSlot-DiTut-u0.js";import{m as G}from"./mergeSlotProps-DEridHif.js";import{S as V}from"./Slide-B_HgMHO0.js";import{L as $,M as F}from"./Menu-C_-X8cS7.js";import{L as K}from"./ListItem-Bmdw8GrH.js";import{L as W}from"./ListItemText-DUhWzkV9.js";import{C as q}from"./Flag-CMGasDVj.js";import{L as Y}from"./LocationService-6NvQT9iL.js";import{D as J}from"./DressService-DkS6e_O5.js";import{B as Q}from"./Badge-zckTAo43.js";import"./vendor-dblfw9z9.js";import"./Tooltip-CKMkVqOx.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./AccountCircle-DdIeIbov.js";import"./listItemTextClasses-BcbgzvlE.js";import"./ownerWindow-ChLfdzZL.js";import"./isHostComponent-DR4iSCFs.js";function X(e){return N("MuiAppBar",e)}E("MuiAppBar",["root","positionFixed","positionAbsolute","positionSticky","positionStatic","positionRelative","colorDefault","colorPrimary","colorSecondary","colorInherit","colorTransparent","colorError","colorInfo","colorSuccess","colorWarning"]);const Z=(e,r)=>e?`${e?.replace(")","")}, ${r})`:r,ee=_(w,{name:"MuiAppBar",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,r[`position${t(o.position)}`],r[`color${t(o.color)}`]]}})(D((({theme:e})=>({display:"flex",flexDirection:"column",width:"100%",boxSizing:"border-box",flexShrink:0,variants:[{props:{position:"fixed"},style:{position:"fixed",zIndex:(e.vars||e).zIndex.appBar,top:0,left:"auto",right:0,"@media print":{position:"absolute"}}},{props:{position:"absolute"},style:{position:"absolute",zIndex:(e.vars||e).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"sticky"},style:{position:"sticky",zIndex:(e.vars||e).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"static"},style:{position:"static"}},{props:{position:"relative"},style:{position:"relative"}},{props:{color:"inherit"},style:{"--AppBar-color":"inherit"}},{props:{color:"default"},style:{"--AppBar-background":e.vars?e.vars.palette.AppBar.defaultBg:e.palette.grey[100],"--AppBar-color":e.vars?e.vars.palette.text.primary:e.palette.getContrastText(e.palette.grey[100]),...e.applyStyles("dark",{"--AppBar-background":e.vars?e.vars.palette.AppBar.defaultBg:e.palette.grey[900],"--AppBar-color":e.vars?e.vars.palette.text.primary:e.palette.getContrastText(e.palette.grey[900])})}},...Object.entries(e.palette).filter(z(["contrastText"])).map((([r])=>({props:{color:r},style:{"--AppBar-background":(e.vars??e).palette[r].main,"--AppBar-color":(e.vars??e).palette[r].contrastText}}))),{props:e=>!0===e.enableColorOnDark&&!["inherit","transparent"].includes(e.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)"}},{props:e=>!1===e.enableColorOnDark&&!["inherit","transparent"].includes(e.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...e.applyStyles("dark",{backgroundColor:e.vars?Z(e.vars.palette.AppBar.darkBg,"var(--AppBar-background)"):null,color:e.vars?Z(e.vars.palette.AppBar.darkColor,"var(--AppBar-color)"):null})}},{props:{color:"transparent"},style:{"--AppBar-background":"transparent","--AppBar-color":"inherit",backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...e.applyStyles("dark",{backgroundImage:"none"})}}]})))),re=f.forwardRef((function(a,s){const n=e({props:a,name:"MuiAppBar"}),{className:i,color:c="primary",enableColorOnDark:l=!1,position:p="fixed",...m}=n,d={...n,color:c,position:p,enableColorOnDark:l},h=(e=>{const{color:r,position:o,classes:a}=e,s={root:["root",`color${t(r)}`,`position${t(o)}`]};return I(s,X,a)})(d);return r.jsx(ee,{square:!0,component:"header",ownerState:d,elevation:4,className:o(h.root,i,"fixed"===p&&"mui-fixed"),ref:s,...m})}));function oe(e){return N("MuiDrawer",e)}E("MuiDrawer",["root","docked","paper","anchorLeft","anchorRight","anchorTop","anchorBottom","paperAnchorLeft","paperAnchorRight","paperAnchorTop","paperAnchorBottom","paperAnchorDockedLeft","paperAnchorDockedRight","paperAnchorDockedTop","paperAnchorDockedBottom","modal"]);const te=(e,r)=>{const{ownerState:o}=e;return[r.root,("permanent"===o.variant||"persistent"===o.variant)&&r.docked,r.modal]},ae=_(T,{name:"MuiDrawer",slot:"Root",overridesResolver:te})(D((({theme:e})=>({zIndex:(e.vars||e).zIndex.drawer})))),se=_("div",{shouldForwardProp:O,name:"MuiDrawer",slot:"Docked",skipVariantsResolver:!1,overridesResolver:te})({flex:"0 0 auto"}),ne=_(w,{name:"MuiDrawer",slot:"Paper",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.paper,r[`paperAnchor${t(o.anchor)}`],"temporary"!==o.variant&&r[`paperAnchorDocked${t(o.anchor)}`]]}})(D((({theme:e})=>({overflowY:"auto",display:"flex",flexDirection:"column",height:"100%",flex:"1 0 auto",zIndex:(e.vars||e).zIndex.drawer,WebkitOverflowScrolling:"touch",position:"fixed",top:0,outline:0,variants:[{props:{anchor:"left"},style:{left:0}},{props:{anchor:"top"},style:{top:0,left:0,right:0,height:"auto",maxHeight:"100%"}},{props:{anchor:"right"},style:{right:0}},{props:{anchor:"bottom"},style:{top:"auto",left:0,bottom:0,right:0,height:"auto",maxHeight:"100%"}},{props:({ownerState:e})=>"left"===e.anchor&&"temporary"!==e.variant,style:{borderRight:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:e})=>"top"===e.anchor&&"temporary"!==e.variant,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:e})=>"right"===e.anchor&&"temporary"!==e.variant,style:{borderLeft:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:e})=>"bottom"===e.anchor&&"temporary"!==e.variant,style:{borderTop:`1px solid ${(e.vars||e).palette.divider}`}}]})))),ie={left:"right",right:"left",top:"down",bottom:"up"},ce=f.forwardRef((function(s,n){const i=e({props:s,name:"MuiDrawer"}),c=R(),l=a(),p={enter:c.transitions.duration.enteringScreen,exit:c.transitions.duration.leavingScreen},{anchor:m="left",BackdropProps:d,children:h,className:u,elevation:x=16,hideBackdrop:j=!1,ModalProps:{BackdropProps:S,...v}={},onClose:y,open:C=!1,PaperProps:g={},SlideProps:A,TransitionComponent:b,transitionDuration:k=p,variant:T="temporary",slots:B={},slotProps:N={},...E}=i,_=f.useRef(!1);f.useEffect((()=>{_.current=!0}),[]);const D=function({direction:e},r){return"rtl"===e&&function(e){return["left","right"].includes(e)}(r)?ie[r]:r}({direction:l?"rtl":"ltr"},m),z=m,O={...i,anchor:z,elevation:x,open:C,variant:T,...E},M=(e=>{const{classes:r,anchor:o,variant:a}=e,s={root:["root",`anchor${t(o)}`],docked:[("permanent"===a||"persistent"===a)&&"docked"],modal:["modal"],paper:["paper",`paperAnchor${t(o)}`,"temporary"!==a&&`paperAnchorDocked${t(o)}`]};return I(s,oe,r)})(O),w={slots:{transition:b,...B},slotProps:{paper:g,transition:A,...N,backdrop:G(N.backdrop||{...d,...S},{transitionDuration:k})}},[P,H]=U("root",{ref:n,elementType:ae,className:o(M.root,M.modal,u),shouldForwardComponentProp:!0,ownerState:O,externalForwardedProps:{...w,...E,...v},additionalProps:{open:C,onClose:y,hideBackdrop:j,slots:{backdrop:w.slots.backdrop},slotProps:{backdrop:w.slotProps.backdrop}}}),[L,$]=U("paper",{elementType:ne,shouldForwardComponentProp:!0,className:o(M.paper,g.className),ownerState:O,externalForwardedProps:w,additionalProps:{elevation:"temporary"===T?x:0,square:!0}}),[F,K]=U("docked",{elementType:se,ref:n,className:o(M.root,M.docked,u),ownerState:O,externalForwardedProps:w,additionalProps:E}),[W,q]=U("transition",{elementType:V,ownerState:O,externalForwardedProps:w,additionalProps:{in:C,direction:ie[D],timeout:k,appear:_.current}}),Y=r.jsx(L,{...$,children:h});if("permanent"===T)return r.jsx(F,{...K,children:Y});const J=r.jsx(W,{...q,children:Y});return"persistent"===T?r.jsx(F,{...K,children:J}):r.jsx(P,{...H,children:J})})),le=B(r.jsx("path",{d:"M4 10h3v7H4zm6.5 0h3v7h-3zM2 19h20v3H2zm15-9h3v7h-3zm-5-9L2 6v2h20V6z"})),pe=B(r.jsx("path",{d:"M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 16H5V10h14zM9 14H7v-2h2zm4 0h-2v-2h2zm4 0h-2v-2h2zm-8 4H7v-2h2zm4 0h-2v-2h2zm4 0h-2v-2h2z"})),me=B(r.jsx("path",{d:"M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z"})),de=B([r.jsx("path",{d:"M13 4H6v16h12V9h-5zm3 14H8v-2h8zm0-6v2H8v-2z",opacity:".3"},"0"),r.jsx("path",{d:"M8 16h8v2H8zm0-4h8v2H8zm6-10H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8zm4 18H6V4h7v5h5z"},"1")]),he=B(r.jsx("path",{d:"M10.09 15.59 11.5 17l5-5-5-5-1.41 1.41L12.67 11H3v2h9.67zM19 3H5c-1.11 0-2 .9-2 2v4h2V5h14v14H5v-4H3v4c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2"})),ue=B([r.jsx("path",{d:"M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8m1 13h-2v-6h2zm0-8h-2V7h2z",opacity:".3"},"0"),r.jsx("path",{d:"M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8"},"1")]),xe=B(r.jsx("path",{d:"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2m6.93 6h-2.95c-.32-1.25-.78-2.45-1.38-3.56 1.84.63 3.37 1.91 4.33 3.56M12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96M4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2s.06 1.34.14 2zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56-1.84-.63-3.37-1.9-4.33-3.56m2.95-8H5.08c.96-1.66 2.49-2.93 4.33-3.56C8.81 5.55 8.35 6.75 8.03 8M12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96M14.34 14H9.66c-.09-.66-.16-1.32-.16-2s.07-1.35.16-2h4.68c.09.65.16 1.32.16 2s-.07 1.34-.16 2m.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95c-.96 1.65-2.49 2.93-4.33 3.56M16.36 14c.08-.66.14-1.32.14-2s-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2z"})),je=B(r.jsx("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"})),Se=B(r.jsx("path",{d:"M3 18h18v-2H3zm0-5h18v-2H3zm0-7v2h18V6z"})),ve=B(r.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16"})),fe=B(r.jsx("path",{d:"M22 3H7c-.69 0-1.23.35-1.59.88L0 12l5.41 8.11c.36.53.97.89 1.66.89H22c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 13.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m5 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m5 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5"})),ye=B(r.jsx("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"})),Ce=B(r.jsx("path",{d:"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3m-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3m0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5m8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5"})),ge=B(r.jsx("path",{d:"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"})),Ae=new s({fr:{DASHBOARD:"Tableau de bord",SCHEDULER:"Planificateur",HOME:"Accueil",COMPANIES:"Fournisseurs",LOCATIONS:"Lieux",CARS:"Voitures",DRESSES:"Robes",USERS:"Utilisateurs",ABOUT:"À propos",TOS:"Conditions d'utilisation",CONTACT:"Contact",LANGUAGE:"Langue",SETTINGS:"Paramètres",SIGN_OUT:"Déconnexion",COUNTRIES:"Pays",BANK_DETAILS:"Détails bancaires",PRICING:"Tarification"},en:{DASHBOARD:"Dashboard",SCHEDULER:"Vehicle Scheduler",HOME:"Home",COMPANIES:"Suppliers",LOCATIONS:"Locations",CARS:"Cars",DRESSES:"Dresses",USERS:"Users",ABOUT:"About",TOS:"Terms of Service",CONTACT:"Contact",LANGUAGE:"Language",SETTINGS:"Settings",SIGN_OUT:"Sign out",COUNTRIES:"Countries",BANK_DETAILS:"Bank Details",PRICING:"Pricing"},es:{DASHBOARD:"Panel de control",SCHEDULER:"Programador",HOME:"Inicio",COMPANIES:"Proveedores",LOCATIONS:"Ubicaciones",CARS:"Coches",DRESSES:"Vestidos",USERS:"Usuarios",ABOUT:"Acerca de",TOS:"Términos de servicio",CONTACT:"Contacto",LANGUAGE:"Idioma",SETTINGS:"Configuración",SIGN_OUT:"Cerrar sesión",COUNTRIES:"Países",BANK_DETAILS:"Detalles bancarios",PRICING:"Precios"}});n(Ae);const be=e=>{const o=i.c(75),{hidden:t}=e,a=y(),{user:s}=c(),{notificationCount:T}=l(),[B,N]=f.useState();let E;o[0]===Symbol.for("react.memo_cache_sentinel")?(E=p(m.DEFAULT_LANGUAGE),o[0]=E):E=o[0];const[_,I]=f.useState(E),[D,z]=f.useState(null),[O,w]=f.useState(null),[R,U]=f.useState(null),[G,V]=f.useState(null),[X,Z]=f.useState(!1),[ee,oe]=f.useState(!1),[te,ae]=f.useState(null),se=Boolean(D),ne=Boolean(R),ie=Boolean(O),be=Boolean(G);let ke;o[1]===Symbol.for("react.memo_cache_sentinel")?(ke={list:{width:250},formControl:{margin:1,minWidth:120},selectEmpty:{marginTop:2},grow:{flexGrow:1},menuButton:{marginRight:2}},o[1]=ke):ke=o[1];const Te=ke;let Be;o[2]===Symbol.for("react.memo_cache_sentinel")?(Be=e=>{z(e.currentTarget)},o[2]=Be):Be=o[2];const Ne=Be;let Ee;o[3]===Symbol.for("react.memo_cache_sentinel")?(Ee=()=>{U(null)},o[3]=Ee):Ee=o[3];const _e=Ee;let Ie;o[4]===Symbol.for("react.memo_cache_sentinel")?(Ie=e=>{w(e.currentTarget)},o[4]=Ie):Ie=o[4];const De=Ie;let ze;o[5]!==a?(ze=()=>{a(0)},o[5]=a,o[6]=ze):ze=o[6];const Oe=ze;let Me;o[7]!==X||o[8]!==Oe||o[9]!==s?(Me=async e=>{w(null);const{code:r}=e.currentTarget.dataset;if(r){I(p(r));const e=d();if(X&&s){const o={id:s._id,language:r};200===await h(o)?(u(r),r&&r!==e&&Oe()):x(j.CHANGE_LANGUAGE_ERROR,{type:"error"})}else u(r),r&&r!==e&&Oe()}},o[7]=X,o[8]=Oe,o[9]=s,o[10]=Me):Me=o[10];const we=Me;let Re;o[11]===Symbol.for("react.memo_cache_sentinel")?(Re=()=>{z(null),_e()},o[11]=Re):Re=o[11];const Pe=Re;let He;o[12]!==a?(He=()=>{Pe(),a("/settings")},o[12]=a,o[13]=He):He=o[13];const Le=He;let Ue;o[14]===Symbol.for("react.memo_cache_sentinel")?(Ue=async()=>{Pe(),await S()},o[14]=Ue):Ue=o[14];const Ge=Ue;let Ve;o[15]===Symbol.for("react.memo_cache_sentinel")?(Ve=e=>{U(e.currentTarget)},o[15]=Ve):Ve=o[15];const $e=Ve;let Fe;o[16]===Symbol.for("react.memo_cache_sentinel")?(Fe=e=>{V(e.currentTarget)},o[16]=Fe):Fe=o[16];const Ke=Fe;let We;o[17]===Symbol.for("react.memo_cache_sentinel")?(We=()=>{V(null)},o[17]=We):We=o[17];const qe=We;let Ye;o[18]!==a?(Ye=()=>{a("/notifications")},o[18]=a,o[19]=Ye):Ye=o[19];const Je=Ye;let Qe,Xe,Ze,er,rr,or,tr,ar,sr,nr,ir,cr,lr;o[20]===Symbol.for("react.memo_cache_sentinel")?(Qe=()=>{const e=v();I(p(e)),n(Ae,e)},Xe=[],o[20]=Qe,o[21]=Xe):(Qe=o[20],Xe=o[21]),f.useEffect(Qe,Xe),o[22]!==s?(Ze=()=>{s?(N(s),Z(!0)):(N(void 0),Z(!1))},er=[s],o[22]=s,o[23]=Ze,o[24]=er):(Ze=o[23],er=o[24]),f.useEffect(Ze,er),o[25]!==B||o[26]!==t?(rr=()=>{(async()=>{if(!t&&B){const e=await C();ae(e),Z(!0),oe(!0)}})()},or=[t,B],o[25]=B,o[26]=t,o[27]=rr,o[28]=or):(rr=o[27],or=o[28]),f.useEffect(rr,or),o[29]===Symbol.for("react.memo_cache_sentinel")?(tr={vertical:"top",horizontal:"right"},o[29]=tr):tr=o[29],o[30]===Symbol.for("react.memo_cache_sentinel")?(ar={vertical:"top",horizontal:"right"},o[30]=ar):ar=o[30],o[31]===Symbol.for("react.memo_cache_sentinel")?(sr=r.jsx(ge,{className:"header-action"}),nr=r.jsx(b,{children:Ae.SETTINGS}),o[31]=sr,o[32]=nr):(sr=o[31],nr=o[32]),o[33]!==Le?(ir=r.jsxs(k,{onClick:Le,children:[sr,nr]}),o[33]=Le,o[34]=ir):ir=o[34],o[35]===Symbol.for("react.memo_cache_sentinel")?(cr=r.jsxs(k,{onClick:Ge,children:[r.jsx(he,{className:"header-action"}),r.jsx(b,{children:Ae.SIGN_OUT})]}),o[35]=cr):cr=o[35],o[36]!==D||o[37]!==se||o[38]!==ir?(lr=r.jsxs(F,{anchorEl:D,anchorOrigin:tr,id:"primary-account-menu",keepMounted:!0,transformOrigin:ar,open:se,onClose:Pe,className:"menu",children:[ir,cr]}),o[36]=D,o[37]=se,o[38]=ir,o[39]=lr):lr=o[39];const pr=lr;let mr,dr,hr,ur,xr,jr,Sr,vr;o[40]===Symbol.for("react.memo_cache_sentinel")?(mr={vertical:"top",horizontal:"right"},o[40]=mr):mr=o[40],o[41]===Symbol.for("react.memo_cache_sentinel")?(dr={vertical:"top",horizontal:"right"},o[41]=dr):dr=o[41],o[42]===Symbol.for("react.memo_cache_sentinel")?(hr=r.jsx(ge,{className:"header-action"}),ur=r.jsx("p",{children:Ae.SETTINGS}),o[42]=hr,o[43]=ur):(hr=o[42],ur=o[43]),o[44]!==Le?(xr=r.jsxs(k,{onClick:Le,children:[hr,ur]}),o[44]=Le,o[45]=xr):xr=o[45],o[46]===Symbol.for("react.memo_cache_sentinel")?(jr=r.jsxs(k,{onClick:De,children:[r.jsx(xe,{className:"header-action"}),r.jsx("p",{children:Ae.LANGUAGE})]}),o[46]=jr):jr=o[46],o[47]===Symbol.for("react.memo_cache_sentinel")?(Sr=r.jsxs(k,{onClick:Ge,children:[r.jsx(he,{className:"header-action"}),r.jsx("p",{children:Ae.SIGN_OUT})]}),o[47]=Sr):Sr=o[47],o[48]!==ne||o[49]!==R||o[50]!==xr?(vr=r.jsxs(F,{anchorEl:R,anchorOrigin:mr,id:"mobile-menu",keepMounted:!0,transformOrigin:dr,open:ne,onClose:_e,className:"menu",children:[xr,jr,Sr]}),o[48]=ne,o[49]=R,o[50]=xr,o[51]=vr):vr=o[51];const fr=vr;let yr,Cr,gr,Ar;o[52]===Symbol.for("react.memo_cache_sentinel")?(yr={vertical:"top",horizontal:"right"},o[52]=yr):yr=o[52],o[53]===Symbol.for("react.memo_cache_sentinel")?(Cr={vertical:"top",horizontal:"right"},o[53]=Cr):Cr=o[53],o[54]!==we?(gr=m._LANGUAGES.map((e=>r.jsx(k,{onClick:we,"data-code":e.code,children:e.label},e.code))),o[54]=we,o[55]=gr):gr=o[55],o[56]!==we||o[57]!==ie||o[58]!==O||o[59]!==gr?(Ar=r.jsx(F,{anchorEl:O,anchorOrigin:yr,id:"language-menu",keepMounted:!0,transformOrigin:Cr,open:ie,onClose:we,className:"menu",children:gr}),o[56]=we,o[57]=ie,o[58]=O,o[59]=gr,o[60]=Ar):Ar=o[60];const br=Ar;let kr;return o[61]!==te?.showBankDetailsPage||o[62]!==Je||o[63]!==t||o[64]!==ee||o[65]!==be||o[66]!==X||o[67]!==_?.label||o[68]!==a||o[69]!==T||o[70]!==br||o[71]!==pr||o[72]!==fr||o[73]!==s?(kr=!t&&r.jsxs("div",{style:Te.grow,className:"header",children:[r.jsx(re,{position:"fixed",sx:{bgcolor:"#121212"},children:r.jsxs(P,{className:"toolbar",children:[ee&&X&&r.jsx(L,{edge:"start",sx:Te.menuButton,color:"inherit","aria-label":"open drawer",onClick:Ke,children:r.jsx(Se,{})}),r.jsx(r.Fragment,{children:r.jsx(ce,{open:be,onClose:qe,className:"menu  side-menu",children:r.jsxs($,{sx:Te.list,children:[r.jsxs(K,{onClick:()=>{a("/"),qe()},children:[r.jsx(H,{children:r.jsx(me,{})}),r.jsx(W,{primary:Ae.DASHBOARD})]}),r.jsxs(K,{onClick:()=>{a("/scheduler"),qe()},children:[r.jsx(H,{children:r.jsx(pe,{})}),r.jsx(W,{primary:Ae.SCHEDULER})]}),r.jsxs(K,{onClick:()=>{a("/suppliers"),qe()},children:[r.jsx(H,{children:r.jsx(g,{})}),r.jsx(W,{primary:Ae.COMPANIES})]}),r.jsxs(K,{onClick:()=>{a("/countries"),qe()},children:[r.jsx(H,{children:r.jsx(q,{})}),r.jsx(W,{primary:Ae.COUNTRIES})]}),r.jsxs(K,{onClick:()=>{a("/locations"),qe()},children:[r.jsx(H,{children:r.jsx(Y,{})}),r.jsx(W,{primary:Ae.LOCATIONS})]}),r.jsxs(K,{onClick:()=>{a("/dresses"),qe()},children:[r.jsx(H,{children:r.jsx(J,{})}),r.jsx(W,{primary:Ae.DRESSES})]}),r.jsxs(K,{onClick:()=>{a("/users"),qe()},children:[r.jsx(H,{children:r.jsx(Ce,{})}),r.jsx(W,{primary:Ae.USERS})]}),r.jsxs(K,{onClick:()=>{a("/pricing"),qe()},children:[r.jsx(H,{children:r.jsx(ve,{})}),r.jsx(W,{primary:Ae.PRICING})]}),te?.showBankDetailsPage&&r.jsxs(K,{onClick:()=>{a("/bank-details"),qe()},children:[r.jsx(H,{children:r.jsx(le,{})}),r.jsx(W,{primary:Ae.BANK_DETAILS})]}),r.jsxs(K,{onClick:()=>{a("/about"),qe()},children:[r.jsx(H,{children:r.jsx(ue,{})}),r.jsx(W,{primary:Ae.ABOUT})]}),r.jsxs(K,{onClick:()=>{a("/tos"),qe()},children:[r.jsx(H,{children:r.jsx(de,{})}),r.jsx(W,{primary:Ae.TOS})]}),r.jsxs(K,{onClick:()=>{a("/contact"),qe()},children:[r.jsx(H,{children:r.jsx(je,{})}),r.jsx(W,{primary:Ae.CONTACT})]})]})})}),r.jsx("div",{style:Te.grow}),r.jsxs("div",{className:"header-desktop",children:[X&&r.jsx(L,{"aria-label":"",color:"inherit",onClick:Je,children:r.jsx(Q,{badgeContent:T>0?T:null,color:"error",children:r.jsx(ye,{})})}),ee&&r.jsx(M,{variant:"contained",startIcon:r.jsx(xe,{}),onClick:De,disableElevation:!0,className:"btn-primary",children:_?.label}),X&&s&&r.jsx(L,{edge:"end","aria-label":"account","aria-controls":"primary-account-menu","aria-haspopup":"true",onClick:Ne,color:"inherit",children:r.jsx(A,{record:s,type:s.type,size:"small",readonly:!0})})]}),r.jsxs("div",{className:"header-mobile",children:[!X&&r.jsx(M,{variant:"contained",startIcon:r.jsx(xe,{}),onClick:De,disableElevation:!0,className:"btn-primary",children:_?.label}),X&&r.jsx(L,{color:"inherit",onClick:Je,children:r.jsx(Q,{badgeContent:T>0?T:null,color:"error",children:r.jsx(ye,{})})}),X&&r.jsx(L,{"aria-label":"show more","aria-controls":"mobile-menu","aria-haspopup":"true",onClick:$e,color:"inherit",children:r.jsx(fe,{})})]})]})}),fr,pr,br]}),o[61]=te?.showBankDetailsPage,o[62]=Je,o[63]=t,o[64]=ee,o[65]=be,o[66]=X,o[67]=_?.label,o[68]=a,o[69]=T,o[70]=br,o[71]=pr,o[72]=fr,o[73]=s,o[74]=kr):kr=o[74],kr};export{be as default};
