import{R as e,j as r,a as t,ac as s,U as a,e as i,F as o,z as n,p as l,M as u,aw as m,L as p,N as d,D as c}from"../entries/index-CEzJO5Xy.js";import{d as j,r as h}from"./router-BtYqujaw.js";import{L as f}from"./Layout-BQBjg4Lf.js";import{s as x}from"./create-supplier-D6E0ETct.js";import{s as g,i as S}from"./create-user-yOc6o3tp.js";import{v as E}from"./SupplierService-DSnTbAgG.js";import{E as v}from"./Error-7KgmWHkR.js";import{S as I}from"./SimpleBackdrop-Bf3qjF13.js";import{A as N}from"./Avatar-BtfxKR-8.js";import{D as A}from"./DatePicker-B527VDUP.js";import{P as C}from"./Paper-CcwAvfvc.js";import{I as y}from"./Info-C_WcR51V.js";import{F as _,I as L}from"./InputLabel-BbcIE26O.js";import{S as D}from"./TextField-BAse--ht.js";import{M as R}from"./MenuItem-suKfXYI2.js";import{I as T}from"./Input-BQdee9z7.js";import{F as b}from"./FormHelperText-DFSsjBsL.js";import{F as M,S as P}from"./Switch-BWPUOSX1.js";import{B as w}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./normalizeInterval-a5pcD5yp.js";import"./format-4arn0GRM.js";import"./useMobilePicker-Cpitw7qm.js";import"./getThemeProps-gt86ccpv.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./IconButton-CnBvmeAK.js";import"./useFormControl-B7jXtRD7.js";import"./ListItem-D1VHRhQp.js";import"./isHostComponent-DR4iSCFs.js";import"./Menu-ZU0DMgjT.js";import"./mergeSlotProps-Cay5TZBz.js";import"./Chip-CAtDqtgp.js";import"./DressService-J0XavNJj.js";import"./LocationService-BtQFgoWL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";import"./DatePicker-CyKPL9FL.js";import"./fr-CaQg1DLH.js";import"./OutlinedInput-g8mR4MM3.js";import"./listItemTextClasses-DFwCkkgK.js";import"./SwitchBase-BIeqtL5F.js";const U=()=>{const U=j(),[W,B]=h.useState(),[O,k]=h.useState(!1),[F,G]=h.useState(""),[q,H]=h.useState(""),[Y,z]=h.useState(""),[V,$]=h.useState(""),[Z,K]=h.useState(""),[Q,J]=h.useState(!1),[X,ee]=h.useState(!1),[re,te]=h.useState(!1),[se,ae]=h.useState(!1),[ie,oe]=h.useState(!1),[ne,le]=h.useState(""),[ue,me]=h.useState(!1),[pe,de]=h.useState(""),[ce,je]=h.useState(!0),[he,fe]=h.useState(!0),[xe,ge]=h.useState(!1),[Se,Ee]=h.useState(),[ve,Ie]=h.useState(!0),[Ne,Ae]=h.useState(""),[Ce,ye]=h.useState(""),[_e,Le]=h.useState(""),[De,Re]=h.useState(!1),Te=async e=>{if(!e)return oe(!1),!0;try{return 200===await E({fullName:e})?(oe(!1),J(!1),!0):(oe(!0),me(!1),J(!1),!1)}catch(r){return n(r),!0}},be=async e=>{if(!e)return ee(!1),je(!0),!1;if(!d.isEmail(e))return ee(!1),je(!1),!1;try{return 200===await c({email:e})?(ee(!1),je(!0),!0):(ee(!0),je(!0),me(!1),J(!1),!1)}catch(r){return n(r),!0}},Me=e=>{if(e){const r=d.isMobilePhone(e);return fe(r),r}return fe(!0),!0},Pe=r=>{if(r&&m(r)&&pe===e.User){const e=(S({start:r,end:new Date}).years??0)>=i.MINIMUM_AGE;return Ie(e),e}return Ie(!0),!0},we=pe===e.Supplier,Ue=pe===e.User;return r.jsxs(f,{onLoad:r=>{if(r&&r.verified){const t=o(r);B(r),k(t),de(t?"":e.User),te(!0)}},strict:!0,children:[W&&r.jsx("div",{className:"create-user",children:r.jsxs(C,{className:"user-form user-form-wrapper",elevation:10,style:re?{}:{display:"none"},children:[r.jsx("h1",{className:"user-form-title",children:g.CREATE_USER_HEADING}),r.jsxs("form",{onSubmit:async r=>{try{if(r.preventDefault(),!W)return void n();if(pe===e.Supplier){if(!(await Te(F)))return}else oe(!1);if(!(await be(q)))return;if(!Me(Y))return;if(!Pe(Se))return;if(pe===e.Supplier&&!ne)return me(!0),void J(!1);const t=l(),s=O?void 0:W._id,a={email:q,phone:Y,location:V,bio:Z,fullName:F,type:pe,avatar:ne,birthDate:Se,language:t,supplier:s,minimumRentalDays:Ne?Number(Ne):void 0,priceChangeRate:Ce?Number(Ce):void 0,supplierDressLimit:_e?Number(_e):void 0,notifyAdminOnNewDress:pe===e.Supplier?De:void 0};pe===e.Supplier&&(a.payLater=xe),200===await u(a)?U("/users"):J(!0)}catch(t){n(t)}},children:[r.jsx(N,{type:pe,mode:"create",record:null,size:"large",readonly:!1,onBeforeUpload:()=>{ae(!0)},onChange:r=>{ae(!1),le(r),null!==r&&pe===e.Supplier&&me(!1)},color:"disabled",className:"avatar-ctn"}),we&&r.jsxs("div",{className:"info",children:[r.jsx(y,{}),r.jsx("span",{children:x.RECOMMENDED_IMAGE_SIZE})]}),O&&r.jsxs(_,{fullWidth:!0,margin:"dense",style:{marginTop:we?0:39},children:[r.jsx(L,{className:"required",children:t.TYPE}),r.jsxs(D,{label:t.TYPE,value:pe,onChange:async r=>{const t=r.target.value;de(t),t===e.Supplier?await Te(F):oe(!1)},variant:"standard",required:!0,fullWidth:!0,children:[r.jsx(R,{value:e.Admin,children:s(a.Admin)}),r.jsx(R,{value:e.Supplier,children:s(a.Supplier)}),r.jsx(R,{value:e.User,children:s(a.User)})]})]}),r.jsxs(_,{fullWidth:!0,margin:"dense",children:[r.jsx(L,{className:"required",children:t.FULL_NAME}),r.jsx(T,{id:"full-name",type:"text",error:ie,required:!0,onBlur:async r=>{pe===e.Supplier?await Te(r.target.value):oe(!1)},onChange:e=>{G(e.target.value),e.target.value||oe(!1)},autoComplete:"off"}),r.jsx(b,{error:ie,children:ie&&x.INVALID_SUPPLIER_NAME||""})]}),r.jsxs(_,{fullWidth:!0,margin:"dense",children:[r.jsx(L,{className:"required",children:t.EMAIL}),r.jsx(T,{id:"email",type:"text",error:!ce||X,onBlur:async e=>{await be(e.target.value)},onChange:e=>{H(e.target.value),e.target.value||(ee(!1),je(!0))},autoComplete:"off",required:!0}),r.jsxs(b,{error:!ce||X,children:[!ce&&t.EMAIL_NOT_VALID||"",X&&t.EMAIL_ALREADY_REGISTERED||""]})]}),Ue&&r.jsx(r.Fragment,{children:r.jsxs(_,{fullWidth:!0,margin:"dense",children:[r.jsx(A,{label:g.BIRTH_DATE,value:Se,required:!0,onChange:e=>{if(e){const r=Pe(e);Ee(e),Ie(r)}},language:W&&W.language||i.DEFAULT_LANGUAGE}),r.jsx(b,{error:!ve,children:!ve&&t.BIRTH_DATE_NOT_VALID||""})]})}),r.jsxs(_,{fullWidth:!0,margin:"dense",children:[r.jsx(L,{className:Ue?"required":"",children:t.PHONE}),r.jsx(T,{id:"phone",type:"text",onBlur:e=>{Me(e.target.value)},onChange:e=>{z(e.target.value),e.target.value||fe(!0)},error:!he,required:!!Ue,autoComplete:"off"}),r.jsx(b,{error:!he,children:!he&&t.PHONE_NOT_VALID||""})]}),r.jsxs(_,{fullWidth:!0,margin:"dense",children:[r.jsx(L,{children:t.LOCATION}),r.jsx(T,{id:"location",type:"text",onChange:e=>{$(e.target.value)},autoComplete:"off"})]}),r.jsxs(_,{fullWidth:!0,margin:"dense",children:[r.jsx(L,{children:t.BIO}),r.jsx(T,{id:"bio",type:"text",onChange:e=>{K(e.target.value)},autoComplete:"off"})]}),we&&r.jsxs(r.Fragment,{children:[r.jsx(_,{fullWidth:!0,margin:"dense",children:r.jsx(M,{control:r.jsx(P,{checked:xe,onChange:e=>{ge(e.target.checked)},color:"primary"}),label:t.PAY_LATER})}),r.jsx(_,{fullWidth:!0,margin:"dense",children:r.jsx(M,{control:r.jsx(P,{checked:De,disabled:W?.type===a.Supplier,onChange:e=>{Re(e.target.checked)},color:"primary"}),label:t.NOTIFY_ADMIN_ON_NEW_CAR})}),r.jsxs(_,{fullWidth:!0,margin:"dense",children:[r.jsx(L,{children:t.SUPPLIER_CAR_LIMIT}),r.jsx(T,{type:"text",onChange:e=>{Le(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^\\d+$"}}})]}),r.jsxs(_,{fullWidth:!0,margin:"dense",children:[r.jsx(L,{children:t.MIN_RENTAL_DAYS}),r.jsx(T,{type:"text",onChange:e=>{Ae(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^\\d+$"}}})]}),r.jsxs(_,{fullWidth:!0,margin:"dense",children:[r.jsx(L,{children:t.PRICE_CHANGE_RATE}),r.jsx(T,{type:"text",onChange:e=>{ye(e.target.value)},autoComplete:"off",slotProps:{input:{inputMode:"numeric",pattern:"^-?\\d+(\\.\\d+)?$"}}})]})]}),r.jsxs("div",{className:"buttons",children:[r.jsx(w,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",children:t.CREATE}),r.jsx(w,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:async()=>{try{ne&&await p(ne),U("/users")}catch{U("/users")}},children:t.CANCEL})]}),r.jsxs("div",{className:"form-error",children:[Q&&r.jsx(v,{message:t.GENERIC_ERROR}),ue&&r.jsx(v,{message:t.IMAGE_REQUIRED})]})]})]})}),se&&r.jsx(I,{text:t.PLEASE_WAIT})]})};export{U as default};
