export var UserType;
(function (UserType) {
    UserType["Admin"] = "admin";
    UserType["Supplier"] = "supplier";
    UserType["User"] = "user";
})(UserType || (UserType = {}));
export var AppType;
(function (AppType) {
    AppType["Backend"] = "backend";
    AppType["Frontend"] = "frontend";
})(AppType || (AppType = {}));
export var DressType;
(function (DressType) {
    DressType["Traditional"] = "traditional";
    DressType["Modern"] = "modern";
    DressType["Designer"] = "designer";
    DressType["Vintage"] = "vintage";
    DressType["Casual"] = "casual";
    DressType["Wedding"] = "wedding";
    DressType["Evening"] = "evening";
    DressType["Cocktail"] = "cocktail";
    DressType["Prom"] = "prom";
    DressType["Other"] = "other";
    DressType["Unknown"] = "unknown";
})(DressType || (DressType = {}));
export var DressRange;
(function (DressRange) {
    DressRange["Mini"] = "mini";
    DressRange["Midi"] = "midi";
    DressRange["Maxi"] = "maxi";
    DressRange["Bridal"] = "bridal";
    DressRange["Evening"] = "evening";
    DressRange["Cocktail"] = "cocktail";
    DressRange["Casual"] = "casual";
})(DressRange || (DressRange = {}));
export var DressAccessories;
(function (DressAccessories) {
    DressAccessories["Veil"] = "veil";
    DressAccessories["Jewelry"] = "jewelry";
    DressAccessories["Shoes"] = "shoes";
    DressAccessories["Headpiece"] = "headpiece";
})(DressAccessories || (DressAccessories = {}));
export var DressSize;
(function (DressSize) {
    DressSize["XS"] = "xs";
    DressSize["S"] = "s";
    DressSize["M"] = "m";
    DressSize["L"] = "l";
    DressSize["XL"] = "xl";
    DressSize["XXL"] = "xxl";
    DressSize["ExtraSmall"] = "extraSmall";
    DressSize["Small"] = "small";
    DressSize["Medium"] = "medium";
    DressSize["Large"] = "large";
    DressSize["ExtraLarge"] = "extraLarge";
    DressSize["DoubleExtraLarge"] = "doubleExtraLarge";
    DressSize["Custom"] = "custom";
})(DressSize || (DressSize = {}));
export var DressMaterial;
(function (DressMaterial) {
    DressMaterial["Silk"] = "silk";
    DressMaterial["Cotton"] = "cotton";
    DressMaterial["Lace"] = "lace";
    DressMaterial["Satin"] = "satin";
    DressMaterial["Chiffon"] = "chiffon";
    DressMaterial["Tulle"] = "tulle";
    DressMaterial["Organza"] = "organza";
    DressMaterial["Velvet"] = "velvet";
    DressMaterial["Polyester"] = "polyester";
    DressMaterial["Crepe"] = "crepe";
})(DressMaterial || (DressMaterial = {}));
export var DressStyle;
(function (DressStyle) {
    DressStyle["Traditional"] = "traditional";
    DressStyle["Modern"] = "modern";
    DressStyle["Designer"] = "designer";
    DressStyle["Vintage"] = "vintage";
})(DressStyle || (DressStyle = {}));
export var RentalTerm;
(function (RentalTerm) {
    RentalTerm["Limited"] = "limited";
    RentalTerm["Unlimited"] = "unlimited";
})(RentalTerm || (RentalTerm = {}));
export var BookingStatus;
(function (BookingStatus) {
    BookingStatus["Void"] = "void";
    BookingStatus["Pending"] = "pending";
    BookingStatus["Deposit"] = "deposit";
    BookingStatus["Paid"] = "paid";
    BookingStatus["Reserved"] = "reserved";
    BookingStatus["Cancelled"] = "cancelled";
})(BookingStatus || (BookingStatus = {}));
export var Availablity;
(function (Availablity) {
    Availablity["Available"] = "available";
    Availablity["Unavailable"] = "unavailable";
})(Availablity || (Availablity = {}));
export var RecordType;
(function (RecordType) {
    RecordType["Admin"] = "admin";
    RecordType["Supplier"] = "supplier";
    RecordType["User"] = "user";
    RecordType["Dress"] = "dress";
    RecordType["Location"] = "location";
    RecordType["Country"] = "country";
})(RecordType || (RecordType = {}));
export var PaymentGateway;
(function (PaymentGateway) {
    PaymentGateway["PayPal"] = "payPal";
    PaymentGateway["Stripe"] = "stripe";
    PaymentGateway["Visa"] = "visa";
})(PaymentGateway || (PaymentGateway = {}));
export var SocialSignInType;
(function (SocialSignInType) {
    SocialSignInType["Facebook"] = "facebook";
    SocialSignInType["Apple"] = "apple";
    SocialSignInType["Google"] = "google";
})(SocialSignInType || (SocialSignInType = {}));
