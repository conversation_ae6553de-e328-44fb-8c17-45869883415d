import { Request, Response } from 'express';
/**
 * Upsert BankDetails.
 *
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const upsert: (req: Request, res: Response) => Promise<void>;
/**
 * Get BankDetails.
 *
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export declare const get: (req: Request, res: Response) => Promise<void>;
