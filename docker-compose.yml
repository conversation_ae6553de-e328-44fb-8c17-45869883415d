services:
  mongo:
    image: mongo:latest
    command: mongod --quiet --logpath /dev/null
    restart: always
    environment:
      # Provide your credentials here
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin
    ports:
      - 27018:27017
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb

  mongo-express:
    image: mongo-express:latest
    restart: always
    ports:
      - 8084:8081
    environment:
      ME_CONFIG_MONGODB_URL: *********************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    depends_on:
      - mongo

  bc-api:
    build:
      context: .
      dockerfile: ./api/Dockerfile
    env_file: ./api/.env.docker
    restart: always
    ports:
      - 4002:4002
    depends_on:
      - mongo
    environment:
      - TZ=Asia/Jerusalem
      - BC_DEFAULT_LANGUAGE=ar
      - BC_BASE_CURRENCY=ILS
      - BC_TIMEZONE=Asia/Jerusalem
    volumes:
      - cdn:/var/www/cdn/bookdresses
      - api_logs:/bookdresses/api/logs

  bc-backend:
    build:
      context: .
      dockerfile: ./backend/Dockerfile
    depends_on:
      - bc-api
    environment:
      - TZ=Asia/Jerusalem
      - BC_DEFAULT_LANGUAGE=ar
      - BC_BASE_CURRENCY=ILS
      - BC_TIMEZONE=Asia/Jerusalem
    ports:
      - 3001:3001

  bc-frontend:
    build:
      context: .
      dockerfile: ./frontend/Dockerfile
    depends_on:
      - bc-api
    environment:
      - TZ=Asia/Jerusalem
      - BC_DEFAULT_LANGUAGE=ar
      - BC_BASE_CURRENCY=ILS
      - BC_TIMEZONE=Asia/Jerusalem
    ports:
      - 8080:80
      - 8443:443
    volumes:
      - cdn:/var/www/cdn/bookdresses

volumes:
  cdn:
  mongodb_data:
  mongodb_config:
  api_logs:
