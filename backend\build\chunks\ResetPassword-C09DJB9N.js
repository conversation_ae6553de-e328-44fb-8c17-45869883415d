import{c as s,u as r,j as e,a as o,e as t,w as a,d as n,g as i,x as m,z as c,C as d}from"../entries/index-xsXxT3-W.js";import{d as l,r as p}from"./router-BtYqujaw.js";import{u as j,z as w,s as h}from"./zod-4O8Zwsja.js";import{L as f}from"./Layout-DaeN7D4t.js";import{s as u}from"./change-password-CrWxmtFu.js";import{s as S}from"./reset-password-xiMRPUfP.js";import{E as x}from"./Error-FiYP5RHa.js";import b from"./NoMatch-DMPclUW6.js";import{I as g,F as _}from"./InputLabel-C8rcdOGQ.js";import{I as N}from"./Input-D1AdR9CM.js";import{B as P}from"./Button-BeKLLPpp.js";import{F as y}from"./FormHelperText-DDZ4BMA4.js";import{P as C}from"./Paper-C-atefOs.js";import"./vendor-dblfw9z9.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";const E=w.object({password:w.string().min(t.PASSWORD_MIN_LENGTH,{message:o.PASSWORD_ERROR}),confirmPassword:w.string()}).refine((s=>s.password===s.confirmPassword),{path:["confirmPassword"],message:o.PASSWORDS_DONT_MATCH}),R=()=>{const t=s.c(69),w=l(),{setUser:R,setUserLoaded:A}=r(),[O,W]=p.useState(""),[D,I]=p.useState(""),[v,L]=p.useState(""),[H,F]=p.useState(!1),[T,q]=p.useState(!1),[M,G]=p.useState(!1);let U;t[0]===Symbol.for("react.memo_cache_sentinel")?(U={resolver:h(E),mode:"onSubmit"},t[0]=U):U=t[0];const{register:k,handleSubmit:z,formState:B,setError:J,clearErrors:V}=j(U),{errors:K,isSubmitting:Q}=B;let X;t[1]!==D||t[2]!==w||t[3]!==R||t[4]!==A||t[5]!==v||t[6]!==O?(X=async s=>{const{password:r}=s;try{const s={userId:O,token:v,password:r};if(200===await a(s)){const s=await n({email:D,password:r});if(200===s.status){const r=await i(s.data._id);G(!0),R(r),A(!0),200===await m(O)?w("/"):c()}else c()}else c()}catch(e){c(e)}},t[1]=D,t[2]=w,t[3]=R,t[4]=A,t[5]=v,t[6]=O,t[7]=X):X=t[7];const Y=X;let Z;t[8]!==J?(Z=async s=>{if(s)q(!0);else{const s=new URLSearchParams(window.location.search);if(s.has("u")&&s.has("e")&&s.has("t")){const e=s.get("u"),o=s.get("e"),t=s.get("t");if(e&&o&&t)try{200===await d(e,o,t)?(W(e),I(o),L(t),F(!0)):q(!0)}catch(r){const s=r;console.error(s),J("root",{})}else q(!0)}else q(!0)}},t[8]=J,t[9]=Z):Z=t[9];const $=Z,ss=H?"":"hidden";let rs,es;t[10]===Symbol.for("react.memo_cache_sentinel")?(rs=e.jsx("h1",{children:S.RESET_PASSWORD_HEADING}),t[10]=rs):rs=t[10],t[11]!==z||t[12]!==Y?(es=z(Y),t[11]=z,t[12]=Y,t[13]=es):es=t[13];const os=!!K.password;let ts,as,ns,is;t[14]===Symbol.for("react.memo_cache_sentinel")?(ts=e.jsx(g,{className:"required",children:u.NEW_PASSWORD}),t[14]=ts):ts=t[14],t[15]!==k?(as=k("password"),t[15]=k,t[16]=as):as=t[16],t[17]!==V?(ns=()=>V(),t[17]=V,t[18]=ns):ns=t[18],t[19]!==ns||t[20]!==as?(is=e.jsx(N,{...as,type:"password",required:!0,autoComplete:"new-password",onChange:ns}),t[19]=ns,t[20]=as,t[21]=is):is=t[21];const ms=!!K.password,cs=K.password?.message||"";let ds,ls;t[22]!==ms||t[23]!==cs?(ds=e.jsx(y,{error:ms,children:cs}),t[22]=ms,t[23]=cs,t[24]=ds):ds=t[24],t[25]!==is||t[26]!==ds||t[27]!==os?(ls=e.jsxs(_,{fullWidth:!0,margin:"dense",error:os,children:[ts,is,ds]}),t[25]=is,t[26]=ds,t[27]=os,t[28]=ls):ls=t[28];const ps=!!K.confirmPassword;let js,ws,hs,fs;t[29]===Symbol.for("react.memo_cache_sentinel")?(js=e.jsx(g,{className:"required",children:o.CONFIRM_PASSWORD}),t[29]=js):js=t[29],t[30]!==k?(ws=k("confirmPassword"),t[30]=k,t[31]=ws):ws=t[31],t[32]!==V?(hs=()=>V(),t[32]=V,t[33]=hs):hs=t[33],t[34]!==ws||t[35]!==hs?(fs=e.jsx(N,{type:"password",...ws,required:!0,autoComplete:"new-password",onChange:hs}),t[34]=ws,t[35]=hs,t[36]=fs):fs=t[36];const us=!!K.confirmPassword,Ss=K.confirmPassword?.message||"";let xs,bs,gs,_s,Ns,Ps,ys,Cs,Es,Rs;return t[37]!==us||t[38]!==Ss?(xs=e.jsx(y,{error:us,children:Ss}),t[37]=us,t[38]=Ss,t[39]=xs):xs=t[39],t[40]!==ps||t[41]!==fs||t[42]!==xs?(bs=e.jsxs(_,{fullWidth:!0,margin:"dense",error:ps,children:[js,fs,xs]}),t[40]=ps,t[41]=fs,t[42]=xs,t[43]=bs):bs=t[43],t[44]!==Q?(gs=e.jsx(P,{type:"submit",className:"btn-primary btn-margin btn-margin-bottom",variant:"contained",disabled:Q,children:o.SAVE}),t[44]=Q,t[45]=gs):gs=t[45],t[46]!==w?(_s=e.jsx(P,{variant:"outlined",color:"primary",className:"btn-margin-bottom",onClick:()=>w("/"),children:o.CANCEL}),t[46]=w,t[47]=_s):_s=t[47],t[48]!==gs||t[49]!==_s?(Ns=e.jsxs("div",{className:"buttons",children:[gs,_s]}),t[48]=gs,t[49]=_s,t[50]=Ns):Ns=t[50],t[51]!==ls||t[52]!==bs||t[53]!==Ns||t[54]!==es?(Ps=e.jsx("div",{className:"reset-password",children:e.jsxs(C,{className:"reset-password-form",elevation:10,children:[rs,e.jsxs("form",{onSubmit:es,children:[ls,bs,Ns]})]})}),t[51]=ls,t[52]=bs,t[53]=Ns,t[54]=es,t[55]=Ps):Ps=t[55],t[56]!==Ps||t[57]!==ss?(ys=e.jsx("div",{className:ss,children:Ps}),t[56]=Ps,t[57]=ss,t[58]=ys):ys=t[58],t[59]!==K.root?(Cs=K.root&&e.jsx(x,{}),t[59]=K.root,t[60]=Cs):Cs=t[60],t[61]!==M||t[62]!==T?(Es=!M&&T&&e.jsx(b,{hideHeader:!0}),t[61]=M,t[62]=T,t[63]=Es):Es=t[63],t[64]!==$||t[65]!==ys||t[66]!==Cs||t[67]!==Es?(Rs=e.jsxs(f,{onLoad:$,strict:!1,children:[ys,Cs,Es]}),t[64]=$,t[65]=ys,t[66]=Cs,t[67]=Es,t[68]=Rs):Rs=t[68],Rs};export{R as default};
