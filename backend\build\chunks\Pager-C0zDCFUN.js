import{c as s,j as a,a as e}from"../entries/index-CEzJO5Xy.js";import{I as o}from"./IconButton-CnBvmeAK.js";import{P as r,N as c}from"./ArrowForwardIos-BMce9t8T.js";const i=i=>{const n=s.c(7),{page:t,pageSize:l,totalRecords:d,rowCount:m,onNext:j,onPrevious:x}=i;let p;return n[0]!==j||n[1]!==x||n[2]!==t||n[3]!==l||n[4]!==m||n[5]!==d?(p=(t>1||m<d)&&a.jsx("div",{className:"pager-container",children:a.jsxs("div",{className:"pager",children:[a.jsx("div",{className:"row-count",children:`${(t-1)*l+1}-${m} ${e.OF} ${d}`}),a.jsxs("div",{className:"actions",children:[a.jsx(o,{onClick:x,disabled:1===t,children:a.jsx(r,{className:"icon"})}),a.jsx(o,{onClick:j,disabled:m>=d,children:a.jsx(c,{className:"icon"})})]})]})})||a.jsx(a.Fragment,{}),n[0]=j,n[1]=x,n[2]=t,n[3]=l,n[4]=m,n[5]=d,n[6]=p):p=n[6],p};export{i as P};
