import mongoose from 'mongoose';
import escapeStringRegexp from 'escape-string-regexp';
import { Expo } from 'expo-server-sdk';
import path from 'node:path';
import * as bookcarsTypes from "../../../../packages/bookcars-types/index.js";
import i18n from "../lang/i18n.js";
import Booking from "../models/Booking.js";
import User from "../models/User.js";
import Token from "../models/Token.js";
import Dress from "../models/Dress.js";
import Location from "../models/Location.js";
import Notification from "../models/Notification.js";
import NotificationCounter from "../models/NotificationCounter.js";
import PushToken from "../models/PushToken.js";
import * as helper from "../common/helper.js";
import * as mailHelper from "../common/mailHelper.js";
import * as env from "../config/env.config.js";
import * as logger from "../common/logger.js";
import stripeAPI from "../stripe.js";
/**
 * Create a Booking.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const create = async (req, res) => {
  try {
    const {
      body
    } = req;
    const booking = new Booking(body.booking);
    await booking.save();
    res.json(booking);
  } catch (err) {
    logger.error(`[booking.create] ${i18n.t('DB_ERROR')} ${JSON.stringify(req.body)}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};
/**
 * Notify a supplier or admin.
 *
 * @async
 * @param {env.User} driver
 * @param {string} bookingId
 * @param {env.User} user
 * @param {boolean} notificationMessage
 * @returns {void}
 */
export const notify = async (driver, bookingId, user, notificationMessage) => {
  i18n.locale = user.language;
  // notification
  const message = `${driver.fullName} ${notificationMessage} ${bookingId}.`;
  const notification = new Notification({
    user: user._id,
    message,
    booking: bookingId
  });
  await notification.save();
  let counter = await NotificationCounter.findOne({
    user: user._id
  });
  if (counter && typeof counter.count !== 'undefined') {
    counter.count += 1;
    await counter.save();
  } else {
    counter = new NotificationCounter({
      user: user._id,
      count: 1
    });
    await counter.save();
  }
  // mail
  if (user.enableEmailNotifications) {
    const mailOptions = {
      from: env.SMTP_FROM,
      to: user.email,
      subject: message,
      html: `<p>
    ${i18n.t('HELLO')}${user.fullName},<br><br>
    ${message}<br><br>
    ${helper.joinURL(env.BACKEND_HOST, `update-booking?b=${bookingId}`)}<br><br>
    ${i18n.t('REGARDS')}<br>
    </p>`
    };
    await mailHelper.sendMail(mailOptions);
  }
};
/**
 * Send checkout confirmation email to driver.
 *
 * @async
 * @param {env.User} user
 * @param {env.Booking} booking
 * @param {boolean} payLater
 * @returns {unknown}
 */
export const confirm = async (user, supplier, booking, payLater) => {
  const {
    language
  } = user;
  const locale = language === 'fr' ? 'fr-FR' : 'en-US';
  const options = {
    weekday: 'long',
    month: 'long',
    year: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    timeZone: env.TIMEZONE
  };
  const from = booking.from.toLocaleString(locale, options);
  const to = booking.to.toLocaleString(locale, options);
  const dress = await Dress.findById(booking.dress).populate('supplier');
  if (!dress) {
    logger.info(`Dress ${booking.dress} not found`);
    return false;
  }
  const pickupLocation = await Location.findById(booking.pickupLocation).populate('values');
  if (!pickupLocation) {
    logger.info(`Pick-up location ${booking.pickupLocation} not found`);
    return false;
  }
  const pickupLocationName = pickupLocation.values.filter(value => value.language === language)[0].value;
  const dropOffLocation = await Location.findById(booking.dropOffLocation).populate('values');
  if (!dropOffLocation) {
    logger.info(`Drop-off location ${booking.pickupLocation} not found`);
    return false;
  }
  const dropOffLocationName = dropOffLocation.values.filter(value => value.language === language)[0].value;
  let contractFile = null;
  if (supplier.contracts && supplier.contracts.length > 0) {
    contractFile = supplier.contracts.find(c => c.language === user.language)?.file || null;
    if (!contractFile) {
      contractFile = supplier.contracts.find(c => c.language === 'en')?.file || null;
    }
  }
  const mailOptions = {
    from: env.SMTP_FROM,
    to: user.email,
    subject: `${i18n.t('BOOKING_CONFIRMED_SUBJECT_PART1')} ${booking._id} ${i18n.t('BOOKING_CONFIRMED_SUBJECT_PART2')}`,
    html: `<p>
        ${i18n.t('HELLO')}${user.fullName},<br><br>
        ${!payLater ? `${i18n.t('BOOKING_CONFIRMED_PART1')} ${booking._id} ${i18n.t('BOOKING_CONFIRMED_PART2')}` + '<br><br>' : ''}
        ${i18n.t('BOOKING_CONFIRMED_PART3')}${dress.supplier.fullName}${i18n.t('BOOKING_CONFIRMED_PART4')}${pickupLocationName}${i18n.t('BOOKING_CONFIRMED_PART5')}` + `${from} ${i18n.t('BOOKING_CONFIRMED_PART6')}` + `${dress.name}${i18n.t('BOOKING_CONFIRMED_PART7')}` + `<br><br>${i18n.t('BOOKING_CONFIRMED_PART8')}<br><br>` + `${i18n.t('BOOKING_CONFIRMED_PART9')}${dress.supplier.fullName}${i18n.t('BOOKING_CONFIRMED_PART10')}${dropOffLocationName}${i18n.t('BOOKING_CONFIRMED_PART11')}` + `${to} ${i18n.t('BOOKING_CONFIRMED_PART12')}` + `<br><br>${i18n.t('BOOKING_CONFIRMED_PART13')}<br><br>${i18n.t('BOOKING_CONFIRMED_PART14')}${env.FRONTEND_HOST}<br><br>
        ${i18n.t('REGARDS')}<br>
        </p>`
  };
  if (contractFile) {
    const file = path.join(env.CDN_CONTRACTS, contractFile);
    if (await helper.pathExists(file)) {
      mailOptions.attachments = [{
        path: file
      }];
    }
  }
  await mailHelper.sendMail(mailOptions);
  return true;
};
/**
 * Complete checkout process and create Booking.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const checkout = async (req, res) => {
  try {
    let user;
    const {
      body
    } = req;
    const {
      customer
    } = body;
    if (!body.booking) {
      throw new Error('Booking not found');
    }
    const supplier = await User.findById(body.booking.supplier);
    if (!supplier) {
      throw new Error(`Supplier ${body.booking.supplier} not found`);
    }
    if (customer) {
      customer.verified = false;
      customer.blacklisted = false;
      customer.type = bookcarsTypes.UserType.User;
      user = new User(customer);
      await user.save();
      const token = new Token({
        user: user._id,
        token: helper.generateToken()
      });
      await token.save();
      i18n.locale = user.language;
      const mailOptions = {
        from: env.SMTP_FROM,
        to: user.email,
        subject: i18n.t('ACCOUNT_ACTIVATION_SUBJECT'),
        html: `<p>
        ${i18n.t('HELLO')}${user.fullName},<br><br>
        ${i18n.t('ACCOUNT_ACTIVATION_LINK')}<br><br>
        ${helper.joinURL(env.FRONTEND_HOST, 'activate')}/?u=${encodeURIComponent(user.id)}&e=${encodeURIComponent(user.email)}&t=${encodeURIComponent(token.token)}<br><br>
        ${i18n.t('REGARDS')}<br>
        </p>`
      };
      await mailHelper.sendMail(mailOptions);
      body.booking.customer = user.id;
    } else {
      user = await User.findById(body.booking.customer);
    }
    if (!user) {
      throw new Error(`User ${body.booking.customer} not found`);
    }
    if (!body.payLater) {
      const {
        payPal,
        paymentIntentId,
        sessionId
      } = body;
      if (!payPal && !paymentIntentId && !sessionId) {
        throw new Error('paymentIntentId and sessionId not found');
      }
      if (!payPal) {
        body.booking.customerId = body.customerId;
      }
      if (paymentIntentId) {
        const paymentIntent = await stripeAPI.paymentIntents.retrieve(paymentIntentId);
        if (paymentIntent.status !== 'succeeded') {
          const message = `Payment failed: ${paymentIntent.status}`;
          logger.error(message, body);
          res.status(400).send(message);
        }
        body.booking.paymentIntentId = paymentIntentId;
        body.booking.status = body.booking.isDeposit ? bookcarsTypes.BookingStatus.Deposit : bookcarsTypes.BookingStatus.Paid;
      } else {
        //
        // Bookings created from checkout with Stripe are temporary
        // and are automatically deleted if the payment checkout session expires.
        //
        let expireAt = new Date();
        expireAt.setSeconds(expireAt.getSeconds() + env.BOOKING_EXPIRE_AT);
        body.booking.sessionId = !payPal ? body.sessionId : undefined;
        body.booking.status = bookcarsTypes.BookingStatus.Void;
        body.booking.expireAt = expireAt;
        //
        // Non verified and active users created from checkout with Stripe are temporary
        // and are automatically deleted if the payment checkout session expires.
        //
        if (!user.verified) {
          expireAt = new Date();
          expireAt.setSeconds(expireAt.getSeconds() + env.USER_EXPIRE_AT);
          user.expireAt = expireAt;
          await user.save();
        }
      }
    }
    const {
      customerId
    } = body;
    if (customerId) {
      user.customerId = customerId;
      await user?.save();
    }
    const {
      language
    } = user;
    i18n.locale = language;
    const booking = new Booking(body.booking);
    await booking.save();
    if (booking.status === bookcarsTypes.BookingStatus.Paid && body.paymentIntentId && body.customerId) {
      const dress = await Dress.findById(booking.dress);
      if (!dress) {
        throw new Error(`Dress ${booking.dress} not found`);
      }
      dress.rentals += 1;
      await dress.save();
    }
    if (body.payLater || booking.status === bookcarsTypes.BookingStatus.Paid && body.paymentIntentId && body.customerId) {
      // Mark dress as fully booked
      // if (env.MARK_DRESS_AS_FULLY_BOOKED_ON_CHECKOUT) {
      //   await Dress.updateOne({ _id: booking.dress }, { fullyBooked: false })
      // }
      // Send confirmation email to customer
      if (!(await confirm(user, supplier, booking, body.payLater))) {
        res.sendStatus(400);
        return;
      }
      // Notify supplier
      i18n.locale = supplier.language;
      let message = body.payLater ? i18n.t('BOOKING_PAY_LATER_NOTIFICATION') : i18n.t('BOOKING_PAID_NOTIFICATION');
      await notify(user, booking.id, supplier, message);
      // Notify admin
      const admin = !!env.ADMIN_EMAIL && (await User.findOne({
        email: env.ADMIN_EMAIL,
        type: bookcarsTypes.UserType.Admin
      }));
      if (admin) {
        i18n.locale = admin.language;
        message = body.payLater ? i18n.t('BOOKING_PAY_LATER_NOTIFICATION') : i18n.t('BOOKING_PAID_NOTIFICATION');
        await notify(user, booking.id, admin, message);
      }
    }
    res.status(200).send({
      bookingId: booking.id
    });
  } catch (err) {
    logger.error(`[booking.checkout] ${i18n.t('ERROR')}`, err);
    res.status(400).send(i18n.t('ERROR') + err);
  }
};
/**
 * Notify customer and send push notification.
 *
 * @async
 * @param {env.Booking} booking
 * @returns {void}
 */
const notifyCustomer = async booking => {
  const customer = await User.findById(booking.customer);
  if (!customer) {
    logger.info(`Customer ${booking.customer} not found`);
    return;
  }
  i18n.locale = customer.language;
  const message = `${i18n.t('BOOKING_UPDATED_NOTIFICATION_PART1')} ${booking._id} ${i18n.t('BOOKING_UPDATED_NOTIFICATION_PART2')}`;
  const notification = new Notification({
    user: customer._id,
    message,
    booking: booking._id
  });
  await notification.save();
  let counter = await NotificationCounter.findOne({
    user: customer._id
  });
  if (counter && typeof counter.count !== 'undefined') {
    counter.count += 1;
    await counter.save();
  } else {
    counter = new NotificationCounter({
      user: customer._id,
      count: 1
    });
    await counter.save();
  }
  // mail
  if (customer.enableEmailNotifications) {
    const mailOptions = {
      from: env.SMTP_FROM,
      to: customer.email,
      subject: message,
      html: `<p>
    ${i18n.t('HELLO')}${customer.fullName},<br><br>
    ${message}<br><br>
    ${helper.joinURL(env.FRONTEND_HOST, `booking?b=${booking._id}`)}<br><br>
    ${i18n.t('REGARDS')}<br>
    </p>`
    };
    await mailHelper.sendMail(mailOptions);
  }
  // push notification
  const pushToken = await PushToken.findOne({
    user: customer._id
  });
  if (pushToken) {
    const {
      token
    } = pushToken;
    const expo = new Expo({
      accessToken: env.EXPO_ACCESS_TOKEN,
      useFcmV1: true
    });
    if (!Expo.isExpoPushToken(token)) {
      logger.info(`Push token ${token} is not a valid Expo push token.`);
      return;
    }
    const messages = [{
      to: token,
      sound: 'default',
      body: message,
      data: {
        user: customer._id,
        notification: notification._id,
        booking: booking._id
      }
    }];
    // The Expo push notification service accepts batches of notifications so
    // that you don't need to send 1000 requests to send 1000 notifications. We
    // recommend you batch your notifications to reduce the number of requests
    // and to compress them (notifications with similar content will get
    // compressed).
    const chunks = expo.chunkPushNotifications(messages);
    const tickets = [];
    (async () => {
      // Send the chunks to the Expo push notification service. There are
      // different strategies you could use. A simple one is to send one chunk at a
      // time, which nicely spreads the load out over time:
      for (const chunk of chunks) {
        try {
          const ticketChunks = await expo.sendPushNotificationsAsync(chunk);
          tickets.push(...ticketChunks);
          // NOTE: If a ticket contains an error code in ticket.details.error, you
          // must handle it appropriately. The error codes are listed in the Expo
          // documentation:
          // https://docs.expo.io/push-notifications/sending-notifications/#individual-errors
          for (const ticketChunk of ticketChunks) {
            if (ticketChunk.status === 'ok') {
              logger.info(`Push notification sent: ${ticketChunk.id}`);
            } else {
              throw new Error(ticketChunk.message);
            }
          }
        } catch (error) {
          logger.error('Error while sending push notification', error);
        }
      }
    })();
  }
};
/**
 * Update Booking.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const update = async (req, res) => {
  try {
    const {
      body
    } = req;
    const booking = await Booking.findById(body.booking._id);
    if (booking) {
      const {
        supplier,
        dress,
        customer,
        pickupLocation,
        dropOffLocation,
        from,
        to,
        status,
        cancellation,
        amendments,
        price,
        isDeposit
      } = body.booking;
      const previousStatus = booking.status;
      booking.supplier = new mongoose.Types.ObjectId(supplier);
      booking.dress = new mongoose.Types.ObjectId(dress);
      booking.customer = new mongoose.Types.ObjectId(customer);
      booking.pickupLocation = new mongoose.Types.ObjectId(pickupLocation);
      booking.dropOffLocation = new mongoose.Types.ObjectId(dropOffLocation);
      booking.from = from;
      booking.to = to;
      booking.status = status;
      booking.cancellation = cancellation;
      booking.amendments = amendments;
      booking.price = price;
      booking.isDeposit = isDeposit || false;
      await booking.save();
      if (previousStatus !== status) {
        // notify customer
        await notifyCustomer(booking);
      }
      res.json(booking);
      return;
    }
    logger.error('[booking.update] Booking not found:', body.booking._id);
    res.sendStatus(204);
  } catch (err) {
    logger.error(`[booking.update] ${i18n.t('DB_ERROR')} ${JSON.stringify(req.body)}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};
/**
 * Update Booking Status.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const updateStatus = async (req, res) => {
  try {
    const {
      body
    } = req;
    const {
      ids: _ids,
      status
    } = body;
    const ids = _ids.map(id => new mongoose.Types.ObjectId(id));
    const bulk = Booking.collection.initializeOrderedBulkOp();
    const bookings = await Booking.find({
      _id: {
        $in: ids
      }
    });
    bulk.find({
      _id: {
        $in: ids
      }
    }).update({
      $set: {
        status
      }
    });
    await bulk.execute();
    for (const booking of bookings) {
      if (booking.status !== status) {
        // Increment rentals count when booking status changes to Paid
        if (status === bookcarsTypes.BookingStatus.Paid && booking.dress) {
          const dress = await Dress.findById(booking.dress);
          if (dress) {
            dress.rentals = (dress.rentals || 0) + 1;
            await dress.save();
          }
        }
        await notifyCustomer(booking);
      }
    }
    res.sendStatus(200);
  } catch (err) {
    logger.error(`[booking.updateStatus] ${i18n.t('DB_ERROR')} ${JSON.stringify(req.body)}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};
/**
 * Delete Bookings.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const deleteBookings = async (req, res) => {
  try {
    const {
      body
    } = req;
    const ids = body.map(id => new mongoose.Types.ObjectId(id));
    await Booking.deleteMany({
      _id: {
        $in: ids
      }
    });
    res.sendStatus(200);
  } catch (err) {
    logger.error(`[booking.deleteBookings] ${i18n.t('DB_ERROR')} ${JSON.stringify(req.body)}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};
/**
 * Delete temporary Booking created from checkout session.
 *
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const deleteTempBooking = async (req, res) => {
  const {
    bookingId,
    sessionId
  } = req.params;
  try {
    const booking = await Booking.findOne({
      _id: bookingId,
      sessionId,
      status: bookcarsTypes.BookingStatus.Void,
      expireAt: {
        $ne: null
      }
    });
    if (booking) {
      const user = await User.findOne({
        _id: booking.customer,
        verified: false,
        expireAt: {
          $ne: null
        }
      });
      await user?.deleteOne();
    }
    await booking?.deleteOne();
    res.sendStatus(200);
  } catch (err) {
    logger.error(`[booking.deleteTempBooking] ${i18n.t('DB_ERROR')} ${JSON.stringify({
      bookingId,
      sessionId
    })}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};
/**
 * Get Booking by ID.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const getBooking = async (req, res) => {
  const {
    id
  } = req.params;
  try {
    const booking = await Booking.findById(id).populate('supplier').populate({
      path: 'dress',
      populate: {
        path: 'supplier',
        model: 'User'
      }
    }).populate('customer').populate({
      path: 'pickupLocation',
      populate: {
        path: 'values',
        model: 'LocationValue'
      }
    }).populate({
      path: 'dropOffLocation',
      populate: {
        path: 'values',
        model: 'LocationValue'
      }
    }).lean();
    if (booking) {
      const {
        language
      } = req.params;
      booking.supplier = {
        _id: booking.supplier._id,
        fullName: booking.supplier.fullName,
        avatar: booking.supplier.avatar,
        payLater: booking.supplier.payLater,
        priceChangeRate: booking.supplier.priceChangeRate
      };
      booking.dress.supplier = {
        _id: booking.dress.supplier._id,
        fullName: booking.dress.supplier.fullName,
        avatar: booking.dress.supplier.avatar,
        payLater: booking.dress.supplier.payLater,
        priceChangeRate: booking.dress.supplier.priceChangeRate
      };
      booking.pickupLocation.name = booking.pickupLocation.values.filter(value => value.language === language)[0].value;
      booking.dropOffLocation.name = booking.dropOffLocation.values.filter(value => value.language === language)[0].value;
      res.json(booking);
      return;
    }
    logger.error('[booking.getBooking] Booking not found:', id);
    res.sendStatus(204);
  } catch (err) {
    logger.error(`[booking.getBooking] ${i18n.t('DB_ERROR')} ${id}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};
/**
 * Get Booking by sessionId.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const getBookingId = async (req, res) => {
  const {
    sessionId
  } = req.params;
  try {
    const booking = await Booking.findOne({
      sessionId
    });
    if (!booking) {
      logger.error('[booking.getBookingId] Booking not found (sessionId):', sessionId);
      res.sendStatus(204);
      return;
    }
    res.json(booking?.id);
  } catch (err) {
    logger.error(`[booking.getBookingId] (sessionId) ${i18n.t('DB_ERROR')} ${sessionId}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};
/**
 * Get Bookings.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const getBookings = async (req, res) => {
  try {
    const {
      body
    } = req;
    const page = Number.parseInt(req.params.page, 10);
    const size = Number.parseInt(req.params.size, 10);
    const suppliers = body.suppliers.map(id => new mongoose.Types.ObjectId(id));
    const {
      statuses,
      user,
      dress
    } = body;
    const from = body.filter && body.filter.from && new Date(body.filter.from) || null;
    const dateBetween = body.filter && body.filter.dateBetween && new Date(body.filter.dateBetween) || null;
    const to = body.filter && body.filter.to && new Date(body.filter.to) || null;
    const pickupLocation = body.filter && body.filter.pickupLocation || null;
    const dropOffLocation = body.filter && body.filter.dropOffLocation || null;
    let keyword = body.filter && body.filter.keyword || '';
    const options = 'i';
    const $match = {
      $and: [{
        'supplier._id': {
          $in: suppliers
        }
      }, {
        status: {
          $in: statuses
        }
      }, {
        expireAt: null
      }]
    };
    if (user) {
      $match.$and.push({
        'customer._id': {
          $eq: new mongoose.Types.ObjectId(user)
        }
      });
    }
    if (dress) {
      $match.$and.push({
        'dress._id': {
          $eq: new mongoose.Types.ObjectId(dress)
        }
      });
    }
    if (dateBetween) {
      const dateBetweenStart = new Date(dateBetween);
      dateBetweenStart.setHours(0, 0, 0, 0);
      const dateBetweenEnd = new Date(dateBetween);
      dateBetweenEnd.setHours(23, 59, 59, 999);
      $match.$and.push({
        $and: [{
          from: {
            $lte: dateBetweenEnd
          }
        }, {
          to: {
            $gte: dateBetweenStart
          }
        }]
      });
    } else if (from) {
      $match.$and.push({
        from: {
          $gte: from
        }
      }); // $from >= from
    }
    if (to) {
      $match.$and.push({
        to: {
          $lte: to
        }
      }); // $to < to
    }
    if (pickupLocation) {
      $match.$and.push({
        'pickupLocation._id': {
          $eq: new mongoose.Types.ObjectId(pickupLocation)
        }
      });
    }
    if (dropOffLocation) {
      $match.$and.push({
        'dropOffLocation._id': {
          $eq: new mongoose.Types.ObjectId(dropOffLocation)
        }
      });
    }
    if (keyword) {
      const isObjectId = helper.isValidObjectId(keyword);
      if (isObjectId) {
        $match.$and.push({
          _id: {
            $eq: new mongoose.Types.ObjectId(keyword)
          }
        });
      } else {
        keyword = escapeStringRegexp(keyword);
        $match.$and.push({
          $or: [{
            'supplier.fullName': {
              $regex: keyword,
              $options: options
            }
          }, {
            'customer.fullName': {
              $regex: keyword,
              $options: options
            }
          }, {
            'dress.name': {
              $regex: keyword,
              $options: options
            }
          }]
        });
      }
    }
    const {
      language
    } = req.params;
    const data = await Booking.aggregate([{
      $lookup: {
        from: 'User',
        let: {
          supplierId: '$supplier'
        },
        pipeline: [{
          $match: {
            $expr: {
              $eq: ['$_id', '$$supplierId']
            }
          }
        }],
        as: 'supplier'
      }
    }, {
      $unwind: {
        path: '$supplier',
        preserveNullAndEmptyArrays: false
      }
    }, {
      $lookup: {
        from: 'Dress',
        let: {
          dressId: '$dress'
        },
        pipeline: [{
          $match: {
            $expr: {
              $eq: ['$_id', '$$dressId']
            }
          }
        }],
        as: 'dress'
      }
    }, {
      $unwind: {
        path: '$dress',
        preserveNullAndEmptyArrays: false
      }
    }, {
      $lookup: {
        from: 'User',
        let: {
          customerId: '$customer'
        },
        pipeline: [{
          $match: {
            $expr: {
              $eq: ['$_id', '$$customerId']
            }
          }
        }],
        as: 'customer'
      }
    }, {
      $unwind: {
        path: '$customer',
        preserveNullAndEmptyArrays: false
      }
    }, {
      $lookup: {
        from: 'Location',
        let: {
          pickupLocationId: '$pickupLocation'
        },
        pipeline: [{
          $match: {
            $expr: {
              $eq: ['$_id', '$$pickupLocationId']
            }
          }
        }, {
          $lookup: {
            from: 'LocationValue',
            let: {
              values: '$values'
            },
            pipeline: [{
              $match: {
                $and: [{
                  $expr: {
                    $in: ['$_id', '$$values']
                  }
                }, {
                  $expr: {
                    $eq: ['$language', language]
                  }
                }]
              }
            }],
            as: 'value'
          }
        }, {
          $addFields: {
            name: '$value.value'
          }
        }],
        as: 'pickupLocation'
      }
    }, {
      $unwind: {
        path: '$pickupLocation',
        preserveNullAndEmptyArrays: false
      }
    }, {
      $lookup: {
        from: 'Location',
        let: {
          dropOffLocationId: '$dropOffLocation'
        },
        pipeline: [{
          $match: {
            $expr: {
              $eq: ['$_id', '$$dropOffLocationId']
            }
          }
        }, {
          $lookup: {
            from: 'LocationValue',
            let: {
              values: '$values'
            },
            pipeline: [{
              $match: {
                $and: [{
                  $expr: {
                    $in: ['$_id', '$$values']
                  }
                }, {
                  $expr: {
                    $eq: ['$language', language]
                  }
                }]
              }
            }],
            as: 'value'
          }
        }, {
          $addFields: {
            name: '$value.value'
          }
        }],
        as: 'dropOffLocation'
      }
    }, {
      $unwind: {
        path: '$dropOffLocation',
        preserveNullAndEmptyArrays: false
      }
    }, {
      $match
    }, {
      $facet: {
        resultData: [{
          $sort: {
            createdAt: -1,
            _id: 1
          }
        }, {
          $skip: (page - 1) * size
        }, {
          $limit: size
        }],
        pageInfo: [{
          $count: 'totalRecords'
        }]
      }
    }]);
    const bookings = data[0].resultData;
    for (const booking of bookings) {
      const {
        _id,
        fullName,
        avatar,
        priceChangeRate
      } = booking.supplier;
      booking.supplier = {
        _id,
        fullName,
        avatar,
        priceChangeRate
      };
    }
    res.json(data);
  } catch (err) {
    logger.error(`[booking.getBookings] ${i18n.t('DB_ERROR')} ${JSON.stringify(req.body)}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};
/**
 * Check if a customer has Bookings.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const hasBookings = async (req, res) => {
  const {
    customer
  } = req.params;
  try {
    const count = await Booking.find({
      customer: new mongoose.Types.ObjectId(customer)
    }).limit(1).countDocuments();
    if (count === 1) {
      res.sendStatus(200);
      return;
    }
    res.sendStatus(204);
  } catch (err) {
    logger.error(`[booking.hasBookings] ${i18n.t('DB_ERROR')} ${customer}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};
/**
 * Cancel a Booking.
 *
 * @export
 * @async
 * @param {Request} req
 * @param {Response} res
 * @returns {unknown}
 */
export const cancelBooking = async (req, res) => {
  const {
    id
  } = req.params;
  try {
    const booking = await Booking.findOne({
      _id: new mongoose.Types.ObjectId(id)
    }).populate('supplier').populate('customer');
    if (booking && booking.cancellation && !booking.cancelRequest) {
      booking.cancelRequest = true;
      await booking.save();
      // Notify supplier
      const supplier = await User.findById(booking.supplier);
      if (!supplier) {
        logger.info(`Supplier ${booking.supplier} not found`);
        res.sendStatus(204);
        return;
      }
      i18n.locale = supplier.language;
      await notify(booking.customer, booking.id, supplier, i18n.t('CANCEL_BOOKING_NOTIFICATION'));
      // Notify admin
      const admin = !!env.ADMIN_EMAIL && (await User.findOne({
        email: env.ADMIN_EMAIL,
        type: bookcarsTypes.UserType.Admin
      }));
      if (admin) {
        i18n.locale = admin.language;
        await notify(booking.customer, booking.id, admin, i18n.t('CANCEL_BOOKING_NOTIFICATION'));
      }
      res.sendStatus(200);
      return;
    }
    res.sendStatus(204);
  } catch (err) {
    logger.error(`[booking.cancelBooking] ${i18n.t('DB_ERROR')} ${id}`, err);
    res.status(400).send(i18n.t('DB_ERROR') + err);
  }
};