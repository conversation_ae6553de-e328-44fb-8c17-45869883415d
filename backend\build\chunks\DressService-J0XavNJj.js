import{aC as e,p as t}from"../entries/index-CEzJO5Xy.js";const s=t=>e.post("/api/create-dress",t,{withCredentials:!0}).then((e=>e.data)),a=t=>e.put("/api/update-dress",t,{withCredentials:!0}).then((e=>e.status)),i=t=>e.delete(`/api/delete-dress/${t}`,{withCredentials:!0}).then((e=>e.status)),n=s=>e.get(`/api/dress/${s}/${t()}`,{withCredentials:!0}).then((e=>e.data)),d=(t,s,a,i)=>e.post(`/api/dresses/${a}/${i}/?s=${encodeURIComponent(t)}`,s,{withCredentials:!0}).then((e=>e.data)),r=t=>e.get(`/api/check-dress/${t}`,{withCredentials:!0}).then((e=>e.status)),p=t=>{const s=new FormData;return s.append("image",t),e.post("/api/create-dress-image",s,{withCredentials:!0}).then((e=>e.data))},h=(t,s)=>{const a=new FormData;return a.append("image",s),e.put(`/api/update-dress-image/${t}`,a,{withCredentials:!0}).then((e=>e.status))},o=t=>e.delete(`/api/delete-dress-image/${t}`,{withCredentials:!0}).then((e=>e.status)),l=t=>e.post(`/api/delete-temp-dress-image/${encodeURIComponent(t)}`,null,{withCredentials:!0}).then((e=>e.status));export{n as a,l as b,r as c,i as d,s as e,o as f,d as g,p as h,h as i,a as u};
