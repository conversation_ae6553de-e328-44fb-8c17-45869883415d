import{r as e}from"./router-BtYqujaw.js";import{g as o,a as t,s,c as r,o as a,r as n}from"./Button-BeKLLPpp.js";import{u as d}from"./useFormControl-B7jXtRD7.js";import{u as i}from"./useSlot-DiTut-u0.js";import{j as l,l as c}from"../entries/index-xsXxT3-W.js";import{u as p}from"./Grow-Cp8xsNYl.js";function u(e){return o("PrivateSwitchBase",e)}t("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const m=s(a)({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:e,ownerState:o})=>"start"===e&&"small"!==o.size,style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:e,ownerState:o})=>"end"===e&&"small"!==o.size,style:{marginRight:-12}}]}),h=s("input",{shouldForwardProp:n})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),g=e.forwardRef((function(e,o){const{autoFocus:t,checked:s,checkedIcon:a,defaultChecked:n,disabled:g,disableFocusRipple:f=!1,edge:b=!1,icon:P,id:w,inputProps:F,inputRef:k,name:v,onBlur:x,onChange:S,onFocus:y,readOnly:B,required:j=!1,tabIndex:R,type:C,value:z,slots:I={},slotProps:N={},...q}=e,[E,L]=p({controlled:s,default:Boolean(n),name:"SwitchBase",state:"checked"}),O=d();let T=g;O&&void 0===T&&(T=O.disabled);const G="checkbox"===C||"radio"===C,J={...e,checked:E,disabled:T,disableFocusRipple:f,edge:b},$=(e=>{const{classes:o,checked:t,disabled:s,edge:a}=e,n={root:["root",t&&"checked",s&&"disabled",a&&`edge${c(a)}`],input:["input"]};return r(n,u,o)})(J),A={slots:I,slotProps:{input:F,...N}},[D,H]=i("root",{ref:o,elementType:m,className:$.root,shouldForwardComponentProp:!0,externalForwardedProps:{...A,component:"span",...q},getSlotProps:e=>({...e,onFocus:o=>{e.onFocus?.(o),(e=>{y&&y(e),O&&O.onFocus&&O.onFocus(e)})(o)},onBlur:o=>{e.onBlur?.(o),(e=>{x&&x(e),O&&O.onBlur&&O.onBlur(e)})(o)}}),ownerState:J,additionalProps:{centerRipple:!0,focusRipple:!f,disabled:T,role:void 0,tabIndex:null}}),[K,M]=i("input",{ref:k,elementType:h,className:$.input,externalForwardedProps:A,getSlotProps:e=>({...e,onChange:o=>{e.onChange?.(o),(e=>{if(e.nativeEvent.defaultPrevented)return;const o=e.target.checked;L(o),S&&S(e,o)})(o)}}),ownerState:J,additionalProps:{autoFocus:t,checked:s,defaultChecked:n,disabled:T,id:G?w:void 0,name:v,readOnly:B,required:j,tabIndex:R,type:C,..."checkbox"===C&&void 0===z?{}:{value:z}}});return l.jsxs(D,{...H,children:[l.jsx(K,{...M}),E?a:P]})}));export{g as S};
