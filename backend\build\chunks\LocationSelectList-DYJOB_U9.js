import{Q as t,j as e,R as a,e as o,z as r}from"../entries/index-CEzJO5Xy.js";import{r as s}from"./router-BtYqujaw.js";import{g as n}from"./LocationService-BtQFgoWL.js";import{M as l}from"./MultipleSelect-C7xTvWe9.js";const i=({value:i,multiple:c,label:u,required:p,variant:g,onChange:f})=>{const[S,m]=s.useState(!1),[d,h]=s.useState(!1),[I,j]=s.useState([]),[v,y]=s.useState(!0),[D,E]=s.useState(1),[b,x]=s.useState(""),[A,C]=s.useState([]);s.useEffect((()=>{const e=c?i:[i];i&&!t(A,e)&&C(e)}),[i,c,A]);const F=async(t,e,a)=>{try{if(v||1===t){h(!0);const r=await n(e,t,o.PAGE_SIZE),s=r&&r.length>0?r[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!s)return;const l=Array.isArray(s.pageInfo)&&s.pageInfo.length>0?s.pageInfo[0].totalRecords:0,i=1===t?s.resultData:[...I,...s.resultData];j(i),y(s.resultData.length>0),a&&a({rows:s.resultData,rowCount:l})}}catch(s){r(s)}finally{h(!1)}};return e.jsx(l,{loading:d,label:u||"",callbackFromMultipleSelect:t=>{f&&f(t)},options:I,selectedOptions:A,required:p||!1,multiple:c,type:a.Location,variant:g||"standard",ListboxProps:{onScroll:t=>{const e=t.currentTarget;if(v&&!d&&e.scrollTop+e.clientHeight>=e.scrollHeight-o.PAGE_OFFSET){const t=D+1;E(t),F(t,b)}}},onFocus:()=>{if(!S){const t=1;j([]),E(t),F(t,b,(()=>{m(!0)}))}},onInputChange:t=>{const e=t&&t.target&&"value"in t.target&&t.target.value||"";e!==b&&(j([]),E(1),x(e),F(1,e))},onClear:()=>{j([]),E(1),x(""),y(!0),F(1,"")}})};export{i as L};
