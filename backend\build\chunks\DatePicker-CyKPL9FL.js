import{b as e,a,d as o,g as t}from"./format-4arn0GRM.js";import{j as n,ar as r,k as s}from"../entries/index-CEzJO5Xy.js";import{g as i,a as d,_ as l,s as m,c as u}from"./Button-DGZYUY3P.js";import{r as c}from"./router-BtYqujaw.js";import{u as f,d as b,f as h,g as p,h as y,r as g,P as w,i as v,j as P,k as j,l as M,m as O,n as T,C as k,o as D,p as x,q as W,v as R,s as C,t as S,w as N,D as F,x as H}from"./useMobilePicker-Cpitw7qm.js";import{P as A}from"./getThemeProps-gt86ccpv.js";import{T as z}from"./Backdrop-Bzn12VyM.js";import{r as X}from"./useSlot-CtA82Ni6.js";const Y={lessThanXSeconds:{one:"menos de un segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"medio minuto",lessThanXMinutes:{one:"menos de un minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"alrededor de 1 hora",other:"alrededor de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 día",other:"{{count}} días"},aboutXWeeks:{one:"alrededor de 1 semana",other:"alrededor de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"alrededor de 1 mes",other:"alrededor de {{count}} meses"},xMonths:{one:"1 mes",other:"{{count}} meses"},aboutXYears:{one:"alrededor de 1 año",other:"alrededor de {{count}} años"},xYears:{one:"1 año",other:"{{count}} años"},overXYears:{one:"más de 1 año",other:"más de {{count}} años"},almostXYears:{one:"casi 1 año",other:"casi {{count}} años"}},V={date:e({formats:{full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/y"},defaultWidth:"full"}),time:e({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:e({formats:{full:"{{date}} 'a las' {{time}}",long:"{{date}} 'a las' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},E={lastWeek:"'el' eeee 'pasado a la' p",yesterday:"'ayer a la' p",today:"'hoy a la' p",tomorrow:"'mañana a la' p",nextWeek:"eeee 'a la' p",other:"P"},q={lastWeek:"'el' eeee 'pasado a las' p",yesterday:"'ayer a las' p",today:"'hoy a las' p",tomorrow:"'mañana a las' p",nextWeek:"eeee 'a las' p",other:"P"},L={code:"es",formatDistance:(e,a,o)=>{let t;const n=Y[e];return t="string"==typeof n?n:1===a?n.one:n.other.replace("{{count}}",a.toString()),o?.addSuffix?o.comparison&&o.comparison>0?"en "+t:"hace "+t:t},formatLong:V,formatRelative:(e,a,o,t)=>1!==a.getHours()?q[e]:E[e],localize:{ordinalNumber:(e,a)=>Number(e)+"º",era:a({values:{narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","después de cristo"]},defaultWidth:"wide"}),quarter:a({values:{narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1º trimestre","2º trimestre","3º trimestre","4º trimestre"]},defaultWidth:"wide",argumentCallback:e=>Number(e)-1}),month:a({values:{narrow:["e","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],wide:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"]},defaultWidth:"wide"}),day:a({values:{narrow:["d","l","m","m","j","v","s"],short:["do","lu","ma","mi","ju","vi","sá"],abbreviated:["dom","lun","mar","mié","jue","vie","sáb"],wide:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"]},defaultWidth:"wide"}),dayPeriod:a({values:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"mañana",afternoon:"tarde",evening:"tarde",night:"noche"},abbreviated:{am:"AM",pm:"PM",midnight:"medianoche",noon:"mediodia",morning:"mañana",afternoon:"tarde",evening:"tarde",night:"noche"},wide:{am:"a.m.",pm:"p.m.",midnight:"medianoche",noon:"mediodia",morning:"mañana",afternoon:"tarde",evening:"tarde",night:"noche"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"de la mañana",afternoon:"de la tarde",evening:"de la tarde",night:"de la noche"},abbreviated:{am:"AM",pm:"PM",midnight:"medianoche",noon:"mediodia",morning:"de la mañana",afternoon:"de la tarde",evening:"de la tarde",night:"de la noche"},wide:{am:"a.m.",pm:"p.m.",midnight:"medianoche",noon:"mediodia",morning:"de la mañana",afternoon:"de la tarde",evening:"de la tarde",night:"de la noche"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:t({matchPattern:/^(\d+)(º)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}}),era:o({matchPatterns:{narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|a\.?\s?e\.?\s?c\.?|d\.?\s?c\.?|e\.?\s?c\.?)/i,wide:/^(antes de cristo|antes de la era com[uú]n|despu[eé]s de cristo|era com[uú]n)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^ac/i,/^dc/i],wide:[/^(antes de cristo|antes de la era com[uú]n)/i,/^(despu[eé]s de cristo|era com[uú]n)/i]},defaultParseWidth:"any"}),quarter:o({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:o({matchPatterns:{narrow:/^[efmajsond]/i,abbreviated:/^(ene|feb|mar|abr|may|jun|jul|ago|sep|oct|nov|dic)/i,wide:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^e/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^en/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i]},defaultParseWidth:"any"}),day:o({matchPatterns:{narrow:/^[dlmjvs]/i,short:/^(do|lu|ma|mi|ju|vi|s[áa])/i,abbreviated:/^(dom|lun|mar|mi[ée]|jue|vie|s[áa]b)/i,wide:/^(domingo|lunes|martes|mi[ée]rcoles|jueves|viernes|s[áa]bado)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^do/i,/^lu/i,/^ma/i,/^mi/i,/^ju/i,/^vi/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:o({matchPatterns:{narrow:/^(a|p|mn|md|(de la|a las) (mañana|tarde|noche))/i,any:/^([ap]\.?\s?m\.?|medianoche|mediodia|(de la|a las) (mañana|tarde|noche))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mn/i,noon:/^md/i,morning:/mañana/i,afternoon:/tarde/i,evening:/tarde/i,night:/noche/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:1}};function B(e){return i("MuiDatePickerToolbar",e)}d("MuiDatePickerToolbar",["root","title"]);const I=["toolbarFormat","toolbarPlaceholder","className","classes"],Q=m(w,{name:"MuiDatePickerToolbar",slot:"Root"})({}),_=m(z,{name:"MuiDatePickerToolbar",slot:"Title"})({variants:[{props:{pickerOrientation:"landscape"},style:{margin:"auto 16px auto auto"}}]}),G=c.forwardRef((function(e,a){const o=f({props:e,name:"MuiDatePickerToolbar"}),{toolbarFormat:t,toolbarPlaceholder:i="––",className:d,classes:m}=o,w=l(o,I),v=b(),{value:P,views:j,orientation:M}=h(),O=p(),T=y(),k=(e=>u({root:["root"],title:["title"]},B,e))(m),D=c.useMemo((()=>{if(!v.isValid(P))return i;const e=g(v,{format:t,views:j},!0);return v.formatByString(P,e)}),[P,t,i,v,j]);return n.jsx(Q,r({ref:a,toolbarTitle:O.datePickerToolbarTitle,className:s(k.root,d)},w,{children:n.jsx(_,{variant:"h4",align:"landscape"===M?"left":"center",ownerState:T,className:k.title,children:D})}))}));function J(e,a){const o=f({props:e,name:a}),t=v(o),n=c.useMemo((()=>null==o.localeText?.toolbarTitle?o.localeText:r({},o.localeText,{datePickerToolbarTitle:o.localeText.toolbarTitle})),[o.localeText]);return r({},o,t,{localeText:n},P({views:o.views,openTo:o.openTo,defaultViews:["year","day"],defaultOpenTo:"day"}),{slots:r({toolbar:G},o.slots)})}const K=["slots","slotProps"],U=c.forwardRef((function(e,a){const o=f({props:e,name:"MuiDateField"}),{slots:t,slotProps:r}=o,s=l(o,K),i=(e=>{const a=j(e);return M({manager:a,props:e})})(O({slotProps:r,ref:a,externalForwardedProps:s}));return n.jsx(T,{slots:t,slotProps:r,fieldResponse:i,defaultOpenPickerIcon:k})})),Z=c.forwardRef((function(e,a){const o=b(),t=J(e,"MuiDesktopDatePicker"),n=r({day:D,month:D,year:D},t.viewRenderers),s=r({},t,{closeOnSelect:t.closeOnSelect??!0,viewRenderers:n,format:g(o,t,!1),yearsPerRow:t.yearsPerRow??4,slots:r({field:U},t.slots),slotProps:r({},t.slotProps,{field:e=>r({},X(t.slotProps?.field,e),x(t)),toolbar:r({hidden:!0},t.slotProps?.toolbar)})}),{renderPicker:i}=W({ref:a,props:s,valueManager:C,valueType:"date",validator:R,steps:null});return i()}));Z.propTypes={autoFocus:A.bool,className:A.string,closeOnSelect:A.bool,dayOfWeekFormatter:A.func,defaultValue:A.object,disabled:A.bool,disableFuture:A.bool,disableHighlightToday:A.bool,disableOpenPicker:A.bool,disablePast:A.bool,displayWeekNumber:A.bool,enableAccessibleFieldDOMStructure:A.any,fixedWeekNumber:A.number,format:A.string,formatDensity:A.oneOf(["dense","spacious"]),inputRef:S,label:A.node,loading:A.bool,localeText:A.object,maxDate:A.object,minDate:A.object,monthsPerRow:A.oneOf([3,4]),name:A.string,onAccept:A.func,onChange:A.func,onClose:A.func,onError:A.func,onMonthChange:A.func,onOpen:A.func,onSelectedSectionsChange:A.func,onViewChange:A.func,onYearChange:A.func,open:A.bool,openTo:A.oneOf(["day","month","year"]),orientation:A.oneOf(["landscape","portrait"]),readOnly:A.bool,reduceAnimations:A.bool,referenceDate:A.object,renderLoading:A.func,selectedSections:A.oneOfType([A.oneOf(["all","day","empty","hours","meridiem","minutes","month","seconds","weekDay","year"]),A.number]),shouldDisableDate:A.func,shouldDisableMonth:A.func,shouldDisableYear:A.func,showDaysOutsideCurrentMonth:A.bool,slotProps:A.object,slots:A.object,sx:A.oneOfType([A.arrayOf(A.oneOfType([A.func,A.object,A.bool])),A.func,A.object]),timezone:A.string,value:A.object,view:A.oneOf(["day","month","year"]),viewRenderers:A.shape({day:A.func,month:A.func,year:A.func}),views:A.arrayOf(A.oneOf(["day","month","year"]).isRequired),yearsOrder:A.oneOf(["asc","desc"]),yearsPerRow:A.oneOf([3,4])};const $=c.forwardRef((function(e,a){const o=b(),t=J(e,"MuiMobileDatePicker"),n=r({day:D,month:D,year:D},t.viewRenderers),s=r({},t,{viewRenderers:n,format:g(o,t,!1),slots:r({field:U},t.slots),slotProps:r({},t.slotProps,{field:e=>r({},X(t.slotProps?.field,e),x(t)),toolbar:r({hidden:!1},t.slotProps?.toolbar)})}),{renderPicker:i}=N({ref:a,props:s,valueManager:C,valueType:"date",validator:R,steps:null});return i()}));$.propTypes={autoFocus:A.bool,className:A.string,closeOnSelect:A.bool,dayOfWeekFormatter:A.func,defaultValue:A.object,disabled:A.bool,disableFuture:A.bool,disableHighlightToday:A.bool,disableOpenPicker:A.bool,disablePast:A.bool,displayWeekNumber:A.bool,enableAccessibleFieldDOMStructure:A.any,fixedWeekNumber:A.number,format:A.string,formatDensity:A.oneOf(["dense","spacious"]),inputRef:S,label:A.node,loading:A.bool,localeText:A.object,maxDate:A.object,minDate:A.object,monthsPerRow:A.oneOf([3,4]),name:A.string,onAccept:A.func,onChange:A.func,onClose:A.func,onError:A.func,onMonthChange:A.func,onOpen:A.func,onSelectedSectionsChange:A.func,onViewChange:A.func,onYearChange:A.func,open:A.bool,openTo:A.oneOf(["day","month","year"]),orientation:A.oneOf(["landscape","portrait"]),readOnly:A.bool,reduceAnimations:A.bool,referenceDate:A.object,renderLoading:A.func,selectedSections:A.oneOfType([A.oneOf(["all","day","empty","hours","meridiem","minutes","month","seconds","weekDay","year"]),A.number]),shouldDisableDate:A.func,shouldDisableMonth:A.func,shouldDisableYear:A.func,showDaysOutsideCurrentMonth:A.bool,slotProps:A.object,slots:A.object,sx:A.oneOfType([A.arrayOf(A.oneOfType([A.func,A.object,A.bool])),A.func,A.object]),timezone:A.string,value:A.object,view:A.oneOf(["day","month","year"]),viewRenderers:A.shape({day:A.func,month:A.func,year:A.func}),views:A.arrayOf(A.oneOf(["day","month","year"]).isRequired),yearsOrder:A.oneOf(["asc","desc"]),yearsPerRow:A.oneOf([3,4])};const ee=["desktopModeMediaQuery"],ae=c.forwardRef((function(e,a){const o=f({props:e,name:"MuiDatePicker"}),{desktopModeMediaQuery:t=F}=o,s=l(o,ee);return H(t,{defaultMatches:!0})?n.jsx(Z,r({ref:a},s)):n.jsx($,r({ref:a},s))}));export{ae as D,L as e};
