import * as bookcarsTypes from ':bookcars-types';
/**
 * Format a number.
 *
 * @export
 * @param {number} x
 * @param {string} language ISO 639-1 language code
 * @returns {string}
 */
export declare const formatNumber: (x: number, language: string) => string;
/**
 * Format a Date number to two digits.
 *
 * @export
 * @param {number} n
 * @returns {string}
 */
export declare const formatDatePart: (n: number) => string;
/**
 * Capitalize a string.
 *
 * @export
 * @param {string} str
 * @returns {string}
 */
export declare const capitalize: (str: string) => string;
/**
 * Check if a value is a Date.
 *
 * @export
 * @param {?*} [value]
 * @returns {boolean}
 */
export declare const isDate: (value?: any) => boolean;
/**
 * Join two url parts.
 *
 * @param {?string} [part1]
 * @param {?string} [part2]
 * @returns {string}
 */
export declare const joinURL: (part1?: string, part2?: string) => string;
/**
 * Check if a string is an integer.
 *
 * @param {string} val
 * @returns {boolean}
 */
export declare const isInteger: (val: string) => boolean;
/**
 * Check if a string is a year.
 *
 * @param {string} val
 * @returns {boolean}
 */
export declare const isYear: (val: string) => boolean;
/**
 * Check if a string is a CVV.
 *
 * @param {string} val
 * @returns {boolean}
 */
export declare const isCvv: (val: string) => boolean;
/**
 * Check if two arrays are equal.
 *
 * @param {*} a
 * @param {*} b
 * @returns {boolean}
 */
export declare const arrayEqual: (a: any, b: any) => boolean;
/**
 * Clone an object or array.
 *
 * @param {*} obj
 * @returns {*}
 */
export declare const clone: (obj: any) => any;
/**
 * Clone an array.
 *
 * @export
 * @template T
 * @param {T[]} arr
 * @returns {(T[] | undefined | null)}
 */
export declare const cloneArray: <T>(arr: T[]) => T[] | undefined | null;
/**
 * Check if two filters are equal.
 *
 * @param {?(bookcarsTypes.Filter | null)} [a]
 * @param {?(bookcarsTypes.Filter | null)} [b]
 * @returns {boolean}
 */
export declare const filterEqual: (a?: bookcarsTypes.Filter | null, b?: bookcarsTypes.Filter | null) => boolean;
/**
 * Flatten Supplier array.
 *
 * @param {bookcarsTypes.User[]} suppliers
 * @returns {string[]}
 */
export declare const flattenSuppliers: (suppliers: bookcarsTypes.User[]) => string[];
/**
 * Get number of days between two dates.
 *
 * @param {?Date} [from]
 * @param {?Date} [to]
 * @returns {number}
 */
export declare const days: (from?: Date, to?: Date) => number;
/**
 * Check if currency is written from right to left.
 *
 * @returns {*}
 */
export declare const currencyRTL: (currencySymbol: string) => boolean;
/**
 * Format price
 *
 * @param {number} price
 * @param {string} currency
 * @param {string} language ISO 639-1 language code
 * @returns {boolean}
 */
export declare const formatPrice: (price: number, currency: string, language: string) => string;
/**
 * Calculate total price.
 *
 * @param {bookcarsTypes.Dress} dress
 * @param {Date} from
 * @param {Date} to
 * @param {number} priceChangeRate
 * @param {?bookcarsTypes.DressOptions} [options]
 * @returns {number}
 */
export declare const calculateTotalPrice: (dress: bookcarsTypes.Dress, from: Date, to: Date, priceChangeRate: number, options?: bookcarsTypes.DressOptions) => number;
/**
 * Convert price from a given currency to another.
 *
 * @async
 * @param {number} amount
 * @param {string} from
 * @param {string} to
 * @returns {Promise<number>}
 */
export declare const convertPrice: (amount: number, from: string, to: string) => Promise<number>;
/**
 * Check if currency is supported.
 *
 * @param {string} currency
 * @returns {boolean}
 */
export declare const checkCurrency: (currency: string) => boolean;
/**
 * Check whether language is french
 *
 * @param {string} language
 * @returns {boolean}
 */
export declare const isFrench: (language?: string) => language is "fr";
/**
 * Return all dress types.
 *
 * @returns {bookcarsTypes.DressType[]}
 */
export declare const getAllDressTypes: () => bookcarsTypes.DressType[];
/**
 * Randomize (shuffle) an array.
 *
 * @param {any[]} array
 */
export declare const shuffle: (array: any[]) => void;
/**
 * Return all dress ranges.
 *
 * @returns {bookcarsTypes.DressRange[]}
 */
export declare const getAllRanges: () => bookcarsTypes.DressRange[];
/**
 * Return all dress accessories.
 *
 * @returns {bookcarsTypes.DressAccessories[]}
 */
export declare const getAllAccessories: () => bookcarsTypes.DressAccessories[];
/**
 * Return all dress materials.
 *
 * @returns {bookcarsTypes.DressMaterial[]}
 */
export declare const getAllMaterials: () => bookcarsTypes.DressMaterial[];
/**
 * Return all dress materials.
 *
 * @returns {bookcarsTypes.DressMaterial[]}
 */
export declare const getAllDressMaterials: () => bookcarsTypes.DressMaterial[];
/**
 * Return all dress sizes.
 *
 * @returns {bookcarsTypes.DressSize[]}
 */
export declare const getAllDressSizes: () => bookcarsTypes.DressSize[];
/**
 * Return all dress styles.
 *
 * @returns {bookcarsTypes.DressStyle[]}
 */
export declare const getAllDressStyles: () => bookcarsTypes.DressStyle[];
/**
 * Calculate distance between two points on map.
 *
 * @param {number} lat1
 * @param {number} lon1
 * @param {number} lat2
 * @param {number} lon2
 * @param {('K' | 'M')} unit
 * @returns {number}
 */
export declare const distance: (lat1: number, lon1: number, lat2: number, lon2: number, unit: "K" | "M") => number;
/**
 * Format distance in Km/m.
 *
 * @param {number} d distance
 * @param {string} language
 * @returns {string}
 */
export declare const formatDistance: (d: number, language: string) => string;
/**
 * Removes a start line terminator character from a string.
 *
 * @export
 * @param {string} str
 * @param {string} char
 * @returns {string}
 */
export declare const trimStart: (str: string, char: string) => string;
/**
 * Removes a leading and trailing line terminator character from a string.
 *
 * @export
 * @param {string} str
 * @param {string} char
 * @returns {string}
 */
export declare const trimEnd: (str: string, char: string) => string;
/**
 * Removes a stating, leading and trailing line terminator character from a string.
 *
 * @export
 * @param {string} str
 * @param {string} char
 * @returns {string}
 */
export declare const trim: (str: string, char: string) => string;
/**
 * Wait a certain time in milliseconds.
 *
 * @param {number} milliseconds
 * @returns {Promise<unknown>}
 */
export declare const delay: (milliseconds: number) => Promise<unknown>;
/**
 * Truncates a string.
 *
 * @param {string} str
 * @param {number} maxLength
 * @returns {string}
 */
export declare const truncateString: (str: string, maxLength: number) => string;
