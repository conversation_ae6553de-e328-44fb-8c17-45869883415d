import{j as e,R as s,G as a,e as n,c as t}from"../entries/index-CEzJO5Xy.js";import{r}from"./router-BtYqujaw.js";import{A as i}from"./Autocomplete-CviOU_ku.js";import{A as o}from"./Avatar-Dix3YM8x.js";import{A as l}from"./AccountCircle-khVEeiad.js";import{L as m}from"./LocationService-BtQFgoWL.js";import{C as p}from"./Flag-BR6CpE1z.js";import{C as c}from"./Chip-CAtDqtgp.js";import{T as d,I as u}from"./TextField-BAse--ht.js";const j=r.forwardRef(((s,a)=>{const n=t.c(7);let i,o;n[0]!==s?(({children:i,...o}=s),n[0]=s,n[1]=i,n[2]=o):(i=n[1],o=n[2]);const l=r.useRef(null);let m,p;return n[3]===Symbol.for("react.memo_cache_sentinel")?(m=()=>l.current,n[3]=m):m=n[3],r.useImperativeHandle(a,m),n[4]!==i||n[5]!==o?(p=e.jsx("ul",{...o,ref:l,role:"list-box",children:i}),n[4]=i,n[5]=o,n[6]=p):p=n[6],p})),x=({label:t,reference:x,selectedOptions:h,key:g,required:N,options:f,ListboxProps:y,loading:E,multiple:_,type:A,variant:C,readOnly:I,callbackFromMultipleSelect:P,onFocus:v,onInputChange:S,onClear:b,onOpen:k})=>{const[O,D]=r.useState([]),[R,F]=r.useState("");return f||(f=[]),r.useEffect((()=>{h&&D(h),h&&0===h.length&&F("")}),[h,A]),e.jsx("div",{className:"multiple-select",children:e.jsx(i,{readOnly:I,options:f,value:_?O:O.length>0?O[0]:null,getOptionLabel:e=>e&&e.name||"",isOptionEqualToValue:(e,s)=>e._id===s._id,onChange:(e,s)=>{if(!e||"keydown"!==e.type||!("key"in e)||"Enter"!==e.key)if(g=g||"",_)D(s),P&&P(s,g,x),0===s.length&&b&&b();else{const e=s&&[s]||[];D(e),P&&P(e,g,x),s||b&&b()}},onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},clearOnBlur:!1,clearOnEscape:!1,loading:E,multiple:_,handleHomeEndKeys:!1,renderInput:r=>{const{inputProps:i}=r;if(i.autoComplete="off",A===s.User&&!_&&1===O.length&&O[0]){const s=O[0];return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:s.image?e.jsx(o,{src:a(n.CDN_USERS,s.image),className:"avatar-small suo"}):e.jsx(l,{className:"avatar-small suo",color:"disabled"})}),r.InputProps.startAdornment]})}}})}if(A===s.Supplier&&!_&&1===O.length&&O[0]){const s=O[0];return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:e.jsx("div",{className:"supplier-ia",children:e.jsx("img",{src:a(n.CDN_USERS,s.image),alt:s.name})})}),r.InputProps.startAdornment]})}}})}if(A===s.Location&&!_&&1===O.length&&O[0])return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:e.jsx(m,{})}),r.InputProps.startAdornment]})}}});if(A===s.Country&&!_&&1===O.length&&O[0])return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:e.jsx(p,{})}),r.InputProps.startAdornment]})}}});if(A===s.Dress&&!_&&1===O.length&&O[0]){const s=O[0];return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N,slotProps:{input:{...r.InputProps,startAdornment:e.jsxs(e.Fragment,{children:[e.jsx(u,{position:"start",children:e.jsx("img",{src:a(n.CDN_CARS,s.image),alt:s.name,style:{height:n.SELECTED_CAR_OPTION_IMAGE_HEIGHT}})}),r.InputProps.startAdornment]})}}})}return e.jsx(d,{...r,label:t,variant:C||"outlined",required:N&&O&&0===O.length})},inputValue:R,onInputChange:(e,s)=>{F(s),S&&S(e)},renderTags:(e,s)=>e.map(((e,a)=>r.createElement(c,{...s({index:a}),key:e._id,label:e.name}))),renderOption:(t,i)=>{"key"in t&&delete t.key;const c=t;return A===s.User?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image",children:i.image?e.jsx(o,{src:a(n.CDN_USERS,i.image),className:"avatar-medium"}):e.jsx(l,{className:"avatar-medium",color:"disabled"})}),e.jsx("span",{className:"option-name",children:i.name})):A===s.Supplier?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image supplier-ia",children:e.jsx("img",{src:a(n.CDN_USERS,i.image),alt:i.name})}),e.jsx("span",{className:"option-name",children:i.name})):A===s.Location?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image",children:e.jsx(m,{})}),e.jsx("span",{className:"option-name",children:i.name})):A===s.Country?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image",children:e.jsx(p,{})}),e.jsx("span",{className:"option-name",children:i.name})):A===s.Dress?r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{className:"option-image car-ia",children:e.jsx("img",{src:a(n.CDN_CARS,i.image),alt:i.name,style:{height:n.CAR_OPTION_IMAGE_HEIGHT}})}),e.jsx("span",{className:"car-option-name",children:i.name})):r.createElement("li",{...c,key:i._id,className:`${t.className} ms-option`},e.jsx("span",{children:i.name}))},onFocus:v||void 0,onOpen:k||void 0,slotProps:{listbox:{component:j,...y}}})})};export{x as M};
