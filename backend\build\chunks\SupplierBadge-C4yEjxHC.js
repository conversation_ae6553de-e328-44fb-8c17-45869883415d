import{c as s,j as e,G as a,e as l}from"../entries/index-CEzJO5Xy.js";const r=r=>{const i=s.c(2),{supplier:p}=r;let c;return i[0]!==p?(c=p&&e.jsxs("div",{className:"supplier-badge",title:p.fullName,children:[e.jsx("span",{className:"supplier-badge-logo",children:e.jsx("img",{src:a(l.CDN_USERS,p.avatar),alt:p.fullName})}),e.jsx("a",{href:`/supplier?c=${p._id}`,className:"supplier-badge-info",children:p.fullName})]}),i[0]=p,i[1]=c):c=i[1],c};export{r as S};
