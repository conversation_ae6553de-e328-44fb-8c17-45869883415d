import React, { useState } from 'react'
import {
  Input,
  InputLabel,
  FormControl,
  FormControlLabel,
  Switch,
  Button,
  Paper,
  Select,
  MenuItem,
  SelectChangeEvent,
  FormHelperText
} from '@mui/material'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { useNavigate } from 'react-router-dom'
import * as bookcarsTypes from ':bookcars-types'
import Layout from '../components/Layout'
import * as BookingService from '../services/BookingService'
import * as helper from '../common/helper'
import { strings as commonStrings } from '../lang/common'
import { strings } from '../lang/create-booking'
import UserSelectList from '../components/UserSelectList'
import DressSelectList from '../components/DressSelectList'
import LocationSelectList from '../components/LocationSelectList'

import '../assets/css/create-booking.css'

const CreateBooking: React.FC = () => {
  const navigate = useNavigate()
  const [user, setUser] = useState<bookcarsTypes.User>()
  const [loading, setLoading] = useState(false)
  const [visible, setVisible] = useState(false)
  const [supplier, setSupplier] = useState('')
  const [dress, setDress] = useState('')
  const [driver, setDriver] = useState('')
  const [pickupLocation, setPickupLocation] = useState('')
  const [dropOffLocation, setDropOffLocation] = useState('')
  const [from, setFrom] = useState<Date | null>(null)
  const [to, setTo] = useState<Date | null>(null)
  const [status, setStatus] = useState(bookcarsTypes.BookingStatus.Pending)
  const [cancellation, setCancellation] = useState(false)
  const [amendments, setAmendments] = useState(false)
  const [price, setPrice] = useState('')
  const [formError, setFormError] = useState(false)
  const [error, setError] = useState(false)

  const handleSupplierChange = (values: bookcarsTypes.Option[]) => {
    setSupplier(values.length > 0 ? values[0]._id : '')
  }

  const handleDressChange = (values: bookcarsTypes.Option[]) => {
    setDress(values.length > 0 ? values[0]._id : '')
  }

  const handleDriverChange = (values: bookcarsTypes.Option[]) => {
    setDriver(values.length > 0 ? values[0]._id : '')
  }

  const handlePickupLocationChange = (values: bookcarsTypes.Option[]) => {
    setPickupLocation(values.length > 0 ? values[0]._id : '')
  }

  const handleDropOffLocationChange = (values: bookcarsTypes.Option[]) => {
    setDropOffLocation(values.length > 0 ? values[0]._id : '')
  }

  const handleStatusChange = (e: SelectChangeEvent<string>) => {
    setStatus(e.target.value as bookcarsTypes.BookingStatus)
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    try {
      if (!supplier || !dress || !driver || !pickupLocation || !dropOffLocation || !from || !to || !price) {
        setFormError(true)
        setError(false)
        return
      }

      setLoading(true)
      setFormError(false)
      setError(false)

      const data: bookcarsTypes.UpsertBookingPayload = {
        booking: {
          supplier,
          dress,
          driver,
          pickupLocation,
          dropOffLocation,
          from,
          to,
          status,
          cancellation,
          amendments,
          price: Number(price)
        }
      }

      const booking = await BookingService.create(data)

      if (booking && booking._id) {
        navigate('/')
      } else {
        helper.error()
      }
    } catch (err) {
      helper.error(err)
    } finally {
      setLoading(false)
    }
  }

  const onLoad = (user?: bookcarsTypes.User) => {
    if (user && user.verified) {
      setUser(user)
      setVisible(true)
    }
  }

  return (
    <Layout onLoad={onLoad} strict>
      <div className="create-booking">
        <Paper className="booking-form booking-form-wrapper" elevation={10} style={visible ? {} : { display: 'none' }}>
          <h1 className="booking-form-title">{strings.NEW_BOOKING}</h1>
          <form onSubmit={handleSubmit}>
            {helper.admin(user) && (
              <FormControl fullWidth margin="dense">
                <UserSelectList
                  label={commonStrings.SUPPLIER}
                  required
                  type={bookcarsTypes.UserType.Supplier}
                  onChange={handleSupplierChange}
                />
              </FormControl>
            )}

            <FormControl fullWidth margin="dense">
              <DressSelectList
                label={strings.DRESS}
                required
                supplier={supplier}
                onChange={handleDressChange}
              />
            </FormControl>

            <FormControl fullWidth margin="dense">
              <UserSelectList
                label={strings.DRIVER}
                required
                type={bookcarsTypes.UserType.User}
                onChange={handleDriverChange}
              />
            </FormControl>

            <FormControl fullWidth margin="dense">
              <LocationSelectList
                label={strings.PICKUP_LOCATION}
                required
                onChange={handlePickupLocationChange}
              />
            </FormControl>

            <FormControl fullWidth margin="dense">
              <LocationSelectList
                label={strings.DROP_OFF_LOCATION}
                required
                onChange={handleDropOffLocationChange}
              />
            </FormControl>

            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <FormControl fullWidth margin="dense">
                <DateTimePicker
                  label={strings.FROM}
                  value={from}
                  onChange={setFrom}
                  slotProps={{
                    textField: {
                      required: true,
                      variant: 'standard'
                    }
                  }}
                />
              </FormControl>

              <FormControl fullWidth margin="dense">
                <DateTimePicker
                  label={strings.TO}
                  value={to}
                  onChange={setTo}
                  slotProps={{
                    textField: {
                      required: true,
                      variant: 'standard'
                    }
                  }}
                />
              </FormControl>
            </LocalizationProvider>

            <FormControl fullWidth margin="dense">
              <InputLabel className="required">{strings.STATUS}</InputLabel>
              <Select
                value={status}
                onChange={handleStatusChange}
                variant="standard"
                required
              >
                <MenuItem value={bookcarsTypes.BookingStatus.Pending}>{strings.PENDING}</MenuItem>
                <MenuItem value={bookcarsTypes.BookingStatus.Deposit}>{strings.DEPOSIT}</MenuItem>
                <MenuItem value={bookcarsTypes.BookingStatus.Paid}>{strings.PAID}</MenuItem>
                <MenuItem value={bookcarsTypes.BookingStatus.Reserved}>{strings.RESERVED}</MenuItem>
                <MenuItem value={bookcarsTypes.BookingStatus.Cancelled}>{strings.CANCELLED}</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth margin="dense">
              <InputLabel className="required">{strings.PRICE}</InputLabel>
              <Input
                type="number"
                required
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                inputProps={{ min: 0, step: 0.01 }}
              />
            </FormControl>

            <FormControl fullWidth margin="dense" className="checkbox-fc">
              <FormControlLabel
                control={
                  <Switch
                    checked={cancellation}
                    onChange={(e) => setCancellation(e.target.checked)}
                    color="primary"
                  />
                }
                label={strings.CANCELLATION}
              />
            </FormControl>

            <FormControl fullWidth margin="dense" className="checkbox-fc">
              <FormControlLabel
                control={
                  <Switch
                    checked={amendments}
                    onChange={(e) => setAmendments(e.target.checked)}
                    color="primary"
                  />
                }
                label={strings.AMENDMENTS}
              />
            </FormControl>

            <div className="buttons">
              <Button type="submit" variant="contained" className="btn-primary btn-margin-bottom" size="small" disabled={loading}>
                {commonStrings.CREATE}
              </Button>
              <Button variant="contained" className="btn-secondary btn-margin-bottom" size="small" onClick={() => navigate('/')}>
                {commonStrings.CANCEL}
              </Button>
            </div>

            <div className="form-error">
              {formError && <FormHelperText error>{commonStrings.FORM_ERROR}</FormHelperText>}
              {error && <FormHelperText error>{commonStrings.GENERIC_ERROR}</FormHelperText>}
            </div>
          </form>
        </Paper>
      </div>
    </Layout>
  )
}

export default CreateBooking
