import{u as e,e as t,j as s,R as a,S as r,a as i,z as o}from"../entries/index-CEzJO5Xy.js";import{d as l,r as n}from"./router-BtYqujaw.js";import{L as c}from"./Layout-BQBjg4Lf.js";import{a as d,u as m}from"./DressService-J0XavNJj.js";import{E as u}from"./Error-koMug0_G.js";import{E as j}from"./Error-7KgmWHkR.js";import{S as p}from"./SimpleBackdrop-Bf3qjF13.js";import h from"./NoMatch-jvHCs4x8.js";import{A as g}from"./Avatar-BtfxKR-8.js";import{S,D as x,a as f,b as C,c as N}from"./create-dress-BwzUhfal.js";import{L as b}from"./LocationSelectList-DYJOB_U9.js";import{P as v}from"./Paper-CcwAvfvc.js";import{I as y}from"./Info-C_WcR51V.js";import{F as E,I}from"./InputLabel-BbcIE26O.js";import{I as P}from"./Input-BQdee9z7.js";import{F as k}from"./FormHelperText-DFSsjBsL.js";import{T as M}from"./TextField-BAse--ht.js";import{F as A,S as R}from"./Switch-BWPUOSX1.js";import{B as W}from"./Button-DGZYUY3P.js";import"./vendor-dblfw9z9.js";import"./Backdrop-Bzn12VyM.js";import"./useSlot-CtA82Ni6.js";import"./LocationService-BtQFgoWL.js";import"./Grow-CjOKj0i1.js";import"./ownerWindow-ChLfdzZL.js";import"./Badge-B3LKl4T2.js";import"./AccountCircle-khVEeiad.js";import"./Tooltip-BkJF6Mu0.js";import"./Box-CHHh9iS3.js";import"./Check-D745pofy.js";import"./Avatar-Dix3YM8x.js";import"./Checkroom-Bt6MiDKF.js";import"./DialogTitle-BZXwroUN.js";import"./SupplierService-DSnTbAgG.js";import"./MultipleSelect-C7xTvWe9.js";import"./Autocomplete-CviOU_ku.js";import"./OutlinedInput-g8mR4MM3.js";import"./useFormControl-B7jXtRD7.js";import"./Chip-CAtDqtgp.js";import"./IconButton-CnBvmeAK.js";import"./Flag-BR6CpE1z.js";import"./MenuItem-suKfXYI2.js";import"./Menu-ZU0DMgjT.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-Cay5TZBz.js";import"./listItemTextClasses-DFwCkkgK.js";import"./SwitchBase-BIeqtL5F.js";const L=()=>{const L=l(),{user:_}=e(),[D,B]=n.useState(!1),[O,T]=n.useState(!1),[U,q]=n.useState(!1),[w,G]=n.useState(!1),[$,F]=n.useState(),[z,Y]=n.useState(!1),[V,Z]=n.useState(!1),[H,K]=n.useState(""),[J,Q]=n.useState(""),[X,ee]=n.useState([]),[te,se]=n.useState(""),[ae,re]=n.useState(""),[ie,oe]=n.useState(""),[le,ne]=n.useState(""),[ce,de]=n.useState(""),[me,ue]=n.useState(""),[je,pe]=n.useState(""),[he,ge]=n.useState(""),[Se,xe]=n.useState(!1),[fe,Ce]=n.useState([]),[Ne,be]=n.useState(!0),[ve,ye]=n.useState(!1),[Ee,Ie]=n.useState(!1),[Pe,ke]=n.useState(""),[Me,Ae]=n.useState(""),[Re,We]=n.useState(""),[Le,_e]=n.useState(""),[De,Be]=n.useState(""),[Oe,Te]=n.useState(""),[Ue,qe]=n.useState(!1),[we,Ge]=n.useState(""),[$e,Fe]=n.useState(""),[ze,Ye]=n.useState(String(t.MINIMUM_AGE)),[Ve,Ze]=n.useState(!0),[He,Ke]=n.useState(!1),[Je,Qe]=n.useState(""),Xe=e=>{const s=Number.parseInt(e,10);return!Number.isNaN(s)&&s>=t.MINIMUM_AGE},et=e=>""===e?-1:Number(e),tt=e=>e&&Number(e)||null;return s.jsxs(c,{onLoad:async()=>{B(!0),Ke(!1),Y(!1),Z(!1),T(!1),q(!1);try{const e=new URLSearchParams(window.location.search);if(e.has("dr")){const t=e.get("dr");if(t&&""!==t){const e=await d(t);e?(F(e),K(e.name),Q(e.supplier._id||""),ee(e.locations),se(e.dailyPrice.toString()),re(e.discountedDailyPrice?.toString()||""),oe(e.biWeeklyPrice?.toString()||""),ne(e.discountedBiWeeklyPrice?.toString()||""),de(e.weeklyPrice?.toString()||""),ue(e.discountedWeeklyPrice?.toString()||""),pe(e.monthlyPrice?.toString()||""),ge(e.discountedMonthlyPrice?.toString()||""),xe(e.isDateBasedPrice),Ce(e.dateBasedPrices||[]),be(e.available),ye(e.fullyBooked||!1),Ie(e.comingSoon||!1),ke(e.type),Ae(e.size),We(e.style||""),_e(e.color),Be(e.length.toString()),Te(e.material),qe(e.customizable||!1),Ge(-1===e.cancellation?"":e.cancellation.toString()),Fe(-1===e.amendments?"":e.amendments.toString()),Ye(e.minimumAge.toString()),Qe(e.deposit.toString()),G(!0)):T(!0)}else T(!0)}else T(!0)}catch(e){q(!0)}finally{B(!1)}},strict:!0,children:[!U&&!O&&s.jsx("div",{className:"create-dress",children:s.jsx(v,{className:"dress-form dress-form-wrapper",elevation:10,style:w?{}:{display:"none"},children:s.jsxs("form",{onSubmit:async e=>{try{if(e.preventDefault(),B(!0),!Xe(ze))return Ke(!0),void Y(!1);if(!$?.image)return Y(!0),void Z(!1);const t={_id:$?._id,loggedUser:_?._id,name:H,supplier:J,locations:X.map((e=>e._id)),dailyPrice:Number(te),discountedDailyPrice:tt(ae),biWeeklyPrice:tt(ie),discountedBiWeeklyPrice:tt(le),weeklyPrice:tt(ce),discountedWeeklyPrice:tt(me),monthlyPrice:tt(je),discountedMonthlyPrice:tt(he),deposit:Number(Je),available:Ne,fullyBooked:ve,comingSoon:Ee,type:Pe,size:Me,color:Le,length:Number(De),material:Oe,customizable:Ue,cancellation:et(we),amendments:et($e),isDateBasedPrice:Se,dateBasedPrices:fe,range:"",accessories:[]};200===await m(t)?L("/dresses"):o()}catch(t){o(t)}finally{B(!1)}},children:[s.jsx(g,{type:a.Dress,mode:"update",record:$,hideDelete:!0,size:"large",readonly:!1,onBeforeUpload:()=>{B(!0)},onChange:e=>{B(!1),Y(!1),Z(!1)},onValidate:e=>{e||Z(!0)},color:"disabled",className:"avatar-ctn"}),s.jsxs("div",{className:"info",children:[s.jsx(y,{}),s.jsx("span",{children:r.RECOMMENDED_IMAGE_SIZE})]}),s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:i.NAME}),s.jsx(P,{id:"name",type:"text",required:!0,onChange:e=>{K(e.target.value)},autoComplete:"off",value:H})]}),s.jsx(E,{fullWidth:!0,margin:"dense",children:s.jsx(S,{label:i.SUPPLIER,required:!0,value:J?{_id:J,name:"",image:""}:void 0,onChange:e=>{Q(e.length>0?e[0]._id:"")}})}),s.jsx(E,{fullWidth:!0,margin:"dense",children:s.jsx(b,{label:r.LOCATIONS,multiple:!0,required:!0,value:X,onChange:e=>{ee(e)}})}),s.jsx(E,{fullWidth:!0,margin:"dense",children:s.jsx(x,{label:r.DRESS_TYPE,required:!0,value:Pe,onChange:e=>{ke(e)}})}),s.jsx(E,{fullWidth:!0,margin:"dense",children:s.jsx(f,{label:r.DRESS_SIZE,required:!0,value:Me,onChange:e=>{Ae(e)}})}),s.jsx(E,{fullWidth:!0,margin:"dense",children:s.jsx(C,{label:r.DRESS_STYLE,required:!0,value:Re,onChange:e=>{We(e)}})}),s.jsx(E,{fullWidth:!0,margin:"dense",children:s.jsx(N,{label:r.MATERIAL,required:!0,value:Oe,onChange:e=>{Te(e)}})}),s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:r.COLOR}),s.jsx(P,{id:"color",type:"text",required:!0,onChange:e=>{_e(e.target.value)},autoComplete:"off",value:Le})]}),s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsxs(I,{className:"required",children:[r.LENGTH," (cm)"]}),s.jsx(P,{id:"length",type:"number",required:!0,onChange:e=>{Be(e.target.value)},autoComplete:"off",value:De})]}),s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:`${r.PRICE} (${i.CURRENCY})`}),s.jsx(P,{id:"price",type:"number",required:!0,onChange:e=>{se(e.target.value)},autoComplete:"off",value:te})]}),s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{children:`${r.DEPOSIT} (${i.CURRENCY})`}),s.jsx(P,{id:"deposit",type:"number",onChange:e=>{Qe(e.target.value)},autoComplete:"off",value:Je})]}),s.jsxs(E,{fullWidth:!0,margin:"dense",children:[s.jsx(I,{className:"required",children:i.MINIMUM_AGE}),s.jsx(P,{id:"minimum-age",type:"number",required:!0,error:!Ve,onChange:e=>{Ye(e.target.value);const t=Xe(e.target.value);Ze(t),t||Ke(!0)},autoComplete:"off",value:ze}),s.jsx(k,{error:!Ve,children:!Ve&&i.MINIMUM_AGE_NOT_VALID||""})]}),s.jsx(E,{fullWidth:!0,margin:"dense",children:s.jsx(M,{label:`${r.CANCELLATION} (${i.CURRENCY})`,slotProps:{htmlInput:{inputMode:"numeric",pattern:"^\\d+(\\.\\d+)?$"}},onChange:e=>{Ge(e.target.value)},variant:"standard",autoComplete:"off",value:we})}),s.jsx(E,{fullWidth:!0,margin:"dense",children:s.jsx(M,{label:`${r.AMENDMENTS} (${i.CURRENCY})`,slotProps:{htmlInput:{inputMode:"numeric",pattern:"^\\d+(\\.\\d+)?$"}},onChange:e=>{Fe(e.target.value)},variant:"standard",autoComplete:"off",value:$e})}),s.jsx(E,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(A,{control:s.jsx(R,{checked:Ue,onChange:e=>{qe(e.target.checked)},color:"primary"}),label:r.CUSTOMIZABLE,className:"checkbox-fcl"})}),s.jsx(E,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(A,{control:s.jsx(R,{checked:Ne,onChange:e=>{be(e.target.checked)},color:"primary"}),label:r.AVAILABLE,className:"checkbox-fcl"})}),s.jsx(E,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(A,{control:s.jsx(R,{checked:ve,onChange:e=>{ye(e.target.checked)},color:"primary"}),label:r.FULLY_BOOKED,className:"checkbox-fcl"})}),s.jsx(E,{fullWidth:!0,margin:"dense",className:"checkbox-fc",children:s.jsx(A,{control:s.jsx(R,{checked:Ee,onChange:e=>{Ie(e.target.checked)},color:"primary"}),label:r.COMING_SOON,className:"checkbox-fcl"})}),s.jsxs("div",{className:"buttons",children:[s.jsx(W,{type:"submit",variant:"contained",className:"btn-primary btn-margin-bottom",size:"small",disabled:D,children:i.UPDATE}),s.jsx(W,{variant:"contained",className:"btn-secondary btn-margin-bottom",size:"small",onClick:()=>{L("/dresses")},children:i.CANCEL})]}),s.jsxs("div",{className:"form-error",children:[z&&s.jsx(j,{message:i.IMAGE_REQUIRED}),V&&s.jsx(j,{message:r.IMAGE_SIZE_ERROR}),He&&s.jsx(j,{message:i.FORM_ERROR})]})]})})}),D&&s.jsx(p,{text:i.LOADING}),U&&s.jsx(u,{}),O&&s.jsx(h,{})]})};export{L as default};
