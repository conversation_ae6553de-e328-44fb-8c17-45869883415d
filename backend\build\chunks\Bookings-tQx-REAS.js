import{c as e,j as t,a as o,J as s,a5 as a,e as i,F as r,a7 as n}from"../entries/index-xsXxT3-W.js";import{r as l,d as m}from"./router-BtYqujaw.js";import{L as c}from"./Layout-DaeN7D4t.js";import{s as p,a as d,S as j,b as u}from"./booking-filter-BtI42XbQ.js";import{B as f}from"./BookingList-XbPSaIIt.js";import{u as g,a as h,z as S,s as b}from"./zod-4O8Zwsja.js";import{L as x}from"./LocationSelectList-BP49A3oC.js";import{D as k}from"./DatePicker-mLpxfIrI.js";import{A as C}from"./Accordion-Z5bnZGK6.js";import{F as L}from"./InputLabel-C8rcdOGQ.js";import{T as _}from"./TextField-D_yQOTzE.js";import{B as y}from"./Button-BeKLLPpp.js";import{I as v}from"./IconButton-CxOCoGF3.js";import{C as N}from"./Clear-CDOl64hX.js";import{S as O}from"./Search-CKOds7xB.js";import{h as w}from"./SupplierService-9DC5V5ZJ.js";import"./vendor-dblfw9z9.js";import"./BookingStatus-BaSj8uqV.js";import"./Grow-Cp8xsNYl.js";import"./ownerWindow-ChLfdzZL.js";import"./useSlot-DiTut-u0.js";import"./Paper-C-atefOs.js";import"./Backdrop-Czag2Ija.js";import"./cars-xqBbVU4C.js";import"./MenuItem-P0BnGnrT.js";import"./Menu-C_-X8cS7.js";import"./isHostComponent-DR4iSCFs.js";import"./mergeSlotProps-DEridHif.js";import"./listItemTextClasses-BcbgzvlE.js";import"./Link-sHEcszvT.js";import"./fr-DJt_zj3p.js";import"./Check-BO6X9Q-4.js";import"./DataGrid-DJUEcXft.js";import"./getThemeProps-DSP27jpP.js";import"./Switch-C5asfh_w.js";import"./useFormControl-B7jXtRD7.js";import"./SwitchBase-DrUkTXjH.js";import"./Tooltip-CKMkVqOx.js";import"./Toolbar-BTa0QYME.js";import"./KeyboardArrowRight-LSgfnPVa.js";import"./Chip-MGF1mKZa.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./Autocomplete-CWN5GAd4.js";import"./Input-D1AdR9CM.js";import"./OutlinedInput-BX8yFQbF.js";import"./ListItemText-DUhWzkV9.js";import"./Checkbox-F0AjCtoF.js";import"./Edit-Bc0UCPtn.js";import"./Delete-BfnPAJno.js";import"./LocationService-6NvQT9iL.js";import"./MultipleSelect-DovAF4K6.js";import"./Avatar-Dvwllg8p.js";import"./Flag-CMGasDVj.js";import"./DatePicker-BDzBD9XN.js";import"./FormHelperText-DDZ4BMA4.js";import"./ListItem-Bmdw8GrH.js";const A=S.object({from:S.date().optional(),to:S.date().optional(),pickupLocation:S.string().optional(),dropOffLocation:S.string().optional(),keyword:S.string().optional()}),T=a=>{const i=e.c(57),{collapse:r,className:n,language:m,onSubmit:c}=a,d=l.useRef(null);let j;i[0]===Symbol.for("react.memo_cache_sentinel")?(j={resolver:b(A),mode:"onChange"},i[0]=j):j=i[0];const{control:u,register:f,handleSubmit:S,setValue:w}=g(j);let T;i[1]!==u?(T={control:u},i[1]=u,i[2]=T):T=i[2];const{from:I,to:D,keyword:F}=h(T),[P,E]=l.useState(void 0);let R;i[3]!==c?(R=e=>{let t={from:e.from,to:e.to,pickupLocation:e.pickupLocation,dropOffLocation:e.dropOffLocation,keyword:e.keyword};e.from||e.to||e.pickupLocation||e.dropOffLocation||e.keyword||(t=null),c&&c(s(t))},i[3]=c,i[4]=R):R=i[4];const B=R,H=(n?`${n} `:"")+"booking-filter";let W,M,G,U,z,K,J,q,$,Q,V;i[5]!==B||i[6]!==S?(W=S(B),i[5]=B,i[6]=S,i[7]=W):W=i[7],i[8]===Symbol.for("react.memo_cache_sentinel")?(M=t.jsx("input",{autoComplete:"false",name:"hidden",type:"text",style:{display:"none"}}),i[8]=M):M=i[8],i[9]!==f?(G=f("from"),i[9]=f,i[10]=G):G=i[10],i[11]!==w||i[12]!==D?(U=e=>{if(e){D&&D.getTime()<=e.getTime()&&w("to",void 0);const t=new Date(e);t.setDate(e.getDate()+1),E(t)}else E(void 0);w("from",e||void 0)},i[11]=w,i[12]=D,i[13]=U):U=i[13],i[14]!==I||i[15]!==m||i[16]!==G||i[17]!==U?(z=t.jsx(L,{fullWidth:!0,margin:"dense",children:t.jsx(k,{...G,label:o.FROM,value:I,onChange:U,language:m,variant:"standard"})}),i[14]=I,i[15]=m,i[16]=G,i[17]=U,i[18]=z):z=i[18],i[19]!==f?(K=f("to"),i[19]=f,i[20]=K):K=i[20],i[21]!==w?(J=e=>w("to",e||void 0),i[21]=w,i[22]=J):J=i[22],i[23]!==m||i[24]!==P||i[25]!==K||i[26]!==J||i[27]!==D?(q=t.jsx(L,{fullWidth:!0,margin:"dense",children:t.jsx(k,{...K,label:o.TO,minDate:P,value:D,onChange:J,language:m,variant:"standard"})}),i[23]=m,i[24]=P,i[25]=K,i[26]=J,i[27]=D,i[28]=q):q=i[28],i[29]!==w?($=t.jsx(L,{fullWidth:!0,margin:"dense",children:t.jsx(x,{label:p.PICK_UP_LOCATION,variant:"standard",onChange:e=>w("pickupLocation",e.length>0?e[0]._id:"")})}),i[29]=w,i[30]=$):$=i[30],i[31]!==w?(Q=t.jsx(L,{fullWidth:!0,margin:"dense",children:t.jsx(x,{label:p.DROP_OFF_LOCATION,variant:"standard",onChange:e=>w("dropOffLocation",e.length>0?e[0]._id:"")})}),i[31]=w,i[32]=Q):Q=i[32],i[33]!==f?(V=f("keyword"),i[33]=f,i[34]=V):V=i[34];const X=F||"";let Y,Z,ee,te,oe,se;return i[35]!==w?(Y=e=>w("keyword",e.target.value),i[35]=w,i[36]=Y):Y=i[36],i[37]!==F||i[38]!==w?(Z={input:{endAdornment:F?t.jsx(v,{size:"small",onClick:()=>{w("keyword",""),d.current?.focus()},children:t.jsx(N,{className:"d-adornment-icon"})}):t.jsx(O,{className:"d-adornment-icon"})}},i[37]=F,i[38]=w,i[39]=Z):Z=i[39],i[40]!==V||i[41]!==X||i[42]!==Y||i[43]!==Z?(ee=t.jsx(L,{fullWidth:!0,margin:"dense",children:t.jsx(_,{...V,inputRef:d,variant:"standard",value:X,onChange:Y,placeholder:o.SEARCH_PLACEHOLDER,slotProps:Z,className:"bf-search"})}),i[40]=V,i[41]=X,i[42]=Y,i[43]=Z,i[44]=ee):ee=i[44],i[45]===Symbol.for("react.memo_cache_sentinel")?(te=t.jsx(y,{type:"submit",variant:"contained",className:"btn-primary btn-search",fullWidth:!0,children:o.SEARCH}),i[45]=te):te=i[45],i[46]!==q||i[47]!==$||i[48]!==Q||i[49]!==ee||i[50]!==W||i[51]!==z?(oe=t.jsxs("form",{autoComplete:"off",onSubmit:W,children:[M,z,q,$,Q,ee,te]}),i[46]=q,i[47]=$,i[48]=Q,i[49]=ee,i[50]=W,i[51]=z,i[52]=oe):oe=i[52],i[53]!==r||i[54]!==oe||i[55]!==H?(se=t.jsx(C,{title:o.SEARCH,collapse:r,className:H,children:oe}),i[53]=r,i[54]=oe,i[55]=H,i[56]=se):se=i[56],se},I=()=>{const o=e.c(22),s=m(),[p,g]=l.useState(),[h,S]=l.useState(!1),[b,x]=l.useState(!1);let k;o[0]===Symbol.for("react.memo_cache_sentinel")?(k=[],o[0]=k):k=o[0];const[C,L]=l.useState(k),[_,v]=l.useState();let N;o[1]===Symbol.for("react.memo_cache_sentinel")?(N=a().map(D),o[1]=N):N=o[1];const[O,A]=l.useState(N),[I,F]=l.useState(),[P,E]=l.useState(!0),[R,B]=l.useState(0);let H,W,M;o[2]!==p?(H=()=>{if(p&&p.verified){const e=document.querySelector("div.col-1");e&&B(e.clientHeight)}},W=[p],o[2]=p,o[3]=H,o[4]=W):(H=o[3],W=o[4]),l.useEffect(H,W),o[5]===Symbol.for("react.memo_cache_sentinel")?(M=e=>{v(e)},o[5]=M):M=o[5];const G=M;let U;o[6]===Symbol.for("react.memo_cache_sentinel")?(U=e=>{A(e)},o[6]=U):U=o[6];const z=U;let K;o[7]===Symbol.for("react.memo_cache_sentinel")?(K=e=>{F(e)},o[7]=K):K=o[7];const J=K;let q;o[8]===Symbol.for("react.memo_cache_sentinel")?(q=async e=>{if(e){const t=r(e);g(e),x(t),S(!t),E(t);const o=await w(),s=t?n(o):[e._id??""];L(o),v(s),S(!0),E(!1)}},o[8]=q):q=o[8];const $=q;let Q,V;return o[9]!==b||o[10]!==C||o[11]!==I||o[12]!==h||o[13]!==P||o[14]!==s||o[15]!==R||o[16]!==O||o[17]!==_||o[18]!==p?(Q=p&&t.jsxs("div",{className:"bookings",children:[t.jsx("div",{className:"col-1",children:h&&t.jsxs(t.Fragment,{children:[t.jsx(y,{variant:"contained",className:"btn-primary cl-new-booking",size:"small",onClick:()=>s("/create-booking"),children:d.NEW_BOOKING}),b&&t.jsx(j,{suppliers:C,onChange:G,className:"cl-supplier-filter"}),t.jsx(u,{onChange:z,className:"cl-status-filter"}),t.jsx(T,{onSubmit:J,language:p&&p.language||i.DEFAULT_LANGUAGE,className:"cl-booking-filter",collapse:!i.isMobile})]})}),t.jsx("div",{className:"col-2",children:t.jsx(f,{containerClassName:"bookings",offset:R,language:p.language,loggedUser:p,suppliers:_,statuses:O,filter:I,loading:P,hideDates:i.isMobile,checkboxSelection:!i.isMobile})})]}),o[9]=b,o[10]=C,o[11]=I,o[12]=h,o[13]=P,o[14]=s,o[15]=R,o[16]=O,o[17]=_,o[18]=p,o[19]=Q):Q=o[19],o[20]!==Q?(V=t.jsx(c,{onLoad:$,strict:!0,children:Q}),o[20]=Q,o[21]=V):V=o[21],V};function D(e){return e.value}export{I as default};
