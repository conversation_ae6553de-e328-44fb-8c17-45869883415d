import{r as e}from"./router-BtYqujaw.js";import{a$ as a,aZ as o,a_ as t,i as r,j as n,aq as i,k as s,b2 as v}from"../entries/index-CEzJO5Xy.js";import{g as l,a as d,s as p,c as u,m as c}from"./Button-DGZYUY3P.js";function m(){const e=a(t);return e[o]||e}function f(e){return l("MuiPaper",e)}d("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const y=p("div",{name:"<PERSON><PERSON><PERSON><PERSON>",slot:"Root",overridesResolver:(e,a)=>{const{ownerState:o}=e;return[a.root,a[o.variant],!o.square&&a.rounded,"elevation"===o.variant&&a[`elevation${o.elevation}`]]}})(c((({theme:e})=>({backgroundColor:(e.vars||e).palette.background.paper,color:(e.vars||e).palette.text.primary,transition:e.transitions.create("box-shadow"),variants:[{props:({ownerState:e})=>!e.square,style:{borderRadius:e.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(e.vars||e).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]})))),P=e.forwardRef((function(e,a){const o=r({props:e,name:"MuiPaper"}),t=m(),{className:l,component:d="div",elevation:p=1,square:c=!1,variant:P="elevation",...b}=o,w={...o,component:d,elevation:p,square:c,variant:P},h=(e=>{const{square:a,elevation:o,variant:t,classes:r}=e;return u({root:["root",t,!a&&"rounded","elevation"===t&&`elevation${o}`]},f,r)})(w);return n.jsx(y,{as:d,ownerState:w,className:s(h.root,l),ref:a,...b,style:{..."elevation"===P&&{"--Paper-shadow":(t.vars||t).shadows[p],...t.vars&&{"--Paper-overlay":t.vars.overlays?.[p]},...!t.vars&&"dark"===t.palette.mode&&{"--Paper-overlay":`linear-gradient(${i("#fff",v(p))}, ${i("#fff",v(p))})`}},...b.style}})}));export{P,m as u};
