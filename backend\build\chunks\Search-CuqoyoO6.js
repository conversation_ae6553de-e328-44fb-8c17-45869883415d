import{c as e,j as o,a as s}from"../entries/index-CEzJO5Xy.js";import{r as t}from"./router-BtYqujaw.js";import{u as r,a as n,z as a,s as l}from"./zod-4O8Zwsja.js";import{T as i,I as m}from"./TextField-BAse--ht.js";import{I as c}from"./IconButton-CnBvmeAK.js";import{C as d}from"./Clear-BpXDeTL8.js";import{S as u}from"./Search-BNrZEqND.js";const p=a.object({keyword:a.string().optional()}),j=a=>{const j=e.c(26),{className:f,onSubmit:h}=a,x=t.useRef(null);let y;j[0]===Symbol.for("react.memo_cache_sentinel")?(y={resolver:l(p),mode:"onSubmit"},j[0]=y):y=j[0];const{register:S,handleSubmit:b,setValue:C,control:_}=r(y);let k;j[1]!==_?(k={control:_},j[1]=_,j[2]=k):k=j[2];const{keyword:w}=n(k);let R;j[3]!==h?(R=e=>{h&&h(e.keyword||"")},j[3]=h,j[4]=R):R=j[4];const g=R;let v,A,E,I,N,z,F,H,L;return j[5]!==g||j[6]!==b?(v=b(g),j[5]=g,j[6]=b,j[7]=v):v=j[7],j[8]===Symbol.for("react.memo_cache_sentinel")?(A=o.jsx("input",{autoComplete:"false",name:"hidden",type:"text",style:{display:"none"}}),j[8]=A):A=j[8],j[9]!==S?(E=S("keyword"),j[9]=S,j[10]=E):E=j[10],j[11]!==w||j[12]!==C?(I=w?o.jsx(m,{position:"end",children:o.jsx(c,{size:"small",onClick:()=>{C("keyword",""),x.current?.focus()},children:o.jsx(d,{style:{width:20,height:20}})})}):null,j[11]=w,j[12]=C,j[13]=I):I=j[13],j[14]!==I?(N={input:{endAdornment:I}},j[14]=I,j[15]=N):N=j[15],j[16]!==E||j[17]!==N?(z=o.jsx(i,{inputRef:x,variant:"standard",...E,placeholder:s.SEARCH_PLACEHOLDER,slotProps:N,className:"sc-search",id:"search"}),j[16]=E,j[17]=N,j[18]=z):z=j[18],j[19]===Symbol.for("react.memo_cache_sentinel")?(F=o.jsx(c,{type:"submit",children:o.jsx(u,{})}),j[19]=F):F=j[19],j[20]!==v||j[21]!==z?(H=o.jsxs("form",{autoComplete:"off",onSubmit:v,children:[A,z,F]}),j[20]=v,j[21]=z,j[22]=H):H=j[22],j[23]!==f||j[24]!==H?(L=o.jsx("div",{className:f,children:H}),j[23]=f,j[24]=H,j[25]=L):L=j[25],L};export{j as S};
