import { Request, Response } from 'express';
/**
 * Get all dresses.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const getDresses: (req: Request, res: Response) => Promise<void>;
/**
 * Get dress by ID.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const getDress: (req: Request, res: Response) => Promise<void>;
/**
 * Create a new dress.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const create: (req: Request, res: Response) => Promise<void>;
/**
 * Update a dress.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const update: (req: Request, res: Response) => Promise<void>;
/**
 * Delete a dress.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const deleteDress: (req: Request, res: Response) => Promise<void>;
/**
 * Check if a dress exists.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const checkDress: (req: Request, res: Response) => Promise<void>;
/**
 * Create a dress image.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const createImage: (req: Request, res: Response) => Promise<void>;
/**
 * Update a dress image.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const updateImage: (req: Request, res: Response) => Promise<void>;
/**
 * Delete a dress image.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const deleteImage: (req: Request, res: Response) => Promise<void>;
/**
 * Delete a temporary dress image.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const deleteTempImage: (req: Request, res: Response) => Promise<void>;
/**
 * Get booking dresses.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const getBookingDresses: (req: Request, res: Response) => Promise<void>;
/**
 * Get frontend dresses.
 *
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export declare const getFrontendDresses: (req: Request, res: Response) => Promise<void>;
