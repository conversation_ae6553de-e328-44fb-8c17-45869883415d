import 'dotenv/config'
import bcrypt from 'bcrypt'
import * as bookcarsTypes from ':bookcars-types'
import * as env from '../config/env.config'
import * as logger from '../common/logger'
import * as databaseHelper from '../common/databaseHelper'
import User from '../models/User'
import Country from '../models/Country'
import Location from '../models/Location'
import LocationValue from '../models/LocationValue'
import Dress from '../models/Dress'

/**
 * Initialize default data for dress rental system
 * Creates default supplier, location (Jenin City, Palestine), and sample dresses
 */
const initializeDefaultData = async (): Promise<boolean> => {
  try {
    logger.info('Initializing default data...')

    // Check if default supplier already exists
    const existingSupplier = await User.findOne({ email: '<EMAIL>' })
    if (existingSupplier) {
      logger.info('Default supplier already exists')
      return true
    }

    // Create default supplier
    const supplier = await createDefaultSupplier()
    logger.info('Default supplier created')

    // Create Palestine country
    const country = await createPalestineCountry(supplier._id!.toString())
    logger.info('Palestine country created')

    // Create Jenin City location
    const location = await createJeninLocation(country._id!.toString(), supplier._id!.toString())
    logger.info('Jenin City location created')

    // Create sample dresses
    await createSampleDresses(supplier._id!.toString(), location._id!.toString())
    logger.info('Sample dresses created')

    logger.info('Default data initialization completed successfully')
    return true
  } catch (err) {
    logger.error('Error while initializing default data:', err)
    return false
  }
}

/**
 * Create default supplier user
 */
const createDefaultSupplier = async () => {
  const salt = await bcrypt.genSalt(10)
  const passwordHash = await bcrypt.hash('supplier123', salt)

  const supplier = new User({
    email: '<EMAIL>',
    fullName: 'Jenin Dress Boutique',
    password: passwordHash,
    language: 'ar',
    type: bookcarsTypes.UserType.Supplier,
    active: true,
    verified: true,
    blacklisted: false,
    enableEmailNotifications: true,
    phone: '**********',
    location: 'Jenin City, Palestine',
    bio: 'Premium dress rental boutique in Jenin City specializing in wedding and evening gowns',
    payLater: true,
    licenseRequired: false,
    minimumRentalDays: 1,
    supplierDressLimit: 100,
    notifyAdminOnNewDress: true
  })

  await supplier.save()
  return supplier
}

/**
 * Create Palestine country with Arabic and English names
 */
const createPalestineCountry = async (supplierId: string) => {
  // Create location values for Palestine
  const palestineValues = [
    new LocationValue({
      language: 'en',
      value: 'Palestine'
    }),
    new LocationValue({
      language: 'fr',
      value: 'Palestine'
    }),
    new LocationValue({
      language: 'es',
      value: 'Palestina'
    })
  ]

  await LocationValue.insertMany(palestineValues)

  // Create country
  const country = new Country({
    values: palestineValues.map(v => v._id),
    supplier: supplierId
  })

  await country.save()
  return country
}

/**
 * Create Jenin City location
 */
const createJeninLocation = async (countryId: string, supplierId: string) => {
  // Create location values for Jenin City
  const jeninValues = [
    new LocationValue({
      language: 'en',
      value: 'Jenin City'
    }),
    new LocationValue({
      language: 'fr',
      value: 'Ville de Jenin'
    }),
    new LocationValue({
      language: 'es',
      value: 'Ciudad de Jenin'
    })
  ]

  await LocationValue.insertMany(jeninValues)

  // Create location (Jenin coordinates)
  const location = new Location({
    country: countryId,
    latitude: 32.4603,
    longitude: 35.2957,
    values: jeninValues.map(v => v._id),
    supplier: supplierId
  })

  await location.save()
  return location
}

/**
 * Create sample dresses
 */
const createSampleDresses = async (supplierId: string, locationId: string) => {
  const sampleDresses = [
    {
      name: 'Elegant Wedding Gown',
      supplier: supplierId,
      minimumAge: 18,
      locations: [locationId],
      dailyPrice: 500,
      deposit: 100,
      available: true,
      type: bookcarsTypes.DressType.Wedding,
      size: bookcarsTypes.DressSize.M,
      color: 'White',
      length: 180,
      material: bookcarsTypes.DressMaterial.Silk,
      cancellation: 24,
      amendments: 24,
      range: bookcarsTypes.DressRange.Bridal,
      accessories: [bookcarsTypes.DressAccessories.Veil],
      rentals: 0,
      designerName: 'Elegant Designs'
    },
    {
      name: 'Evening Party Dress',
      supplier: supplierId,
      minimumAge: 18,
      locations: [locationId],
      dailyPrice: 200,
      deposit: 50,
      available: true,
      type: bookcarsTypes.DressType.Evening,
      size: bookcarsTypes.DressSize.S,
      color: 'Black',
      length: 160,
      material: bookcarsTypes.DressMaterial.Chiffon,
      cancellation: 12,
      amendments: 12,
      range: bookcarsTypes.DressRange.Evening,
      accessories: [bookcarsTypes.DressAccessories.Jewelry],
      rentals: 0,
      designerName: 'Night Elegance'
    },
    {
      name: 'Cocktail Dress',
      supplier: supplierId,
      minimumAge: 18,
      locations: [locationId],
      dailyPrice: 150,
      deposit: 30,
      available: true,
      type: bookcarsTypes.DressType.Cocktail,
      size: bookcarsTypes.DressSize.L,
      color: 'Red',
      length: 120,
      material: bookcarsTypes.DressMaterial.Satin,
      cancellation: 6,
      amendments: 6,
      range: bookcarsTypes.DressRange.Cocktail,
      accessories: [bookcarsTypes.DressAccessories.Shoes],
      rentals: 0,
      designerName: 'Cocktail Couture'
    }
  ]

  await Dress.insertMany(sampleDresses)
  logger.info(`Created ${sampleDresses.length} sample dresses`)
}

// Run the initialization if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    if (await databaseHelper.connect(env.DB_URI, env.DB_SSL, env.DB_DEBUG)) {
      await initializeDefaultData()
      await databaseHelper.close()
      logger.info('Database connection closed')
      process.exit(0)
    } else {
      logger.error('Failed to connect to database')
      process.exit(1)
    }
  })()
}

export { initializeDefaultData }
