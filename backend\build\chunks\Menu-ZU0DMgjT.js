import{r as e}from"./router-BtYqujaw.js";import{i as t,j as o,k as r,as as n,m as s}from"../entries/index-CEzJO5Xy.js";import{g as i,a,s as l,c as p,u,r as c}from"./Button-DGZYUY3P.js";import{u as d}from"./useSlot-CtA82Ni6.js";import{g as f,G as m,M as h,u as g}from"./Grow-CjOKj0i1.js";import{d as b,i as v}from"./isHostComponent-DR4iSCFs.js";import{P}from"./Paper-CcwAvfvc.js";import{a as w,o as y}from"./ownerWindow-ChLfdzZL.js";import{m as x}from"./mergeSlotProps-Cay5TZBz.js";const M=e.createContext({});function S(e){return i("MuiList",e)}a("MuiList",["root","padding","dense","subheader"]);const k=l("ul",{name:"MuiList",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,!o.disablePadding&&t.padding,o.dense&&t.dense,o.subheader&&t.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:({ownerState:e})=>!e.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:e})=>e.subheader,style:{paddingTop:0}}]}),C=e.forwardRef((function(n,s){const i=t({props:n,name:"MuiList"}),{children:a,className:l,component:u="ul",dense:c=!1,disablePadding:d=!1,subheader:f,...m}=i,h=e.useMemo((()=>({dense:c})),[c]),g={...i,component:u,dense:c,disablePadding:d},b=(e=>{const{classes:t,disablePadding:o,dense:r,subheader:n}=e;return p({root:["root",!o&&"padding",r&&"dense",n&&"subheader"]},S,t)})(g);return o.jsx(M.Provider,{value:h,children:o.jsxs(k,{as:u,className:r(b.root,l),ref:s,ownerState:g,...m,children:[f,a]})})}));function E(e,t,o){return e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:o?null:e.firstChild}function j(e,t,o){return e===t?o?e.firstChild:e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:o?null:e.lastChild}function F(e,t){if(void 0===t)return!0;let o=e.innerText;return void 0===o&&(o=e.textContent),o=o.trim().toLowerCase(),0!==o.length&&(t.repeating?o[0]===t.keys[0]:o.startsWith(t.keys.join("")))}function T(e,t,o,r,n,s){let i=!1,a=n(e,t,!!t&&o);for(;a;){if(a===e.firstChild){if(i)return!1;i=!0}const t=!r&&(a.disabled||"true"===a.getAttribute("aria-disabled"));if(a.hasAttribute("tabindex")&&F(a,s)&&!t)return a.focus(),!0;a=n(e,a,o)}return!1}const L=e.forwardRef((function(t,r){const{actions:s,autoFocus:i=!1,autoFocusItem:a=!1,children:l,className:p,disabledItemsFocusable:c=!1,disableListWrap:d=!1,onKeyDown:m,variant:h="selectedMenu",...g}=t,b=e.useRef(null),v=e.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});n((()=>{i&&b.current.focus()}),[i]),e.useImperativeHandle(s,(()=>({adjustStyleForScrollbar:(e,{direction:t})=>{const o=!b.current.style.width;if(e.clientHeight<b.current.clientHeight&&o){const o=`${f(w(e))}px`;b.current.style["rtl"===t?"paddingLeft":"paddingRight"]=o,b.current.style.width=`calc(100% + ${o})`}return b.current}})),[]);const P=u(b,r);let x=-1;e.Children.forEach(l,((t,o)=>{e.isValidElement(t)?(t.props.disabled||("selectedMenu"===h&&t.props.selected||-1===x)&&(x=o),x===o&&(t.props.disabled||t.props.muiSkipListHighlight||t.type.muiSkipListHighlight)&&(x+=1,x>=l.length&&(x=-1))):x===o&&(x+=1,x>=l.length&&(x=-1))}));const M=e.Children.map(l,((t,o)=>{if(o===x){const o={};return a&&(o.autoFocus=!0),void 0===t.props.tabIndex&&"selectedMenu"===h&&(o.tabIndex=0),e.cloneElement(t,o)}return t}));return o.jsx(C,{role:"menu",ref:P,className:p,onKeyDown:e=>{const t=b.current,o=e.key;if(e.ctrlKey||e.metaKey||e.altKey)return void(m&&m(e));const r=y(t).activeElement;if("ArrowDown"===o)e.preventDefault(),T(t,r,d,c,E);else if("ArrowUp"===o)e.preventDefault(),T(t,r,d,c,j);else if("Home"===o)e.preventDefault(),T(t,null,d,c,E);else if("End"===o)e.preventDefault(),T(t,null,d,c,j);else if(1===o.length){const n=v.current,s=o.toLowerCase(),i=performance.now();n.keys.length>0&&(i-n.lastTime>500?(n.keys=[],n.repeating=!0,n.previousKeyMatched=!0):n.repeating&&s!==n.keys[0]&&(n.repeating=!1)),n.lastTime=i,n.keys.push(s);const a=r&&!n.repeating&&F(r,n);n.previousKeyMatched&&(a||T(t,r,!1,c,E,n))?e.preventDefault():n.previousKeyMatched=!1}m&&m(e)},tabIndex:i?0:-1,...g,children:M})}));function D(e){return i("MuiPopover",e)}function R(e,t){let o=0;return"number"==typeof t?o=t:"center"===t?o=e.height/2:"bottom"===t&&(o=e.height),o}function z(e,t){let o=0;return"number"==typeof t?o=t:"center"===t?o=e.width/2:"right"===t&&(o=e.width),o}function H(e){return[e.horizontal,e.vertical].map((e=>"number"==typeof e?`${e}px`:e)).join(" ")}function K(e){return"function"==typeof e?e():e}a("MuiPopover",["root","paper"]);const N=l(h,{name:"MuiPopover",slot:"Root"})({}),O=l(P,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),I=e.forwardRef((function(n,s){const i=t({props:n,name:"MuiPopover"}),{action:a,anchorEl:l,anchorOrigin:u={vertical:"top",horizontal:"left"},anchorPosition:c,anchorReference:f="anchorEl",children:h,className:g,container:P,elevation:M=8,marginThreshold:S=16,open:k,PaperProps:C={},slots:E={},slotProps:j={},transformOrigin:F={vertical:"top",horizontal:"left"},TransitionComponent:T,transitionDuration:L="auto",TransitionProps:I={},disableScrollLock:W=!1,...A}=i,$=e.useRef(),B={...i,anchorOrigin:u,anchorReference:f,elevation:M,marginThreshold:S,transformOrigin:F,TransitionComponent:T,transitionDuration:L,TransitionProps:I},G=(e=>{const{classes:t}=e;return p({root:["root"],paper:["paper"]},D,t)})(B),V=e.useCallback((()=>{if("anchorPosition"===f)return c;const e=K(l),t=(e&&1===e.nodeType?e:y($.current).body).getBoundingClientRect();return{top:t.top+R(t,u.vertical),left:t.left+z(t,u.horizontal)}}),[l,u.horizontal,u.vertical,c,f]),Q=e.useCallback((e=>({vertical:R(e,F.vertical),horizontal:z(e,F.horizontal)})),[F.horizontal,F.vertical]),U=e.useCallback((e=>{const t={width:e.offsetWidth,height:e.offsetHeight},o=Q(t);if("none"===f)return{top:null,left:null,transformOrigin:H(o)};const r=V();let n=r.top-o.vertical,s=r.left-o.horizontal;const i=n+t.height,a=s+t.width,p=w(K(l)),u=p.innerHeight-S,c=p.innerWidth-S;if(null!==S&&n<S){const e=n-S;n-=e,o.vertical+=e}else if(null!==S&&i>u){const e=i-u;n-=e,o.vertical+=e}if(null!==S&&s<S){const e=s-S;s-=e,o.horizontal+=e}else if(a>c){const e=a-c;s-=e,o.horizontal+=e}return{top:`${Math.round(n)}px`,left:`${Math.round(s)}px`,transformOrigin:H(o)}}),[l,f,V,Q,S]),[X,Y]=e.useState(k),q=e.useCallback((()=>{const e=$.current;if(!e)return;const t=U(e);null!==t.top&&e.style.setProperty("top",t.top),null!==t.left&&(e.style.left=t.left),e.style.transformOrigin=t.transformOrigin,Y(!0)}),[U]);e.useEffect((()=>(W&&window.addEventListener("scroll",q),()=>window.removeEventListener("scroll",q))),[l,W,q]),e.useEffect((()=>{k&&q()})),e.useImperativeHandle(a,(()=>k?{updatePosition:()=>{q()}}:null),[k,q]),e.useEffect((()=>{if(!k)return;const e=b((()=>{q()})),t=w(K(l));return t.addEventListener("resize",e),()=>{e.clear(),t.removeEventListener("resize",e)}}),[l,k,q]);let J=L;const Z={slots:{transition:T,...E},slotProps:{transition:I,paper:C,...j}},[_,ee]=d("transition",{elementType:m,externalForwardedProps:Z,ownerState:B,getSlotProps:e=>({...e,onEntering:(t,o)=>{e.onEntering?.(t,o),q()},onExited:t=>{e.onExited?.(t),Y(!1)}}),additionalProps:{appear:!0,in:k}});"auto"!==L||_.muiSupportAuto||(J=void 0);const te=P||(l?y(K(l)).body:void 0),[oe,{slots:re,slotProps:ne,...se}]=d("root",{ref:s,elementType:N,externalForwardedProps:{...Z,...A},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:E.backdrop},slotProps:{backdrop:x("function"==typeof j.backdrop?j.backdrop(B):j.backdrop,{invisible:!0})},container:te,open:k},ownerState:B,className:r(G.root,g)}),[ie,ae]=d("paper",{ref:$,className:G.paper,elementType:O,externalForwardedProps:Z,shouldForwardComponentProp:!0,additionalProps:{elevation:M,style:X?void 0:{opacity:0}},ownerState:B});return o.jsx(oe,{...se,...!v(oe)&&{slots:re,slotProps:ne,disableScrollLock:W},children:o.jsx(_,{...ee,timeout:J,children:o.jsx(ie,{...ae,children:h})})})}));function W(e){return i("MuiMenu",e)}a("MuiMenu",["root","paper","list"]);const A={vertical:"top",horizontal:"right"},$={vertical:"top",horizontal:"left"},B=l(I,{shouldForwardProp:e=>c(e)||"classes"===e,name:"MuiMenu",slot:"Root"})({}),G=l(O,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),V=l(L,{name:"MuiMenu",slot:"List"})({outline:0}),Q=e.forwardRef((function(n,i){const a=t({props:n,name:"MuiMenu"}),{autoFocus:l=!0,children:u,className:c,disableAutoFocusItem:f=!1,MenuListProps:m={},onClose:h,open:b,PaperProps:v={},PopoverClasses:P,transitionDuration:w="auto",TransitionProps:{onEntering:y,...x}={},variant:M="selectedMenu",slots:S={},slotProps:k={},...C}=a,E=s(),j={...a,autoFocus:l,disableAutoFocusItem:f,MenuListProps:m,onEntering:y,PaperProps:v,transitionDuration:w,TransitionProps:x,variant:M},F=(e=>{const{classes:t}=e;return p({root:["root"],paper:["paper"],list:["list"]},W,t)})(j),T=l&&!f&&b,L=e.useRef(null);let D=-1;e.Children.map(u,((t,o)=>{e.isValidElement(t)&&(t.props.disabled||("selectedMenu"===M&&t.props.selected||-1===D)&&(D=o))}));const R={slots:S,slotProps:{list:m,transition:x,paper:v,...k}},z=g({elementType:S.root,externalSlotProps:k.root,ownerState:j,className:[F.root,c]}),[H,K]=d("paper",{className:F.paper,elementType:G,externalForwardedProps:R,shouldForwardComponentProp:!0,ownerState:j}),[N,O]=d("list",{className:r(F.list,m.className),elementType:V,shouldForwardComponentProp:!0,externalForwardedProps:R,getSlotProps:e=>({...e,onKeyDown:t=>{(e=>{"Tab"===e.key&&(e.preventDefault(),h&&h(e,"tabKeyDown"))})(t),e.onKeyDown?.(t)}}),ownerState:j}),I="function"==typeof R.slotProps.transition?R.slotProps.transition(j):R.slotProps.transition;return o.jsx(B,{onClose:h,anchorOrigin:{vertical:"bottom",horizontal:E?"right":"left"},transformOrigin:E?A:$,slots:{root:S.root,paper:H,backdrop:S.backdrop,...S.transition&&{transition:S.transition}},slotProps:{root:z,paper:K,backdrop:"function"==typeof k.backdrop?k.backdrop(j):k.backdrop,transition:{...I,onEntering:(...e)=>{((e,t)=>{L.current&&L.current.adjustStyleForScrollbar(e,{direction:E?"rtl":"ltr"}),y&&y(e,t)})(...e),I?.onEntering?.(...e)}}},open:b,ref:i,transitionDuration:w,ownerState:j,...C,classes:P,children:o.jsx(N,{actions:L,autoFocus:l&&(-1===D||f),autoFocusItem:T,variant:M,...O,children:u})})}));export{C as L,Q as M,I as P,L as a,M as b};
