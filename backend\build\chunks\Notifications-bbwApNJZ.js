import{j as e,b as s,s as i,n as a,aD as t,z as o,e as r,J as c,aE as n,aF as l,ai as d,K as E,a as m,aG as _,c as h}from"../entries/index-CEzJO5Xy.js";import{d as A,r as j}from"./router-BtYqujaw.js";import{L as u}from"./Layout-BQBjg4Lf.js";import{S as f}from"./SimpleBackdrop-Bf3qjF13.js";import{C as L,a as x,P as I,N}from"./ArrowForwardIos-BMce9t8T.js";import{T}from"./Backdrop-Bzn12VyM.js";import{C as R}from"./Checkbox-CDqupZJG.js";import{T as S}from"./Tooltip-BkJF6Mu0.js";import{I as p}from"./IconButton-CnBvmeAK.js";import{c as D,D as g,a as M,b as k}from"./Grow-CjOKj0i1.js";import{D as C}from"./Delete-CnqjtpsJ.js";import{V as v}from"./Visibility-BpW589Gm.js";import{D as y}from"./DialogTitle-BZXwroUN.js";import{B as O}from"./Button-DGZYUY3P.js";import{f as K,e as P}from"./format-4arn0GRM.js";import{f as w}from"./fr-CaQg1DLH.js";import"./vendor-dblfw9z9.js";import"./Paper-CcwAvfvc.js";import"./useSlot-CtA82Ni6.js";import"./SwitchBase-BIeqtL5F.js";import"./useFormControl-B7jXtRD7.js";import"./mergeSlotProps-Cay5TZBz.js";import"./ownerWindow-ChLfdzZL.js";const b=D(e.jsx("path",{d:"M21.99 8c0-.72-.37-1.35-.94-1.7L12 1 2.95 6.3C2.38 6.65 2 7.28 2 8v10c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2zM12 13 3.74 7.84 12 3l8.26 4.84z"})),F=D(e.jsx("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"})),G=new s({fr:{EMPTY_LIST:"Pas de notifications",VIEW:"Consulter",MARK_AS_READ:"Marquer comme lu",MARK_AS_UNREAD:"Marquer comme non lu",MARK_ALL_AS_READ:"Tout marquer comme lu",MARK_ALL_AS_UNREAD:"Tout marquer comme non lu",DELETE_ALL:"Tout supprimer",DELETE_NOTIFICATION:"Êtes-vous sûr de vouloir supprimer cette notification ?",DELETE_NOTIFICATIONS:"Êtes-vous sûr de vouloir supprimer ces notifications ?"},en:{EMPTY_LIST:"No notifications",VIEW:"View",MARK_AS_READ:"Mark as read",MARK_AS_UNREAD:"Mark as unread",MARK_ALL_AS_READ:"Mark all as read",MARK_ALL_AS_UNREAD:"Mark all as unread",DELETE_ALL:"Delete all",DELETE_NOTIFICATION:"Are you sure you want to delete this notification?",DELETE_NOTIFICATIONS:"Are you sure you want to delete these notifications?"},es:{EMPTY_LIST:"No hay notificaciones",VIEW:"Ver",MARK_AS_READ:"Marcar como leído",MARK_AS_UNREAD:"Marcar como no leído",MARK_ALL_AS_READ:"Marcar todo como leído",MARK_ALL_AS_UNREAD:"Marcar todo como no leído",DELETE_ALL:"Eliminar todo",DELETE_NOTIFICATION:"¿Estás seguro de que quieres eliminar esta notificación?",DELETE_NOTIFICATIONS:"¿Estás seguro de que quieres eliminar estas notificaciones?"},ar:{EMPTY_LIST:"لا توجد إشعارات",VIEW:"عرض",MARK_AS_READ:"تحديد كمقروء",MARK_AS_UNREAD:"تحديد كغير مقروء",MARK_ALL_AS_READ:"تحديد الكل كمقروء",MARK_ALL_AS_UNREAD:"تحديد الكل كغير مقروء",DELETE_ALL:"حذف الكل",DELETE_NOTIFICATION:"هل أنت متأكد من أنك تريد حذف هذا الإشعار؟",DELETE_NOTIFICATIONS:"هل أنت متأكد من أنك تريد حذف هذه الإشعارات؟"}});i(G);const Z=({user:s})=>{const i=A(),{setNotificationCount:h}=a(),[u,D]=j.useState(!0),[Z,U]=j.useState(1),[V,q]=j.useState([]),[W,$]=j.useState(-1),[B,Y]=j.useState(-1),[z,H]=j.useState(!1),[J,Q]=j.useState([]),X=j.useRef(null),ee=s&&"fr"===s.language,se=ee?w:P,ie=ee?"eee d LLLL, kk:mm":"eee, d LLLL, kk:mm",ae=j.useCallback((async()=>{if(s&&s._id)try{D(!0);const e=await t(s._id,Z),i=e&&e.length>0?e[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!i)return void o();const a=i.resultData.map((e=>({checked:!1,...e}))),c=Array.isArray(i.pageInfo)&&i.pageInfo.length>0?i.pageInfo[0].totalRecords:0;Y(c),$((Z-1)*r.PAGE_SIZE+a.length),q(a),X.current&&X.current.scrollTo(0,0),D(!1)}catch(e){o(e)}}),[s,Z]);j.useEffect((()=>{ae()}),[ae]);const te=V.filter((e=>e.checked)),oe=V.length>0&&te.length===V.length,re=te.length>0&&te.length<V.length;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"notifications",children:[0===B&&e.jsx(L,{variant:"outlined",className:"empty-list",children:e.jsx(x,{children:e.jsx(T,{color:"textSecondary",children:G.EMPTY_LIST})})}),B>0&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"header-container",children:e.jsxs("div",{className:"header",children:[e.jsx("div",{className:"header-checkbox",children:e.jsx(R,{checked:oe,indeterminate:re,onChange:e=>{re?V.forEach((e=>{e.checked=!1})):V.forEach((s=>{s.checked=e.target.checked})),q(c(V))}})}),te.length>0&&e.jsxs("div",{className:"header-actions",children:[te.some((e=>!e.isRead))&&e.jsx(S,{title:G.MARK_ALL_AS_READ,children:e.jsx(p,{onClick:async()=>{try{if(!s||!s._id)return void o();const e=te.filter((e=>!e.isRead)),i=e.map((e=>e._id));if(200===await n(s._id,i)){const s=c(V);s.filter((e=>i.includes(e._id))).forEach((e=>{e.isRead=!0})),q(s),h((s=>s-e.length))}else o()}catch(e){o(e)}},children:e.jsx(b,{})})}),te.some((e=>e.isRead))&&e.jsx(S,{title:G.MARK_ALL_AS_UNREAD,children:e.jsx(p,{onClick:async()=>{try{if(!s||!s._id)return void o();const e=te.filter((e=>e.isRead)),i=e.map((e=>e._id));if(200===await l(s._id,i)){const s=c(V);s.filter((e=>i.includes(e._id))).forEach((e=>{e.isRead=!1})),q(s),h((s=>s+e.length))}else o()}catch(e){o(e)}},children:e.jsx(F,{})})}),e.jsx(S,{title:G.DELETE_ALL,children:e.jsx(p,{onClick:()=>{Q(te),H(!0)},children:e.jsx(C,{})})})]})]})}),e.jsx("div",{ref:X,className:"notifications-list",children:V.map(((a,t)=>e.jsxs("div",{className:"notification-container",children:[e.jsx("div",{className:"notification-checkbox",children:e.jsx(R,{checked:a.checked,onChange:e=>{a.checked=e.target.checked,q(c(V))}})}),e.jsxs("div",{className:"notification"+(a.isRead?"":" unread"),children:[e.jsx("div",{className:"date",children:a.createdAt&&d(K(new Date(a.createdAt),ie,{locale:se}))}),e.jsxs("div",{className:"message-container",children:[e.jsx("div",{className:"message",children:a.message}),e.jsxs("div",{className:"actions",children:[(a.booking||a.dress)&&e.jsx(S,{title:G.VIEW,children:e.jsx(p,{onClick:async()=>{try{if(!s||!s._id)return void o();const e=()=>{const e=a.booking?`/update-booking?b=${a.booking}`:`/update-dress?dr=${a.dress}`;i(e)};if(a.isRead)e();else if(200===await n(s._id,[a._id])){const s=E(V);s[t].isRead=!0,q(s),h((e=>e-1)),e()}else o()}catch(e){o(e)}},children:e.jsx(v,{})})}),a.isRead?e.jsx(S,{title:G.MARK_AS_UNREAD,children:e.jsx(p,{onClick:async()=>{try{if(!s||!s._id)return void o();if(200===await l(s._id,[a._id])){const e=E(V);e[t].isRead=!1,q(e),h((e=>e+1))}else o()}catch(e){o(e)}},children:e.jsx(F,{})})}):e.jsx(S,{title:G.MARK_AS_READ,children:e.jsx(p,{onClick:async()=>{try{if(!s||!s._id)return void o();if(200===await n(s._id,[a._id])){const e=E(V);e[t].isRead=!0,q(e),h((e=>e-1))}else o()}catch(e){o(e)}},children:e.jsx(b,{})})}),e.jsx(S,{title:m.DELETE,children:e.jsx(p,{onClick:()=>{Q([a]),H(!0)},children:e.jsx(C,{})})})]})]})]})]},a._id)))}),e.jsxs("div",{className:"footer",children:[W>-1&&e.jsx("div",{className:"row-count",children:`${(Z-1)*r.PAGE_SIZE+1}-${W} ${m.OF} ${B}`}),e.jsxs("div",{className:"actions",children:[e.jsx(p,{disabled:1===Z,onClick:()=>{const e=Z-1;$(e<Math.ceil(B/r.PAGE_SIZE)?(e-1)*r.PAGE_SIZE+r.PAGE_SIZE:B),U(e)},children:e.jsx(I,{className:"icon"})}),e.jsx(p,{disabled:(Z-1)*r.PAGE_SIZE+V.length>=B,onClick:()=>{const e=Z+1;$(e<Math.ceil(B/r.PAGE_SIZE)?(e-1)*r.PAGE_SIZE+r.PAGE_SIZE:B),U(e)},children:e.jsx(N,{className:"icon"})})]})]}),e.jsxs(g,{disableEscapeKeyDown:!0,maxWidth:"xs",open:z,children:[e.jsx(y,{className:"dialog-header",children:m.CONFIRM_TITLE}),e.jsx(M,{children:J.length>1?G.DELETE_NOTIFICATIONS:G.DELETE_NOTIFICATION}),e.jsxs(k,{className:"dialog-actions",children:[e.jsx(O,{onClick:()=>{H(!1)},variant:"contained",className:"btn-secondary",children:m.CANCEL}),e.jsx(O,{onClick:async()=>{try{if(!s||!s._id)return void o();const e=J.map((e=>e._id));if(200===await _(s._id,e)){if(J.length===V.length){const e=1,s=B-J.length;$(e<Math.ceil(s/r.PAGE_SIZE)?(e-1)*r.PAGE_SIZE+r.PAGE_SIZE:s),Z>1?U(1):ae()}else{const s=c(V);q(s.filter((s=>!e.includes(s._id)))),$(W-J.length),Y(B-J.length)}h((e=>e-J.filter((e=>!e.isRead)).length)),H(!1)}else o()}catch(e){o(e)}},variant:"contained",color:"error",children:m.DELETE})]})]})]})]}),u&&e.jsx(f,{text:m.LOADING})]})},U=()=>{const s=h.c(3),[i,a]=j.useState();let t;s[0]===Symbol.for("react.memo_cache_sentinel")?(t=async e=>{a(e)},s[0]=t):t=s[0];const o=t;let r;return s[1]!==i?(r=e.jsx(u,{onLoad:o,strict:!0,children:e.jsx(Z,{user:i})}),s[1]=i,s[2]=r):r=s[2],r};export{U as default};
