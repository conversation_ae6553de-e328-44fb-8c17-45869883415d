import{j as e,b as s,s as i,n as a,aG as t,z as o,e as r,J as c,aH as n,aI as l,ag as d,K as h,a as m,aJ as E,c as _}from"../entries/index-xsXxT3-W.js";import{d as A,r as j}from"./router-BtYqujaw.js";import{L as u}from"./Layout-DaeN7D4t.js";import{S as f}from"./SimpleBackdrop-CqsJhYJ4.js";import{C as x,a as L,P as I,N}from"./ArrowForwardIos-BCaVe-sv.js";import{T as S}from"./Backdrop-Czag2Ija.js";import{C as p}from"./Checkbox-F0AjCtoF.js";import{T as R}from"./Tooltip-CKMkVqOx.js";import{I as T}from"./IconButton-CxOCoGF3.js";import{c as g,D as k,a as D,b as M,d as v}from"./Grow-Cp8xsNYl.js";import{D as C}from"./Delete-BfnPAJno.js";import{V as y}from"./Visibility-D3efFHY1.js";import{B as P}from"./Button-BeKLLPpp.js";import{f as w,a as O,e as K}from"./fr-DJt_zj3p.js";import"./vendor-dblfw9z9.js";import"./Paper-C-atefOs.js";import"./useSlot-DiTut-u0.js";import"./SwitchBase-DrUkTXjH.js";import"./useFormControl-B7jXtRD7.js";import"./mergeSlotProps-DEridHif.js";import"./ownerWindow-ChLfdzZL.js";const b=g(e.jsx("path",{d:"M21.99 8c0-.72-.37-1.35-.94-1.7L12 1 2.95 6.3C2.38 6.65 2 7.28 2 8v10c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2zM12 13 3.74 7.84 12 3l8.26 4.84z"})),F=g(e.jsx("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"})),G=new s({fr:{EMPTY_LIST:"Pas de notifications",VIEW:"Consulter",MARK_AS_READ:"Marquer comme lu",MARK_AS_UNREAD:"Marquer comme non lu",MARK_ALL_AS_READ:"Tout marquer comme lu",MARK_ALL_AS_UNREAD:"Tout marquer comme non lu",DELETE_ALL:"Tout supprimer",DELETE_NOTIFICATION:"Êtes-vous sûr de vouloir supprimer cette notification ?",DELETE_NOTIFICATIONS:"Êtes-vous sûr de vouloir supprimer ces notifications ?"},en:{EMPTY_LIST:"No notifications",VIEW:"View",MARK_AS_READ:"Mark as read",MARK_AS_UNREAD:"Mark as unread",MARK_ALL_AS_READ:"Mark all as read",MARK_ALL_AS_UNREAD:"Mark all as unread",DELETE_ALL:"Delete all",DELETE_NOTIFICATION:"Are you sure you want to delete this notification?",DELETE_NOTIFICATIONS:"Are you sure you want to delete these notifications?"},es:{EMPTY_LIST:"No hay notificaciones",VIEW:"Ver",MARK_AS_READ:"Marcar como leído",MARK_AS_UNREAD:"Marcar como no leído",MARK_ALL_AS_READ:"Marcar todo como leído",MARK_ALL_AS_UNREAD:"Marcar todo como no leído",DELETE_ALL:"Eliminar todo",DELETE_NOTIFICATION:"¿Estás seguro de que quieres eliminar esta notificación?",DELETE_NOTIFICATIONS:"¿Estás seguro de que quieres eliminar estas notificaciones?"}});i(G);const Z=({user:s})=>{const i=A(),{setNotificationCount:_}=a(),[u,g]=j.useState(!0),[Z,V]=j.useState(1),[U,q]=j.useState([]),[$,B]=j.useState(-1),[W,z]=j.useState(-1),[Y,J]=j.useState(!1),[H,Q]=j.useState([]),X=j.useRef(null),ee=s&&"fr"===s.language,se=ee?O:K,ie=ee?"eee d LLLL, kk:mm":"eee, d LLLL, kk:mm",ae=j.useCallback((async()=>{if(s&&s._id)try{g(!0);const e=await t(s._id,Z),i=e&&e.length>0?e[0]:{pageInfo:{totalRecord:0},resultData:[]};if(!i)return void o();const a=i.resultData.map((e=>({checked:!1,...e}))),c=Array.isArray(i.pageInfo)&&i.pageInfo.length>0?i.pageInfo[0].totalRecords:0;z(c),B((Z-1)*r.PAGE_SIZE+a.length),q(a),X.current&&X.current.scrollTo(0,0),g(!1)}catch(e){o(e)}}),[s,Z]);j.useEffect((()=>{ae()}),[ae]);const te=U.filter((e=>e.checked)),oe=U.length>0&&te.length===U.length,re=te.length>0&&te.length<U.length;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"notifications",children:[0===W&&e.jsx(x,{variant:"outlined",className:"empty-list",children:e.jsx(L,{children:e.jsx(S,{color:"textSecondary",children:G.EMPTY_LIST})})}),W>0&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"header-container",children:e.jsxs("div",{className:"header",children:[e.jsx("div",{className:"header-checkbox",children:e.jsx(p,{checked:oe,indeterminate:re,onChange:e=>{re?U.forEach((e=>{e.checked=!1})):U.forEach((s=>{s.checked=e.target.checked})),q(c(U))}})}),te.length>0&&e.jsxs("div",{className:"header-actions",children:[te.some((e=>!e.isRead))&&e.jsx(R,{title:G.MARK_ALL_AS_READ,children:e.jsx(T,{onClick:async()=>{try{if(!s||!s._id)return void o();const e=te.filter((e=>!e.isRead)),i=e.map((e=>e._id));if(200===await n(s._id,i)){const s=c(U);s.filter((e=>i.includes(e._id))).forEach((e=>{e.isRead=!0})),q(s),_((s=>s-e.length))}else o()}catch(e){o(e)}},children:e.jsx(b,{})})}),te.some((e=>e.isRead))&&e.jsx(R,{title:G.MARK_ALL_AS_UNREAD,children:e.jsx(T,{onClick:async()=>{try{if(!s||!s._id)return void o();const e=te.filter((e=>e.isRead)),i=e.map((e=>e._id));if(200===await l(s._id,i)){const s=c(U);s.filter((e=>i.includes(e._id))).forEach((e=>{e.isRead=!1})),q(s),_((s=>s+e.length))}else o()}catch(e){o(e)}},children:e.jsx(F,{})})}),e.jsx(R,{title:G.DELETE_ALL,children:e.jsx(T,{onClick:()=>{Q(te),J(!0)},children:e.jsx(C,{})})})]})]})}),e.jsx("div",{ref:X,className:"notifications-list",children:U.map(((a,t)=>e.jsxs("div",{className:"notification-container",children:[e.jsx("div",{className:"notification-checkbox",children:e.jsx(p,{checked:a.checked,onChange:e=>{a.checked=e.target.checked,q(c(U))}})}),e.jsxs("div",{className:"notification"+(a.isRead?"":" unread"),children:[e.jsx("div",{className:"date",children:a.createdAt&&d(w(new Date(a.createdAt),ie,{locale:se}))}),e.jsxs("div",{className:"message-container",children:[e.jsx("div",{className:"message",children:a.message}),e.jsxs("div",{className:"actions",children:[(a.booking||a.dress)&&e.jsx(R,{title:G.VIEW,children:e.jsx(T,{onClick:async()=>{try{if(!s||!s._id)return void o();const e=()=>{const e=a.booking?`/update-booking?b=${a.booking}`:`/update-dress?dr=${a.dress}`;i(e)};if(a.isRead)e();else if(200===await n(s._id,[a._id])){const s=h(U);s[t].isRead=!0,q(s),_((e=>e-1)),e()}else o()}catch(e){o(e)}},children:e.jsx(y,{})})}),a.isRead?e.jsx(R,{title:G.MARK_AS_UNREAD,children:e.jsx(T,{onClick:async()=>{try{if(!s||!s._id)return void o();if(200===await l(s._id,[a._id])){const e=h(U);e[t].isRead=!1,q(e),_((e=>e+1))}else o()}catch(e){o(e)}},children:e.jsx(F,{})})}):e.jsx(R,{title:G.MARK_AS_READ,children:e.jsx(T,{onClick:async()=>{try{if(!s||!s._id)return void o();if(200===await n(s._id,[a._id])){const e=h(U);e[t].isRead=!0,q(e),_((e=>e-1))}else o()}catch(e){o(e)}},children:e.jsx(b,{})})}),e.jsx(R,{title:m.DELETE,children:e.jsx(T,{onClick:()=>{Q([a]),J(!0)},children:e.jsx(C,{})})})]})]})]})]},a._id)))}),e.jsxs("div",{className:"footer",children:[$>-1&&e.jsx("div",{className:"row-count",children:`${(Z-1)*r.PAGE_SIZE+1}-${$} ${m.OF} ${W}`}),e.jsxs("div",{className:"actions",children:[e.jsx(T,{disabled:1===Z,onClick:()=>{const e=Z-1;B(e<Math.ceil(W/r.PAGE_SIZE)?(e-1)*r.PAGE_SIZE+r.PAGE_SIZE:W),V(e)},children:e.jsx(I,{className:"icon"})}),e.jsx(T,{disabled:(Z-1)*r.PAGE_SIZE+U.length>=W,onClick:()=>{const e=Z+1;B(e<Math.ceil(W/r.PAGE_SIZE)?(e-1)*r.PAGE_SIZE+r.PAGE_SIZE:W),V(e)},children:e.jsx(N,{className:"icon"})})]})]}),e.jsxs(k,{disableEscapeKeyDown:!0,maxWidth:"xs",open:Y,children:[e.jsx(D,{className:"dialog-header",children:m.CONFIRM_TITLE}),e.jsx(M,{children:H.length>1?G.DELETE_NOTIFICATIONS:G.DELETE_NOTIFICATION}),e.jsxs(v,{className:"dialog-actions",children:[e.jsx(P,{onClick:()=>{J(!1)},variant:"contained",className:"btn-secondary",children:m.CANCEL}),e.jsx(P,{onClick:async()=>{try{if(!s||!s._id)return void o();const e=H.map((e=>e._id));if(200===await E(s._id,e)){if(H.length===U.length){const e=1,s=W-H.length;B(e<Math.ceil(s/r.PAGE_SIZE)?(e-1)*r.PAGE_SIZE+r.PAGE_SIZE:s),Z>1?V(1):ae()}else{const s=c(U);q(s.filter((s=>!e.includes(s._id)))),B($-H.length),z(W-H.length)}_((e=>e-H.filter((e=>!e.isRead)).length)),J(!1)}else o()}catch(e){o(e)}},variant:"contained",color:"error",children:m.DELETE})]})]})]})]}),u&&e.jsx(f,{text:m.LOADING})]})},V=()=>{const s=_.c(3),[i,a]=j.useState();let t;s[0]===Symbol.for("react.memo_cache_sentinel")?(t=async e=>{a(e)},s[0]=t):t=s[0];const o=t;let r;return s[1]!==i?(r=e.jsx(u,{onLoad:o,strict:!0,children:e.jsx(Z,{user:i})}),s[1]=i,s[2]=r):r=s[2],r};export{V as default};
