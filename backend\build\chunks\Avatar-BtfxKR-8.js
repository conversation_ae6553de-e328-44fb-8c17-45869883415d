import{j as a,p as e,a as s,h as i,R as t,z as r,G as c,e as o,az as n,g as l,L as d,aA as m,aB as h}from"../entries/index-CEzJO5Xy.js";import{r as v}from"./router-BtYqujaw.js";import{b as u,f as x,a as j,h as f,i as p}from"./DressService-J0XavNJj.js";import{L as g,a as N,f as w,e as E,h as D,i as y}from"./LocationService-BtQFgoWL.js";import{B as S}from"./Badge-B3LKl4T2.js";import{T as L}from"./Tooltip-BkJF6Mu0.js";import{B as C}from"./Box-CHHh9iS3.js";import{V as _}from"./Check-D745pofy.js";import{A as b}from"./Avatar-Dix3YM8x.js";import{c as R,D as z,a as A,b as I}from"./Grow-CjOKj0i1.js";import{D as O}from"./Checkroom-Bt6MiDKF.js";import{A as T}from"./AccountCircle-khVEeiad.js";import{D as U}from"./DialogTitle-BZXwroUN.js";import{B as V}from"./Button-DGZYUY3P.js";const M=R([a.jsx("path",{d:"m13.99 15.41-4-4-4 4-.99-.99V19h14v-6.57l-1.01-1.01zM5 11.59l.99 1 4-4 4 4 4-4.01L19 9.59V5H5z",opacity:".3"},"0"),a.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16H5v-4.58l.99.99 4-4 4 4 4-3.99L19 12.43zm0-9.41-1.01-1.01-4 4.01-4-4-4 4-.99-1V5h14z"},"1")]),H=R(a.jsx("path",{d:"M12 7V3H2v18h20V7zm-2 12H4v-2h6zm0-4H4v-2h6zm0-4H4V9h6zm0-4H4V5h6zm10 12h-8V9h8zm-2-8h-4v2h4zm0 4h-4v2h4z"})),$=R([a.jsx("circle",{cx:"12",cy:"12",r:"3.2"},"0"),a.jsx("path",{d:"M9 2 7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5"},"1")]),k=({avatar:R,width:k,height:B,mode:P,type:F,record:G,size:W,readonly:K,color:Q,className:Y,verified:X,hideDelete:Z,onValidate:q,onBeforeUpload:J,onChange:aa})=>{const[ea,sa]=v.useState(!1),[ia,ta]=v.useState(!1),[ra,ca]=v.useState(!1),[oa,na]=v.useState(),[la,da]=v.useState(null),[ma,ha]=v.useState(!0);v.useEffect((()=>{da(R)}),[R]);const va=async(a,e)=>{if(k&&B){const s=window.URL||window.webkitURL,i=new Image,t=s.createObjectURL(a);i.onload=async()=>{k!==i.width||B!==i.height?q&&q(!1):(q&&q(!0),e&&await e()),s.revokeObjectURL(t)},i.src=t}else e&&e()},ua=()=>{if(!F)return void ca(!0);const a=document.getElementById("upload");a.value="",setTimeout((()=>{a.click()}),0)},xa=()=>{ta(!1)},ja=()=>F===t.Dress?"create"===P?o.CDN_TEMP_DRESSES:o.CDN_DRESSES:F===t.Location?"create"===P?o.CDN_TEMP_LOCATIONS:o.CDN_LOCATIONS:"create"===P?o.CDN_TEMP_USERS:o.CDN_USERS;v.useEffect((()=>{const a=e();s.setLanguage(a),i()?G?(na(G),F===t.Dress||F===t.Location?da(G.image):da(G.avatar),ha(!1)):"create"===P&&ha(!1):(sa(!0),r())}),[G,F,P]);const fa={width:o.SUPPLIER_IMAGE_WIDTH},pa={width:o.DRESS_IMAGE_WIDTH},ga={maxWidth:"100%",maxHeight:"100%"},Na=la?la.startsWith("http")?la:c(ja(),la):"",wa=la?a.jsx(b,{src:Na,className:W?`avatar-${W}`:"avatar"}):a.jsx(a.Fragment,{}),Ea=a.jsx(T,{className:W?`avatar-${W}`:"avatar",color:Q||"inherit"});return ea||ma?null:a.jsxs("div",{className:Y,children:[la?K?F===t.Dress?a.jsx("img",{style:pa,src:c(ja(),la),alt:oa&&oa.name}):F===t.Location?a.jsx("img",{style:ga,src:c(ja(),la),alt:oa&&oa.name}):F===t.Supplier?a.jsx("div",{className:"supplier-avatar-readonly",children:a.jsx("img",{src:c(ja(),la),alt:oa&&oa.fullName})}):X&&oa&&oa.verified?a.jsx(S,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:a.jsx(L,{title:s.VERIFIED,children:a.jsx(C,{sx:{borderRadius:"50%"},className:W?`user-avatar-verified-${W}`:"user-avatar-verified-medium",children:a.jsx(_,{className:W?`user-avatar-verified-icon-${W}`:"user-avatar-verified-icon-medium"})})}),children:wa}):wa:a.jsx(S,{overlap:"circular",anchorOrigin:{vertical:"top",horizontal:"right"},badgeContent:Z?a.jsx(a.Fragment,{}):a.jsx(L,{title:s.DELETE_IMAGE,children:a.jsx(C,{sx:{borderRadius:"50%"},className:"avatar-action-box",onClick:a=>{a.preventDefault(),ta(!0)},children:a.jsx(M,{className:"avatar-action-icon"})})}),children:a.jsx(S,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},className:F===t.Supplier?"supplier-avatar":"",badgeContent:a.jsx(L,{title:s.UPLOAD_IMAGE,children:a.jsx(C,{sx:{borderRadius:"50%"},className:"avatar-action-box",onClick:ua,children:a.jsx($,{className:"avatar-action-icon"})})}),children:F===t.Dress?a.jsx("div",{className:"dress-avatar",children:a.jsx("img",{src:c(ja(),la),alt:oa&&oa.name})}):F===t.Location?a.jsx("div",{className:"location-avatar",children:a.jsx("img",{src:c(ja(),la),alt:oa&&oa.name})}):F===t.Supplier?a.jsx("img",{style:fa,src:c(ja(),la),alt:oa&&oa.fullName}):a.jsx(b,{src:Na,className:W?`avatar-${W}`:"avatar"})})}):K?F===t.Dress?a.jsx(O,{style:pa,color:Q||"inherit"}):F===t.Location?a.jsx(g,{style:ga,color:Q||"inherit"}):F===t.Supplier?a.jsx(H,{style:fa,color:Q||"inherit"}):X&&oa&&oa.verified?a.jsx(S,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:a.jsx(L,{title:s.VERIFIED,children:a.jsx(C,{sx:{borderRadius:"50%"},className:W?`user-avatar-verified-${W}`:"user-avatar-verified-medium",children:a.jsx(_,{className:W?`user-avatar-verified-icon-${W}`:"user-avatar-verified-icon-medium"})})}),children:Ea}):Ea:a.jsx(S,{overlap:"circular",anchorOrigin:{vertical:"top",horizontal:"right"},children:a.jsx(S,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:a.jsx(L,{title:s.UPLOAD_IMAGE,children:a.jsx(C,{sx:{borderRadius:"50%"},className:"avatar-action-box",onClick:ua,children:a.jsx($,{className:"avatar-action-icon"})})}),children:F===t.Dress?a.jsx(O,{className:W?`avatar-${W}`:"avatar",color:Q||"inherit"}):F===t.Location?a.jsx(g,{className:W?`avatar-${W}`:"avatar",color:Q||"inherit"}):F===t.Supplier?a.jsx(H,{className:W?`avatar-${W}`:"avatar",color:Q||"inherit"}):a.jsx(T,{className:W?`avatar-${W}`:"avatar",color:Q||"inherit"})})}),a.jsxs(z,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ra,children:[a.jsx(U,{className:"dialog-header",children:s.INFO}),a.jsx(A,{children:s.USER_TYPE_REQUIRED}),a.jsx(I,{className:"dialog-actions",children:a.jsx(V,{onClick:()=>{ca(!1)},variant:"contained",className:"btn-secondary",children:s.CLOSE})})]}),a.jsxs(z,{disableEscapeKeyDown:!0,maxWidth:"xs",open:ia,children:[a.jsx(U,{className:"dialog-header",children:s.CONFIRM_TITLE}),a.jsx(A,{children:s.DELETE_AVATAR_CONFIRM}),a.jsxs(I,{className:"dialog-actions",children:[a.jsx(V,{onClick:()=>{xa()},className:"btn-secondary",children:s.CANCEL}),a.jsx(V,{onClick:async()=>{try{if(F===t.Admin||F===t.Supplier||F===t.User)if(oa&&"update"===P){const{_id:a}=oa;if(!a)return void r();if(200===await n(a)){const e=await l(a);e?(na(e),da(null),aa&&aa(""),xa()):r()}else r()}else oa||"create"!==P||(200===await d(la)?(da(null),aa&&aa(""),xa()):r());else if(F===t.Dress)if(oa||"create"!==P){if(oa&&"update"===P){const{_id:a}=oa;if(!a)return void r();if(200===await x(a)){const e=await j(a);e?(na(e),da(null),aa&&aa(""),xa()):r()}else r()}}else 200===await u(la)?(da(null),aa&&aa(""),xa()):r();else if(F===t.Location)if(oa||"create"!==P){if(oa&&"update"===P){const{_id:a}=oa;if(!a)return void r();if(200===await w(a)){const e=await E(a);e?(na(e),da(null),aa&&aa(""),xa()):r()}else r()}}else 200===await N(la)?(da(null),aa&&aa(""),xa()):r()}catch(a){r(a)}},color:"error",variant:"contained",children:s.DELETE})]})]}),!K&&a.jsx("input",{id:"upload",type:"file",hidden:!0,onChange:a=>{if(!a.target.files)return void r();J&&J();const e=new FileReader,s=a.target.files[0];e.onloadend=async()=>{if(F===t.Admin||F===t.Supplier||F===t.User){if("create"===P){const a=async()=>{try{la&&await d(la);const a=await m(s);da(a),aa&&aa(a)}catch(a){r(a)}};await va(s,a)}else if(oa&&"update"===P){const a=async()=>{try{const{_id:a}=oa;if(!a)return void r();if(200===await h(a,s)){const e=await l(a);e?(na(e),da(e.avatar||""),aa&&aa(e.avatar||"")):r()}else r()}catch(a){r(a)}};await va(s,a)}}else if(F===t.Dress){if("create"===P){const a=async()=>{try{la&&await u(la);const a=await f(s);da(a),aa&&aa(a)}catch(a){r(a)}};await va(s,a)}else if("update"===P){const a=async()=>{try{if(!oa)return void r();const{_id:a}=oa;if(!a)return void r();if(200===await p(a,s)){const e=await j(a);e?(na(e),da(e.image||""),aa&&aa(e.image||"")):r()}else r()}catch(a){r(a)}};await va(s,a)}}else if(F===t.Location)if("create"===P){const a=async()=>{try{la&&await N(la);const a=await D(s);da(a),aa&&aa(a)}catch(a){r(a)}};await va(s,a)}else if("update"===P){const a=async()=>{try{if(!oa)return void r();const{_id:a}=oa;if(!a)return void r();if(200===await y(a,s)){const e=await E(a);e?(na(e),da(e.image||""),aa&&aa(e.image||"")):r()}else r()}catch(a){r(a)}};await va(s,a)}},e.readAsDataURL(s)}})]})};export{k as A,H as S};
