import LocalizedStrings from 'localized-strings'
import * as langHelper from '@/common/langHelper'

const strings = new LocalizedStrings({
  fr: {
    DASHBOARD: 'Tableau de bord',
    SCHEDULER: 'Planificateur',
    HOME: 'Accueil',
    COMPANIES: 'Fournisseurs',
    LOCATIONS: 'Lieux',
    CARS: 'Voitures',
    DRESSES: 'Robes',
    USERS: 'Utilisateurs',
    ABOUT: 'À propos',
    TOS: "Conditions d'utilisation",
    CONTACT: 'Contact',
    LANGUAGE: 'Langue',
    SETTINGS: 'Paramètres',
    SIGN_OUT: 'Déconnexion',
    COUNTRIES: 'Pays',
    BANK_DETAILS: 'Détails bancaires',
    PRICING: 'Tarification',
  },
  en: {
    DASHBOARD: 'Dashboard',
    SCHEDULER: 'Scheduler',
    HOME: 'Home',
    COMPANIES: 'Suppliers',
    LOCATIONS: 'Locations',
    CARS: 'Cars',
    DRESSES: 'Dresses',
    USERS: 'Users',
    ABOUT: 'About',
    TOS: 'Terms of Service',
    CONTACT: 'Contact',
    LANGUAGE: 'Language',
    SETTINGS: 'Settings',
    SIGN_OUT: 'Sign out',
    COUNTRIES: 'Countries',
    BANK_DETAILS: 'Bank Details',
    PRICING: 'Pricing',
  },
  es: {
    DASHBOARD: 'Panel de control',
    SCHEDULER: 'Programador',
    HOME: 'Inicio',
    COMPANIES: 'Proveedores',
    LOCATIONS: 'Ubicaciones',
    CARS: 'Coches',
    DRESSES: 'Vestidos',
    USERS: 'Usuarios',
    ABOUT: 'Acerca de',
    TOS: 'Términos de servicio',
    CONTACT: 'Contacto',
    LANGUAGE: 'Idioma',
    SETTINGS: 'Configuración',
    SIGN_OUT: 'Cerrar sesión',
    COUNTRIES: 'Países',
    BANK_DETAILS: 'Detalles bancarios',
    PRICING: 'Precios',
  },
  ar: {
    DASHBOARD: 'لوحة التحكم',
    SCHEDULER: 'المجدول',
    HOME: 'الرئيسية',
    COMPANIES: 'الموردين',
    LOCATIONS: 'المواقع',
    CARS: 'السيارات',
    DRESSES: 'الفساتين',
    USERS: 'المستخدمين',
    ABOUT: 'حول',
    TOS: 'شروط الخدمة',
    CONTACT: 'اتصل بنا',
    LANGUAGE: 'اللغة',
    SETTINGS: 'الإعدادات',
    SIGN_OUT: 'تسجيل الخروج',
    COUNTRIES: 'البلدان',
    BANK_DETAILS: 'التفاصيل المصرفية',
    PRICING: 'التسعير',
  },
})

langHelper.setLanguage(strings)
export { strings }
