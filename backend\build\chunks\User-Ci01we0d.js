import{c as e,a5 as s,R as t,j as o,a as i,G as r,e as a,ab as c,z as n,g as l,F as m,a7 as p}from"../entries/index-xsXxT3-W.js";import{d,r as j}from"./router-BtYqujaw.js";import{s as h}from"./user-list-CNjMkwmb.js";import{L as u}from"./Layout-DaeN7D4t.js";import{S as f}from"./SimpleBackdrop-CqsJhYJ4.js";import{A as S}from"./Avatar-CvDHTACZ.js";import{B as x}from"./BookingList-XbPSaIIt.js";import _ from"./NoMatch-DMPclUW6.js";import{h as y}from"./SupplierService-9DC5V5ZJ.js";import{T as b}from"./Backdrop-Czag2Ija.js";import{L as N}from"./Link-sHEcszvT.js";import{T as v}from"./Tooltip-CKMkVqOx.js";import{I as g}from"./IconButton-CxOCoGF3.js";import{E}from"./Edit-Bc0UCPtn.js";import{D as C}from"./Delete-BfnPAJno.js";import{a as L,b as T,d as k,D as w}from"./Grow-Cp8xsNYl.js";import{B as D}from"./Button-BeKLLPpp.js";import"./vendor-dblfw9z9.js";import"./DressService-DkS6e_O5.js";import"./LocationService-6NvQT9iL.js";import"./Badge-zckTAo43.js";import"./AccountCircle-DdIeIbov.js";import"./useSlot-DiTut-u0.js";import"./Box-Dm2ZtwWL.js";import"./Check-BO6X9Q-4.js";import"./Avatar-Dvwllg8p.js";import"./cars-xqBbVU4C.js";import"./BookingStatus-BaSj8uqV.js";import"./InputLabel-C8rcdOGQ.js";import"./isHostComponent-DR4iSCFs.js";import"./useFormControl-B7jXtRD7.js";import"./ownerWindow-ChLfdzZL.js";import"./TextField-D_yQOTzE.js";import"./OutlinedInput-BX8yFQbF.js";import"./Input-D1AdR9CM.js";import"./FormHelperText-DDZ4BMA4.js";import"./Menu-C_-X8cS7.js";import"./Paper-C-atefOs.js";import"./mergeSlotProps-DEridHif.js";import"./MenuItem-P0BnGnrT.js";import"./listItemTextClasses-BcbgzvlE.js";import"./fr-DJt_zj3p.js";import"./DataGrid-DJUEcXft.js";import"./getThemeProps-DSP27jpP.js";import"./Switch-C5asfh_w.js";import"./SwitchBase-DrUkTXjH.js";import"./Toolbar-BTa0QYME.js";import"./KeyboardArrowRight-LSgfnPVa.js";import"./Chip-MGF1mKZa.js";import"./Autocomplete-CWN5GAd4.js";import"./ListItemText-DUhWzkV9.js";import"./Checkbox-F0AjCtoF.js";const I=()=>{const I=e.c(44),B=d();let M;I[0]===Symbol.for("react.memo_cache_sentinel")?(M=s().map(A),I[0]=M):M=I[0];const F=M,[P,U]=j.useState(),[G,H]=j.useState(),[R,O]=j.useState(!1),[z,K]=j.useState(!0),[W,J]=j.useState(!1),[V,$]=j.useState(!1);let q;I[1]===Symbol.for("react.memo_cache_sentinel")?(q=[],I[1]=q):q=I[1];const[Q,X]=j.useState(q),[Y,Z]=j.useState(0);let ee,se,te;I[2]!==R?(ee=()=>{if(R){const e=document.querySelector(".col-1");e&&Z(e.clientHeight)}},se=[R],I[2]=R,I[3]=ee,I[4]=se):(ee=I[3],se=I[4]),j.useEffect(ee,se),I[5]===Symbol.for("react.memo_cache_sentinel")?(te=()=>{K(!0)},I[5]=te):te=I[5];const oe=te;let ie;I[6]===Symbol.for("react.memo_cache_sentinel")?(ie=()=>{K(!1)},I[6]=ie):ie=I[6];const re=ie;let ae;I[7]===Symbol.for("react.memo_cache_sentinel")?(ae=()=>{$(!0)},I[7]=ae):ae=I[7];const ce=ae;let ne;I[8]!==B||I[9]!==G?(ne=async()=>{try{G?($(!1),200===await c([G._id])?B("/users"):(n(),K(!1))):n()}catch(e){n(e)}},I[8]=B,I[9]=G,I[10]=ne):ne=I[10];const le=ne;let me;I[11]===Symbol.for("react.memo_cache_sentinel")?(me=()=>{$(!1)},I[11]=me):me=I[11];const pe=me;let de;I[12]===Symbol.for("react.memo_cache_sentinel")?(de=async e=>{if(e&&e.verified){K(!0);const t=new URLSearchParams(window.location.search);if(t.has("u")){const o=t.get("u");if(o&&""!==o)try{const s=await l(o);if(s){const t=t=>{X(t),U(e),H(s),O(!0),K(!1)};if(m(e)){const e=await y();t(p(e))}else t([e._id])}else K(!1),J(!0)}catch(s){n(s),K(!1),O(!1)}else K(!1),J(!0)}else K(!1),J(!0)}},I[12]=de):de=I[12];const je=de,he=P&&G&&(P.type===t.Admin||P._id===G._id||P.type===t.Supplier&&P._id===G.supplier),ue=G&&G.type===t.Supplier;let fe;I[13]===Symbol.for("react.memo_cache_sentinel")?(fe=[],I[13]=fe):fe=I[13];let Se,xe,_e,ye,be,Ne,ve,ge,Ee,Ce=fe;if(P&&G)if(ue&&P._id===G._id||P.type===t.Admin&&G.type===t.Supplier){const e=G._id;let s;I[14]!==e?(s=[e],I[14]=e,I[15]=s):s=I[15],Ce=s}else if(P.type===t.Supplier&&G.type===t.User){const e=P._id;let s;I[16]!==e?(s=[e],I[16]=e,I[17]=s):s=I[17],Ce=s}else P.type===t.Admin&&(Ce=Q);return I[18]!==Ce||I[19]!==he||I[20]!==P||I[21]!==B||I[22]!==Y||I[23]!==ue||I[24]!==G||I[25]!==R?(Se=P&&G&&R&&o.jsxs("div",{className:"user",children:[o.jsxs("div",{className:"col-1",children:[o.jsx("section",{className:"user-avatar-sec",children:o.jsx(S,{record:G,type:G.type,mode:"update",size:"large",hideDelete:!0,onBeforeUpload:oe,onChange:re,color:"disabled",className:ue?"supplier-avatar":"user-avatar",readonly:!0,verified:!0})}),o.jsx(b,{variant:"h4",className:"user-name",children:G.fullName}),G.bio&&o.jsx(b,{variant:"h6",className:"user-info",children:G.bio}),G.location&&o.jsx(b,{variant:"h6",className:"user-info",children:G.location}),G.phone&&o.jsx(b,{variant:"h6",className:"user-info",children:G.phone}),G.license&&o.jsxs("div",{className:"license",children:[o.jsx("span",{children:i.LICENSE}),o.jsx(N,{href:r(a.CDN_LICENSES,G.license),target:"_blank",children:G.license})]}),o.jsxs("div",{className:"user-actions",children:[he&&o.jsx(v,{title:i.UPDATE,children:o.jsx(g,{onClick:()=>B(`/update-user?u=${G._id}`),children:o.jsx(E,{})})}),he&&o.jsx(v,{title:i.DELETE,children:o.jsx(g,{"data-id":G._id,onClick:ce,children:o.jsx(C,{})})})]})]}),o.jsx("div",{className:"col-2",children:Ce.length>0&&o.jsx(x,{containerClassName:"user",offset:Y,loggedUser:P,user:ue?void 0:G,suppliers:Ce,statuses:F,hideDates:a.isMobile,checkboxSelection:!a.isMobile,hideSupplierColumn:ue,language:P.language})})]}),I[18]=Ce,I[19]=he,I[20]=P,I[21]=B,I[22]=Y,I[23]=ue,I[24]=G,I[25]=R,I[26]=Se):Se=I[26],I[27]===Symbol.for("react.memo_cache_sentinel")?(xe=o.jsx(L,{className:"dialog-header",children:i.CONFIRM_TITLE}),_e=o.jsx(T,{children:h.DELETE_USER}),I[27]=xe,I[28]=_e):(xe=I[27],_e=I[28]),I[29]===Symbol.for("react.memo_cache_sentinel")?(ye=o.jsx(D,{onClick:pe,variant:"contained",className:"btn-secondary",children:i.CANCEL}),I[29]=ye):ye=I[29],I[30]!==le?(be=o.jsxs(k,{className:"dialog-actions",children:[ye,o.jsx(D,{onClick:le,variant:"contained",color:"error",children:i.DELETE})]}),I[30]=le,I[31]=be):be=I[31],I[32]!==V||I[33]!==be?(Ne=o.jsxs(w,{disableEscapeKeyDown:!0,maxWidth:"xs",open:V,children:[xe,_e,be]}),I[32]=V,I[33]=be,I[34]=Ne):Ne=I[34],I[35]!==z?(ve=z&&o.jsx(f,{text:i.LOADING}),I[35]=z,I[36]=ve):ve=I[36],I[37]!==W?(ge=W&&o.jsx(_,{hideHeader:!0}),I[37]=W,I[38]=ge):ge=I[38],I[39]!==Se||I[40]!==Ne||I[41]!==ve||I[42]!==ge?(Ee=o.jsxs(u,{onLoad:je,strict:!0,children:[Se,Ne,ve,ge]}),I[39]=Se,I[40]=Ne,I[41]=ve,I[42]=ge,I[43]=Ee):Ee=I[43],Ee};function A(e){return e.value}export{I as default};
